package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.vo.LuggageCheckVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ILuggageService {

    /**
     * checkLuggage
     *
     * @param luggageInfo luggageInfo
     * @return checkLuggage
     * @throws UnifiedResultException
     */
    LuggageCheckVo checkLuggage(String luggageInfo) throws UnifiedResultException;

    /**
     * addLuggage
     *
     * @param luggageCheckVo luggageCheckVo
     */
    void addLuggage(LuggageCheckVo luggageCheckVo) throws UnifiedResultException;

    /**
     * updateRemark
     *
     * @param luggageCheckVo luggageCheckVo
     * @throws UnifiedResultException 统一异常
     */
    void updateRemark(LuggageCheckVo luggageCheckVo) throws UnifiedResultException;

    /**
     * updateStatus
     *
     * @param luggageCheckVos luggageCheckVos
     * @throws UnifiedResultException 统一异常
     */
    void updateStatus(List<LuggageCheckVo> luggageCheckVos) throws UnifiedResultException;

    /**
     * queryPage
     *
     * @param page           page
     * @param luggageCheckVo luggageCheckVo
     * @return queryPage
     */
    IPage<LuggageCheckVo> queryPage(Page page, LuggageCheckVo luggageCheckVo);

    /**
     * queryAbnormal
     *
     * @param status status
     * @return queryAbnormal
     */
    List<LuggageCheckVo> queryAbnormal(String status);

}
