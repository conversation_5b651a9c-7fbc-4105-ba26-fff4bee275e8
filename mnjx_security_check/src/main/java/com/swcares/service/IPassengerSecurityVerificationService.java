package com.swcares.service;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.obj.dto.PassengerFlightMsgDto;
import com.swcares.obj.dto.PassengerSecurityVerificationDto;

/**
 * <AUTHOR>
 */
public interface IPassengerSecurityVerificationService {

    /**
     * passengerSecurityVerification
     *
     * @param passengerSecurityVerification passengerSecurityVerification
     * @return passengerSecurityVerification
     * @throws UnifiedResultException 统一异常
     */
    UnifiedResult passengerSecurityVerification(PassengerSecurityVerificationDto passengerSecurityVerification) throws UnifiedResultException;


    /**
     * 值机信息查询
     *
     * @param passengerFlightMsgDto passengerFlightMsgDto
     * @return SecurityCheckResultVo
     * @throws UnifiedResultException 统一异常
     */
    UnifiedResult retrieveDetail(PassengerFlightMsgDto passengerFlightMsgDto) throws UnifiedResultException;

}
