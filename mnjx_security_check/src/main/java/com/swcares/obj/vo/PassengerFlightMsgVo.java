package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * [安检旅客详情信息对象]
 *
 * <AUTHOR> by sxl
 * 2023/2/22-15:33
 */
@Data
@Api(tags = "安检旅客详情信息")
public class PassengerFlightMsgVo {

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "姓名XINGMING")
    private String queryName;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "起飞时间")
    private String estimateOff;

    @ApiModelProperty(value = "座位")
    private String psgSeat;

    @ApiModelProperty(value = "起飞机场编号")
    private String org;

    @ApiModelProperty(value = "起飞城市名称")
    private String orgCityName;

    @ApiModelProperty(value = "起飞城市")
    private String orgCityCode;

    @ApiModelProperty(value = "目的地机场编号")
    private String dst;

    @ApiModelProperty(value = "目的地城市名称")
    private String dstCityName;

    @ApiModelProperty(value = "目的地城市")
    private String dstCityCode;

    @ApiModelProperty(value = "证件主键")
    private String nmSsrId;

    @ApiModelProperty(value = "证件编号输入")
    private String idInput;

    @ApiModelProperty(value = "值机序号")
    private String aboardNo;

    @ApiModelProperty(value = "值机序号")
    private String gate;

    @ApiModelProperty(value = "值机主键")
    private String psgCkiId;

    @ApiModelProperty(value = "旅客值机状态")
    private String ckiStatus;
}
