package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class VerifyInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private String verifyId;

    @ApiModelProperty("psgCkiId")
    private String psgCkiId;

    @ApiModelProperty("相似度")
    private String similarity;

    @ApiModelProperty("是否通过")
    private String validat;

    @ApiModelProperty("验证时间")
    private String verifyTime;
}
