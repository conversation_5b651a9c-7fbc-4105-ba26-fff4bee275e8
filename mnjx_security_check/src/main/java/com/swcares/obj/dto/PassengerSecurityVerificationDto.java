package com.swcares.obj.dto;

import io.swagger.annotations.Api;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Api(tags = "旅客安检验证信息")
@Data
public class PassengerSecurityVerificationDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @Valid
    private CheckInDto checkInInfo;

    @Valid
    private CertificateInfoDto certificateInfo;
}
