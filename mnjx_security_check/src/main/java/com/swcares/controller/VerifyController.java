package com.swcares.controller;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.util.Constant;
import com.swcares.obj.vo.VerifyInfoVo;
import com.swcares.service.IVerifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Api(tags = "旅客安检验证接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RequestMapping(value = "/verify")
@RestController
public class VerifyController {

    @Resource
    private IVerifyService verifyService;

    @ApiOperation("添加旅客安检信息")
    @PostMapping("/addVerifyInfo")
    public UnifiedResult addVerifyInfo(@RequestBody VerifyInfoVo verifyInfoVo) {
        verifyService.addVerifyInfo(verifyInfoVo);
        return UnifiedResult.ok(Constant.CREATE_SUCCESS);
    }
}
