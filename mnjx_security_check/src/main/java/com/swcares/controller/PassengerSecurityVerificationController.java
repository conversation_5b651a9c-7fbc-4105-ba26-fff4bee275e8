package com.swcares.controller;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.obj.dto.PassengerFlightMsgDto;
import com.swcares.obj.dto.PassengerSecurityVerificationDto;
import com.swcares.service.IPassengerSecurityVerificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api(tags = "旅客安检验证")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RequestMapping(value = "/passengerSecurityVerification")
@RestController
public class PassengerSecurityVerificationController {

    @Resource
    private IPassengerSecurityVerificationService passengerSecurityVerificationService;

    /**
     * 旅客信息安检
     */
    @PostMapping
    public UnifiedResult passengerSecurityVerification(@RequestBody @Valid PassengerSecurityVerificationDto passengerSecurityVerification) throws UnifiedResultException {
        return passengerSecurityVerificationService.passengerSecurityVerification(passengerSecurityVerification);
    }

    @ApiOperation("安检查询旅客详情")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "securityCheckQueryVo",
                    value = "安检旅客查询条件",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = PassengerFlightMsgDto.class
            )
    })
    @PostMapping("/retrieveDetail")
    public UnifiedResult retrieveDetail(@RequestBody PassengerFlightMsgDto passengerFlightMsgDto) throws UnifiedResultException {
        return passengerSecurityVerificationService.retrieveDetail(passengerFlightMsgDto);
    }
}
