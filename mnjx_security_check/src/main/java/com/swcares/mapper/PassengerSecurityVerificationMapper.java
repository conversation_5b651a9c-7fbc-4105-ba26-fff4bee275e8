package com.swcares.mapper;

import com.swcares.entity.MnjxPlanFlight;
import com.swcares.entity.MnjxPnrNmTicket;
import com.swcares.obj.dto.CheckInDto;
import com.swcares.obj.dto.PassengerFlightMsgDto;
import com.swcares.obj.vo.PassengerCheckInVo;
import com.swcares.obj.vo.PassengerFlightMsgVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface PassengerSecurityVerificationMapper {

    /**
     * 查询旅客值机信息
     *
     * @param check check
     * @return 查询旅客值机信息
     */
    PassengerCheckInVo retrievePassengerCheckIn(@Param("check") CheckInDto check);

    /**
     * retrievePlanFlight
     *
     * @param flightNo   flightNo
     * @param flightDate flightDate
     * @return retrievePlanFlight
     */
    MnjxPlanFlight retrievePlanFlight(@Param("flightNo") String flightNo, @Param("flightDate") String flightDate);

    /**
     * retrieveTicketStatus
     *
     * @param pnrNmId pnrNmId
     * @return retrieveTicketStatus
     */
    MnjxPnrNmTicket retrieveTicketStatus(@Param("pnrNmId") String pnrNmId);

    /**
     * retrieveDetail
     *
     * @param param param
     * @return retrieveDetail
     */
    PassengerFlightMsgVo retrieveDetail(@Param("param") PassengerFlightMsgDto param);
}
