<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.PassengerSecurityVerificationMapper">

    <select id="retrievePassengerCheckIn" resultType="com.swcares.obj.vo.PassengerCheckInVo">
        SELECT mps.pnr_id,
        mps.pnr_seg_id,
        mpc.psg_cki_id,
        mps.flight_no,
        mps.flight_date,
        mps.pnr_seg_no,
        mps.org,
        mps.dst,
        mps.estimate_off,
        mpn.pnr_nm_id,
        mpn.name,
        mpn.query_name,
        mpn.sex,
        mns.ssr_info as id_no,
        mpc.gate,
        mpc.cki_status,
        mpc.aboard_no,
        mpseat.psg_seat
        FROM mnjx_pnr_seg mps
        LEFT JOIN mnjx_pnr_nm mpn ON mps.pnr_id = mpn.pnr_id
        LEFT JOIN mnjx_nm_ssr mns ON mpn.pnr_nm_id = mns.pnr_nm_id AND mns.ssr_type = 'FOID'
        left join mnjx_psg_cki mpc on mpn.pnr_nm_id = mpc.pnr_nm_id
        left join mnjx_psg_seat mpseat on mpc.psg_cki_id = mpseat.psg_cki_id
        WHERE 1 = 1
        <if test="check.flightNo != null and check.flightNo != ''">
            AND mps.flight_no = #{check.flightNo}
        </if>
        <if test="check.flightDate != null and check.flightDate != ''">
            AND mps.flight_date = #{check.flightDate}
        </if>
        <if test="check.idNo != null and check.idNo != ''">
            AND mns.ssr_info LIKE concat('%', #{check.idNo}, '%')
        </if>
        <if test="check.gateNo != null and check.gateNo != ''">
            and mpc.gate = #{check.gateNo}
        </if>
        <if test="check.seatNo != null and check.seatNo != ''">
            and mpseat.psg_seat = #{check.seatNo}
        </if>
        <if test="check.bnNo != null and check.bnNo != ''">
            and mpc.aboard_no = #{check.bnNo}
        </if>
        <if test="check.dept != null and check.dept != ''">
            and mps.org = #{check.dept}
        </if>
        <if test="check.dest != null and check.dest != ''">
            and mps.dst = #{check.dest}
        </if>
        <if test="check.estimateOff != null and check.estimateOff != ''">
            and mps.estimate_off = #{check.estimateOff}
        </if>
    </select>

    <select id="retrievePassengerCheckInAndCarrierFlight" resultType="com.swcares.obj.vo.PassengerCheckInVo">
        SELECT mps.pnr_id,
        mps.pnr_seg_id,
        mpc.psg_cki_id,
        mps.flight_no,
        mps.flight_date,
        mps.pnr_seg_no,
        mps.org,
        mps.dst,
        mps.estimate_off,
        mpn.pnr_nm_id,
        mpn.name,
        mpn.query_name,
        mpn.sex,
        mns.ssr_info as id_no,
        mpc.gate,
        mpc.cki_status,
        mpc.aboard_no,
        mpseat.psg_seat
        FROM mnjx_pnr_seg mps
        LEFT JOIN mnjx_pnr_nm mpn ON mps.pnr_id = mpn.pnr_id
        LEFT JOIN mnjx_nm_ssr mns ON mpn.pnr_nm_id = mns.pnr_nm_id AND mns.ssr_type = 'FOID'
        left join mnjx_psg_cki mpc on mpn.pnr_nm_id = mpc.pnr_nm_id
        left join mnjx_psg_seat mpseat on mpc.psg_cki_id = mpseat.psg_cki_id
        WHERE 1 = 1
        <if test="check.flightNo != null and check.flightNo != ''">
            AND mps.carrier_flight = #{check.flightNo}
        </if>
        <if test="check.flightDate != null and check.flightDate != ''">
            AND mps.flight_date = #{check.flightDate}
        </if>
        <if test="check.idNo != null and check.idNo != ''">
            AND mns.ssr_info LIKE concat('%', #{check.idNo}, '%')
        </if>
        <if test="check.gateNo != null and check.gateNo != ''">
            and mpc.gate = #{check.gateNo}
        </if>
        <if test="check.seatNo != null and check.seatNo != ''">
            and mpseat.psg_seat = #{check.seatNo}
        </if>
        <if test="check.bnNo != null and check.bnNo != ''">
            and mpc.aboard_no = #{check.bnNo}
        </if>
        <if test="check.dept != null and check.dept != ''">
            and mps.org = #{check.dept}
        </if>
        <if test="check.dest != null and check.dest != ''">
            and mps.dst = #{check.dest}
        </if>
        <if test="check.estimateOff != null and check.estimateOff != ''">
            and mps.estimate_off = #{check.estimateOff}
        </if>
    </select>

    <select id="retrievePlanFlight" resultType="com.swcares.entity.MnjxPlanFlight">
        SELECT mpf.*
        FROM mnjx_flight mf
        LEFT JOIN mnjx_tcard mt ON mf.flight_id = mt.flight_id
        LEFT JOIN mnjx_plan_flight mpf ON mt.tcard_id = mpf.tcard_id
        WHERE 1 = 1
        <if test="flightNo != null and flightNo != ''">
            AND mf.flight_no = #{flightNo}
        </if>
        <if test="flightDate != null and flightDate != ''">
            AND mpf.flight_date = #{flightDate}
        </if>
    </select>

    <select id="retrieveTicketStatus" resultType="com.swcares.entity.MnjxPnrNmTicket">
        SELECT mpnti.*
        FROM mnjx_pnr_nm_tn mpnt
                 LEFT JOIN mnjx_pnr_nm_ticket mpnti ON mpnt.tn_id = mpnti.pnr_nm_tn_id
        WHERE 1 = 1
          AND mpnt.pnr_nm_id = #{pnrNmId}
    </select>

    <select id="retrieveDetail" resultType="com.swcares.obj.vo.PassengerFlightMsgVo">
        SELECT
        mpn.`name`,
        mpn.query_name,
        mps.flight_no,
        mps.flight_date,
        mps.estimate_off,
        mpst.psg_seat,
        mps.org,
        mcorg.city_cname org_city_name,
        mcorg.city_code org_city_code,
        mps.dst,
        mcdst.city_cname dst_city_name,
        mcdst.city_code dst_city_code,
        # 证件正常
        mns.nm_ssr_id,
        mns.ssr_info id_input,
        mpc.aboard_no,
        mpc.gate,
        # 值机正常
        mpc.psg_cki_id,
        mpc.cki_status
        FROM
        mnjx_pnr_seg mps
        LEFT JOIN mnjx_pnr mp ON mps.pnr_id = mp.pnr_id
        LEFT JOIN mnjx_pnr_nm mpn ON mpn.pnr_id = mps.pnr_id
        LEFT JOIN mnjx_psg_cki mpc ON mpn.pnr_nm_id = mpc.pnr_nm_id AND mps.pnr_seg_no = mpc.pnr_seg_no
        # 座位
        LEFT JOIN mnjx_psg_seat mpst ON mpc.psg_cki_id = mpst.psg_cki_id
        # 机场
        LEFT JOIN mnjx_airport maorg ON mps.org = maorg.airport_code
        LEFT JOIN mnjx_city mcorg ON maorg.city_id = mcorg.city_id
        LEFT JOIN mnjx_airport madst ON mps.dst = madst.airport_code
        # ID
        LEFT JOIN mnjx_city mcdst ON madst.city_id = mcdst.city_id
        LEFT JOIN mnjx_nm_ssr mns ON mns.pnr_nm_id = mpn.pnr_nm_id AND mns.ssr_type = 'FOID'
        <where>
            <if test="param.flightNo != null and param.flightNo != ''">
                and mps.flight_no = #{param.flightNo}
            </if>
            <if test="param.flightDate != null and param.flightDate != ''">
                and mps.flight_date = #{param.flightDate}
            </if>
            <if test="param.aboardNo != null and param.aboardNo != ''">
                and mpc.aboard_no = #{param.aboardNo}
            </if>
        </where>
    </select>

</mapper>