<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxExLuggageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxExLuggage">
        <id column="ex_luggage_id" property="exLuggageId" />
        <result column="psg_cki_id" property="psgCkiId" />
        <result column="weight" property="weight" />
        <result column="ex_type" property="exType" />
        <result column="lcm" property="lcm" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ex_luggage_id, psg_cki_id, weight, ex_type, lcm
    </sql>

</mapper>
