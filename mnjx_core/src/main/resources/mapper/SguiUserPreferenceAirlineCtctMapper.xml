<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.SguiUserPreferenceAirlineCtctMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.SguiUserPreferenceAirlineCtct">
        <id column="id" property="id" />
        <result column="preference_id" property="preferenceId" />
        <result column="airline" property="airline" />
        <result column="ctct" property="ctct" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, preference_id, airline, ctct, create_time, update_time
    </sql>

</mapper>
