<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxGateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxGate">
        <id column="gate_id" property="gateId"/>
        <result column="airport_id" property="airportId"/>
        <result column="gate_area" property="gateArea"/>
        <result column="gate_zone" property="gateZone"/>
        <result column="gate_no" property="gateNo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        gate_id, airport_id,  gate_area,gate_zone,gate_no
    </sql>

</mapper>
