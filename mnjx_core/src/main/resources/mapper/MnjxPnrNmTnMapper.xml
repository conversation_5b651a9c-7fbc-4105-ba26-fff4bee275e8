<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.MnjxPnrNmTnMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.swcares.entity.MnjxPnrNmTn">
        <id column="tn_id" property="tnId"/>
        <result column="pnr_nm_id" property="pnrNmId"/>
        <result column="nm_xn_id" property="nmXnId"/>
        <result column="pnr_index" property="pnrIndex"/>
        <result column="printer_id" property="printerId"/>
        <result column="issued_time" property="issuedTime"/>
        <result column="issued_si_id" property="issuedSiId"/>
        <result column="issued_airline" property="issuedAirline"/>
        <result column="ticket_no_start" property="ticketNoStart"/>
        <result column="ticket_no_end" property="ticketNoEnd"/>
        <result column="input_value" property="inputValue"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        tn_id, pnr_nm_id, nm_xn_id, pnr_index, printer_id, issued_time, issued_si_id, issued_airline, ticket_no_start, ticket_no_end, input_value
    </sql>

</mapper>
