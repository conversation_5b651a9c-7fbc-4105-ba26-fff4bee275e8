package com.swcares.core.mybatisplus;

import com.baomidou.mybatisplus.annotation.DbType;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
public class MybatisPlusGenInfo {
    /**
     * 数据库相关配置
     */
    private DbType dbType;
    private String driverName;
    private String url;
    private String uname;
    private String pwd;
    /**
     * 配置备注信息
     */
    private String author;
    /**
     * 生成的文件所在的目录，使用相对于当前项目的根目录结构
     */
    private String outputPath;
    /**
     * 指定包名
     */
    private String packageName;

}
