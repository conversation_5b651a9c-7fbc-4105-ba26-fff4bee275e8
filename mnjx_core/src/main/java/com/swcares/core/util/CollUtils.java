package com.swcares.core.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.util.ReflectionUtils;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class CollUtils extends CollUtil {

    public static List<List<String>> splitStringListByString(List<String> sourceList, String splitStr) {
        List<List<String>> resultList = new ArrayList<>();
        List<String> res = new ArrayList<>();
        for (int i = 1; i < sourceList.size(); i++) {
            String s = sourceList.get(i);
            if (splitStr.equals(s)) {
                resultList.add(res);
                res = new ArrayList<>();
            } else {
                res.add(s);
            }
            if (i == sourceList.size() - 1) {
                resultList.add(res);
            }
        }
//        resultList.removeIf(CollUtil::isEmpty);
        return resultList;
    }

    /**
     * Title: sortByOrderStringList
     * Description: 元素集合某个String字段按定义的String列表排序
     *
     * @param sourceList
     * @param orderList
     * @param orderFieldName
     * @return
     * <AUTHOR>
     * @date 2023/1/30 14:48
     */
    public static<T> void sortByOrderStringList(List<T> sourceList, List<String> orderList, String orderFieldName) {
        Collections.sort(sourceList, ((o1, o2) -> {
            int io1 = 0;
            int io2 = 0;
            Class<?> clazz = o1.getClass();
            // 当前对象的字段
            Field[] fields = clazz.getDeclaredFields();
            List<String> fieldNames = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
            if (CollUtil.contains(fieldNames, orderFieldName)) {
                PropertyDescriptor pd = null;
                try {
                    pd = new PropertyDescriptor(orderFieldName, clazz);
                } catch (IntrospectionException e) {
                    e.printStackTrace();
                }
                // 获得get方法
                if (ObjectUtil.isNotEmpty(pd)) {
                    Method getMethod = pd.getReadMethod();
                    Object o1FieldValue = ReflectionUtils.invokeMethod(getMethod, o1);
                    Object o2FieldValue = ReflectionUtils.invokeMethod(getMethod, o2);
                    if (ObjectUtil.isNotEmpty(o1FieldValue) && o1FieldValue instanceof String) {
                        io1 = orderList.indexOf(StrUtil.toString(o1FieldValue));
                        io2 = orderList.indexOf(StrUtil.toString(o2FieldValue));
                    }
                }
            } else {
                // 获取父类
                Class<?> superClazz = clazz.getSuperclass();
                if (!superClazz.equals(Object.class)) {
                    Field[] superFields = superClazz.getDeclaredFields();
                    List<String> superFieldNames = Arrays.stream(superFields).map(Field::getName).collect(Collectors.toList());
                    if (CollUtil.contains(superFieldNames, orderFieldName)) {
                        PropertyDescriptor pd = null;
                        try {
                            pd = new PropertyDescriptor(orderFieldName, superClazz);
                        } catch (IntrospectionException e) {
                            e.printStackTrace();
                        }
                        // 获得get方法
                        if (ObjectUtil.isNotEmpty(pd)) {
                            Method getMethod = pd.getReadMethod();
                            Object o1FieldValue = ReflectionUtils.invokeMethod(getMethod, o1);
                            Object o2FieldValue = ReflectionUtils.invokeMethod(getMethod, o2);
                            if (ObjectUtil.isNotEmpty(o1FieldValue) && o1FieldValue instanceof String) {
                                io1 = orderList.indexOf(StrUtil.toString(o1FieldValue));
                                io2 = orderList.indexOf(StrUtil.toString(o2FieldValue));
                            }
                        }
                    }
                }
            }
            if (io1 != -1) {
                io1 = sourceList.size() - io1;
            }
            if (io2 != -1) {
                io2 = sourceList.size() - io2;
            }
            return io2 - io1;
        }));
    }
}
