package com.swcares.core.util;

/**
 * 存放缓存Key
 *
 * <AUTHOR>
 * @date 2022/6/7
 */
public class CacheKeyConstant {

    /**
     * 获取缓存对象
     */
    public static final String CACHE_ETERM_KEY = "cacheEtermKey";
    public static final String CACHE_WEATHER_KEY = "cacheWeatherKey";

    /**
     * 分屏使用
     */
    public static final String CACHE_SCREEN_KEY = "cacheScreenKey";
    //===================================================================================================================================//
    //========================以下为指令结果或指令的其他数据缓存Key，注明由哪个指令产生（只允许存放跨指令使用的缓存Key）===========================//
    //==================================================================================================================================//
    /**
     * AV指令：缓存AV结果集
     */
    public static final String CACHE_AV_LIST_KEY = "avList";
    /**
     * EX指令：缓存EX指令
     */
    public static final String CACHE_EX_CMD_KEY = "exCmd";
    /**
     * EX指令：缓存EX指令结果集
     */
    public static final String CACHE_EX_CMD_RESULT = "exResult";
    /**
     * FD指令：缓存FD指令
     */
    public static final String CACHE_FD_CMD_KEY = "fdCmd";
    /**
     * PD指令：缓存PD指令结果集
     */
    public static final String CACHE_PD_CMD_RESULT = "pdResult";
    /**
     * PD指令：缓存PD指令ABC参数结果集
     */
    public static final String CACHE_PD_ABC_CMD_RESULT = "pdAbcResult";
    /**
     * PD指令：缓存PD指令SBY参数结果集
     */
    public static final String CACHE_PD_SBY_CMD_RESULT = "pdSbyResult";

    /**
     * PR指令：缓存PR指令结果集
     */
    public static final String CACHE_PR_CMD_RESULT = "prResult";

    /**
     * PR指令：缓存PR指令结果集
     */
    public static final String CACHE_SB_CMD_RESULT = "sbResult";

    /**
     * PR指令，方便PA、PU、PW
     */
    public static final String CACHE_AUW_CMD_RESULT = "auwResult";

    /**
     * PA指令：缓存PA结果集
     */
    public static final String CACHE_PA_CMD_RESULT = "paResult";
    
    /**
     * PW指令：缓存PW结果集
     */
    public static final String CACHE_PW_CMD_RESULT = "pwResult";
    
    /**
     * HBPW指令：缓存PW结果集
     */
    public static final String CACHE_HBPW_CMD_RESULT = "hbpwResult";

    /**
     * PA指令：缓存PA结果集
     */
    public static final String CACHE_HBPA_CMD_RESULT = "hbpaResult";

    /**
     * PA指令：缓存PA结果集
     */
    public static final String CACHE_HBPR_CMD_RESULT = "hbprResult";
    
    /**
     * PD指令：缓存PD指令结果集
     */
    public static final String CACHE_PU_CMD_RESULT = "puResult";
}
