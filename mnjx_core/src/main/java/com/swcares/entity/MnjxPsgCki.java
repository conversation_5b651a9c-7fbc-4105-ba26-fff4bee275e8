package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_psg_cki")
public class MnjxPsgCki extends Model<MnjxPsgCki> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "psg_cki_id", type = IdType.ASSIGN_ID)
    private String psgCkiId;

    @ApiModelProperty(value = "旅客ID")
    @TableField("pnr_nm_id")
    private String pnrNmId;

    @TableField("pnr_seg_no")
    private String pnrSegNo;

    @TableField("cabin_class")
    private String cabinClass;

    @TableField("sell_cabin")
    private String sellCabin;

    @TableField("gate")
    private String gate;

    @ApiModelProperty(value = "值机状态:NACC ACC SB DL GT")
    @TableField("cki_status")
    private String ckiStatus;

    @ApiModelProperty(value = "登机牌号")
    @TableField(value = "aboard_no", updateStrategy = FieldStrategy.IGNORED)
    private String aboardNo;

    @ApiModelProperty(value = "登机时间")
    @TableField(value = "aboard_time", updateStrategy = FieldStrategy.IGNORED)
    private String aboardTime;

    @ApiModelProperty(value = "是否候补旅客:1 候补 2 附加机组")
    @TableField(value = "is_hb", updateStrategy = FieldStrategy.IGNORED)
    private String isHb;

    @ApiModelProperty(value = "候补号")
    @TableField(value = "hb_no", updateStrategy = FieldStrategy.IGNORED)
    private String hbNo;

    @ApiModelProperty(value = "AEC")
    @TableField(value = "is_aec", updateStrategy = FieldStrategy.IGNORED)
    private String isAec;

    @ApiModelProperty(value = "升降舱 Y升舱 N降舱，W未操作 ZY愿升舱，ZN自愿降舱")
    @TableField(value = "upgn", updateStrategy = FieldStrategy.IGNORED)
    private String upgn;

    @ApiModelProperty(value = "取消已接收旅客的订座记录 Y已取消，N或者其他状态未处理")
    @TableField(value = "is_xres", updateStrategy = FieldStrategy.IGNORED)
    private String isXres;

    @TableField(value = "is_change", updateStrategy = FieldStrategy.IGNORED)
    private String isChange;

    @ApiModelProperty(value = "旅客行李重量")
    @TableField(value = "luggage_weight", updateStrategy = FieldStrategy.IGNORED)
    private Integer luggageWeight;

    @ApiModelProperty(value = "婴儿登机状态:0  1  null")
    @TableField("abd_status_infi")
    private String abdStatusInfi;

    @ApiModelProperty(value = "URES  1  0")
    @TableField(value = "ures", updateStrategy = FieldStrategy.IGNORED)
    private String ures;

    @ApiModelProperty(value = "NREC  1  0")
    @TableField(value = "nrec", updateStrategy = FieldStrategy.IGNORED)
    private String nrec;

    @ApiModelProperty(value = "旅客是否做过预升降舱操作")
    @TableField(value = "pre_upgn_operate", updateStrategy = FieldStrategy.IGNORED)
    private String preUpgnOperate;

    @ApiModelProperty(value = "旅客接收因没有座位产生的CAP标识。0或空：否，1：是")
    @TableField(value = "cap", updateStrategy = FieldStrategy.IGNORED)
    private String cap;

    @ApiModelProperty(value = "标识旅客该航段是由通程值机接收的（该旅客的航班是联程航班的情况）")
    @TableField("is_through_check_in")
    private String isThroughCheckIn;

    @ApiModelProperty(value = "接收该旅客时的office前三位，拉下旅客时删除")
    @TableField("check_in_airport_code")
    private String checkInAirportCode;

    @Override
    protected Serializable pkVal() {
        return this.psgCkiId;
    }

}
