package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.swcares.core.util.Constant;
import com.swcares.core.validator.ValueTypeConstraint;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_plane_model")
public class MnjxPlaneModel extends Model<MnjxPlaneModel> {

    @ApiModelProperty(value = "ID", hidden = true)
    @TableId(value = "plane_model_id", type = IdType.ASSIGN_ID)
    @Pattern(regexp = "\\d+", message = "只能是数字")
    private String planeModelId;

    @ApiModelProperty(value = "机型", example = "A330")
    @TableField("plane_model_type")
    @NotBlank(message = "机型不能为空")
    @Size(max = 8)
    @Pattern(regexp = "[0-9A-Za-z]+", message = "飞机机型格式不正确")
    private String planeModelType;

    @ApiModelProperty(value = "版本号", example = "V3A5X8Y2")
    @TableField("plane_model_version")
    @NotBlank(message = "版本号不能为空")
    @Size(max = 20, message = "飞机版本号太大")
    @Pattern(regexp = "[0-9A-Za-z]+", message = "飞机版本号格式不正确")
    private String planeModelVersion;

    @ApiModelProperty(value = "飞机类型：J喷气式飞机 B空中客机 H直升机 T货机", allowableValues = "J,B,H,T", example = "B")
    @TableField("plane_model_kind")
    @ValueTypeConstraint(value = {Constant.J, Constant.T, Constant.H, Constant.B}, message = "飞机类型不存在，飞机类型只有这四种：J喷气式飞机 B空中客机 H直升机 T货机")
    @NotBlank(message = "飞机类型不能为空")
    private String planeModelKind;

    @ApiModelProperty(value = "宽体机标识：WIDE-BODY，宽体机，0：不是  1：是宽体机", example = "0")
    @TableField("is_wide")
    @NotBlank(message = "飞机宽体标识不能为空")
    @ValueTypeConstraint(message = "飞机宽体标识错误，只允许输入0或1")
    private String isWide;

    @ApiModelProperty(value = "发动机型号", example = "WS18A涡扇发动机（仿俄罗斯D-30K2)")
    @TableField("engine_model")
    private String engineModel;

    @ApiModelProperty(value = "是否有氧舱： 0无 1有", allowableValues = "0,1", example = "1")
    @TableField("is_o")
    @NotBlank(message = "是否有氧舱标识不能为空")
    @ValueTypeConstraint()
    private String isO;

    @ApiModelProperty(value = "启用日期")
    @TableField("available_date")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate availableDate;

    @ApiModelProperty(value = "E:娱乐标识 如果从这一机场出发后提供某些娱乐活动，则该字段为E", example = "1")
    @TableField("is_recreation")
    @ValueTypeConstraint(message = "娱乐标识错误，只允许输入0或1")
    private String isRecreation;


    @Override
    protected Serializable pkVal() {
        return this.planeModelId;
    }

}
