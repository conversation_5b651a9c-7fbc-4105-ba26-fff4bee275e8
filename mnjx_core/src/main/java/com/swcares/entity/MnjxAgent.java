package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_agent")
public class MnjxAgent extends Model<MnjxAgent> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "agent_id", type = IdType.ASSIGN_ID)
    private String agentId;

    @ApiModelProperty(value = "代理人中文名")
    @NotBlank(message = "代理人中文名不能为空")
    @TableField("agent_cname")
    private String agentCname;

    @ApiModelProperty(value = "代理人英文名")
    @TableField("agent_ename")
    private String agentEname;

    @ApiModelProperty(value = "代理人IATA代码")
    @NotBlank(message = "代理人IATA代码不能为空")
    @TableField("agent_iata")
    private String agentIata;

    @ApiModelProperty(value = "联系人")
    @TableField("agent_contact_cname")
    private String agentContactCname;

    @ApiModelProperty(value = "电话")
    @TableField("agent_contact_phone")
    private String agentContactPhone;

    @ApiModelProperty(value = "地址")
    @TableField("agent_contact_address")
    private String agentContactAddress;

    @ApiModelProperty(value = "状态：1启用 ，0停用")
    @NotBlank(message = "状态不能为空")
    @TableField("agent_status")
    private String agentStatus;

    @ApiModelProperty(value = "是否具有GP政采票权限:1有 ，0无")
    @NotNull(message = "政采票权限不能为空")
    @TableField("is_gp")
    private Integer isGp;

    @Override
    protected Serializable pkVal() {
        return this.agentId;
    }

}
