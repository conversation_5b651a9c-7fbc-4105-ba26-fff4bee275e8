package com.swcares.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("mnjx_nm_fn")
@ApiModel(value="MnjxNmFn对象", description="")
public class MnjxNmFn extends Model<MnjxNmFn> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "nm_fn_id", type = IdType.ASSIGN_ID)
    private String nmFnId;

    @TableField("pnr_nm_id")
    private String pnrNmId;

    @ApiModelProperty(value = "PNR序号")
    @TableField("pnr_index")
    private Integer pnrIndex;

    @ApiModelProperty(value = "是否婴儿FN记录 0：否 1：是")
    @TableField("is_baby")
    private Integer isBaby;

    @ApiModelProperty(value = "F票面价货币代码")
    @TableField("f_currency")
    private String fCurrency;

    @ApiModelProperty(value = "F票面价")
    @TableField("f_price")
    private BigDecimal fPrice;

    @ApiModelProperty(value = "S货币代码")
    @TableField("s_currency")
    private String sCurrency;

    @ApiModelProperty(value = "S价格")
    @TableField("s_price")
    private BigDecimal sPrice;

    @ApiModelProperty(value = "代理费率，百分之XX")
    @TableField("c_rate")
    private BigDecimal cRate;

    @ApiModelProperty(value = "X货币代码")
    @TableField("x_currency")
    private String xCurrency;

    @ApiModelProperty(value = "X税收总价格")
    @TableField("x_price")
    private BigDecimal xPrice;

    @ApiModelProperty(value = "CN基建费货币代码，如果免基建费，本字段存EXEMPT")
    @TableField("t_cn_currency")
    private String tCnCurrency;

    @ApiModelProperty(value = "CN基建费价格")
    @TableField("t_cn_price")
    private BigDecimal tCnPrice;

    @ApiModelProperty(value = "YQ燃油费货币代码，如果免燃油费，本字段存EXEMPT")
    @TableField("t_yq_currency")
    private String tYqCurrency;

    @ApiModelProperty(value = "YQ燃油费价格")
    @TableField("t_yq_price")
    private BigDecimal tYqPrice;

    @ApiModelProperty(value = "票面价及税费价之和货币代码")
    @TableField("a_currency")
    private String aCurrency;

    @ApiModelProperty(value = "票面价及税费价之和")
    @TableField("a_price")
    private BigDecimal aPrice;

    @ApiModelProperty(value = "自动运价类型：AD CH IN GM JC")
    @TableField("pat_type")
    private String patType;

    @ApiModelProperty(value = "输入值")
    @TableField("input_value")
    private String inputValue;


    @Override
    protected Serializable pkVal() {
        return this.nmFnId;
    }

}
