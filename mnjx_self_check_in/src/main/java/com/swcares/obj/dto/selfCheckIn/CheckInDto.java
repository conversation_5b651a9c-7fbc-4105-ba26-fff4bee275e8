package com.swcares.obj.dto.selfCheckIn;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/11 14:36
 */
@Data
@Api("值机参数")
public class CheckInDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "舱位")
    private String cabinClass;

    @ApiModelProperty(value = "座位号，非必填")
    private String seatNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "出发机场三字码")
    private String orgAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    private String dstAirportCode;

    @ApiModelProperty(value = "旅客姓名")
    private String name;

    private String pnrNmId;

    private String pnrSegId;

    private String psgCkiId;

    private String psgSeatId;
}
