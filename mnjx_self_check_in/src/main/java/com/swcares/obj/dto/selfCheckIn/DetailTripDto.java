package com.swcares.obj.dto.selfCheckIn;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/10/18 9:19
 */
@Data
@Api("查询详情参数")
public class DetailTripDto {

    @ApiModelProperty(value = "身份证号")
    @NotNull(message = "身份证号不能为空")
    private String idCard;

    @ApiModelProperty(value = "姓名")
    @NotNull(message = "姓名不能为空")
    private String inputName;

    @ApiModelProperty(value = "航班号")
    @NotNull(message = "航班号不能为空")
    private String flightNo;

    @ApiModelProperty(value = "出发机场三字码")
    @NotNull(message = "出发机场不能为空")
    private String orgAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    @NotNull(message = "到达机场不能为空")
    private String dstAirportCode;
}
