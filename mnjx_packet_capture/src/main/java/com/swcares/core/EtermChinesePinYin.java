package com.swcares.core;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public class EtermChinesePinYin {
    @Getter
    private final static List<EtermChinesePinYin> ETERM_CHINESE_PINYIN = new ArrayList<>();

    /*
     * 此为航信的中文拼音（错字集合）
     */
    static {
        ETERM_CHINESE_PINYIN.add(new EtermChinesePinYin("椎", "zhuang"));
        ETERM_CHINESE_PINYIN.add(new EtermChinesePinYin("拎", "ling"));
        ETERM_CHINESE_PINYIN.add(new EtermChinesePinYin("吕", "lv"));
    }

    /**
     * 中文
     */
    @Getter
    private String chinese;
    /**
     * 航信拼音
     */
    @Getter
    private String pinYin;


    /**
     * 通过航信的中文获取航信的拼音
     *
     * @param chinese  中文
     * @return 通过航信的中文获取航信的拼音
     */
    public static EtermChinesePinYin getPinYin(String chinese) {
        return ETERM_CHINESE_PINYIN.stream()
                .filter(etermChineseCode -> etermChineseCode.chinese.equals(chinese))
                .findFirst()
                .orElse(null);
    }
}
