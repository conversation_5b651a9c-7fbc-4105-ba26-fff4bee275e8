# 更新航段组行动代码为RR接口实现总结

## 接口信息
- **路径**: `/sgui-tc/v2/crs/rebook/updateSegmentRR`
- **方法**: POST
- **功能**: 更新航段组行动代码为RR

## 请求参数
```json
{
  "pnrNo": "JM9D7R"
}
```

## 响应结果
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "SUCCESS"
}
```

## 实现文件

### 1. DTO类
- **文件**: `sgui/src/main/java/com/swcares/obj/dto/UpdateSegmentRRDto.java`
- **说明**: 定义请求参数结构

### 2. 服务接口
- **文件**: `sgui/src/main/java/com/swcares/service/tc/ITcV2Service.java`
- **方法**: `String updateSegmentRR(UpdateSegmentRRDto dto) throws SguiResultException`

### 3. 服务实现
- **文件**: `sgui/src/main/java/com/swcares/service/tc/impl/TcV2ServiceImpl.java`
- **方法**: `updateSegmentRR(UpdateSegmentRRDto dto)`

### 4. 控制器
- **文件**: `sgui/src/main/java/com/swcares/controller/tc/TcV2Controller.java`
- **端点**: `@PostMapping("/crs/rebook/updateSegmentRR")`

## 业务逻辑

### 主要步骤：
1. **参数校验**: 验证PNR编号不为空
2. **查询PNR**: 通过pnrNo查询mnjx_pnr表获取pnrId
3. **更新航段信息**: 
   - 查询mnjx_pnr_seg表中该PNR的所有航段
   - 更新action_code为"RR"
   - 将input_value中的" HK1 "替换为" RR1 "
4. **更新历史记录**:
   - 按航段的pnr_index和pnr_id查询mnjx_pnr_record表
   - 替换对应记录的input_value中的" HK1 "为" RR1 "

### 涉及的数据库表：
- `mnjx_pnr`: PNR主表
- `mnjx_pnr_seg`: PNR航段表
- `mnjx_pnr_record`: PNR历史记录表

### 关键字段更新：
- `mnjx_pnr_seg.action_code`: 更新为"RR"
- `mnjx_pnr_seg.input_value`: " HK1 " → " RR1 "
- `mnjx_pnr_record.input_value`: " HK1 " → " RR1 "

## 异常处理
- PNR编号为空时抛出异常
- 未找到PNR信息时抛出异常
- 未找到航段信息时抛出异常
- 数据库更新失败时抛出异常

## 返回值
- 成功时返回字符串"SUCCESS"
- 失败时抛出SguiResultException异常
