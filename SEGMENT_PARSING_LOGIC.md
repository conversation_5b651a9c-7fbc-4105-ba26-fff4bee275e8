# 航段解析逻辑详解

## 航段信息格式

### 正常航段格式
```
CA1311 PEK CTU 2025-08-22 K 0730 1005
```
**字段说明**：
- `CA1311`: 航班号（CA=航司代码，1311=航班号）
- `PEK`: 出发机场代码
- `CTU`: 到达机场代码
- `2025-08-22`: 航班日期
- `K`: 舱位代码
- `0730`: 起飞时间
- `1005`: 到达时间

### 缺口航段格式
```
SA CAN SZX
```
**字段说明**：
- `SA`: 缺口航段标识
- `CAN`: 出发机场代码
- `SZX`: 到达机场代码

## 完整示例解析

### 示例数据
```
segInfo = "FM9301 SHA CAN 2025-09-12 K 0730 1005/SA CAN SZX/HU7175 SZX TFU 2025-09-13 K 0915 1150"
issueInfo = "1-3"
```

### 解析步骤

#### 1. 按/分隔segInfo
```
segments = [
    "FM9301 SHA CAN 2025-09-12 K 0730 1005",  // 航段1
    "SA CAN SZX",                              // 航段2（缺口）
    "HU7175 SZX TFU 2025-09-13 K 0915 1150"   // 航段3
]
```

#### 2. 解析issueInfo获取航段序号
```
issueInfo = "1-3"
segmentNumbers = [1, 2, 3]
```

#### 3. 逐个构建航段信息

**航段1（正常航段）**：
```json
{
  "airlineCode": "FM",
  "fltNo": "9301",
  "depAirportCode": "SHA",
  "arrAirportCode": "CAN",
  "depDate": "2025-09-12",
  "status": "OPEN FOR USE",
  "operateAirline": null
}
```

**航段2（缺口航段）**：
```json
{
  "airlineCode": "",
  "fltNo": "VOID",
  "depAirportCode": "CAN",
  "arrAirportCode": "SZX",
  "depDate": "",
  "status": null,
  "operateAirline": null
}
```

**航段3（正常航段）**：
```json
{
  "airlineCode": "HU",
  "fltNo": "7175",
  "depAirportCode": "SZX",
  "arrAirportCode": "TFU",
  "depDate": "2025-09-13",
  "status": "OPEN FOR USE",
  "operateAirline": null
}
```

## 处理逻辑代码

### buildAirSeg方法核心逻辑
```java
private GetTicketDigestsByNameVo.AirSeg buildAirSeg(String segInfo, int segmentNumber, MnjxPnrNmTicket ticket, MnjxPnr pnr) {
    String[] parts = segInfo.trim().split("\\s+");
    GetTicketDigestsByNameVo.AirSeg airSeg = new GetTicketDigestsByNameVo.AirSeg();

    // 检查是否为缺口航段（SA段）
    if ("SA".equals(parts[0])) {
        // 缺口航段特殊处理
        airSeg.setStatus(null);
        airSeg.setFltNo("VOID");
        airSeg.setDepDate("");
        airSeg.setAirlineCode("");
        airSeg.setDepAirportCode(parts[1]);
        airSeg.setArrAirportCode(parts[2]);
        airSeg.setOperateAirline(null);
        return airSeg;
    }

    // 正常航段处理
    // ... 其他逻辑
}
```

## 航段类型对比

| 字段 | 正常航段 | 缺口航段 |
|------|----------|----------|
| airlineCode | 航班号前2位 | "" |
| fltNo | 航班号后面部分 | "VOID" |
| depAirportCode | 解析字段1 | 解析字段1 |
| arrAirportCode | 解析字段2 | 解析字段2 |
| depDate | 解析字段3 | "" |
| status | 根据票面状态 | null |
| operateAirline | null | null |

## 特殊情况处理

### 1. 票面状态影响
当票面状态为EXCHANGED、REFUNDED或PNR状态为DEL时：
- 正常航段：fltNo设置为"OPEN"，depDate设置为""
- 缺口航段：保持原有逻辑不变

### 2. 航段序号奇偶性
- 奇数航段序号：使用ticketStatus1
- 偶数航段序号：使用ticketStatus2
- 缺口航段：status直接设置为null，不受此规则影响

### 3. 数据验证
- 正常航段：至少需要4个字段（航班号、出发地、到达地、日期）
- 缺口航段：至少需要3个字段（SA标识、出发地、到达地）

## 测试用例

### 测试用例1：纯正常航段
```
segInfo = "CA1311 PEK CTU 2025-08-22 K 0100 0200/MU5678 CTU SHA 2025-08-23 K 0800 1030"
issueInfo = "1,2"
```

### 测试用例2：包含缺口航段
```
segInfo = "FM9301 SHA CAN 2025-09-12 K 0730 1005/SA CAN SZX/HU7175 SZX TFU 2025-09-13 K 0915 1150"
issueInfo = "1-3"
```

### 测试用例3：多个缺口航段
```
segInfo = "CA1234 PEK SHA 2025-09-10 K 0800 1100/SA SHA CAN/SA CAN SZX/MU5678 SZX CTU 2025-09-11 K 1400 1630"
issueInfo = "1-4"
```

### 测试用例4：异常数据
```
segInfo = "SA CAN"  // 缺少到达地
segInfo = "CA1234 PEK"  // 正常航段字段不足
```

## 业务含义

### 缺口航段的作用
1. **行程连接**：表示旅客需要自行安排的交通段
2. **票务处理**：不占用实际航班座位
3. **系统标识**：用VOID标识区别于正常航班

### 显示效果
- 正常航段：显示具体航班信息和时间
- 缺口航段：显示为VOID，提醒旅客自行安排

## 错误处理

### 常见错误
1. **格式错误**：segInfo格式不符合预期
2. **字段缺失**：必要字段缺失
3. **数据类型错误**：日期格式错误等

### 处理策略
```java
try {
    // 解析逻辑
} catch (Exception e) {
    log.error("构建航段信息异常: {}", segInfo, e);
    return null;  // 返回null，由上层处理
}
```

## 性能优化

### 字符串处理优化
- 使用split()方法一次性分割
- 避免重复的字符串操作
- 提前验证必要字段数量

### 内存使用优化
- 及时释放临时变量
- 避免创建不必要的对象
- 合理使用StringBuilder（如需要）
