package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxBroadcastTemplate;
import com.swcares.obj.dto.TemplateConfigDto;
import com.swcares.obj.vo.AudioVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15 16:05
 */
public interface ITemplateConfigService {

    void createTemplate(TemplateConfigDto templateConfigDto) throws UnifiedResultException;

    List<MnjxBroadcastTemplate> retrieveTemplate() throws UnifiedResultException;

    void updateTemplate(MnjxBroadcastTemplate mnjxBroadcastTemplate) throws UnifiedResultException;

    void deleteTemplate(String templateId) throws UnifiedResultException;

    List<AudioVo> generateTemplateToAudioData(String templateId) throws UnifiedResultException;
}
