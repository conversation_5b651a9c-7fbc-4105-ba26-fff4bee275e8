package com.swcares.controller;

import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.obj.vo.AudioVo;
import com.swcares.obj.vo.FlightVo;
import com.swcares.service.IBroadcastService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.IOUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15 14:18
 */
@Slf4j
@Api(tags = {"语音播报"})
@RestController
@RequestMapping("/broadcast")
public class BroadcastController {

    @Resource
    private IBroadcastService iBroadcastService;

    @ApiOperation("查询所有需要播报的航班")
    @GetMapping("/retrieveFlight")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "航班号",
                    name = "flightNo",
                    required = false,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            ),
            @ApiImplicitParam(
                    value = "登机口号",
                    name = "gate",
                    required = false,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            )
    })
    public UnifiedResult retrieveFlight(@RequestParam(required = false) String flightNo, @RequestParam(required = false) String gate) throws UnifiedResultException {
        List<FlightVo> flightVoList = iBroadcastService.retrieveFlight(flightNo, gate);
        return UnifiedResult.ok(Constant.RETRIEVE_SUCCESS, flightVoList);
    }

    @ApiOperation("播放语音")
    @GetMapping("/playNormalAudio")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "航班号",
                    name = "flightNo",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            ),
            @ApiImplicitParam(
                    value = "语音类型：1-值机开放；2-值机截止；3-开始登机；4-催促登机；5-变更登机口；6-航班延误；7-航班取消",
                    name = "audioType",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            )
    })
    public UnifiedResult playNormalAudio(@RequestParam String flightNo, @RequestParam String audioType) throws UnifiedResultException {
        InputStream inputStream = iBroadcastService.getAudioStream(flightNo, audioType);
        byte[] bytes;
        try {
            bytes = IOUtils.toByteArray(inputStream);
            inputStream.close();
        } catch (IOException e) {
            throw new UnifiedResultException(e.getMessage());
        }
        return UnifiedResult.ok("success", bytes);
    }

    @ApiOperation("变更登机口")
    @PostMapping("/changeGate")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "航班信息",
                    name = "flightVo",
                    required = true,
                    dataTypeClass = FlightVo.class,
                    paramType = Constant.PARAM_TYPE_BODY
            ),
            @ApiImplicitParam(
                    value = "更换后的登机口号",
                    name = "newGate",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            )
    })
    public UnifiedResult changeGate(@RequestBody FlightVo flightVo, @RequestParam String newGate) throws UnifiedResultException {
        List<AudioVo> audioVoList = iBroadcastService.changeGate(flightVo, newGate);
        return UnifiedResult.ok(Constant.UPDATE_SUCCESS, audioVoList);
    }

    @ApiOperation("修改状态")
    @PutMapping("/updateStatus")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "航班号",
                    name = "flightNo",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            ),
            @ApiImplicitParam(
                    value = "模板编号",
                    name = "audioType",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            ),
            @ApiImplicitParam(
                    value = "修改状态",
                    name = "status",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            )
    })
    public UnifiedResult updateStatus(@RequestParam String flightNo, @RequestParam String audioType, @RequestParam String status) throws UnifiedResultException {
        boolean res = iBroadcastService.updateStatus(flightNo, audioType, status);
        return res ? UnifiedResult.ok(Constant.UPDATE_SUCCESS) : UnifiedResult.fail(Constant.UPDATE_FAIL);
    }
}
