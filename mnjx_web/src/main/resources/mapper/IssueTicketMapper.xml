<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.IssueTicketMapper">

    <select id="retrievePnrCrs" resultType="java.lang.String">
		select
			distinct mp.pnr_crs
		from
			mnjx_pnr_seg mps ,
			mnjx_pnr_nm mpn,
			mnjx_pnr mp
		where
			mps.pnr_id = mpn.pnr_id
			and mps.pnr_id = mp.pnr_id
			and mps.flight_no = #{flightNo}
			and mps.flight_date = #{flightDate}
			and mpn.pnr_nm_id not in (
			select
				mpn1.pnr_nm_id
			from
				mnjx_pnr_seg mps1 ,
				mnjx_pnr_nm mpn1,
				mnjx_pnr mp1,
				mnjx_pnr_nm_tn mpnt
			where
				mps1.pnr_id = mpn1.pnr_id
				and mps1.pnr_id = mp1.pnr_id
				and mpn1.pnr_nm_id = mpnt.pnr_nm_id
				and mps1.flight_no = #{flightNo}
				and mps1.flight_date = #{flightDate} )
    </select>

    <select id="retrieveFlightNoAndFlightDate" resultType="com.swcares.obj.dto.IssueDto">
        select distinct
            mps.flight_no ,
            mps.flight_date
        from
            mnjx_pnr_seg mps ,
            mnjx_pnr_nm mpn,
            mnjx_pnr mp
        where
            mps.pnr_id = mpn.pnr_id
            and mps.pnr_id = mp.pnr_id
            and mpn.pnr_nm_id not in (
            select
                mpn1.pnr_nm_id
            from
                mnjx_pnr_seg mps1 ,
                mnjx_pnr_nm mpn1,
                mnjx_pnr mp1,
                mnjx_pnr_nm_tn mpnt
            where
                mps1.pnr_id = mpn1.pnr_id
                and mps1.pnr_id = mp1.pnr_id
                and mpn1.pnr_nm_id = mpnt.pnr_nm_id  )
            <if test="start != null and start != '' and end != null and end != ''">
	            and mps.flight_date between #{start} and #{end}
            </if>
    </select>
</mapper>