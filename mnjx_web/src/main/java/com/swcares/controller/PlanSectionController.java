package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.service.IPlanSectionService;
import com.swcares.obj.vo.PlanSectionPageVo;
import com.swcares.obj.vo.PlanSectionRetrieveVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * description：航段计划管理
 *
 * <AUTHOR>
 */
@Api(tags = "航段计划管理")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/planSection")
public class PlanSectionController {

    @Resource
    private IPlanSectionService iPlanSectionService;

    @ApiOperation("分页查询航节飞行计划信息")
    @PostMapping("/retrievePage")
    @PageCheck(model = 2, sizeName = "limit")
    public IPage<PlanSectionPageVo> retrievePage(@Valid @RequestBody PlanSectionRetrieveVo planSectionRetrieveVo) {
        return iPlanSectionService.retrievePage(planSectionRetrieveVo);
    }
}
