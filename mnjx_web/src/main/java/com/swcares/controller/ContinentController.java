package com.swcares.controller;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxContinent;
import com.swcares.obj.vo.ContinentQueryVo;
import com.swcares.service.IContinentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2024-02-26 10:44:56
 */
@Api(tags = "洲际管理")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@Slf4j
@RestController
@RequestMapping("/continent")
public class ContinentController {

    @Resource
    private IContinentService iContinentService;

    @ApiOperation("新增洲际信息")
    @PostMapping("/create")
    public String create(@RequestBody @Valid MnjxContinent mnjxContinent) throws UnifiedResultException {
        boolean isOk = iContinentService.create(mnjxContinent);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("查询洲信息")
    @PostMapping("/retrieveContinentByCond")
    public List<MnjxContinent> retrieveContinentByCond(@RequestBody ContinentQueryVo continentQueryVo) {
        return iContinentService.retrieveContinentByCond(continentQueryVo);
    }

    @ApiOperation("查询所有大洲信息（提供下拉）")
    @GetMapping("/retrieveContinentList")
    public List<MnjxContinent> retrieveContinentList() {
        return iContinentService.retrieveContinentList();
    }

}
