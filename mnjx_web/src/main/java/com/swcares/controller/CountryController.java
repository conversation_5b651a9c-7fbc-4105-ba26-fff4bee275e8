package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.utils.ExcelUtils;
import com.swcares.entity.MnjxCountry;
import com.swcares.obj.vo.CountryCreateByImportVo;
import com.swcares.obj.vo.CountryQueryVo;
import com.swcares.obj.vo.CountryUpdateVo;
import com.swcares.obj.vo.CountryVo;
import com.swcares.service.ICountryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/23-13:15
 */
@Api(tags = "国家管理")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@Slf4j
@RestController
@RequestMapping("/country")
public class CountryController {

    @Resource
    private ICountryService iCountryService;

    @ApiOperation("单个新增国家信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxCountry",
                    value = "国家信息对象",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = MnjxCountry.class
            )
    })
    @PostMapping("/create")
    public String create(@RequestBody @Valid MnjxCountry mnjxCountry) throws UnifiedResultException {
        boolean isOk = iCountryService.create(mnjxCountry);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("导入国家信息")
    @PostMapping("/createByImport")
    public String createByImport(@RequestPart("file") MultipartFile file) throws Exception {
        // 这个就是导入的数据
        List<CountryCreateByImportVo> countryCreateByImportVos = ExcelUtils.read(file, CountryCreateByImportVo.class);
        // 将列表数据转换成核心对象
        List<MnjxCountry> mnjxCountries = iCountryService.createTransformVo2Entity(countryCreateByImportVos);
        // 将导入的数据写入数据库
        iCountryService.createByImport(mnjxCountries);
        return Constant.IMPORT_SUCCESS;
    }

    @ApiOperation("查询所国家信息")
    @PostMapping("/retrieveList")
    public List<MnjxCountry> retrieveList() {
        return iCountryService.retrieveList();
    }

    @ApiOperation("分页查询国家信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    required = true,
                    name = "current",
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    required = true,
                    name = "limit",
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    value = "查询对象",
                    name = "countryQueryVo",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = CountryQueryVo.class
            )
    })
    @PostMapping("/retrieveListByPage/{current}/{limit}")
    @PageCheck
    public IPage<CountryVo> retrievePageByCond(@PathVariable long current,
                                               @PathVariable long limit,
                                               @RequestBody CountryQueryVo countryQueryVo) {
        return iCountryService.retrievePageByCond(new Page<>(current, limit), countryQueryVo);
    }

    @ApiOperation("根据id获取国家信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "id",
                    value = "id",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class
            )
    })
    @GetMapping("/retrieveById/{id}")
    public MnjxCountry retrieveById(@PathVariable String id) {
        return iCountryService.retrieveById(id);
    }

    @ApiOperation("修改国家信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "mnjxCountry",
                    value = "国家信息对象",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = MnjxCountry.class
            )
    })
    @PutMapping("/update")
    public String update(@RequestBody @Valid MnjxCountry mnjxCountry) throws UnifiedResultException {
        boolean isOk = iCountryService.update(mnjxCountry);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation("修改国家信息--是否启用该国家")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "id",
                    value = "国家ID",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    name = "status",
                    value = "状态",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            )
    })
    @PutMapping("/updateStatusById/{id}")
    public String updateStatusById(@PathVariable String id, @RequestParam String status) throws UnifiedResultException {
        boolean isOk = iCountryService.updateStatusById(id, status);
        if (isOk) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "ids",
                    value = "国家ID列表",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = List.class
            )
    })
    @DeleteMapping("/deleteIds")
    public String deleteIds(@RequestBody List<String> ids) throws UnifiedResultException {
        boolean isOk = iCountryService.deleteIds(ids);
        if (isOk) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }

    @ApiOperation(value = "为国家分配所属洲", notes = "为国家分配所属洲")
    @PostMapping("/updateContinent")
    public String updateContinent(@RequestBody CountryUpdateVo countryUpdateVo) {
        return iCountryService.updateContinent(countryUpdateVo);
    }
}
