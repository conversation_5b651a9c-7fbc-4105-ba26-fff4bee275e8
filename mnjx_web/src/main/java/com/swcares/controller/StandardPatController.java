package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ObjectUtils;
import com.swcares.core.utils.ExcelUtils;
import com.swcares.entity.MnjxCity;
import com.swcares.entity.MnjxStandardPat;
import com.swcares.obj.vo.StandardPatCreateVo;
import com.swcares.obj.vo.StandardPatRetrieveVo;
import com.swcares.obj.vo.StandardPatVo;
import com.swcares.obj.vo.excel.StandPatExcellVo;
import com.swcares.service.IStandardPatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * description：基准运价管理
 *
 * <AUTHOR>
 */
@Api(tags = "基准运价管理")
@Slf4j
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/standardPat")
public class StandardPatController {

    @Resource
    private IStandardPatService iStandardPatService;

    @ApiOperation("新增基准运价")
    @PostMapping("/create")
    public String create(@RequestBody @Valid StandardPatCreateVo standardPatCreateVo) throws InstantiationException, IllegalAccessException, UnifiedResultException {
        MnjxStandardPat mnjxStandardPat = ObjectUtils.vo2Entity(standardPatCreateVo, MnjxStandardPat.class);
        boolean isOk = iStandardPatService.create(mnjxStandardPat);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("查询所有城市信息")
    @GetMapping("/retrieveCityList")
    public List<MnjxCity> retrieveCityList() {
        return iStandardPatService.retrieveCityList();
    }

    @ApiOperation("分页+条件查询基准运价")
    @PostMapping("/retrievePageByCond/{current}/{limit}")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    name = "current",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class),
            @ApiImplicitParam(
                    value = "每页记录数",
                    name = "limit",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class),
            @ApiImplicitParam(
                    value = "查询条件",
                    name = "standardPatRetrieveVo",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = StandardPatRetrieveVo.class
            )
    })
    public IPage<StandardPatVo> retrievePageByCond(@PathVariable("current") long current,
                                                   @PathVariable("limit") long limit,
                                                   @RequestBody StandardPatRetrieveVo standardPatRetrieveVo) {
        return iStandardPatService.retrievePageByCond(new Page<>(current, limit), standardPatRetrieveVo);
    }

    @ApiOperation("根据id获取基准运价信息")
    @GetMapping("/retrieveById/{standardPatId}")
    @ApiImplicitParam(
            value = "基准运价id",
            required = true,
            name = "standardPatId",
            dataTypeClass = String.class,
            paramType = Constant.PARAM_TYPE_PATH
    )
    public StandardPatVo retrieveById(@PathVariable String standardPatId) {
        return iStandardPatService.retrieveById(standardPatId);
    }


    @ApiOperation("修改")
    @ApiImplicitParam(
            value = "基准运价对象",
            required = true,
            name = "mnjxStandardPat",
            dataTypeClass = MnjxStandardPat.class,
            paramType = Constant.PARAM_TYPE_PATH
    )
    @PutMapping("/update")
    public String update(@RequestBody @Valid MnjxStandardPat mnjxStandardPat) throws UnifiedResultException {
        boolean result = iStandardPatService.update(mnjxStandardPat);
        if (result) {
            return Constant.UPDATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.UPDATE_FAIL);
        }
    }

    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @DeleteMapping("/deleteByIds")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "基准云价ID列表",
                    name = "ids",
                    required = true,
                    allowMultiple = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = List.class,
                    example = "1493875375885201410,1494133925735784450,1496667904624992258"
            )
    })
    public String deleteByIds(@RequestBody List<String> ids) throws UnifiedResultException {
        boolean isOk = iStandardPatService.deleteByIds(ids);
        if (isOk) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }


    @ApiOperation("导入基准运价列表")
    @PostMapping("createByExcell")
    public List<String> createByExcell(@RequestPart("file") MultipartFile file) throws IOException, UnifiedResultException {
        // 将excell中的数据全部转换为内存中的vo对象
        List<StandPatExcellVo> standPatExcellVos = ExcelUtils.read(file, StandPatExcellVo.class);
        // 将vo对象全部转换为entity对象
        //数据组装
        List<String> failList = iStandardPatService.vo2Entity(standPatExcellVos);
        return failList;
    }


}
