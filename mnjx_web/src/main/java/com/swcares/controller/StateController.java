package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.entity.MnjxState;
import com.swcares.obj.dto.StateQueryDto;
import com.swcares.obj.vo.StateVo;
import com.swcares.service.IStateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4 15:34
 */
@Api(tags = "州管理")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/state")
@Slf4j
public class StateController {

    @Resource
    private IStateService iStateService;

    @ApiOperation("创建州")
    @PostMapping("/create")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "条件对象",
                    name = "mnjxState",
                    required = true,
                    dataTypeClass = MnjxState.class,
                    paramType = Constant.PARAM_TYPE_BODY
            )
    })
    public UnifiedResult createState(@RequestBody MnjxState mnjxState) throws UnifiedResultException {
        iStateService.createState(mnjxState);
        return UnifiedResult.ok();
    }

    @ApiOperation("分页查询州")
    @PostMapping("/retrievePageByCond/{current}/{limit}")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    name = "current",
                    required = true,
                    dataTypeClass = long.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    name = "limit",
                    required = true,
                    dataTypeClass = long.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    value = "查询条件对象",
                    name = "stateQueryDto",
                    dataTypeClass = StateQueryDto.class,
                    paramType = Constant.PARAM_TYPE_BODY
            )
    })
    public IPage<StateVo> retrievePageByCond(@PathVariable long current, @PathVariable long limit, @RequestBody StateQueryDto stateQueryDto) {
        return iStateService.retrievePageByCond(new Page<>(current, limit), stateQueryDto);
    }

    @ApiOperation("查询所有州（提供下拉）")
    @GetMapping("/retrieveStateList")
    public List<MnjxState> retrieveStateList() {
        return iStateService.retrieveStateList();
    }

    @ApiOperation("更新州信息")
    @PutMapping("/updateState")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "条件对象",
                    name = "mnjxState",
                    required = true,
                    dataTypeClass = MnjxState.class,
                    paramType = Constant.PARAM_TYPE_BODY
            )
    })
    public UnifiedResult updateState(@RequestBody MnjxState mnjxState) throws UnifiedResultException {
        iStateService.updateState(mnjxState);
        return UnifiedResult.ok(Constant.UPDATE_SUCCESS);
    }

    @ApiOperation("启用停用州")
    @PutMapping("/updateStateStatus")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "州ID",
                    name = "stateId",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            ),
            @ApiImplicitParam(
                    value = "状态",
                    name = "status",
                    required = true,
                    dataTypeClass = String.class,
                    paramType = Constant.PARAM_TYPE_QUERY
            )
    })
    public UnifiedResult updateStateStatus(@RequestParam String stateId, @RequestParam String status) {
        iStateService.updateStateStatus(stateId, status);
        return UnifiedResult.ok(Constant.UPDATE_SUCCESS);
    }

    @ApiOperation("删除州")
    @DeleteMapping("/deleteState")
    @ApiImplicitParam(
            value = "州ID",
            name = "stateId",
            required = true,
            dataTypeClass = String.class,
            paramType = Constant.PARAM_TYPE_QUERY
    )
    public UnifiedResult deleteState(@RequestParam String stateId) throws UnifiedResultException {
        iStateService.deleteState(stateId);
        return UnifiedResult.ok();
    }

    @ApiOperation("批量绑定州所属国家")
    @PutMapping("/batchBindCountry")
    @ApiImplicitParam(
            name = "countryId",
            value = "国家ID",
            required = true,
            paramType = Constant.PARAM_TYPE_QUERY,
            dataTypeClass = String.class
    )
    public UnifiedResult batchBindCountry(@RequestBody List<String> stateIdList, @RequestParam String countryId) throws UnifiedResultException {
        iStateService.batchBindCountry(stateIdList, countryId);
        return UnifiedResult.ok();
    }

    @ApiOperation("解除已绑定的国家")
    @PutMapping("/unbindCountry")
    @ApiImplicitParam(
            name = "stateId",
            value = "州ID",
            required = true,
            dataTypeClass = String.class,
            paramType = Constant.PARAM_TYPE_QUERY
    )
    public UnifiedResult unbindCountry(@RequestParam String stateId) {
        iStateService.unbindCountry(stateId);
        return UnifiedResult.ok();
    }
}
