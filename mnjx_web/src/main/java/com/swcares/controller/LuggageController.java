package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.util.Constant;
import com.swcares.service.ILuggageService;
import com.swcares.obj.vo.LuggageRetrieveVo;
import com.swcares.obj.vo.LuggageVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "行李查询")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RequestMapping(value = "/luggage")
@RestController
public class LuggageController {

    @Resource
    private ILuggageService iLuggageService;


    @PostMapping("/retrievePageByCond/{current}/{limit}")
    @ApiOperation("根据条件分页搜索行李信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    value = "当前页码",
                    name = "current",
                    required = true,
                    dataTypeClass = long.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    value = "每页记录数",
                    name = "limit",
                    required = true,
                    dataTypeClass = long.class,
                    paramType = Constant.PARAM_TYPE_PATH
            ),
            @ApiImplicitParam(
                    value = "行李搜索传输对象",
                    name = "luggageRetrieveVo",
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = LuggageRetrieveVo.class
            )
    })
    @PageCheck
    public IPage<LuggageVo> retrievePageByCond(@PathVariable long current,
                                               @PathVariable long limit,
                                               @RequestBody LuggageRetrieveVo luggageRetrieveVo) {
        return iLuggageService.retrievePageByCond(new Page<>(current, limit), luggageRetrieveVo);
    }
}
