package com.swcares.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.function.PageCheck;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ObjectUtils;
import com.swcares.entity.MnjxCnd;
import com.swcares.obj.vo.CndCreateVo;
import com.swcares.obj.vo.CndModelVo;
import com.swcares.obj.vo.CndRetrieveVo;
import com.swcares.obj.vo.CndUpdateVo;
import com.swcares.service.ICndService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/25-13:15
 */
@Api(tags = "CND信息管理")
@SwaggerGroup(SwaggerGroup.GroupType.BASIC)
@RestController
@RequestMapping("/cnd")
@Slf4j
public class CndController {

    @Resource
    private ICndService iCndService;

    @ApiOperation("新增CND信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "cndCreateVo",
                    value = "cnd对象",
                    paramType = Constant.PARAM_TYPE_BODY,
                    required = true,
                    dataTypeClass = CndCreateVo.class
            )
    })
    @PostMapping("/create")
    public String create(@RequestBody @Validated CndCreateVo cndCreateVo) throws UnifiedResultException, InstantiationException, IllegalAccessException {
        MnjxCnd mnjxCnd = ObjectUtils.vo2Entity(cndCreateVo, MnjxCnd.class);
        boolean isOk = iCndService.create(mnjxCnd);
        if (isOk) {
            return Constant.CREATE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.CREATE_FAIL);
        }
    }

    @ApiOperation("分页查询CND信息")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "current",
                    value = "当前页",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    name = "limit",
                    value = "每页条数",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = long.class
            ),
            @ApiImplicitParam(
                    name = "cndRetrieveVo",
                    value = "查询对象",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    dataTypeClass = CndRetrieveVo.class
            )
    })
    @PostMapping("/retrievePage/{current}/{limit}")
    @PageCheck
    public IPage<CndRetrieveVo> retrievePage(@PathVariable("current") long current,
                                             @PathVariable("limit") long limit,
                                             @RequestBody @Validated CndRetrieveVo cndRetrieveVo) {
        return iCndService.retrievePage(new Page<>(current, limit), cndRetrieveVo);
    }

    @ApiOperation("查询cnd号")
    @PostMapping("/retrieveCndNo")
    public List<MnjxCnd> retrieveCndNo() {
        return iCndService.retrieveCndNo();
    }

    @ApiOperation("根据航司ID获取cnd列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "airlineId",
                    value = "航司ID",
                    paramType = "path",
                    required = true,
                    dataTypeClass = String.class
            )
    })
    @PostMapping("/retrieveByAirlineId/{airlineId}")
    public List<MnjxCnd> retrieveByAirlineId(@PathVariable("airlineId") String airlineId) {
        return iCndService.retrieveByAirlineId(airlineId);
    }


    @ApiOperation("根据机型版本获取CND列表")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "planeModelId",
                    value = "机型ID",
                    paramType = "path",
                    required = true,
                    dataTypeClass = String.class
            )
    })
    @PostMapping("/retrieveByplaneModelId/{planeModelId}")
    public List<MnjxCnd> retrieveByplaneModelId(@PathVariable("planeModelId") String planeModelId) {
        return iCndService.retrieveByPlaneModelId(planeModelId);
    }

    /**
     * 根据id查询数据
     */
    @ApiOperation(value = "根据id查询数据", notes = "根据ID查询CND号")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "id",
                    value = "id",
                    required = true,
                    paramType = Constant.PARAM_TYPE_PATH,
                    dataTypeClass = String.class
            )
    })
    @GetMapping("/retrieveById/{id}")
    public MnjxCnd retrieveById(@PathVariable String id) {
        return iCndService.retrieveById(id);
    }

    @GetMapping("/retrieveCndModel")
    public List<CndModelVo> retrieveCndModel() {
        return iCndService.retrieveCndModel();
    }

    @ApiOperation("修改CND布局信息")
    @PutMapping("/updateById")
    public String updateById(@RequestBody @Valid CndUpdateVo cndUpdateVo) throws UnifiedResultException, InstantiationException, IllegalAccessException {
        MnjxCnd mnjxCnd = ObjectUtils.vo2Entity(cndUpdateVo, MnjxCnd.class);
        return iCndService.updateById(mnjxCnd) ? Constant.UPDATE_SUCCESS : Constant.UPDATE_FAIL;
    }

    @ApiOperation(value = "根据id批量删除", notes = "根据id批量删除")
    @ApiImplicitParams({
            @ApiImplicitParam(
                    name = "ids",
                    value = "CndId列表",
                    required = true,
                    paramType = Constant.PARAM_TYPE_BODY,
                    allowMultiple = true,
                    dataTypeClass = List.class
            )
    })
    @DeleteMapping("/deleteByIds")
    public String deleteByIds(@RequestBody List<String> ids) throws UnifiedResultException {
        boolean isOk = iCndService.deleteByIds(ids);
        if (isOk) {
            return Constant.DELETE_SUCCESS;
        } else {
            throw new UnifiedResultException(Constant.DELETE_FAIL);
        }
    }
}
