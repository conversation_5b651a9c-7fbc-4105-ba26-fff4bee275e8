package com.swcares.core.security;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

/**
 * 密码比较器
 * 为什么要单独处理，主要就是在新增和更新用户时，需要松动的调用这个方法要给密码加密
 *
 * <AUTHOR>
 */
@Component
public class Md5PasswordEncoder implements PasswordEncoder {
    /**
     * 加密方法
     *
     * @param rawPassword 原生密码
     * @return 密码摘要
     */
    @Override
    public String encode(CharSequence rawPassword) {
        return DigestUtil.md5Hex(StrUtil.toString(rawPassword));
    }

    /**
     * 密码匹配方法
     *
     * @param rawPassword     明文
     * @param encodedPassword 密文
     * @return 是否匹配
     */
    @Override
    public boolean matches(CharSequence rawPassword, String encodedPassword) {
        return encodedPassword.equals(this.encode(rawPassword));
    }

}
