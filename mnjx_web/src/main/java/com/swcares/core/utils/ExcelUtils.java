package com.swcares.core.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.analysis.ExcelReadExecutor;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.ReUtils;
import com.swcares.core.util.StrUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Pattern;

/**
 * 这个类继承了ExcelUtil<br/>
 * ExcelUtil使用的POI框架来实现的。我不是很推荐,这个是在比较恼火的情况下使用<br/>
 * 这个类里面新增的方法默认使用easyexcel来实现的。所以我们要优先使用ExcelUtils里面的方法
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtils extends ExcelUtil {

    /**
     * 解析表格数据（这种方式就是消耗了内存）<br/>
     * 使用easyexcel的方式读取数据<br/>
     * 不要采用hutool种的读取方式，因为里面实际就是用poi框架来实现的<br/>
     *
     * @param inputStream 输入流
     * @param clazz       要转换的模型
     * @param listener    读取监听器
     * @param <T>         类型
     * @return 解析后的数据
     */
    public static <T> List<T> read(InputStream inputStream, Class<T> clazz, ReadListener<T> listener) {
        List<T> list = new ArrayList<>();
        // 如果由自己的读取监听器，就使用自己的，没有就使用后面定义的
        EasyExcel.read(inputStream, clazz, Optional.ofNullable(listener).orElse(new AnalysisEventListener<T>() {
            // 每读取一行就调用该方法
            @Override
            public void invoke(T data, AnalysisContext context) {
                list.add(data);
            }

            // 全部读取完成就调用该方法
            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                log.info("表格数据解析完成");
            }
        })).sheet().doRead();
        return list;
    }

    /**
     * 用流的方式将excell数据读取出来
     *
     * @param inputStream 流数据
     * @param clazz       指定类型
     * @param <T>         确定类型
     * @return 解析出来的数据
     */
    public static <T> List<T> read(InputStream inputStream, Class<T> clazz) {
        return read(inputStream, clazz, null);
    }


    /**
     * 接收http提交的xls文件，并且解析
     *
     * @param multipartFile web提交的xls文件
     * @param clazz         要转换的类型
     * @param <T>           指定的类型的class
     * @return 所有的数据
     * @throws IOException IO异常
     */
    public static <T> List<T> read(MultipartFile multipartFile, Class<T> clazz) throws IOException, UnifiedResultException {
        // 获取文件名
        String fileName = multipartFile.getOriginalFilename();
        // 文件类型是否匹配
        boolean isXlsx = ExcelUtils.isTypeXlsx(fileName);
        // 不匹配
        if (!isXlsx) {
            throw new UnifiedResultException("我们只支持xlsx类型的文件导入");
        }
        // 匹配的情况下，读取文件数据为指定类型
        return read(multipartFile.getInputStream(), clazz, null);
    }

    /**
     * 判断文件类型是否未xlsx类型
     *
     * @param fileName 文件名
     * @return 是否是指定类型
     */
    private static boolean isTypeXlsx(String fileName) {
        return StrUtil.contains(fileName, ExcelTypeEnum.XLSX.getValue());
    }
}
