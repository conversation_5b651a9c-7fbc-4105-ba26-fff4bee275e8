package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxPrinter;
import com.swcares.obj.vo.OfficeListVo;
import com.swcares.obj.vo.OfficeVo;
import com.swcares.obj.vo.PrinterRetrieveVo;
import com.swcares.obj.vo.PrinterVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IPrinterService {

    /**
     * create
     *
     * @param mnjxPrinter mnjxPrinter
     * @return create
     * @throws UnifiedResultException 统一异常
     */
    boolean create(MnjxPrinter mnjxPrinter) throws UnifiedResultException;

    /**
     * retrieveListByPage
     *
     * @param current           current
     * @param limit             limit
     * @param printerRetrieveVo printerRetrieveVo
     * @return retrieveListByPage
     */
    IPage<PrinterVo> retrieveListByPage(long current, long limit, PrinterRetrieveVo printerRetrieveVo);

    /**
     * retrieveOrgDropdownList
     *
     * @param officeType officeType
     * @return retrieveOrgDropdownList
     */
    List<OfficeListVo> retrieveOrgDropdownList(String officeType);

    /***
     * retrieveOfficeNoDropdownList
     *
     * @param officeType officeType
     * @param orgId orgId
     * @return
     */
    List<OfficeVo> retrieveOfficeNoDropdownList(String officeType, String orgId);

    /**
     * update
     *
     * @param mnjxPrinter mnjxPrinter
     * @return update
     * @throws UnifiedResultException 统一异常
     */
    boolean update(MnjxPrinter mnjxPrinter) throws UnifiedResultException;


    /**
     * deleteByIds
     *
     * @param ids ids
     * @return deleteByIds
     */
    boolean deleteByIds(List<String> ids);
}
