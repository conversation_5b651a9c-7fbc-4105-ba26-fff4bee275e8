package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.entity.MnjxAllowedIp;
import com.swcares.obj.dto.IpConfigDto;

/**
 * <AUTHOR>
 * @date 2023/7/6 9:39
 */
public interface IIpConfigService {

    /**
     * 新建IP授权配置
     *
     * @param ipConfigDto
     * @return UnifiedResult
     */
    UnifiedResult createIpConfig(IpConfigDto ipConfigDto);

    /**
     * 查询IP配置列表
     *
     * @param page
     * @param ipConfigDto
     * @return
     */
    IPage<MnjxAllowedIp> retrieveIpConfigList(IPage<MnjxAllowedIp> page, IpConfigDto ipConfigDto);

    /**
     * 修改IP配置信息
     *
     * @param ipConfigDto
     * @return
     */
    UnifiedResult updateIpConfig(IpConfigDto ipConfigDto);
}
