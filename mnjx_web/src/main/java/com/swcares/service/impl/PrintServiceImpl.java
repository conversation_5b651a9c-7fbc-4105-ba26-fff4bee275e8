package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.service.*;
import jline.internal.Log;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class PrintServiceImpl implements IPrintService {

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmFnService iMnjxFnService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxNmSsrService iMnjxSsrService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxAirportService iMnjxAirportcodeService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxPsgOperateRecordService iMnjxPsgOperateRecordService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxCndService mnjxCndService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmUmService iMnjxPnrNmUmService;

    @Resource
    private IMnjxPrintDataService iMnjxPrintDataService;

    private static final Pattern REG_FOID = Pattern.compile("SSR\\sFOID\\s[A-Z]{2}\\s\\w{3}\\s([\\w]+)/\\w{2,}");

    @Override
    public List<Map<String, Object>> getPreviewData(List<String> tktNoList) throws Exception {
        if (CollUtil.isNotEmpty(tktNoList) && (tktNoList.size() == 1)) {
            String tktNo = tktNoList.get(0);
            if (tktNo.length() == 6) {
                tktNoList = this.getTktNoByPnr(tktNo);
            }
        }
        // 票号是否在一个pnr中
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<String> allTicketNoList = new ArrayList<>();
        this.validateTicketNo(tktNoList, allTicketNoList);
        this.constructResultMap(listMap, allTicketNoList);
        if (listMap.size() > 1) {
            listMap = listMap.stream()
                    .sorted(Comparator.comparing(map -> map.get("ticketNo").toString()))
                    .collect(Collectors.toList());
            // 当传入的票号只有1位时，移除其联票的信息
            if (tktNoList.size() == 1) {
                String inputTicketNo = tktNoList.get(0);
                Iterator<Map<String, Object>> iterator = listMap.iterator();
                while (iterator.hasNext()) {
                    Map<String, Object> map = iterator.next();
                    if (!inputTicketNo.equals(map.get("ticketNo"))) {
                        iterator.remove();
                    }
                }
                // 移除联票信息后数据为空，表示当前查询的票号不允许打印（退、改、挂起等）
                if (CollUtil.isEmpty(listMap)) {
                    throw new UnifiedResultException("当前票号无法打印，请检查后继续！");
                }
            } else {
                for (int i = 1; i < listMap.size(); i++) {
                    Map<String, Object> map = listMap.get(i);
                    String ticketNo = map.get("ticketNo").toString();
                    map.clear();
                    map.put("ticketNo", ticketNo);
                }
            }

//            String inputTicketNo = tktNoList.get(0);
//            listMap = listMap.stream()
//                    .filter(m -> m.get("ticketNo").equals(inputTicketNo))
//                    .collect(Collectors.toList());
//            for (String tktNo : allTicketNoList) {
//                if (!tktNo.equals(inputTicketNo)) {
//                    Map<String, Object> map = new HashMap<>();
//                    map.put("ticketNo", tktNo);
//                    listMap.add(map);
//                }
//            }
//            listMap = listMap.stream()
//                    .sorted(Comparator.comparing(map -> map.get("ticketNo").toString()))
//                    .collect(Collectors.toList());
        } else {
            if (!tktNoList.get(0).equals(listMap.get(0).get("ticketNo"))) {
                throw new UnifiedResultException("当前票号无法打印，请检查后继续！");
            }
        }
        return listMap;
    }

    @Override
    public List<String> getBoardingPrintData(String key) {
        List<MnjxPrintData> printDataList = iMnjxPrintDataService.lambdaQuery()
                .like(MnjxPrintData::getPrintKey, key)
                .eq(MnjxPrintData::getPrintType, Constant.STR_ZERO)
                .eq(MnjxPrintData::getAllowPrint, Constant.STR_ONE)
                .list();
        if (CollUtil.isNotEmpty(printDataList)) {
            iMnjxPrintDataService.lambdaUpdate()
                    .set(MnjxPrintData::getAllowPrint, Constant.STR_ZERO)
                    .in(MnjxPrintData::getPrintDataId, printDataList.stream().map(MnjxPrintData::getPrintDataId).collect(Collectors.toList()))
                    .update();
            return printDataList.stream().map(MnjxPrintData::getPrintValue).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getLuggagePrintData(String key, String bagNo) {
        List<String> printValueList = new ArrayList<>();
        List<MnjxPrintData> printDataList = iMnjxPrintDataService.lambdaQuery()
                .eq(MnjxPrintData::getPrintKey, key)
                .ne(MnjxPrintData::getPrintType, Constant.STR_ZERO)
                .eq(MnjxPrintData::getAllowPrint, Constant.STR_ONE)
                .orderByDesc(MnjxPrintData::getCreateTime)
                .list();
        if (CollUtil.isNotEmpty(printDataList)) {
            // 如果传入了行李编号，按行李编号返回
            if (StrUtil.isNotEmpty(bagNo)) {
                // 传入的是R，返回所有的行李数据
                if ("R".equals(bagNo)) {
                    printValueList.addAll(printDataList.stream()
                            .map(MnjxPrintData::getPrintValue)
                            .collect(Collectors.toList()));
                } else {
                    MnjxPrintData printData = printDataList.stream()
                            .filter(p -> bagNo.equals(p.getPrintType()))
                            .collect(Collectors.toList())
                            .get(0);
                    iMnjxPrintDataService.lambdaUpdate()
                            .set(MnjxPrintData::getAllowPrint, Constant.STR_ZERO)
                            .eq(MnjxPrintData::getPrintDataId, printData.getPrintDataId())
                            .update();
                    printValueList.add(printData.getPrintValue());
                }
            }
            // 没有传入行李编号，返回最近的一条行李
            else {
//                for (MnjxPrintData printData : printDataList) {
//                    printValueList.add(printData.getPrintValue());
//                }
                printValueList.add(printDataList.get(0).getPrintValue());
            }
        }
        return printValueList;
    }

    @Override
    public String getSavedForPrintDataList(String key) throws UnifiedResultException {
        MnjxPrintData printData = iMnjxPrintDataService.lambdaQuery()
                .eq(MnjxPrintData::getPrintKey, key)
                .one();
        if (ObjectUtil.isEmpty(printData)) {
            throw new UnifiedResultException(StrUtil.format("没有查询到{}的结果", key));
        }
        return printData.getPrintValue();
    }

    @Override
    public List<Map<String, Object>> getPrintData(List<String> tktNoList) throws Exception {
        List<Map<String, Object>> listMap = new ArrayList<>();
        List<String> allTicketNoList = new ArrayList<>();
        this.validateTicketNo(tktNoList, allTicketNoList);
        this.constructResultMap(listMap, allTicketNoList);
        return listMap;
    }

    private void validateTicketNo(List<String> tktNoList, List<String> allTicketNoList) throws UnifiedResultException {
        if (tktNoList.size() == 1) {
            String ticketNo = tktNoList.get(0);
            // 票号不存在
            MnjxPnrNmTicket mnjxPnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                    .one();
            if (ObjectUtil.isEmpty(mnjxPnrNmTicket)) {
                throw new UnifiedResultException(Constant.TKT_NOT_FUND);
            }

            MnjxPnrNmTn ticketTn = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getTnId, mnjxPnrNmTicket.getPnrNmTnId())
                    .one();
            if (ObjectUtil.isEmpty(ticketTn)) {
                throw new UnifiedResultException("当前票号无法打印，请检查后继续！");
            }
            List<MnjxPnrNmTicket> allPnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getPnrNmTnId, ticketTn.getTnId())
                    .list();
            allTicketNoList.addAll(allPnrNmTicket.stream()
                    .map(MnjxPnrNmTicket::getTicketNo)
                    .collect(Collectors.toList()));
        } else {
            // 判断输入的票号串是否是联票
            List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                    .in(MnjxPnrNmTicket::getTicketNo, tktNoList)
                    .list();
            if (CollUtil.isEmpty(nmTicketList)) {
                throw new UnifiedResultException(Constant.TKT_NOT_FUND);
            }
            List<String> tnIdList = nmTicketList.stream()
                    .map(MnjxPnrNmTicket::getPnrNmTnId)
                    .distinct()
                    .collect(Collectors.toList());
            List<MnjxPnrNmTn> nmTnList = iMnjxPnrNmTnService.listByIds(tnIdList);
            if (CollUtil.isEmpty(nmTnList)) {
                throw new UnifiedResultException("当前票号无法打印，请检查后继续！");
            }
            List<String> nmIdList = nmTnList.stream()
                    .map(MnjxPnrNmTn::getPnrNmId)
                    .distinct()
                    .collect(Collectors.toList());
            List<String> xnIdList = nmTnList.stream()
                    .map(MnjxPnrNmTn::getNmXnId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(xnIdList)) {
                List<MnjxNmXn> nmXnList = iMnjxNmXnService.listByIds(xnIdList);
                nmIdList.addAll(nmXnList.stream()
                        .map(MnjxNmXn::getPnrNmId)
                        .distinct()
                        .collect(Collectors.toList()));
            }
            nmIdList = nmIdList.stream()
                    .distinct()
                    .collect(Collectors.toList());
            List<MnjxPnrNm> nmList = iMnjxPnrNmService.listByIds(nmIdList);
            List<String> pnrIdList = nmList.stream()
                    .map(MnjxPnrNm::getPnrId)
                    .distinct()
                    .collect(Collectors.toList());
            if (pnrIdList.size() != 1) {
                throw new UnifiedResultException("当前查询的客票非联票，请检查后重新查询！");
            }
            List<MnjxPnrNmTicket> allPnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                    .in(MnjxPnrNmTicket::getPnrNmTnId, tnIdList)
                    .list();
            allTicketNoList.addAll(allPnrNmTicket.stream()
                    .map(MnjxPnrNmTicket::getTicketNo)
                    .collect(Collectors.toList()));
        }
    }

    private void constructResultMap(List<Map<String, Object>> listMap, List<String> allTicketNoList) throws Exception {
        for (String tktNo : allTicketNoList) {
            this.getPrintDataByTktNo(listMap, tktNo);
        }
        listMap = listMap.stream()
                .sorted(Comparator.comparing(map -> map.get("ticketNo").toString()))
                .collect(Collectors.toList());
        // 处理列表关联数据
        if (listMap.size() > 1) {
            Map<String, List<Map<String, Object>>> map = listMap.stream()
                    .collect(Collectors.groupingBy(m -> m.get("tnId").toString()));
            for (Map.Entry<String, List<Map<String, Object>>> entry : map.entrySet()) {
                List<Map<String, Object>> list = entry.getValue();
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map1 = list.get(i);
                    if (i != 0) {
                        map1.put("fcny", "CNY0.00");
                        map1.put("cnyCn", "CNY0.00");
                        map1.put("cnyYq", "CNY0.00");
                        map1.put("acny", "CNY0.00");
                    }
                    if (list.size() > 1) {
                        List<String> ticketNoList = list.stream()
                                .map(l -> l.get("ticketNo").toString())
                                .sorted()
                                .collect(Collectors.toList());
                        String firstTicket = ticketNoList.get(0);
                        String lastTicket = ticketNoList.get(ticketNoList.size() - 1);
                        map1.put("totalTicketNo", StrUtil.format("{}-{}", firstTicket, lastTicket.substring(lastTicket.length() - 2)));
                    }
                }
            }
        }
    }

    private void getPrintDataByTktNo(List<Map<String, Object>> listMap, String ticketNo) throws Exception {
        // 票号不存在
        MnjxPnrNmTicket mnjxPnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                .one();
        if (ObjectUtil.isEmpty(mnjxPnrNmTicket)) {
            throw new UnifiedResultException(Constant.TKT_NOT_FUND);
        }

        MnjxPnrNmTn ticketTn = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getTnId, mnjxPnrNmTicket.getPnrNmTnId())
                .one();
        if (ObjectUtil.isEmpty(ticketTn)) {
            throw new UnifiedResultException("当前票号无法打印，请检查后继续！");
        }
        MnjxNmXn mnjxNmXn = null;
        MnjxPnrNm mnjxPnrNm = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrNmId, ticketTn.getPnrNmId())
                .one();
        if (mnjxPnrNm == null) {
            mnjxNmXn = iMnjxNmXnService.getById(ticketTn.getNmXnId());
            mnjxPnrNm = iMnjxPnrNmService.getById(mnjxNmXn.getPnrNmId());
        }
        String pnrNmId = mnjxPnrNm.getPnrNmId();
        String pnrId = mnjxPnrNm.getPnrId();
        MnjxPnr mnjxPnr = iMnjxPnrService.getById(pnrId);

        StringBuilder sb = new StringBuilder(ticketNo);
        sb.insert(3, "-");
        String tmpTktNo = sb.toString();
        // 判断票是否符合打票条件
        if (Constant.TICKET_STATUS_EXCHANGED.equals(mnjxPnrNmTicket.getTicketStatus1())) {
            MnjxPnrRecord recordTicket = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrType, "TN")
                    .eq(MnjxPnrRecord::getChangeMark, "X")
                    .like(MnjxPnrRecord::getInputValue, tmpTktNo)
                    .one();

            // 客票存在未使用状态
            if (Constant.TICKET_STATUS_OPEN_FOR_USE.equals(mnjxPnrNmTicket.getTicketStatus1())) {
                if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN)
                        .after(DateUtil.parse(
                                DateUtil.offset(DateUtil.parse(recordTicket.getIssuedTime(), DatePattern.NORM_DATETIME_PATTERN), DateField.YEAR, 1)
                                        .toDateStr(),
                                DatePattern.NORM_DATE_PATTERN))) {
                    throw new UnifiedResultException("该票号/PNR已过期无法打印！");
                }
            }
        } else {
            // 客票状态为REFUND或SUSPENDED或EXCHANGED，不进行处理
            String ticketStatus1 = mnjxPnrNmTicket.getTicketStatus1();
            if (StrUtil.isNotEmpty(ticketStatus1) && StrUtil.equalsAny(ticketStatus1, Constant.TICKET_STATUS_SUSPENDED, Constant.REFUNDED, Constant.TICKET_STATUS_EXCHANGED)) {
//                throw new UnifiedResultException("当前票号无法打印，请检查后继续！");
                return;
            }
            String ticketStatus2 = mnjxPnrNmTicket.getTicketStatus2();
            if (StrUtil.isNotEmpty(ticketStatus2) && StrUtil.equalsAny(ticketStatus2, Constant.TICKET_STATUS_SUSPENDED, Constant.REFUNDED, Constant.TICKET_STATUS_EXCHANGED)) {
//                throw new UnifiedResultException("当前票号无法打印，请检查后继续！");
                return;
            }
            // 客票行程单有效期进行校验
            if (Constant.TICKET_STATUS_USED_OR_FLOWN.equals(ticketStatus1)) {
                // 客票已使用
                if (StrUtil.isNotEmpty(ticketStatus2)) {
                    if (Constant.TICKET_STATUS_USED_OR_FLOWN.equals(ticketStatus2)) {
                        MnjxPnrSeg twoSeg = iMnjxPnrSegService.lambdaQuery()
                                .eq(MnjxPnrSeg::getPnrSegId, mnjxPnrNmTicket.getS2Id())
                                .one();
                        MnjxPsgCki twoCki = iMnjxPsgCkiService.lambdaQuery()
                                .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                                .eq(MnjxPsgCki::getPnrSegNo, twoSeg.getPnrSegNo())
                                .one();
                        MnjxPsgOperateRecord twoBab = iMnjxPsgOperateRecordService.lambdaQuery()
                                .eq(MnjxPsgOperateRecord::getPsgCkiId, twoCki.getPsgCkiId())
                                .eq(MnjxPsgOperateRecord::getOperateType, "BAB")
                                .one();
                        if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN).after(DateUtil.parse(
                                DateUtil.offset(twoBab.getOperateTime(), DateField.DAY_OF_YEAR, 7).toDateStr(), DatePattern.NORM_DATE_PATTERN))) {
                            throw new UnifiedResultException("该票号/PNR已过期无法打印！");
                        }
                    }
                } else {
                    MnjxPnrSeg oneSeg = iMnjxPnrSegService.lambdaQuery()
                            .eq(MnjxPnrSeg::getPnrSegId, mnjxPnrNmTicket.getS1Id())
                            .one();
                    MnjxPsgCki oneCki = iMnjxPsgCkiService.lambdaQuery()
                            .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                            .eq(MnjxPsgCki::getPnrSegNo, oneSeg.getPnrSegNo())
                            .one();
                    MnjxPsgOperateRecord oneBab = iMnjxPsgOperateRecordService.lambdaQuery()
                            .eq(MnjxPsgOperateRecord::getPsgCkiId, oneCki.getPsgCkiId())
                            .eq(MnjxPsgOperateRecord::getOperateType, "BAB")
                            .one();
                    if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN).after(DateUtil
                            .parse(DateUtil.offset(oneBab.getOperateTime(), DateField.DAY_OF_YEAR, 7).toDateStr(), DatePattern.NORM_DATE_PATTERN))) {
                        throw new UnifiedResultException("该票号/PNR已过期无法打印！");
                    }
                }
            } else {
                // 客票存在未使用状态
                if (StrUtil.isNotBlank(ticketStatus1) && Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticketStatus1)) {
                    if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN)
                            .after(DateUtil.parse(
                                    DateUtil.offset(DateUtil.parse(ticketTn.getIssuedTime(), DatePattern.NORM_DATETIME_PATTERN), DateField.YEAR, 1)
                                            .toDateStr(),
                                    DatePattern.NORM_DATE_PATTERN))) {
                        throw new UnifiedResultException("该票号/PNR已过期无法打印！");
                    }
                }
                if (StrUtil.isNotBlank(ticketStatus2) && Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticketStatus2)) {
                    if (DateUtil.parse(DateUtil.today(), DatePattern.NORM_DATE_PATTERN)
                            .after(DateUtil.parse(DateUtil
                                    .offset(DateUtil.parse(ticketTn.getIssuedTime(), DatePattern.NORM_DATETIME_PATTERN), DateField.YEAR, 1)
                                    .toDateStr(), DatePattern.NORM_DATE_PATTERN))) {
                        throw new UnifiedResultException("该票号/PNR已过期无法打印！");
                    }
                }
            }
        }
        List<MnjxPnrSeg> segList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnrId)
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();
        if (CollUtil.isEmpty(segList)) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        if (segList.size() > 4) {
            throw new UnifiedResultException(Constant.ILLEGAL_SEGMENT);
        }
        segList = segList.stream()
                .filter(s -> StrUtil.equalsAny(s.getPnrSegId(), mnjxPnrNmTicket.getS1Id(), mnjxPnrNmTicket.getS2Id()))
                .collect(Collectors.toList());

        String psgName;
        if (ObjectUtil.isNotEmpty(mnjxNmXn)) {
            psgName = StrUtil.format("{} INF({})", mnjxNmXn.getXnCname(), DateUtils.ym2Com(mnjxNmXn.getXnBirthday()));
        } else {
            String psgType = mnjxPnrNm.getPsgType();
            if (Constant.STR_ONE.equals(psgType)) {
                psgName = StrUtil.format("{} CHD", mnjxPnrNm.getName());
            } else {
                psgName = mnjxPnrNm.getName();
            }
            MnjxPnrNmUm nmUm = iMnjxPnrNmUmService.lambdaQuery()
                    .eq(MnjxPnrNmUm::getPnrNmId, mnjxPnrNm.getPnrNmId())
                    .one();
            if (ObjectUtil.isNotEmpty(nmUm)) {
                psgName = StrUtil.format("{}(UM{})", mnjxPnrNm.getName(), nmUm.getUmAge());
            }
        }
        String bookOffice = mnjxPnr.getCreateOfficeNo();
        String patType = "AD";
        MnjxNmSsr ssr = iMnjxSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                .eq(MnjxNmSsr::getSsrType, "FOID")
                .one();
        String ssrInfo;
        if (ObjectUtil.isNotNull(ssr)) {
            ssrInfo = ssr.getSsrInfo();
        } else {
            ssrInfo = "";
        }
        MnjxOffice office = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, bookOffice)
                .one();
        Map<String, Object> map = new HashMap<>(1024);
        map.put("tnId", ticketTn.getTnId());
        List<MnjxPnrRecord> eiRecords = iMnjxPnrRecordService.lambdaQuery()
                .eq(MnjxPnrRecord::getPnrId, pnrId)
                .eq(MnjxPnrRecord::getPnrType, "EI")
                .list();
        if (CollUtil.isNotEmpty(eiRecords)) {
            map.put("eiInfo", eiRecords.get(0).getInputValue());
        } else {
            List<MnjxPnrRecord> nmEiRecords = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnrId)
                    .eq(MnjxPnrRecord::getPnrType, "NM EI")
                    .list();
            if (CollUtil.isNotEmpty(nmEiRecords)) {
                map.put("eiInfo", nmEiRecords.get(0).getInputValue().subSequence(0, nmEiRecords.get(0).getInputValue().indexOf("/")));
            }
        }
        map.put("psgName", psgName);
        map.put("officeNo", office.getOfficeNo());
        // ssr有可能是空
        String idNumber = StrUtil.EMPTY;
        if (StrUtil.isNotEmpty(ssrInfo) && ReUtil.isMatch(REG_FOID, ssrInfo)) {
            idNumber = ReUtil.get(REG_FOID, ssrInfo, 1);
            if (StrUtil.isNotBlank(idNumber) && idNumber.startsWith("NI")) {
                if (mnjxNmXn != null) {
                    if (idNumber.startsWith("NI")) {
                        idNumber = idNumber.replace("NI", "(INFANT)");
                    }
                }
            }
        }
        map.put("IDNO", idNumber);
        map.put("pnrICs", mnjxPnr.getPnrCrs());
        String maxNum = null;
        if (StrUtil.isBlank(maxNum)) {
            map.put("serialNumber", "0000000001");
        } else {
            BigDecimal bigDecimal = new BigDecimal(maxNum);
            String fill = StrUtil.fill(bigDecimal.add(new BigDecimal("1")).toString(), '0', 10, true);
            map.put("serialNumber", fill);
        }
        if (Constant.OFFICE_TYPE_AGENT.equals(office.getOfficeType())) {
            MnjxAgent agent = iMnjxAgentService.lambdaQuery().eq(MnjxAgent::getAgentId, office.getOrgId()).one();
            map.put("agentCode", agent.getAgentIata());
            map.put("issudeBy", agent.getAgentCname());
        } else if (Constant.OFFICE_TYPE_AIRPORT.equals(office.getOfficeType())) {
            MnjxAirport airport = iMnjxAirportcodeService.lambdaQuery().eq(MnjxAirport::getAirportId, office.getOrgId()).one();
            map.put("agentCode", "");
            map.put("issudeBy", airport.getAirportCname());
        } else if (Constant.OFFICE_TYPE_AIRLINE.equals(office.getOfficeType())) {
            MnjxAirline airline = iMnjxAirlineService.lambdaQuery().eq(MnjxAirline::getAirlineId, office.getOrgId()).one();
            map.put("agentCode", "");
            map.put("issudeBy", airline.getAirlineFullName());
        }
        map.put("segSize", segList.size());
        for (int i = 0; i < segList.size(); i++) {
            MnjxPnrSeg mnjxPnrSeg = segList.get(i);
            map.put("org" + i, mnjxPnrSeg.getOrg());
            map.put("dst" + i, mnjxPnrSeg.getDst());
            MnjxAirport offAirport = iMnjxAirportcodeService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, mnjxPnrSeg.getOrg())
                    .one();
            MnjxCity offCity = iMnjxCityService.lambdaQuery()
                    .eq(MnjxCity::getCityId, offAirport.getCityId())
                    .one();
            map.put("orgName" + i, offCity.getCityCname());
            MnjxAirport arrAirport = iMnjxAirportcodeService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, mnjxPnrSeg.getDst())
                    .one();
            MnjxCity arrCity = iMnjxCityService.lambdaQuery()
                    .eq(MnjxCity::getCityId, arrAirport.getCityId())
                    .one();
            map.put("dstName" + i, arrCity.getCityCname());
            MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                    .eq(MnjxFlight::getFlightNo, mnjxPnrSeg.getFlightNo())
                    .one();
            // flight查询为空，说明当前航段是SA航段
            if (ObjectUtil.isEmpty(flight)) {
                map.put("fltNo" + i, "VOID");
                continue;
            }
            MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                    .eq(MnjxTcard::getFlightId, flight.getFlightId())
                    .one();
            String fltNo = mnjxPnrSeg.getFlightNo();
            // tcard为空，说明是共享航班，需要找承运航班查询
            if (ObjectUtil.isEmpty(tcard)) {
                fltNo = mnjxPnrSeg.getCarrierFlight();
                if (StrUtil.isNotBlank(fltNo)) {
                    flight = iMnjxFlightService.lambdaQuery()
                            .eq(MnjxFlight::getFlightNo, fltNo)
                            .one();
                    if (ObjectUtil.isNotEmpty(flight)) {
                        tcard = iMnjxTcardService.lambdaQuery()
                                .eq(MnjxTcard::getFlightId, flight.getFlightId())
                                .one();
                        if (ObjectUtil.isEmpty(tcard)) {
                            throw new UnifiedResultException(Constant.ERROR);
                        }
                    } else {
                        throw new UnifiedResultException(Constant.ERROR);
                    }
                }
            }
            map.put("fltNo" + i, mnjxPnrSeg.getFlightNo());
            map.put("fltDate" + i, mnjxPnrSeg.getFlightDate());
            // 20230725修改
            List<MnjxPlanSection> planSections = this.getMatchPlanSections(tcard.getTcardId(), mnjxPnrSeg.getFlightDate(), mnjxPnrSeg.getOrg(), mnjxPnrSeg.getDst());
            map.put("time" + i, planSections.get(planSections.size() - 1).getEstimateOff());
            MnjxPsgCki cki = iMnjxPsgCkiService.lambdaQuery()
                    .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                    .eq(MnjxPsgCki::getPnrSegNo, mnjxPnrSeg.getPnrSegNo())
                    .one();
            // CKI为空的时候说明成人退票了，婴儿还没有退票
            if (ObjectUtil.isEmpty(cki)) {
                throw new UnifiedResultException("成人已退票，婴儿票无法继续打印");
            } else {
                map.put("cabinClass" + i, cki.getSellCabin());
            }
            if (ObjectUtil.isNotEmpty(mnjxNmXn)) {
                map.put("CABINKG" + i, "0K");
            } else {
                map.put("CABINKG" + i, getCndCabinWeight(fltNo, cki.getCabinClass()) + "K");
            }
            MnjxNmSsr chldSsr = iMnjxSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getSsrType, "CHLD")
                    .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
                    .one();
            if (ObjectUtil.isNull(chldSsr)) {
                map.put("fareBasis" + i, cki.getCabinClass() + cki.getSellCabin());
            } else {
                map.put("fareBasis" + i, cki.getCabinClass() + "CH");
            }
        }
        map.put("ticketNo", ticketNo);
        // 这里设置一份完整票号，如果是联票在后面的逻辑才会替换成联票格式
        map.put("totalTicketNo", ticketNo);
        map.put("issuedTime", ticketTn.getIssuedTime());
        MnjxNmFn fn = null;
        MnjxPnrFn pnrFn = null;
        MnjxPnrRecord fnRecord = null;
        if (Constant.TICKET_STATUS_EXCHANGED.equals(mnjxPnrNmTicket.getTicketStatus1())) {
            List<MnjxPnrRecord> fnRecords = iMnjxPnrRecordService.lambdaQuery()
                    .in(MnjxPnrRecord::getPnrType, "FN", "NM FN")
                    .eq(MnjxPnrRecord::getPnrId, pnrId)
                    .list();
            if (CollUtil.isNotEmpty(fnRecords)) {
                if (mnjxNmXn != null) {
                    List<MnjxPnrRecord> xnRecords = fnRecords.stream()
                            .filter(f -> f.getInputValue().contains("/IN/"))
                            .collect(Collectors.toList());
                    fnRecord = xnRecords.get(0);
                } else {
                    List<MnjxPnrRecord> pnrRecords = fnRecords.stream()
                            .filter(f -> !f.getInputValue().contains("/IN/"))
                            .collect(Collectors.toList());
                    fnRecord = pnrRecords.get(0);
                }
            }
        } else {
            List<MnjxNmFn> fnList = iMnjxFnService.lambdaQuery()
                    .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                    .list();
            if (CollUtil.isEmpty(fnList)) {
                List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                        .eq(MnjxPnrFn::getPnrId, pnrId)
                        .list();
                if (CollUtil.isNotEmpty(pnrFnList)) {
                    if (mnjxNmXn != null) {
                        List<MnjxPnrFn> xnFns = pnrFnList.stream()
                                .filter(f -> "IN".equals(f.getPatType()))
                                .collect(Collectors.toList());
                        pnrFn = xnFns.get(0);
                    } else {
                        List<MnjxPnrFn> xnFns = pnrFnList.stream()
                                .filter(f -> !"IN".equals(f.getPatType()))
                                .collect(Collectors.toList());
                        pnrFn = xnFns.get(0);
                    }
                }
            } else {
                // 暂时只处理一个fn，如果nmId查不到的，用pnrId查应该是每个旅客都是同一个fn
                if (mnjxNmXn != null) {
                    List<MnjxNmFn> xnFns = fnList.stream()
                            .filter(f -> "IN".equals(f.getPatType()))
                            .collect(Collectors.toList());
                    fn = xnFns.get(0);
                } else {
                    List<MnjxNmFn> nmFns = fnList.stream()
                            .filter(f -> !"IN".equals(f.getPatType()))
                            .collect(Collectors.toList());
                    fn = nmFns.get(0);
                }

                if (ObjectUtil.isNull(fn)) {
                    List<MnjxPnrFn> pnrFnList = iMnjxPnrFnService.lambdaQuery()
                            .eq(MnjxPnrFn::getPnrId, pnrId)
                            .eq(MnjxPnrFn::getPatType, patType)
                            .list();
                    if (CollUtil.isNotEmpty(pnrFnList)) {
                        if (mnjxNmXn != null) {
                            List<MnjxPnrFn> xnFns = pnrFnList.stream()
                                    .filter(f -> "IN".equals(f.getPatType()))
                                    .collect(Collectors.toList());
                            pnrFn = xnFns.get(0);
                        } else {
                            List<MnjxPnrFn> nmFns = pnrFnList.stream()
                                    .filter(f -> !"IN".equals(f.getPatType()))
                                    .collect(Collectors.toList());
                            pnrFn = nmFns.get(0);
                        }

                    }
                }
            }
            if (ObjectUtil.isNull(fn) && ObjectUtil.isNull(pnrFn)) {
                List<MnjxPnrRecord> fnRecords = iMnjxPnrRecordService.lambdaQuery()
                        .in(MnjxPnrRecord::getPnrType, "FN", "NM FN")
                        .eq(MnjxPnrRecord::getPnrId, pnrId)
                        .list();
                if (CollUtil.isNotEmpty(fnRecords)) {
                    if (mnjxNmXn != null) {
                        List<MnjxPnrRecord> xnFns = fnRecords.stream()
                                .filter(f -> f.getInputValue().contains("/IN/"))
                                .collect(Collectors.toList());
                        fnRecord = xnFns.get(0);
                    } else {
                        List<MnjxPnrRecord> nmFns = fnRecords.stream()
                                .filter(f -> !f.getInputValue().contains("/IN/"))
                                .collect(Collectors.toList());
                        fnRecord = nmFns.get(0);
                    }
                }
            }
        }
        if (ObjectUtil.isNotNull(fn)) {
            map.put("fcny", fn.getFCurrency() + fn.getFPrice());
            map.put("cnyCn", fn.getTCnCurrency() + fn.getTCnPrice());
            map.put("cnyYq", fn.getTYqCurrency() + fn.getTYqPrice());
            map.put("acny", fn.getACurrency() + fn.getAPrice());
        } else if (ObjectUtil.isNotNull(pnrFn)) {
            map.put("fcny", pnrFn.getFCurrency() + pnrFn.getFPrice());
            map.put("cnyCn", pnrFn.getTCnCurrency() + pnrFn.getTCnPrice());
            map.put("cnyYq", pnrFn.getTYqCurrency() + pnrFn.getTYqPrice());
            map.put("acny", pnrFn.getACurrency() + pnrFn.getAPrice());
        } else if (ObjectUtil.isNotNull(fnRecord)) {
            String[] values = fnRecord.getInputValue().split("/");
            for (String value : values) {
                if (value.contains("FCNY")) {
                    map.put("fcny", value.substring(1));
                    continue;
                }
                if (value.contains("ACNY")) {
                    map.put("acny", value.substring(1));
                    continue;
                }
                if (value.length() > 3) {
                    String tailStr = value.substring(value.length() - 2);
                    if ("CN".equals(tailStr)) {
                        map.put("cnyCn", value.substring(1, value.length() - 2));
                    }
                    if ("YQ".equals(tailStr)) {
                        map.put("cnyYq", value.substring(1, value.length() - 2));
                    }
                }
            }
        }
        listMap.add(map);
    }

    private List<MnjxPlanSection> getMatchPlanSections(String tCardId, String flightDate, String orgCode, String dstCode) {
        MnjxPlanFlight planFlight = iMnjxPlanFlightService.lambdaQuery()
                .eq(MnjxPlanFlight::getFlightDate, flightDate)
                .eq(MnjxPlanFlight::getTcardId, tCardId)
                .one();

        MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, orgCode.substring(0, 3))
                .one();
        MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                .eq(MnjxAirport::getAirportCode, dstCode)
                .one();
        List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                .orderByAsc(MnjxPlanSection::getIsLastSection)
                .orderByAsc(MnjxPlanSection::getEstimateOffChange)
                .orderByAsc(MnjxPlanSection::getEstimateOff)
                .list();
        boolean findFirstAirport = false;
        List<MnjxPlanSection> thisPsgPlanSectionList = new ArrayList<>();
        for (MnjxPlanSection mnjxPlanSection : planSectionList) {
            // 输入的航段是多航段中的某一个航段，或者是单航段
            if (orgAirport.getAirportId().equals(mnjxPlanSection.getDepAptId()) && dstAirport.getAirportId().equals(mnjxPlanSection.getArrAptId())) {
                thisPsgPlanSectionList.add(mnjxPlanSection);
            }
            // 输入的航段是多航段中多个航段的组合
            else {
                // 出发
                if (orgAirport.getAirportId().equals(mnjxPlanSection.getDepAptId())) {
                    findFirstAirport = true;
                    thisPsgPlanSectionList.add(mnjxPlanSection);
                }
                // 如果都没有匹配到，说明是中间航段，也需要把开舱数据加进去进行筛选
                else if (findFirstAirport) {
                    thisPsgPlanSectionList.add(mnjxPlanSection);
                    if (dstAirport.getAirportId().equals(mnjxPlanSection.getArrAptId())) {
                        findFirstAirport = false;
                    }
                }
            }
        }
        return thisPsgPlanSectionList;
    }

    private int getCndCabinWeight(String flightNo, String cabinClass) {
        MnjxFlight flight = iMnjxFlightService.lambdaQuery()
                .eq(MnjxFlight::getFlightNo, flightNo)
                .one();
        MnjxTcard tcard = iMnjxTcardService.lambdaQuery()
                .eq(MnjxTcard::getFlightId, flight.getFlightId())
                .one();
        MnjxCnd cnd = mnjxCndService.lambdaQuery()
                .eq(MnjxCnd::getCndId, tcard.getCndId())
                .one();
        int weight;
        if (cabinClass.equals(cnd.getFirstCabinClass())) {
            weight = cnd.getFirstWeight();
        } else if (cabinClass.equals(cnd.getSecondCabinClass())) {
            weight = cnd.getSecondWeight();
        } else if (cabinClass.equals(cnd.getThirdCabinClass())) {
            weight = cnd.getThirdWeight();
        } else if (cabinClass.equals(cnd.getFourthCabinClass())) {
            weight = cnd.getFourthWeight();
        } else {
            weight = cnd.getFifthWeight();
        }
        return weight;
    }

    /**
     * @param pnr
     * @return
     * @throws UnifiedResultException <br>
     * @title：getTktNoByPnr <br>
     * @description：pnr查询票号 <br>
     * <AUTHOR> <br>
     * @date 2023/07/25 <br>
     */
    @Override
    public List<String> getTktNoByPnr(String pnr) throws UnifiedResultException {
        MnjxPnr mnjxPnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, pnr)
                .one();
        if (ObjectUtil.isEmpty(mnjxPnr)) {
            Log.error("PNR不存在：" + pnr);
            throw new UnifiedResultException("请输入正确的票号/PNR！");
        }
        Set<String> setCollection = new HashSet<>();
        List<MnjxPnrNm> mnjxPnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, mnjxPnr.getPnrId())
                .list();
        for (MnjxPnrNm mnjxPnrNm : mnjxPnrNmList) {
            String pnrNmId = mnjxPnrNm.getPnrNmId();
            List<MnjxPnrNmTn> mnjxPnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getPnrNmId, pnrNmId)
                    .list();
            for (MnjxPnrNmTn mnjxPnrNmTn : mnjxPnrNmTnList) {
                this.queryTktNo(setCollection, mnjxPnrNmTn.getTnId());
            }
            MnjxNmXn mnjxNmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, pnrNmId)
                    .one();
            if (ObjectUtil.isNotEmpty(mnjxNmXn)) {
                List<MnjxPnrNmTn> xnPnrNmTnList = iMnjxPnrNmTnService.lambdaQuery()
                        .eq(MnjxPnrNmTn::getNmXnId, mnjxNmXn.getNmXnId())
                        .list();
                for (MnjxPnrNmTn xnPnrNmTn : xnPnrNmTnList) {
                    this.queryTktNo(setCollection, xnPnrNmTn.getTnId());
                }
            }
        }
        if (CollUtil.isEmpty(setCollection)) {
            Log.error("PNR不存在：" + pnr);
            throw new UnifiedResultException("请输入正确的票号/PNR！");
        }
        return new ArrayList<>(setCollection);
    }

    private void queryTktNo(Set<String> setCollection, String tnId) {
        List<MnjxPnrNmTicket> mnjxPnrNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getPnrNmTnId, tnId)
                .list();
        if (CollUtil.isNotEmpty(mnjxPnrNmTicketList)) {
            for (MnjxPnrNmTicket mpntList : mnjxPnrNmTicketList) {
                setCollection.add(mpntList.getTicketNo());
            }
        }
    }
}
