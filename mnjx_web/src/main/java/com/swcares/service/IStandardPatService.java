package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxCity;
import com.swcares.entity.MnjxStandardPat;
import com.swcares.obj.vo.StandardPatRetrieveVo;
import com.swcares.obj.vo.StandardPatVo;
import com.swcares.obj.vo.excel.StandPatExcellVo;

import java.util.List;

/**
 * description：IStandardPatService
 *
 * <AUTHOR>
 * @date 2021/9/28
 */
public interface IStandardPatService {
    /**
     * 新增基准运价
     *
     * @param mnjxStandardPat 基准运价
     * @return 新增基准运价
     * @throws UnifiedResultException 统一异常
     */
    boolean create(MnjxStandardPat mnjxStandardPat) throws UnifiedResultException;

    /**
     * 查询所有城市信息
     *
     * @return 查询所有城市信息
     */
    List<MnjxCity> retrieveCityList();

    /**
     * 分页+条件查询基准运价
     *
     * @param page                  page
     * @param standardPatRetrieveVo standardPatRetrieveVo
     * @return 分页+条件查询基准运价
     */
    IPage<StandardPatVo> retrievePageByCond(IPage<StandardPatVo> page, StandardPatRetrieveVo standardPatRetrieveVo);

    /**
     * 通过ID查询基准运价
     *
     * @param id id
     * @return 通过ID查询基准运价
     */
    StandardPatVo retrieveById(String id);

    /**
     * 更新
     *
     * @param mnjxStandardPat mnjxStandardPat
     * @return 更新
     * @throws UnifiedResultException 统一异常
     */
    boolean update(MnjxStandardPat mnjxStandardPat) throws UnifiedResultException;

    /**
     * 单个删除
     *
     * @param id id
     * @return 单个删除
     */
    boolean deleteById(String id);

    /**
     * 批量删除基准运价
     *
     * @param ids ids
     * @return 批量删除基准运价
     */
    boolean deleteByIds(List<String> ids);


    List<String> vo2Entity(List<StandPatExcellVo> standPatExcellVos);
}
