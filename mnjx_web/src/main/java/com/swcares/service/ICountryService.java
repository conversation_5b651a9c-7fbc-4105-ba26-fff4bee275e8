package com.swcares.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxCountry;
import com.swcares.obj.vo.CountryCreateByImportVo;
import com.swcares.obj.vo.CountryQueryVo;
import com.swcares.obj.vo.CountryUpdateVo;
import com.swcares.obj.vo.CountryVo;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/24-16:34
 */
public interface ICountryService {

    /**
     * 保存国家信息
     *
     * @param mnjxCountry mnjxCountry
     * @return 保存国家信息
     * <AUTHOR>
     * @date 2022/2/10 10:24
     */
    boolean create(MnjxCountry mnjxCountry);

    /**
     * 导入国家数据
     *
     * @param mnjxCountries mnjxCountries
     * @throws UnifiedResultException 统一异常
     */
    void createByImport(List<MnjxCountry> mnjxCountries) throws UnifiedResultException;

    /**
     * 转换对象
     *
     * @param countryCreateByImportVos 导入的数据对象
     * @return 实体对象
     */
    List<MnjxCountry> createTransformVo2Entity(List<CountryCreateByImportVo> countryCreateByImportVos);

    /**
     * 查询所有国家信息
     *
     * @return 国家信息
     */
    List<MnjxCountry> retrieveList();

    /**
     * [条件加分页]
     *
     * @param page           page
     * @param countryQueryVo 查询条件
     * @return 分页查询
     */
    IPage<CountryVo> retrievePageByCond(IPage<CountryVo> page, CountryQueryVo countryQueryVo);

    /**
     * Title: getById
     * Description: 通过ID查询国家信息<br>
     *
     * @param id id
     * @return 通过ID查询国家信息
     * <AUTHOR>
     * @date 2022/2/10 10:24
     */
    MnjxCountry retrieveById(String id);

    /**
     * Title: updateById
     * Description: 通过ID更新国家信息<br>
     *
     * @param mnjxCountry mnjxCountry
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2022/2/10 10:24
     */
    boolean update(MnjxCountry mnjxCountry);

    /**
     * 更新状态
     *
     * @param id     id
     * @param status status
     * @return 更新状态
     */
    boolean updateStatusById(String id, String status);

    /**
     * Title: removeByIds
     * Description: 批量删除国家信息<br>
     *
     * @param ids ids
     * @return {@link boolean}
     * <AUTHOR>
     * @date 2022/2/10 10:25
     */
    boolean deleteIds(List<String> ids);

    String updateContinent(CountryUpdateVo countryUpdateVo);
}
