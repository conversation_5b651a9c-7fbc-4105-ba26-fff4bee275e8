package com.swcares.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.entity.MnjxContinent;
import com.swcares.obj.vo.ContinentQueryVo;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 * 2021/8/24-16:34
 */
public interface IContinentService {

    /**
     * 保存国际州信息
     *
     */
    boolean create(MnjxContinent mnjxContinent) throws UnifiedResultException;

    List<MnjxContinent> retrieveContinentByCond(ContinentQueryVo continentQueryVo);

    List<MnjxContinent> retrieveContinentList();
}
