package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel("航节分页列表返回对象")
@Data
public class PlanSectionPageVo {

    @ApiModelProperty(value = "航节表ID")
    private String planSectionId;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班状态")
    private String flightStatus;

    @ApiModelProperty(value = "ASR舱位")
    private String allowAsr;

    @ApiModelProperty(value = "航班类型（国际、国内）")
    private String flightType;

    @ApiModelProperty(value = "cnd表号")
    private String cndNo;

    @ApiModelProperty(value = "机型")
    private String eqt;

    @ApiModelProperty(value = "版本号")
    private String vers;

    @ApiModelProperty(value = "是否夜航")
    private String isN;

    @ApiModelProperty(value = "出发站")
    private String offAirport;

    @ApiModelProperty(value = "到达站")
    private String arrAirport;

    @ApiModelProperty(value = "计划登机时间")
    private String estimateBoarding;

    @ApiModelProperty(value = "实际登机时间")
    private String actualBoarding;

    @ApiModelProperty(value = "计划离港时间")
    private String estimateOff;

    @ApiModelProperty(value = "计划进港时间")
    private String estimateArr;

    @ApiModelProperty(value = "实际离港时间")
    private String actualOff;

    @ApiModelProperty(value = "实际进港时间")
    private String actualArr;

    @ApiModelProperty(value = "飞机号")
    private String planeNo;

    @ApiModelProperty(value = "订座情况")
    private String gradeBookNumber;

    @ApiModelProperty(value = "成人旅客数量")
    private Integer adultNumber;

    @ApiModelProperty(value = "儿童旅客数量")
    private Integer childNumber;

    @ApiModelProperty(value = "婴儿旅客数量")
    private Integer infNumber;

    @ApiModelProperty(value = "vip旅客数量")
    private Integer vipNumber;

    @ApiModelProperty(value = "团队旅客数量")
    private Integer groupNumber;

    @ApiModelProperty(value = "进港机位")
    private String arrPosition;

    @ApiModelProperty(value = "登机口")
    private String gate;

    @ApiModelProperty(value = "值机状态")
    private String ckStatus;

    @ApiModelProperty(value = "购票人数")
    private Integer ticketNumber;

    @ApiModelProperty(value = "各舱等值机情况")
    private String gradeCkNumber;

    @ApiModelProperty(value = "值机人数")
    private Integer ckNumber;

    @ApiModelProperty(value = "离港机位")
    private String offPosition;
}
