package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FlightCreateVo implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "航班ID")
	private String flightId;

	@ApiModelProperty(value = "航空公司ID")
	private String airlineId;

	@ApiModelProperty(value = "航班号")
	@NotBlank(message = "请输入航班号")
	@Pattern(regexp = "^[A-Z-0-9]{2}[0-9]{3,4}[ZYXWVUTSRQ]?$", message = "请输入合法的航班号")
	private String flightNo;

	@ApiModelProperty(value = "航班状态：STAGED，过渡区航班 MASTER CANCEL DELETE")
	private String flightStatus;

	@ApiModelProperty(value = "允许ASR舱位")
	private Boolean allowAsr;

	@ApiModelProperty(value = "TCard信息")
	private List<FlightTcardInfoVo> flightTcardVos;
	
	@ApiModelProperty(value = "承运航班号")
	private String carrierFlight;
	
	@ApiModelProperty(value = "共享航班状态")
	private String shareState;
}
