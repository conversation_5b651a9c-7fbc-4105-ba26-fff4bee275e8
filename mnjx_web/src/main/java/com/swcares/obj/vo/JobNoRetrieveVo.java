package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Api(tags = "工作号查询Vo")
@AllArgsConstructor
@NoArgsConstructor
public class JobNoRetrieveVo {
    @ApiModelProperty(value = "工作号",example="1001")
    private String jobNo;

    @ApiModelProperty(value = "office号",example="CTU001")
    private String officeNo;

    @ApiModelProperty(value = "机构名",example="成都机票预定")
    private String organizationName;

    @ApiModelProperty(value = "工作号状态名称",example="0")
    private String jobNoStatus;

    @ApiModelProperty(value = "角色名称",example="0")
    private String accountRole;

    @ApiModelProperty(value = "班级Id")
    private String classId;
}
