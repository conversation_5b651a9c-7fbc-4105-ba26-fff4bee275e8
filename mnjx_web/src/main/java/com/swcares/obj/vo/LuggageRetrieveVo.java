package com.swcares.obj.vo;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Api(tags = "行李搜索传输对象")
@Data
public class LuggageRetrieveVo {
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "行李号")
    private String luggageNo;

    @ApiModelProperty(value = "客票号")
    private String ticketNo;
}
