package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@ApiModel("销售运价分页列表请求对象")
@Data
public class SellRuleQueryVo {
    @NotNull(message = "当前页码不能为空")
    @ApiModelProperty("当前页码")
    private Integer current;
    @NotNull(message = "每页条数不能为空")
    @ApiModelProperty("每页条数")
    private Integer limit;
    @ApiModelProperty("航空公司")
    private String airlineCode;
    @ApiModelProperty("折扣率")
    private String airlineCodeDiscount;
    @ApiModelProperty("航司对象")
    private AirlineRetrieveBaseVo airlineRetrieveBaseVo;


}
