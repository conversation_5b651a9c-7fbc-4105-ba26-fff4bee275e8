package com.swcares.obj.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName  PsgRecoverCheckInVo
 * @Description
 * <AUTHOR>
 */
@Builder
@Data
public class PsgRecoverCheckInVo {
    @ApiModelProperty("旅客姓名")
    private String queryName;
    @ApiModelProperty("身份证号")
    private String idCard;
    @ApiModelProperty("航班号")
    private String fltNo;
    @ApiModelProperty("航班日期")
    private String fltDate;
    @ApiModelProperty("PNR")
    private String pnr;
    @ApiModelProperty("座位号")
    private String seatNo;

}
