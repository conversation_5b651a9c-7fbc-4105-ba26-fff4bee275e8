/*
 Navicat Premium Data Transfer

 Source Server         : 测试环境
 Source Server Type    : MySQL
 Source Server Version : 50731 (5.7.31-log)
 Source Host           : **************:3309
 Source Schema         : xxl_job

 Target Server Type    : MySQL
 Target Server Version : 50731 (5.7.31-log)
 File Encoding         : 65001

 Date: 09/05/2024 09:21:13
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for xxl_job_group
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_group`;
CREATE TABLE `xxl_job_group`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行器AppName',
  `title` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行器名称',
  `address_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '执行器地址类型：0=自动注册、1=手动录入',
  `address_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '执行器地址列表，多地址逗号分隔',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_group
-- ----------------------------
INSERT INTO `xxl_job_group` VALUES (3, 'sts-job-executor', '模拟教学系统的自动任务', 0, 'http://***********:9999/', '2024-05-09 09:21:00');

-- ----------------------------
-- Table structure for xxl_job_info
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_info`;
CREATE TABLE `xxl_job_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
  `job_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `add_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `author` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `alarm_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报警邮件',
  `schedule_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'NONE' COMMENT '调度类型',
  `schedule_conf` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调度配置，值含义取决于调度类型',
  `misfire_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'DO_NOTHING' COMMENT '调度过期策略',
  `executor_route_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器路由策略',
  `executor_handler` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务handler',
  `executor_param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务参数',
  `executor_block_strategy` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '阻塞处理策略',
  `executor_timeout` int(11) NOT NULL DEFAULT 0 COMMENT '任务执行超时时间，单位秒',
  `executor_fail_retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `glue_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'GLUE类型',
  `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'GLUE源代码',
  `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'GLUE备注',
  `glue_updatetime` datetime NULL DEFAULT NULL COMMENT 'GLUE更新时间',
  `child_jobid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子任务ID，多个逗号分隔',
  `trigger_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '调度状态：0-停止，1-运行',
  `trigger_last_time` bigint(13) NOT NULL DEFAULT 0 COMMENT '上次调度时间',
  `trigger_next_time` bigint(13) NOT NULL DEFAULT 0 COMMENT '下次调度时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_info
-- ----------------------------
INSERT INTO `xxl_job_info` VALUES (5, 3, '自动生成航班数据', '2022-06-29 13:55:47', '2023-12-15 17:29:18', 'worm', '', 'CRON', '0 30 0 * * ? *', 'DO_NOTHING', 'RANDOM', 'flightJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2022-06-29 13:55:47', '', 1, 1715185800000, 1715272200000);
INSERT INTO `xxl_job_info` VALUES (6, 3, '自动生成旅客记录', '2022-09-07 22:02:22', '2023-12-15 17:29:13', 'worm', '', 'CRON', '0 0 4 ? * * *', 'DO_NOTHING', 'RANDOM', 'psgJobHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2022-09-07 22:02:22', '', 1, 1715198400000, 1715284800000);
INSERT INTO `xxl_job_info` VALUES (7, 3, '自动变更已登机旅客客票状态', '2022-11-15 09:09:27', '2023-12-15 17:29:10', 'worm', '', 'CRON', '0 30 23 * * ? *', 'DO_NOTHING', 'FIRST', 'ticketStatusChangeToUsedHandler', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2022-11-15 09:09:27', '', 1, 1715182200000, 1715268600000);
INSERT INTO `xxl_job_info` VALUES (8, 3, '删除过期七天的航班和旅客', '2022-12-15 13:50:34', '2023-12-15 17:29:08', 'worm', '', 'CRON', '0 0 22 * * ? *', 'DO_NOTHING', 'FIRST', 'deleteHistoryDate', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2022-12-15 13:50:34', '', 1, 1715176800000, 1715263200000);
INSERT INTO `xxl_job_info` VALUES (9, 3, '删除一天前的打印数据', '2023-11-02 16:02:41', '2023-12-15 17:29:04', 'worm', '', 'CRON', '0 50 23 * * ? *', 'DO_NOTHING', 'FIRST', 'deletePrintData', '', 'SERIAL_EXECUTION', 0, 0, 'BEAN', '', 'GLUE代码初始化', '2023-11-02 16:02:41', '', 1, 1715183400000, 1715269800000);

-- ----------------------------
-- Table structure for xxl_job_lock
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_lock`;
CREATE TABLE `xxl_job_lock`  (
  `lock_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '锁名称',
  PRIMARY KEY (`lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_lock
-- ----------------------------
INSERT INTO `xxl_job_lock` VALUES ('schedule_lock');

-- ----------------------------
-- Table structure for xxl_job_log
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log`;
CREATE TABLE `xxl_job_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `job_group` int(11) NOT NULL COMMENT '执行器主键ID',
  `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
  `executor_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器地址，本次执行的地址',
  `executor_handler` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务handler',
  `executor_param` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务参数',
  `executor_sharding_param` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行器任务分片参数，格式如 1/2',
  `executor_fail_retry_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败重试次数',
  `trigger_time` datetime NULL DEFAULT NULL COMMENT '调度-时间',
  `trigger_code` int(11) NOT NULL COMMENT '调度-结果',
  `trigger_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '调度-日志',
  `handle_time` datetime NULL DEFAULT NULL COMMENT '执行-时间',
  `handle_code` int(11) NOT NULL COMMENT '执行-状态',
  `handle_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '执行-日志',
  `alarm_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '告警状态：0-默认、1-无需告警、2-告警成功、3-告警失败',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `I_trigger_time`(`trigger_time`) USING BTREE,
  INDEX `I_handle_code`(`handle_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1936 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_log
-- ----------------------------
INSERT INTO `xxl_job_log` VALUES (1777, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-08 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-08 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1778, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-08 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-08 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1779, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-08 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-08 22:03:53', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1780, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-08 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-08 23:30:01', 200, '自动任务成功执行完成:2024-04-08 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1781, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-08 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-08 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1782, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-09 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-09 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1783, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-09 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-09 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1784, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-09 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-09 22:03:45', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1785, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-09 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-09 23:30:01', 200, '自动任务成功执行完成:2024-04-09 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1786, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-09 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-09 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1787, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-10 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-10 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1788, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-10 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-10 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1789, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-10 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-10 22:03:45', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1790, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-10 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-10 23:30:01', 200, '自动任务成功执行完成:2024-04-10 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1791, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-10 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-10 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1792, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-11 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-11 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1793, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-11 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-11 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1794, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-11 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-11 22:03:49', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1795, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-11 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-11 23:30:00', 200, '自动任务成功执行完成:2024-04-11 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1796, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-11 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-11 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1797, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-12 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-12 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1798, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-12 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-12 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1799, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-12 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-12 22:03:13', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1800, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-12 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-12 23:30:00', 200, '自动任务成功执行完成:2024-04-12 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1801, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-12 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-12 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1802, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-13 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-13 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1803, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-13 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-13 04:00:05', 200, '自动任务成功执行完成:2024-04-13 04:00:05,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1804, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-13 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-13 22:05:19', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1805, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-13 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-13 23:30:00', 200, '自动任务成功执行完成:2024-04-13 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1806, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-13 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-13 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1807, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-14 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-14 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1808, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-14 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-14 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1809, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-14 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-14 22:03:11', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1810, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-14 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-14 23:30:00', 200, '自动任务成功执行完成:2024-04-14 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1811, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-14 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-14 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1812, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-15 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-15 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1813, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-15 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-15 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1814, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-15 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-15 22:03:28', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1815, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-15 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-15 23:30:01', 200, '自动任务成功执行完成:2024-04-15 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1816, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-15 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-15 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1817, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-16 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-16 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1818, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-16 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-16 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1819, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-16 13:37:06', 200, '任务触发类型：手动触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-16 13:37:11', 200, '自动任务成功执行完成:2024-04-16 13:37:11,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1820, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-16 15:36:07', 200, '任务触发类型：手动触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-16 15:36:13', 200, '自动任务成功执行完成:2024-04-16 15:36:12,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1821, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-16 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-16 22:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1822, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-16 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-16 23:30:00', 200, '自动任务成功执行完成:2024-04-16 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1823, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-16 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-16 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1824, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-17 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-17 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1825, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-17 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：***********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-04-17 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1826, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-17 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-17 22:00:01', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1827, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-17 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-17 23:30:00', 200, '自动任务成功执行完成:2024-04-17 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1828, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-17 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-17 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1829, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-18 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-18 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1830, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-18 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-18 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1831, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-18 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-18 22:00:02', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1832, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-18 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-18 23:30:01', 200, '自动任务成功执行完成:2024-04-18 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1833, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-18 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-18 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1834, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-19 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-19 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1835, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-19 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-19 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1836, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-19 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-19 22:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1837, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-19 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-19 23:30:00', 200, '自动任务成功执行完成:2024-04-19 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1838, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-19 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-19 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1839, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-20 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-20 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1840, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-20 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-20 04:00:09', 200, '自动任务成功执行完成:2024-04-20 04:00:08,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1841, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-20 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-20 22:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1842, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-20 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-20 23:30:01', 200, '自动任务成功执行完成:2024-04-20 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1843, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-20 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-20 23:50:05', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1844, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-21 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-21 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1845, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-21 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-21 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1846, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-21 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-21 22:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1847, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-21 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-21 23:30:00', 200, '自动任务成功执行完成:2024-04-21 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1848, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-21 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-21 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1849, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-22 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-22 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1850, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-22 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-22 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1851, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-22 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-22 22:00:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1852, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-22 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-22 23:30:00', 200, '自动任务成功执行完成:2024-04-22 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1853, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-22 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-22 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1854, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-23 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-23 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1855, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-23 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-23 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1856, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-23 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-23 22:00:01', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1857, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-23 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-23 23:30:01', 200, '自动任务成功执行完成:2024-04-23 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1858, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-23 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-23 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1859, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-24 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-24 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1860, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-24 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-24 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1861, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-24 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-24 22:00:40', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1862, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-24 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-24 23:30:00', 200, '自动任务成功执行完成:2024-04-24 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1863, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-24 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-24 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1864, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-25 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-25 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1865, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-25 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-25 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1866, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-25 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-25 22:03:13', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1867, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-25 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-25 23:30:00', 200, '自动任务成功执行完成:2024-04-25 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1868, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-25 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-25 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1869, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-26 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-26 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1870, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-26 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-26 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1871, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-26 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-26 22:03:09', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1872, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-26 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-26 23:30:00', 200, '自动任务成功执行完成:2024-04-26 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1873, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-26 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-26 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1874, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-27 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-27 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1875, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-27 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-27 04:00:15', 200, '自动任务成功执行完成:2024-04-27 04:00:14,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1876, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-27 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-27 22:05:42', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1877, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-27 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-27 23:30:00', 200, '自动任务成功执行完成:2024-04-27 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1878, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-27 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-27 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1879, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-28 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-28 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1880, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-28 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-28 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1881, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-28 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-28 22:03:25', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1882, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-28 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-28 23:30:00', 200, '自动任务成功执行完成:2024-04-28 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1883, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-28 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-28 23:50:02', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1884, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-29 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-29 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1885, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-29 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-29 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1886, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-29 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-29 22:03:08', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1887, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-29 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-29 23:30:00', 200, '自动任务成功执行完成:2024-04-29 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1888, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-29 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-29 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1889, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-04-30 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-30 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1890, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-04-30 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-30 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1891, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-04-30 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-30 22:03:05', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1892, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-04-30 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-30 23:30:00', 200, '自动任务成功执行完成:2024-04-30 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1893, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-04-30 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-04-30 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1894, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-01 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-01 00:30:07', 200, '自动任务成功执行完成:2024-05-01 00:30:07,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1895, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-01 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-01 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1896, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-05-01 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-01 22:03:19', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1897, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-05-01 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-01 23:30:00', 200, '自动任务成功执行完成:2024-05-01 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1898, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-05-01 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-01 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1899, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-02 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-02 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1900, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-02 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-02 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1901, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-05-02 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-02 22:03:03', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1902, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-05-02 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-02 23:30:00', 200, '自动任务成功执行完成:2024-05-02 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1903, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-05-02 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-02 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1904, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-03 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-03 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1905, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-03 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-03 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1906, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-05-03 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-03 22:02:46', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1907, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-05-03 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-03 23:30:00', 200, '自动任务成功执行完成:2024-05-03 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1908, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-05-03 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-03 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1909, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-04 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-04 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1910, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-04 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-04 04:00:05', 200, '自动任务成功执行完成:2024-05-04 04:00:05,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1911, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-05-04 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-04 22:05:44', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1912, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-05-04 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-04 23:30:00', 200, '自动任务成功执行完成:2024-05-04 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1913, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-05-04 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-04 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1914, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-05 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-05 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1915, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-05 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-05 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1916, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-05-05 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-05 22:03:10', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1917, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-05-05 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-05 23:30:00', 200, '自动任务成功执行完成:2024-05-05 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1918, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-05-05 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-05 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1919, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-06 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-06 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1920, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-06 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-06 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1921, 3, 8, 'http://**********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-05-06 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-06 22:03:02', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1922, 3, 7, 'http://**********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-05-06 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-06 23:30:01', 200, '自动任务成功执行完成:2024-05-06 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1923, 3, 9, 'http://**********:9999/', 'deletePrintData', '', NULL, 0, '2024-05-06 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-06 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1924, 3, 5, 'http://**********:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-07 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-07 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1925, 3, 6, 'http://**********:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-07 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://**********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://**********:9999/<br>code：200<br>msg：null', '2024-05-07 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1926, 3, 8, 'http://*********9:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-05-07 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*********8<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://*********9:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://*********9:9999/<br>code：200<br>msg：null', '2024-05-07 22:02:59', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1927, 3, 7, 'http://*********9:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-05-07 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*********8<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://*********9:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://*********9:9999/<br>code：200<br>msg：null', '2024-05-07 23:30:00', 200, '自动任务成功执行完成:2024-05-07 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1928, 3, 9, 'http://*********9:9999/', 'deletePrintData', '', NULL, 0, '2024-05-07 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：*********8<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://*********9:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://*********9:9999/<br>code：200<br>msg：null', '2024-05-07 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1929, 3, 5, 'http://*********9:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-08 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：*********8<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://*********9:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://*********9:9999/<br>code：200<br>msg：null', '2024-05-08 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1930, 3, 6, 'http://*********9:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-08 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：*********8<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://*********9:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://*********9:9999/<br>code：200<br>msg：null', '2024-05-08 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1931, 3, 8, 'http://***********:9999/', 'deleteHistoryDate', '', NULL, 0, '2024-05-08 22:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********3<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-05-08 22:03:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1932, 3, 7, 'http://***********:9999/', 'ticketStatusChangeToUsedHandler', '', NULL, 0, '2024-05-08 23:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********3<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-05-08 23:30:00', 200, '自动任务成功执行完成:2024-05-08 23:30:00,传入的参数为：', 0);
INSERT INTO `xxl_job_log` VALUES (1933, 3, 9, 'http://***********:9999/', 'deletePrintData', '', NULL, 0, '2024-05-08 23:50:00', 200, '任务触发类型：Cron触发<br>调度机器：**********3<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：第一个<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-05-08 23:50:00', 200, '', 0);
INSERT INTO `xxl_job_log` VALUES (1934, 3, 5, 'http://***********:9999/', 'flightJobHandler', '', NULL, 0, '2024-05-09 00:30:00', 200, '任务触发类型：Cron触发<br>调度机器：**********3<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-05-09 00:30:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);
INSERT INTO `xxl_job_log` VALUES (1935, 3, 6, 'http://***********:9999/', 'psgJobHandler', '', NULL, 0, '2024-05-09 04:00:00', 200, '任务触发类型：Cron触发<br>调度机器：**********3<br>执行器-注册方式：自动注册<br>执行器-地址列表：[http://***********:9999/]<br>路由策略：随机<br>阻塞处理策略：单机串行<br>任务超时时间：0<br>失败重试次数：0<br><br><span style=\"color:#00c0ef;\" > >>>>>>>>>>>触发调度<<<<<<<<<<< </span><br>触发调度：<br>address：http://***********:9999/<br>code：200<br>msg：null', '2024-05-09 04:00:00', 500, '任务已执行过，不进行重调，需要重调请清理航班旅客数据或清空job_execute记录', 1);

-- ----------------------------
-- Table structure for xxl_job_log_report
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_log_report`;
CREATE TABLE `xxl_job_log_report`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `trigger_day` datetime NULL DEFAULT NULL COMMENT '调度-时间',
  `running_count` int(11) NOT NULL DEFAULT 0 COMMENT '运行中-日志数量',
  `suc_count` int(11) NOT NULL DEFAULT 0 COMMENT '执行成功-日志数量',
  `fail_count` int(11) NOT NULL DEFAULT 0 COMMENT '执行失败-日志数量',
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `i_trigger_day`(`trigger_day`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 829 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_log_report
-- ----------------------------
INSERT INTO `xxl_job_log_report` VALUES (651, '2023-11-16 00:00:00', 0, 3, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (652, '2023-11-15 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (653, '2023-11-14 00:00:00', 0, 0, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (654, '2023-11-17 00:00:00', 0, 5, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (655, '2023-11-18 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (656, '2023-11-19 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (657, '2023-11-20 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (658, '2023-11-21 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (659, '2023-11-22 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (660, '2023-11-23 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (661, '2023-11-24 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (662, '2023-11-25 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (663, '2023-11-26 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (664, '2023-11-27 00:00:00', 0, 5, 4, NULL);
INSERT INTO `xxl_job_log_report` VALUES (665, '2023-11-28 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (666, '2023-11-29 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (667, '2023-11-30 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (668, '2023-12-01 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (669, '2023-12-02 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (670, '2023-12-03 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (671, '2023-12-04 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (672, '2023-12-05 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (673, '2023-12-06 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (674, '2023-12-07 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (675, '2023-12-08 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (676, '2023-12-09 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (677, '2023-12-10 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (678, '2023-12-11 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (679, '2023-12-12 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (680, '2023-12-13 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (681, '2023-12-14 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (682, '2023-12-15 00:00:00', 0, 5, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (683, '2023-12-16 00:00:00', 0, 5, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (684, '2023-12-17 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (685, '2023-12-18 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (686, '2023-12-19 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (687, '2023-12-20 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (688, '2023-12-21 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (689, '2023-12-22 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (690, '2023-12-23 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (691, '2023-12-24 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (692, '2023-12-25 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (693, '2023-12-26 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (694, '2023-12-27 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (695, '2023-12-28 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (696, '2023-12-29 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (697, '2023-12-30 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (698, '2023-12-31 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (699, '2024-01-01 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (700, '2024-01-02 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (701, '2024-01-03 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (702, '2024-01-04 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (703, '2024-01-05 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (704, '2024-01-06 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (705, '2024-01-07 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (706, '2024-01-08 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (707, '2024-01-09 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (708, '2024-01-10 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (709, '2024-01-11 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (710, '2024-01-12 00:00:00', 0, 1, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (711, '2024-01-13 00:00:00', 0, 3, 0, NULL);
INSERT INTO `xxl_job_log_report` VALUES (712, '2024-01-14 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (713, '2024-01-15 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (714, '2024-01-16 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (715, '2024-01-17 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (716, '2024-01-18 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (717, '2024-01-19 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (718, '2024-01-20 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (719, '2024-01-21 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (720, '2024-01-22 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (721, '2024-01-23 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (722, '2024-01-24 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (723, '2024-01-25 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (724, '2024-01-26 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (725, '2024-01-27 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (726, '2024-01-28 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (727, '2024-01-29 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (728, '2024-01-30 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (729, '2024-01-31 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (730, '2024-02-01 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (731, '2024-02-02 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (732, '2024-02-03 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (733, '2024-02-04 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (734, '2024-02-05 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (735, '2024-02-06 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (736, '2024-02-07 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (737, '2024-02-08 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (738, '2024-02-09 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (739, '2024-02-10 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (740, '2024-02-11 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (741, '2024-02-12 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (742, '2024-02-13 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (743, '2024-02-14 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (744, '2024-02-15 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (745, '2024-02-16 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (746, '2024-02-17 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (747, '2024-02-18 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (748, '2024-02-19 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (749, '2024-02-20 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (750, '2024-02-21 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (751, '2024-02-22 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (752, '2024-02-23 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (753, '2024-02-24 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (754, '2024-02-25 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (755, '2024-02-26 00:00:00', 0, 2, 3, NULL);
INSERT INTO `xxl_job_log_report` VALUES (756, '2024-02-27 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (757, '2024-02-28 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (758, '2024-02-29 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (759, '2024-03-01 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (760, '2024-03-02 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (761, '2024-03-03 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (762, '2024-03-04 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (763, '2024-03-05 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (764, '2024-03-06 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (765, '2024-03-07 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (766, '2024-03-08 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (767, '2024-03-09 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (768, '2024-03-10 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (769, '2024-03-11 00:00:00', 0, 4, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (770, '2024-03-12 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (771, '2024-03-13 00:00:00', 0, 5, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (772, '2024-03-14 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (773, '2024-03-15 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (774, '2024-03-16 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (775, '2024-03-17 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (776, '2024-03-18 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (777, '2024-03-19 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (778, '2024-03-20 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (779, '2024-03-21 00:00:00', 0, 5, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (780, '2024-03-22 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (781, '2024-03-23 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (782, '2024-03-24 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (783, '2024-03-25 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (784, '2024-03-26 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (785, '2024-03-27 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (786, '2024-03-28 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (787, '2024-03-29 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (788, '2024-03-30 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (789, '2024-03-31 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (790, '2024-04-01 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (791, '2024-04-02 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (792, '2024-04-03 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (793, '2024-04-04 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (794, '2024-04-05 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (795, '2024-04-06 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (796, '2024-04-07 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (797, '2024-04-08 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (798, '2024-04-09 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (799, '2024-04-10 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (800, '2024-04-11 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (801, '2024-04-12 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (802, '2024-04-13 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (803, '2024-04-14 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (804, '2024-04-15 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (805, '2024-04-16 00:00:00', 0, 5, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (806, '2024-04-17 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (807, '2024-04-18 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (808, '2024-04-19 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (809, '2024-04-20 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (810, '2024-04-21 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (811, '2024-04-22 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (812, '2024-04-23 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (813, '2024-04-24 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (814, '2024-04-25 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (815, '2024-04-26 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (816, '2024-04-27 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (817, '2024-04-28 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (818, '2024-04-29 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (819, '2024-04-30 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (820, '2024-05-01 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (821, '2024-05-02 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (822, '2024-05-03 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (823, '2024-05-04 00:00:00', 0, 4, 1, NULL);
INSERT INTO `xxl_job_log_report` VALUES (824, '2024-05-05 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (825, '2024-05-06 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (826, '2024-05-07 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (827, '2024-05-08 00:00:00', 0, 3, 2, NULL);
INSERT INTO `xxl_job_log_report` VALUES (828, '2024-05-09 00:00:00', 0, 0, 2, NULL);

-- ----------------------------
-- Table structure for xxl_job_logglue
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_logglue`;
CREATE TABLE `xxl_job_logglue`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `job_id` int(11) NOT NULL COMMENT '任务，主键ID',
  `glue_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'GLUE类型',
  `glue_source` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'GLUE源代码',
  `glue_remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'GLUE备注',
  `add_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_logglue
-- ----------------------------

-- ----------------------------
-- Table structure for xxl_job_registry
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_registry`;
CREATE TABLE `xxl_job_registry`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `registry_group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `registry_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `registry_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `i_g_k_v`(`registry_group`, `registry_key`, `registry_value`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1455 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_registry
-- ----------------------------
INSERT INTO `xxl_job_registry` VALUES (1454, 'EXECUTOR', 'sts-job-executor', 'http://***********:9999/', '2024-05-09 09:20:46');

-- ----------------------------
-- Table structure for xxl_job_user
-- ----------------------------
DROP TABLE IF EXISTS `xxl_job_user`;
CREATE TABLE `xxl_job_user`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账号',
  `password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密码',
  `role` tinyint(4) NOT NULL COMMENT '角色：0-普通用户、1-管理员',
  `permission` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限：执行器ID列表，多个逗号分割',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `i_username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of xxl_job_user
-- ----------------------------
INSERT INTO `xxl_job_user` VALUES (1, 'admin', 'db88f6de587d4599c8c0726e1048ae4d', 1, NULL);

SET FOREIGN_KEY_CHECKS = 1;
