package com.swcares.component;

import javax.swing.*;
import java.awt.*;

/**
 * description：PrintButton <br>
 *
 * <AUTHOR> <br>
 * date 2023/06/30 <br>
 * @version v1.0 <br>
 */
public class PrintButton {

    Font font = new Font("宋体", Font.PLAIN, 13);

    public JButton createButton1(JFrame frame) {
        JButton button1 = new JButton("创建行程单（F5）");
        button1.setBounds(15, 215, 140, 25);
        button1.setFont(font);

        frame.add(button1);
        return button1;
    }

    public JButton createButton2(JFrame frame) {
        JButton button2 = new JButton("打印行程单（F6）");
        button2.setBounds(15, 240, 140, 25);
        button2.setFont(font);

        frame.add(button2);

        return button2;
    }
}
