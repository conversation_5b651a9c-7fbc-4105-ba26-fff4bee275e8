<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>STS</artifactId>
        <groupId>com.swcares</groupId>
        <version>4.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>mnjx_data</artifactId>
    <packaging>jar</packaging>


    <dependencies>
        <dependency>
            <groupId>com.swcares</groupId>
            <artifactId>mnjx_core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-freemarker</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>