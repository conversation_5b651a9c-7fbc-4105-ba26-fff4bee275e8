package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.BeanUtils;
import com.swcares.core.util.CacheKeyConstant;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.MnjxAirport;
import com.swcares.entity.MnjxCity;
import com.swcares.mapper.DepartureFlightDynamicsMapper;
import com.swcares.mapper.GateScreenMapper;
import com.swcares.obj.dto.GateScreenDto;
import com.swcares.obj.vo.DepartureFlightVo;
import com.swcares.obj.vo.GateScreenVo;
import com.swcares.obj.vo.WeatherVo;
import com.swcares.service.IGateScreenService;
import com.swcares.service.IMnjxAirportService;
import com.swcares.service.IMnjxCityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by yaodan
 * 2022-12-12 14:22:44
 */
@Service
@Slf4j
public class GateScreenServiceImpl implements IGateScreenService {

    private static final Pattern GATE_PATTERN = Pattern.compile("\\d+");

    @Resource
    private GateScreenMapper gateScreenMapper;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private CacheManager cacheManager;

    @Resource
    private DepartureFlightDynamicsMapper departureFlightDynamicsMapper;

    @Override
    public List<GateScreenVo> retrieveFlightByGate(String gate) throws UnifiedResultException {
        if (StrUtil.isEmpty(gate) || !ReUtil.isMatch(GATE_PATTERN, gate)) {
            throw new UnifiedResultException("登机口输入错误");
        }

        // 获取当天SHA出发的航班计划id
        List<String> planFlightIds = departureFlightDynamicsMapper.retrievePlanFlightIdList(DateUtils.today());
        if (CollUtil.isEmpty(planFlightIds)) {
            throw new UnifiedResultException("该登机口当天无计划航班");
        }

        // 获取当天SHA出发的航班计划列表
        List<DepartureFlightVo> departureFlightVos = departureFlightDynamicsMapper.retrieveDepartureFlightDynamics(planFlightIds);
        if (CollUtil.isEmpty(departureFlightVos)) {
            throw new UnifiedResultException("该登机口当天无计划航班");
        }

        // 筛选条件的登机口
        List<DepartureFlightVo> filterGateList = departureFlightVos.stream()
                .filter(d -> gate.equals(d.getGate()))
                .sorted(Comparator.comparing(DepartureFlightVo::getEstimateOff))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(filterGateList)) {
            throw new UnifiedResultException("该登机口当天无计划航班");
        }
        // 获取符合登机口的航班号
        List<String> filterGateFlightNoList = filterGateList.stream()
                .map(DepartureFlightVo::getFlightNo)
                .collect(Collectors.toList());
        // 再重新筛选符合航班号的航班计划列表（涉及到经停航班，需要用航班号筛选，不能直接用登机口号筛选）
        // 记录已过期的航班，用于排除后续有经停的部分
        List<String> timeoutFlightNoList = new ArrayList<>();
        departureFlightVos = departureFlightVos.stream()
                .filter(d -> filterGateFlightNoList.contains(d.getFlightNo()))
                .filter(d -> {
                    if (timeoutFlightNoList.contains(d.getFlightNo())) {
                        return false;
                    }
                    String flightDate = d.getFlightDate();
                    String estimateOff = d.getEstimateOff();
                    String actualOff = d.getActualOff();
                    String estimateOffTime = StrUtil.format("{} {}", flightDate, DateUtils.comHm2hmis(estimateOff));
                    if (StrUtil.isNotEmpty(actualOff)) {
                        String actualOffTime = StrUtil.format("{} {}", flightDate, DateUtils.comHm2hmis(actualOff));
                        if (DateUtils.compare(DateUtils.ymdhms2Date(actualOffTime), DateUtils.date()) > 0) {
                            return true;
                        } else {
                            timeoutFlightNoList.add(d.getFlightNo());
                            return false;
                        }
                    } else {
                        if (DateUtils.compare(DateUtils.ymdhms2Date(estimateOffTime), DateUtils.date()) > 0) {
                            return true;
                        } else {
                            timeoutFlightNoList.add(d.getFlightNo());
                            return false;
                        }
                    }
                }).collect(Collectors.toList());
        if (CollUtil.isEmpty(departureFlightVos)) {
            throw new UnifiedResultException("该登机口当天无计划航班");
        }
        // 拷贝到当前模块使用的对象
        List<GateScreenDto> gateScreenDtos = new ArrayList<>();
        departureFlightVos.stream().forEach(d -> {
            GateScreenDto gateScreenDto = new GateScreenDto();
            BeanUtils.copyProperties(d, gateScreenDto);
            gateScreenDtos.add(gateScreenDto);
        });
        // 按航班号分组
        Map<String, List<GateScreenDto>> listMap = gateScreenDtos.stream()
                .collect(Collectors.groupingBy(GateScreenDto::getFlightNo));
        List<MnjxAirport> mnjxAirports = iMnjxAirportService.list();
        List<MnjxCity> mnjxCities = iMnjxCityService.list();
        // 增加缓存，处理设置天气，航段状态转换处理
        List<GateScreenVo> gateScreenVos = new ArrayList<>();
        List<GateScreenVo> finalGateScreenVos = gateScreenVos;
        listMap.forEach((k, v) -> {
            GateScreenVo gateScreenVo = new GateScreenVo();
            GateScreenDto gateScreenDto = v.stream().filter(s -> Constant.STR_ONE.equals(s.getIsLastSection())).collect(Collectors.toList()).get(0);
            BeanUtils.copyProperties(gateScreenDto, gateScreenVo);
            List<String> citys = v.stream().map(f -> {
                String arrAptId = f.getArrAptId();
                MnjxAirport dstAirport = mnjxAirports.stream().filter(a -> arrAptId.equals(a.getAirportId())).collect(Collectors.toList()).get(0);
                MnjxCity dstCity = mnjxCities.stream().filter(c -> dstAirport.getCityId().equals(c.getCityId())).collect(Collectors.toList()).get(0);
                String cityCname = dstCity.getCityCname();
                f.setDst(dstCity.getCityCode());
                return StrUtil.format("{} {}", cityCname, dstCity.getCityEname());
            }).collect(Collectors.toList());
            gateScreenVo.setCitys(citys);
            // 时间格式转换
            GateScreenDto screenDto = v.get(0);
            String estimateOff = screenDto.getEstimateOff();
            String actualOff = screenDto.getActualOff();
            estimateOff = StrUtil.isNotEmpty(estimateOff) ? DateUtils.comHm2hmis(estimateOff, 1) : StrUtil.EMPTY;
            actualOff = StrUtil.isNotEmpty(actualOff) ? DateUtils.comHm2hmis(actualOff, 1) : StrUtil.EMPTY;
            gateScreenVo.setEstimateOff(estimateOff);
            gateScreenVo.setActualOff(actualOff);

            // 设置天气，先去缓存中获取，没有在随机生成
            Cache cache = cacheManager.getCache(CacheKeyConstant.CACHE_WEATHER_KEY);
            String key = StrUtil.format("{}-{}", gateScreenDto.getDst(), gateScreenDto.getFlightDate());
            WeatherVo weatherVo = new WeatherVo();
            WeatherVo cacheWeatherVo = cache != null ? cache.get(key, WeatherVo.class) : null;
            if (ObjectUtil.isEmpty(cacheWeatherVo)) {
                weatherVo = this.randomWeather();
                cache.put(key, weatherVo);
            } else {
                BeanUtils.copyProperties(cacheWeatherVo, weatherVo);
            }
            gateScreenVo.setWeatherVo(weatherVo);
            // 航段状态处理
            this.setFlightStatus(gateScreenVo, gateScreenDto);
            finalGateScreenVos.add(gateScreenVo);
        });
        // 按照实际起飞 和预计起飞时间排序
        gateScreenVos = gateScreenVos.stream()
                .sorted(Comparator.comparing(GateScreenVo::getEstimateOff, Comparator.nullsFirst(String::compareTo))
                        .thenComparing(g -> StrUtil.isNotEmpty(g.getActualOff()) ? g.getActualOff() : StrUtil.EMPTY, Comparator.nullsLast(String::compareTo)))
                .collect(Collectors.toList());
        return gateScreenVos;
    }

    /**
     * 设置航班状态
     */
    private void setFlightStatus(GateScreenVo gateScreenVo, GateScreenDto gateScreenDto) {
        String flightDate = gateScreenDto.getFlightDate();
        String flightStatus = gateScreenDto.getFlightStatus();
        String estimateOff = gateScreenDto.getEstimateOff();
        String actualOff = gateScreenDto.getActualOff();
        estimateOff = StrUtil.format("{} {}", flightDate, DateUtils.comHm2hmis(estimateOff));
        Date estimateOffTime = DateUtils.ymdhms2Date(estimateOff);
        Date nowDateTime = DateUtils.date();
        Date fltDateTime;
        if (StrUtil.equalsAny(flightStatus, Constant.CK_STATUS_CI, Constant.CK_STATUS_CL, Constant.CK_STATUS_CC)) {
            // 取消 Cancelled 航班被取消
            gateScreenVo.setFlightStatusCname("取消");
            gateScreenVo.setFlightStatusEname("Cancelled");
        } else {
            // 延误 Delay 航班无法按计划时间执飞，计划与预计时间相差30分钟及上的
            if (StrUtil.isNotEmpty(actualOff)) {
                actualOff = StrUtil.format("{} {}", flightDate, DateUtils.comHm2hmis(actualOff));
                Date actualOffTime = DateUtils.ymdhms2Date(actualOff);
                if (DateUtils.compare(estimateOffTime, DateUtils.offsetMinute(actualOffTime, -30)) <= 0 && DateUtils.compare(nowDateTime, DateUtils.offsetHour(actualOffTime, -2)) <= 0) {
                    gateScreenVo.setFlightStatusCname("延误");
                    gateScreenVo.setFlightStatusEname("Delay");
                    return;
                } else {
                    fltDateTime = actualOffTime;
                }
            } else {
                // 航班起飞前两小时，显示正常
                if (DateUtils.compare(nowDateTime, DateUtils.offsetHour(estimateOffTime, -2)) < 0) {
                    gateScreenVo.setFlightStatusCname("正常");
                    gateScreenVo.setFlightStatusEname("On time");
                    return;
                } else {
                    fltDateTime = estimateOffTime;
                }
            }
            if (DateUtils.compare(nowDateTime, DateUtils.offsetHour(fltDateTime, -2)) >= 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -35)) <= 0) {
                // 正在值机，航班起飞前两小时内到起飞前35分钟
                gateScreenVo.setFlightStatusCname("正在值机");
                gateScreenVo.setFlightStatusEname("Checking");
            } else if (DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -35)) > 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -30)) <= 0) {
                // 准备登机 航班起飞前35分钟到起飞前30分钟
                gateScreenVo.setFlightStatusCname("准备登机");
                gateScreenVo.setFlightStatusEname("Board Soon");
            } else if (DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -30)) > 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -20)) <= 0) {
                // 正在登机 Boarding 航班起飞前30分钟到起飞前20分钟
                gateScreenVo.setFlightStatusCname("正在登机");
                gateScreenVo.setFlightStatusEname("Boarding");
            } else if (DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -20)) > 0 && DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -15)) <= 0) {
                // 催促登机 Final Call 航班起飞前20分钟到起飞前15分钟
                gateScreenVo.setFlightStatusCname("催促登机");
                gateScreenVo.setFlightStatusEname("Final Call");
            } else if (DateUtils.compare(nowDateTime, DateUtils.offsetMinute(fltDateTime, -15)) > 0 || DateUtils.compare(nowDateTime, fltDateTime) >= 0) {
                // 结束登机 Gate Closed 航班起飞前15分钟到航班起飞时，显示为结束登机
                gateScreenVo.setFlightStatusCname("结束登机");
                gateScreenVo.setFlightStatusEname("Gate Closed");
            }
        }
    }

    /**
     * 随机生成天气
     */
    private WeatherVo randomWeather() {
        WeatherVo weatherVo = new WeatherVo();
        //天气状态
        String[] weathers = Constant.WEATHER;
        String weather = weathers[new Random().nextInt(weathers.length)];
        String[] weatherTemp = weather.split(StrUtil.SPACE);
        weatherVo.setWeatherCname(weatherTemp[0]);
        weatherVo.setWeatherEname(weatherTemp[1]);
        //温度
        int temperature = RandomUtil.randomInt(-2, 31);
        weatherVo.setTemperature(StrUtil.toString(temperature));
        //PM
        int pmValue = RandomUtil.randomInt(28, 129);
        weatherVo.setPmValue(StrUtil.toString(pmValue));
        //风力
        String[] windPowers = Constant.WIND_POWER;
        String windPower = windPowers[new Random().nextInt(windPowers.length)];
        if ("--".equals(windPower)) {
            weatherVo.setWindPower(windPower);
            weatherVo.setWindDirectionCname("无持续风向");
            weatherVo.setWindDirectionEname("No sustained");
        } else {
            weatherVo.setWindPower(windPower);
            String[] windDirections = Constant.WIND_DIRECTION;
            String windDirection = windDirections[new Random().nextInt(windDirections.length)];
            String[] windDirectionTemp = windDirection.split(StrUtil.SPACE);
            weatherVo.setWindDirectionCname(windDirectionTemp[0]);
            weatherVo.setWindDirectionEname(windDirectionTemp[1]);
        }
        return weatherVo;
    }
}
