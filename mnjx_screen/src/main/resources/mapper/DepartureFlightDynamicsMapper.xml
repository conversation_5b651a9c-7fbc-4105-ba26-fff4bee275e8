<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.mapper.DepartureFlightDynamicsMapper">

    <select id="retrievePlanFlightId" resultType="com.swcares.obj.vo.DepartureFlightVo">
        SELECT
            mpf.plan_flight_id
        FROM
            mnjx_plan_flight mpf
            LEFT JOIN mnjx_plan_section mps ON mpf.plan_flight_id = mps.plan_flight_id
            LEFT JOIN mnjx_tcard mt ON mpf.tcard_id = mt.tcard_id
            LEFT JOIN mnjx_tcard_section mts ON mt.tcard_id = mts.tcard_id
            LEFT JOIN mnjx_airport ma ON mts.airport_id = ma.airport_id
            LEFT JOIN mnjx_flight mf ON mt.flight_id = mf.flight_id
        WHERE
            1 = 1
            <if test="departureFlightDynamicsReqVo.flightDate != null and departureFlightDynamicsReqVo.flightDate != ''">
                AND mpf.flight_date = #{departureFlightDynamicsReqVo.flightDate}
            </if>
            <if test="departureFlightDynamicsReqVo.org != null and departureFlightDynamicsReqVo.org != ''">
                AND mps.dep_apt_id = #{departureFlightDynamicsReqVo.org}
            </if>
            <if test="departureFlightDynamicsReqVo.flightNo != null and departureFlightDynamicsReqVo.flightNo != ''">
                AND mf.flight_no = #{departureFlightDynamicsReqVo.flightNo}
            </if>
        GROUP BY
            mpf.plan_flight_id
        ORDER BY
            mps.estimate_off asc, mps.actual_off, mf.flight_no
    </select>

    <select id="retrieveDepartureFlightDynamics" resultType="com.swcares.obj.vo.DepartureFlightVo">
        SELECT
            mpf.plan_flight_id,
            mf.flight_no,
            mpf.flight_date,
            mpf.airline_code,
            mpf.ck_status as flightStatus,
            mps.gate,
            mps.is_last_section,
            mps.arr_apt_id,
            mps.estimate_off,
            mps.estimate_off_change,
            mps.actual_off,
            mps.actual_boarding,
            mps.actual_takeoff
        FROM
            mnjx_plan_flight mpf
            LEFT JOIN mnjx_plan_section mps ON mpf.plan_flight_id = mps.plan_flight_id
            LEFT JOIN mnjx_airport ma ON mps.dep_apt_id = ma.airport_id
            LEFT JOIN mnjx_tcard mt ON mpf.tcard_id = mt.tcard_id
            LEFT JOIN mnjx_flight mf ON mt.flight_id = mf.flight_id
        WHERE
            1 = 1
            <if test="planFlightIds != null and planFlightIds.size > 0">
                and mpf.plan_flight_id IN
                <foreach collection="planFlightIds" index="index" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
    </select>

    <select id="retrieveCity" resultType="com.swcares.obj.vo.CictVo">
        SELECT
            ma.airport_id,
            mc.city_cname,
            mc.city_ename,
            ma.airport_code
        FROM
            mnjx_airport ma
            LEFT JOIN mnjx_city mc ON ma.city_id = mc.city_id
    </select>

    <select id="retrievePlanFlightIdList" resultType="java.lang.String">
        SELECT
        mpf.plan_flight_id
        FROM
        mnjx_plan_flight mpf
        LEFT JOIN mnjx_plan_section mps ON mpf.plan_flight_id = mps.plan_flight_id
        LEFT JOIN mnjx_tcard mt ON mpf.tcard_id = mt.tcard_id
        LEFT JOIN mnjx_tcard_section mts ON mt.tcard_id = mts.tcard_id
        LEFT JOIN mnjx_airport ma ON mts.airport_id = ma.airport_id
        LEFT JOIN mnjx_flight mf ON mt.flight_id = mf.flight_id
        WHERE
            1 = 1
            AND mpf.flight_date = #{flightDate}
            AND mps.dep_apt_id = (select airport_id from mnjx_airport where airport_code = 'SHA')
        GROUP BY
        mpf.plan_flight_id
        ORDER BY
        mps.estimate_off asc, mps.actual_off, mf.flight_no
    </select>
</mapper>
