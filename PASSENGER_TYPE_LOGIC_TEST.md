# 旅客类型判断逻辑测试

## 旅客类型判断规则

### 优先级顺序
1. **CHD（儿童）**: 查询mnjx_ssr表，ssrType为"CHLD"
2. **GMJC（革命军人家属）**: 查询mnjx_nm_rmk表，rmkName为"GMJC"
3. **ADT（成人）**: 默认类型

### 判断逻辑
```java
private String determinePassengerType(String pnrNmId) {
    // 1. 首先检查是否为儿童
    MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
            .eq(MnjxNmSsr::getPnrNmId, pnrNmId)
            .eq(MnjxNmSsr::getSsrType, "CHLD")
            .one();
    
    if (chldSsr != null) {
        return "CHD";
    }

    // 2. 然后检查是否为革命军人家属
    MnjxNmRmk gmjcRmk = iMnjxNmRmkService.lambdaQuery()
            .eq(MnjxNmRmk::getPnrNmId, pnrNmId)
            .eq(MnjxNmRmk::getRmkName, "GMJC")
            .one();
    
    if (gmjcRmk != null) {
        return "GMJC";
    }

    // 3. 默认为成人
    return "ADT";
}
```

## 测试用例

### 测试用例1：成人旅客
**数据准备**:
- mnjx_pnr_nm表有记录，pnrNmId = "NM001"
- mnjx_ssr表无CHLD类型记录
- mnjx_nm_rmk表无GMJC类型记录

**预期结果**: passengerType = "ADT"

### 测试用例2：儿童旅客
**数据准备**:
- mnjx_pnr_nm表有记录，pnrNmId = "NM002"
- mnjx_ssr表有记录：pnrNmId = "NM002", ssrType = "CHLD"
- mnjx_nm_rmk表无GMJC类型记录

**预期结果**: passengerType = "CHD"

### 测试用例3：革命军人家属
**数据准备**:
- mnjx_pnr_nm表有记录，pnrNmId = "NM003"
- mnjx_ssr表无CHLD类型记录
- mnjx_nm_rmk表有记录：pnrNmId = "NM003", rmkName = "GMJC"

**预期结果**: passengerType = "GMJC"

### 测试用例4：儿童优先级测试
**数据准备**:
- mnjx_pnr_nm表有记录，pnrNmId = "NM004"
- mnjx_ssr表有记录：pnrNmId = "NM004", ssrType = "CHLD"
- mnjx_nm_rmk表有记录：pnrNmId = "NM004", rmkName = "GMJC"

**预期结果**: passengerType = "CHD" (儿童优先级更高)

### 测试用例5：异常情况
**数据准备**:
- pnrNmId为null或空字符串

**预期结果**: passengerType = "ADT" (异常情况下返回默认值)

## 数据库表结构

### mnjx_ssr表关键字段
```sql
CREATE TABLE mnjx_ssr (
    nm_ssr_id VARCHAR(50) PRIMARY KEY,
    pnr_nm_id VARCHAR(50),
    ssr_type VARCHAR(10),
    -- 其他字段...
);
```

### mnjx_nm_rmk表关键字段
```sql
CREATE TABLE mnjx_nm_rmk (
    nm_rmk_id VARCHAR(50) PRIMARY KEY,
    pnr_nm_id VARCHAR(50),
    rmk_name VARCHAR(20),
    rmk_info TEXT,
    -- 其他字段...
);
```

## 响应示例

### 成人旅客响应
```json
{
  "etNumber": "781-355****681",
  "passengerName": "张三",
  "passengerType": "ADT",
  "airSeg": [...]
}
```

### 儿童旅客响应
```json
{
  "etNumber": "781-355****682",
  "passengerName": "李小明",
  "passengerType": "CHD",
  "airSeg": [...]
}
```

### 革命军人家属响应
```json
{
  "etNumber": "781-355****683",
  "passengerName": "王军属",
  "passengerType": "GMJC",
  "airSeg": [...]
}
```

## 性能考虑

### 查询优化
1. **索引建议**:
   - mnjx_ssr表：在(pnr_nm_id, ssr_type)上建立复合索引
   - mnjx_nm_rmk表：在(pnr_nm_id, rmk_name)上建立复合索引

2. **查询顺序**:
   - 先查询CHLD类型的SSR（儿童相对较少）
   - 再查询GMJC类型的RMK（革命军人家属相对较少）
   - 大部分情况下返回默认的ADT类型

### 缓存策略
- 可以考虑对旅客类型进行短期缓存，减少重复查询
- 缓存键：pnrNmId
- 缓存时间：5-10分钟（考虑到PNR可能会有变更）

## 错误处理

### 异常情况处理
1. **数据库连接异常**: 返回默认ADT类型
2. **参数为空**: 返回默认ADT类型
3. **查询超时**: 返回默认ADT类型，记录日志

### 日志记录
```java
log.error("确定旅客类型异常，pnrNmId: {}", pnrNmId, e);
```

## 业务规则说明

### CHLD（儿童）
- 年龄通常在2-12岁之间
- 需要成人陪同
- 票价通常有折扣

### GMJC（革命军人家属）
- 特殊旅客类型
- 可能享受特殊优惠政策
- 需要相关证明文件

### ADT（成人）
- 默认旅客类型
- 年龄通常在12岁以上
- 标准票价
