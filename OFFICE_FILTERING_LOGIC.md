# Office筛选逻辑详解

## 业务背景

在按姓名查询客票接口中，需要确保只返回当前登录用户所属OFFICE出具的票据，避免跨OFFICE数据泄露。

## 数据关联链路

### 关联表结构
```
mnjx_pnr_nm_tn (出票记录) 
    ↓ issuedSiId
mnjx_si (工作号表)
    ↓ office_id  
mnjx_office (Office表)
    ↓ officeNo
当前登录用户的officeNo
```

### 字段说明

#### mnjx_pnr_nm_tn表
- `issuedSiId`: 出票时使用的工作号ID

#### mnjx_si表  
- `si_id`: 工作号主键ID
- `si_no`: 工作号编号
- `office_id`: 工作号所属的Office ID

#### mnjx_office表
- `office_id`: Office主键ID
- `office_no`: Office编号

## 筛选逻辑实现

### 核心方法
```java
private boolean isCurrentOfficeTicket(String issuedSiId, String currentOfficeNo) {
    try {
        // 1. 参数校验
        if (CharSequenceUtil.isEmpty(issuedSiId) || CharSequenceUtil.isEmpty(currentOfficeNo)) {
            return false;
        }

        // 2. 通过issuedSiId查询mnjx_si表，获取office_id
        MnjxSi si = iMnjxSiService.getById(issuedSiId);
        if (si == null || CharSequenceUtil.isEmpty(si.getOfficeId())) {
            return false;
        }

        // 3. 通过office_id查询mnjx_office表，获取officeNo
        MnjxOffice ticketOffice = iMnjxOfficeService.getById(si.getOfficeId());
        if (ticketOffice == null || CharSequenceUtil.isEmpty(ticketOffice.getOfficeNo())) {
            return false;
        }

        // 4. 与当前登录用户的officeNo进行对比
        return currentOfficeNo.equals(ticketOffice.getOfficeNo());

    } catch (Exception e) {
        log.error("检查OFFICE票据归属异常，issuedSiId: {}, currentOfficeNo: {}", issuedSiId, currentOfficeNo, e);
        return false; // 异常情况下返回false，不包含该票据
    }
}
```

### 调用位置
在筛选出票记录时使用：
```java
List<MnjxPnrNmTn> filteredTnList = tnList.stream()
    .filter(tn -> {
        // 时间范围检查
        boolean timeMatch = /* 时间筛选逻辑 */;
        
        // Office归属检查
        boolean officeMatch = this.isCurrentOfficeTicket(tn.getIssuedSiId(), office.getOfficeNo());
        
        return timeMatch && officeMatch;
    })
    .collect(Collectors.toList());
```

## 测试用例

### 测试用例1：同一Office出票
**数据准备**:
```sql
-- 当前登录用户Office
INSERT INTO mnjx_office (office_id, office_no) VALUES ('OFF001', 'SHA001');

-- 工作号
INSERT INTO mnjx_si (si_id, si_no, office_id) VALUES ('SI001', 'SHA001SI01', 'OFF001');

-- 出票记录
INSERT INTO mnjx_pnr_nm_tn (tn_id, issued_si_id) VALUES ('TN001', 'SI001');
```
**预期结果**: `isCurrentOfficeTicket('SI001', 'SHA001')` 返回 `true`

### 测试用例2：不同Office出票
**数据准备**:
```sql
-- 当前登录用户Office
当前用户officeNo = 'SHA001'

-- 其他Office的工作号
INSERT INTO mnjx_office (office_id, office_no) VALUES ('OFF002', 'PEK001');
INSERT INTO mnjx_si (si_id, si_no, office_id) VALUES ('SI002', 'PEK001SI01', 'OFF002');

-- 出票记录
INSERT INTO mnjx_pnr_nm_tn (tn_id, issued_si_id) VALUES ('TN002', 'SI002');
```
**预期结果**: `isCurrentOfficeTicket('SI002', 'SHA001')` 返回 `false`

### 测试用例3：数据缺失情况
**场景1**: issuedSiId为空
```java
isCurrentOfficeTicket(null, 'SHA001') -> false
isCurrentOfficeTicket('', 'SHA001') -> false
```

**场景2**: 工作号不存在
```java
isCurrentOfficeTicket('NONEXISTENT', 'SHA001') -> false
```

**场景3**: Office不存在
```sql
INSERT INTO mnjx_si (si_id, office_id) VALUES ('SI003', 'NONEXISTENT_OFFICE');
```
```java
isCurrentOfficeTicket('SI003', 'SHA001') -> false
```

## 性能优化

### 数据库索引建议
```sql
-- mnjx_si表索引
CREATE INDEX idx_mnjx_si_office_id ON mnjx_si(office_id);

-- mnjx_office表索引  
CREATE INDEX idx_mnjx_office_office_no ON mnjx_office(office_no);

-- mnjx_pnr_nm_tn表索引
CREATE INDEX idx_mnjx_pnr_nm_tn_issued_si_id ON mnjx_pnr_nm_tn(issued_si_id);
```

### 缓存策略
考虑对以下数据进行缓存：
1. **工作号到Office的映射**: `si_id -> office_no`
2. **Office信息**: `office_id -> MnjxOffice`

缓存实现示例：
```java
@Cacheable(value = "office-mapping", key = "#siId")
public String getOfficeNoBysiId(String siId) {
    // 实现逻辑
}
```

### 批量查询优化
如果需要处理大量出票记录，可以考虑批量查询：
```java
// 批量获取工作号信息
List<String> siIds = tnList.stream().map(MnjxPnrNmTn::getIssuedSiId).collect(Collectors.toList());
List<MnjxSi> siList = iMnjxSiService.listByIds(siIds);

// 批量获取Office信息
List<String> officeIds = siList.stream().map(MnjxSi::getOfficeId).collect(Collectors.toList());
List<MnjxOffice> officeList = iMnjxOfficeService.listByIds(officeIds);
```

## 安全考虑

### 数据隔离
- 确保每个Office只能查看自己出具的票据
- 防止通过修改请求参数绕过Office限制
- 记录跨Office访问尝试的审计日志

### 异常处理
- 数据库连接异常时，默认拒绝访问（返回false）
- 数据不一致时，记录错误日志并拒绝访问
- 避免在异常信息中泄露敏感数据

### 日志记录
```java
// 记录Office筛选结果
log.debug("Office筛选结果: issuedSiId={}, currentOfficeNo={}, result={}", 
    issuedSiId, currentOfficeNo, result);

// 记录异常情况
log.error("检查OFFICE票据归属异常，issuedSiId: {}, currentOfficeNo: {}", 
    issuedSiId, currentOfficeNo, e);
```

## 业务场景

### 典型使用场景
1. **代理人查询**: 代理人只能查看自己Office出具的票据
2. **机场查询**: 机场Office只能查看本机场出具的票据  
3. **航空公司查询**: 航空公司只能查看自己出具的票据

### 特殊情况处理
1. **工作号变更**: 如果工作号所属Office发生变更，需要考虑历史数据的处理
2. **Office合并**: Office合并时需要更新相关映射关系
3. **数据迁移**: 系统升级时需要确保Office关联关系的正确性

## 错误码定义

| 错误情况 | 返回值 | 日志级别 | 说明 |
|---------|--------|----------|------|
| 参数为空 | false | DEBUG | 正常业务逻辑 |
| 工作号不存在 | false | WARN | 可能的数据不一致 |
| Office不存在 | false | WARN | 可能的数据不一致 |
| 数据库异常 | false | ERROR | 系统异常 |
| Office不匹配 | false | DEBUG | 正常业务逻辑 |

## 监控指标

### 关键指标
1. **Office筛选成功率**: 成功匹配的比例
2. **数据不一致率**: 工作号或Office不存在的比例
3. **查询响应时间**: Office筛选的平均耗时
4. **异常率**: 筛选过程中的异常比例

### 告警规则
- 数据不一致率超过5%时告警
- 查询响应时间超过100ms时告警
- 异常率超过1%时告警
