import{a9 as T,w as d,ca as n,aX as p,R as i,b5 as m}from"./index-18f146fc.js";const O=()=>{const l=T(),c=d(()=>l.state.user.userName),s=()=>n.config({driver:n.INDEXEDDB,name:"index_db_util",storeName:"airport"});return{saveCrsTemporaryOrder:async a=>{await s();const e=await p("TableData_CRS_Temporary_Order"),r=e?JSON.parse(e.localData??""):[],o={orderNo:"",pnr:a.pnrNo,operationType:a.operationType,operationTime:i().format("YYYY-MM-DD HH:mm:ss"),operationPeople:c.value,passenger:[...a.passengers,...(a.infants??[]).filter(t=>t.name!=="")],itinerary:a.segments.map(t=>`${t.departureAirport}-${t.arrivalAirport}`)};r.unshift(o),await m("TableData_CRS_Temporary_Order",JSON.stringify(r),new Date().getTime())},deleteCrsTemporaryOrder:async()=>{await s();const a=await p("TableData_CRS_Temporary_Order");if(!a)return;const e=JSON.parse(a.localData??"")??[];if(e){for(let r=e.length-1;r>=0;r--){const o=i(e[r].operationTime);i().diff(o,"day")>=1&&e.splice(r,1)}await m("TableData_CRS_Temporary_Order",JSON.stringify(e),new Date().getTime())}},initLocalForage:s}};export{O as u};
