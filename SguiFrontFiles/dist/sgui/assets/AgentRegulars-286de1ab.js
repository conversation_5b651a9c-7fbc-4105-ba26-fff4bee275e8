import{ao as de,b8 as ue,ab as ve,r as S,s as Ne,aJ as pe,w as _e,bm as je,gu as ke,ac as De,dq as ae,gp as Te,i2 as we,av as Ve,b2 as ie,q as fe,ae as Le,x as d,y as j,z as r,P as e,Q as l,a5 as R,B as g,G as n,A as s,J as Y,ai as J,aj as X,ak as se,b9 as be,ah as xe,H as Ie,al as ge,b3 as Fe,am as Ee,an as Ae,dx as Ue,dh as Re,gL as Me,aY as Se,au as Oe,E as Be}from"./index-18f146fc.js";import{C as he}from"./CustomDatePicker-0bb1d9a1.js";import{n as Ye,w as Ge,I as ze,z as He,k as Je}from"./usePassengerFormValidateUtils-37c5a449.js";import{a6 as Qe,T as Xe,s as Ze}from"./regular-crs-4d4d60ea.js";import{q as We,X as oe}from"./pnrUtils-1bb76fce.js";import{E as $e,a as qe}from"./index-c5503643.js";import{E as Ke}from"./index-93952dc4.js";import{E as et,a as tt}from"./index-d7d71e18.js";import{E as Ce}from"./index-385c3d86.js";import{_ as st}from"./_plugin-vue_export-helper-c27b6911.js";import{m as le}from"./Tip-a3c59246.js";import{E as at}from"./index-6ea30548.js";import{E as lt,a as nt}from"./index-3d51361b.js";import"./index-28cc82cf.js";import"./flatten-1142070a.js";import"./index-1c4b8a79.js";import"./index-3a8869fd.js";import"./index-c5921abf.js";import"./isEqual-9c56e106.js";import"./passengerSpecialType-f6d133eb.js";import"./dictApi-38f10700.js";import"./castArray-f685dae0.js";import"./index-2610c77e.js";import"./strings-8b86a061.js";import"./refs-649593ac.js";import"./isUndefined-aa0326a0.js";import"./index-9b639e2a.js";const rt=(p,k)=>de(`${ue}/agencyFrequent/query`,{headers:{gid:k}}).post(p).json(),ot=(p,k)=>de(`${ue}/agencyFrequent/delete`,{headers:{gid:k}}).post({csmIdentifier:p}).json(),it=(p,k)=>de(`${ue}/agencyFrequent/add`,{headers:{gid:k}}).post(p).json(),dt=(p,k)=>de(`${ue}/agencyFrequent/modify`,{headers:{gid:k}}).post(p).json();var W=(p=>(p[p.close=0]="close",p[p.add=1]="add",p[p.edit=2]="edit",p))(W||{});const ut=(p,k)=>{const{t:v}=ve(),x=()=>{const u=oe,P=We.filter(a=>!oe.some(_=>a.value===_.value));return[...u,...P]},o=[{label:v("app.intlPassengerForm.male"),value:"M"},{label:v("app.intlPassengerForm.female"),value:"F"}],w=["NI_I","UU","NI"],C="PP_",y=S(x()),E=()=>({csmIdentifier:0,chineseName:"",englishName:"",genderCode:"M",birthDate:"",mobilePhone:"",emailAddress:"",frequentFlyerNumberInfo:"",frequentFlyerLevelNumberInfo:"",documentList:[{documentTypeCode:oe[0].value,documentNumber:"",expiryDate:"",issueCountryCode:"",nationalityCode:"",issueDate:"",id:`document${ae()}`}],remarkList:[],cardList:[]}),D=S(),O=S(!1),T=S(E()),V=Ne({render(){return pe("em",{class:"iconfont icon-calendar"})}}),G=_e(()=>!!p.showAgencyFrequentFlyerDialogType),z=_e(()=>({chineseName:[{required:!0,message:v("app.intlPassengerForm.required"),trigger:["blur"]},{validator:Ye}],englishName:[{required:!0,message:v("app.intlPassengerForm.required"),trigger:["blur"]},{validator:Ge}],genderCode:[{required:!0,message:v("app.intlPassengerForm.required")}],mobilePhone:[{required:!0,message:v("app.intlPassengerForm.required")},{pattern:/^\d{1,11}$/,trigger:["change","blur"],message:v("app.intlPassengerForm.enterValidMobileFrequentFlyer")}],airlineCode:[{required:!0,message:v("app.intlPassengerForm.required")},{pattern:je,message:v("app.pnrManagement.validate.characterCode")}],frequentFlyerNumber:[{required:!0,message:v("app.intlPassengerForm.required")},{pattern:ke,message:v("app.intlPassengerForm.validate.regularError")}],documentTypeCode:[{required:!0,message:v("app.intlPassengerForm.required")}],documentNumber:[{required:!0,message:v("app.intlPassengerForm.required")},{validator:A,trigger:["blur"]}],nationalityCode:[{required:!0,message:v("app.intlPassengerForm.required")},{pattern:/^[a-zA-Z]{2,3}$/,message:v("app.intlPassengerForm.enterCharacterCode")}],issueCountryCode:[{required:!0,message:v("app.intlPassengerForm.required")},{pattern:/^[a-zA-Z]{2,3}$/,message:v("app.intlPassengerForm.enterCharacterCode")}]})),H=u=>[{required:u!=="NI_I",message:v("app.intlPassengerForm.required")}],Q=(u,P)=>u&&!Te(u)?v(P==="UU"?we.get("documentTypeTip")??"":"app.intlPassengerForm.validate.certError"):"",A=(u,P,a)=>{const q=u.field.split("."),_=parseInt(q[1]),M=T.value.documentList[_].documentTypeCode,ee=["PP_P","PP_IP","PP_I","PP_A_FRE","PP_F","PP_IN"];if(w.includes(M)){const I=Q(P,M);I&&a(I)}else if(ee.includes(M))P&&!Qe.test(P)&&a(new Error(v("app.intlPassengerForm.validate.certError"))),a();else{const I=ze(P,M);if(I.tip){const t=I.min>1?v(I.tip,{min:I.min,max:I.max}):v(I.tip,{max:I.max});a(t)}a()}a()},B=(u,P)=>{var a;T.value[u]=(a=T.value[u])==null?void 0:a.filter(q=>q.id!==P)},c=()=>{const u={documentTypeCode:oe[0].value,documentNumber:"",expiryDate:"",issueCountryCode:"",nationalityCode:"",issueDate:"",id:`document${ae()}`};T.value.documentList.push(u)},i=()=>{const u={sequenceNumber:0,remark:"",id:`remark${ae()}`};T.value.remarkList.push(u)},F=()=>{const u={airlineCode:"",frequentFlyerNumber:"",id:`card${ae()}`};T.value.cardList.push(u)},b=u=>{switch(u){case"documentList":c();break;case"remarkList":i();break;case"cardList":F();break}},f=u=>{D.value&&D.value.validateField(`documentList.${u}.documentNumber`)},N=u=>{m(),k("close",u)},L=u=>{var P;return u.includes("NI")?"NI":u.includes(C)?((P=u.split(C))==null?void 0:P[1])??"":u},U=()=>{const{csmIdentifier:u,chineseName:P,englishName:a,genderCode:q,birthDate:_,mobilePhone:M,emailAddress:ee,frequentFlyerNumberInfo:I,frequentFlyerLevelNumberInfo:t,documentList:te,remarkList:Z,cardList:ne}=T.value;return{csmIdentifier:p.showAgencyFrequentFlyerDialogType===W.edit?u:0,chineseName:P,englishName:a,genderCode:q,birthDate:_,mobilePhone:M,emailAddress:ee,frequentFlyerNumberInfo:I,frequentFlyerLevelNumberInfo:t,documentList:te.map(h=>({documentTypeCode:L(h.documentTypeCode),documentNumber:h.documentNumber,expiryDate:h.expiryDate,issueCountryCode:h.issueCountryCode,nationalityCode:h.nationalityCode,issueDate:h.issueDate})),remarkList:Z.map((h,me)=>({sequenceNumber:me,remark:h.remark})),cardList:ne.map(h=>({airlineCode:h.airlineCode,frequentFlyerNumber:h.frequentFlyerNumber}))}},K=async()=>{const u=p.showAgencyFrequentFlyerDialogType===W.add;D.value.validate(async P=>{if(P)try{O.value=!0;const a=U(),{data:q}=u?await it(a,Ve("081L0139")):await dt(a,"081L0140");if(q.value==="success"){ie({message:"success",type:"success"}),N(!u);return}ie({message:"error",type:"error"})}finally{O.value=!1}})},m=()=>{var u;(u=D.value)==null||u.clearValidate(),T.value=E()},$=()=>{const{csmIdentifier:u,chineseName:P,englishName:a,genderCode:q,birthDate:_,mobilePhone:M,emailAddress:ee,frequentFlyerNumberInfo:I,frequentFlyerLevelNumberInfo:t,documentList:te,remarkList:Z,cardList:ne}=p.editForm,re=h=>{var ye;if(h.includes(w[2]))return w[0];if(h.includes(w[1]))return w[1];const me=`${C}${h}`;return(ye=y.value)!=null&&ye.some(Pe=>Pe.value===me)?`${C}${h}`:h};T.value={csmIdentifier:u,chineseName:P,englishName:a,genderCode:q,birthDate:_,mobilePhone:M,emailAddress:ee,frequentFlyerNumberInfo:I,frequentFlyerLevelNumberInfo:t,documentList:te.map(h=>({documentNumber:h.documentNumber,expiryDate:h.expiryDate,issueCountryCode:h.issueCountryCode,nationalityCode:h.nationalityCode,issueDate:h.issueDate,documentTypeCode:re(h.documentTypeCode),id:`document${ae()}`})),remarkList:Z.map(h=>({sequenceNumber:h.sequenceNumber,remark:h.remark,id:`remark${ae()}`})),cardList:ne.map(h=>({airlineCode:h.airlineCode,frequentFlyerNumber:h.frequentFlyerNumber,id:`card${ae()}`}))}};return De(()=>p.showAgencyFrequentFlyerDialogType,()=>{p.showAgencyFrequentFlyerDialogType===W.edit&&$()},{immediate:!0}),{formRef:D,ruleItem:H,genderTypeArr:o,CERT_TYPE_1:w,loading:O,showDialog:G,form:T,datePrefix:V,delList:B,addList:b,documentTypeChange:f,closeDialog:N,confirmClick:K,FORM_RULES:z,documentType:y}},ct=ut,ce=p=>(Ee("data-v-98f718b6"),p=p(),Ae(),p),mt={class:"dialog-main-title"},pt={class:"content-form-box"},ft={class:"user-id-box"},gt={class:"user-id"},yt={class:"user-tag"},_t={class:"info-box base-info"},ht={class:"info-title-box"},vt={class:"title-text"},bt=ce(()=>e("div",{class:"line-split"},null,-1)),xt={class:"base-info-detail"},Ft={class:"info-box"},$t={class:"document-list"},qt={class:"info-title-box"},Ct={class:"document-title title-text"},Pt=ce(()=>e("div",{class:"line-split"},null,-1)),Nt={key:0,class:"del-btn"},jt={class:"document-info"},kt={key:0},Dt={key:1,class:"inline-block w-[12px]"},Tt={class:"add-btn"},wt={class:"info-box remark-info"},Vt={class:"info-title-box"},Lt={class:"title-text"},It=ce(()=>e("div",{class:"line-split"},null,-1)),Et={class:"remark-list"},At={class:"del-btn"},Ut={class:"add-btn"},Rt={class:"info-box frequent-flyer-info"},Mt={class:"info-title-box"},St={class:"title-text"},Ot=ce(()=>e("div",{class:"line-split"},null,-1)),Bt={class:"frequent-flyer-list"},Yt={class:"del-btn"},Gt={class:"add-btn"},zt={class:"footer-btn-box"},Ht=fe({__name:"AgencyFrequentFlyerDialog",props:{showAgencyFrequentFlyerDialogType:{},headerTitle:{},editForm:{}},emits:["close"],setup(p,{emit:k}){const v=k,x=p,{formRef:o,genderTypeArr:w,loading:C,form:y,showDialog:E,documentType:D,documentTypeChange:O,delList:T,addList:V,datePrefix:G,closeDialog:z,ruleItem:H,confirmClick:Q,FORM_RULES:A,CERT_TYPE_1:B}=ct(x,v);return(c,i)=>{const F=$e,b=Ke,f=xe,N=et,L=tt,U=Ie,K=qe,m=ge,$=Ce,u=Le("trimUpper"),P=Fe;return d(),j($,{modelValue:s(E),"onUpdate:modelValue":i[15]||(i[15]=a=>be(E)?E.value=a:null),width:"1040px",class:"crs-new-ui-init-cls agency-frequent-flyer-dialog long-width crs-btn-dialog-ui","close-on-click-modal":!1,onClose:s(z)},{header:r(()=>[e("span",mt,l(c.headerTitle),1)]),default:r(()=>[R((d(),g("div",null,[e("div",pt,[n(K,{ref_key:"formRef",ref:o,model:s(y),rules:s(A)},{default:r(()=>[c.showAgencyFrequentFlyerDialogType===s(W).edit?(d(),j(b,{key:0},{default:r(()=>[n(F,{label:c.$t("app.intlPassengerForm.frequentTraveler.travelerID"),prop:"csmIdentifier"},{default:r(()=>[e("div",ft,[e("div",gt,l(s(y).csmIdentifier),1),e("div",yt,l(c.$t("app.intlPassengerForm.agencyFrequentFlyer.regularCustomer")),1)])]),_:1},8,["label"])]),_:1})):Y("",!0),e("div",_t,[e("div",ht,[e("div",vt,l(c.$t("app.intlPassengerForm.agencyFrequentFlyer.baseInfo")),1),bt]),e("div",xt,[n(F,{label:c.$t("app.intlPassengerForm.agencyFrequentFlyer.chineseName"),prop:"chineseName"},{default:r(()=>[R(n(f,{modelValue:s(y).chineseName,"onUpdate:modelValue":i[0]||(i[0]=a=>s(y).chineseName=a),disabled:!1},null,8,["modelValue"]),[[u]])]),_:1},8,["label"]),n(F,{label:c.$t("app.intlPassengerForm.agencyFrequentFlyer.englishName"),prop:"englishName"},{default:r(()=>[R(n(f,{modelValue:s(y).englishName,"onUpdate:modelValue":i[1]||(i[1]=a=>s(y).englishName=a),disabled:!1},null,8,["modelValue"]),[[u]])]),_:1},8,["label"]),n(F,{label:c.$t("app.intlPassengerForm.frequentTraveler.gender"),prop:"genderCode"},{default:r(()=>[n(L,{modelValue:s(y).genderCode,"onUpdate:modelValue":i[2]||(i[2]=a=>s(y).genderCode=a)},{default:r(()=>[(d(!0),g(J,null,X(s(w),a=>(d(),j(N,{key:a.value,label:a.label,value:a.value},{default:r(()=>[se(l(a.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"]),n(F,{label:c.$t("app.intlPassengerForm.frequentTraveler.birthday"),prop:"birthDate"},{default:r(()=>[n(he,{modelValue:s(y).birthDate,"onUpdate:modelValue":i[3]||(i[3]=a=>s(y).birthDate=a),type:"date","value-format":"YYYY-MM-DD","prefix-icon":s(G),"disabled-date":s(He)},null,8,["modelValue","prefix-icon","disabled-date"])]),_:1},8,["label"]),n(F,{label:c.$t("app.intlPassengerForm.agencyFrequentFlyer.mobilePhone"),prop:"mobilePhone"},{default:r(()=>[R(n(f,{modelValue:s(y).mobilePhone,"onUpdate:modelValue":i[4]||(i[4]=a=>s(y).mobilePhone=a),disabled:!1},null,8,["modelValue"]),[[u]])]),_:1},8,["label"]),n(F,{label:c.$t("app.intlPassengerForm.frequentTraveler.email"),prop:"emailAddress"},{default:r(()=>[R(n(f,{modelValue:s(y).emailAddress,"onUpdate:modelValue":i[5]||(i[5]=a=>s(y).emailAddress=a),disabled:!1},null,8,["modelValue"]),[[u]])]),_:1},8,["label"]),n(F,{label:c.$t("app.intlPassengerForm.frequentTraveler.clientCode"),prop:"frequentFlyerNumberInfo"},{default:r(()=>[R(n(f,{modelValue:s(y).frequentFlyerNumberInfo,"onUpdate:modelValue":i[6]||(i[6]=a=>s(y).frequentFlyerNumberInfo=a),disabled:!1},null,8,["modelValue"]),[[u]])]),_:1},8,["label"]),n(F,{label:c.$t("app.intlPassengerForm.frequentTraveler.clientLevel"),prop:"frequentFlyerLevelNumberInfo"},{default:r(()=>[R(n(f,{modelValue:s(y).frequentFlyerLevelNumberInfo,"onUpdate:modelValue":i[7]||(i[7]=a=>s(y).frequentFlyerLevelNumberInfo=a),disabled:!1},null,8,["modelValue"]),[[u]])]),_:1},8,["label"])])]),e("div",Ft,[e("div",$t,[(d(!0),g(J,null,X(s(y).documentList,(a,q)=>(d(),g("div",{key:a.id,class:"document-item"},[e("div",qt,[e("div",Ct,l(`${c.$t("app.intlPassengerForm.agencyFrequentFlyer.certificate")}${q+1}`),1),Pt,s(y).documentList.length>1?(d(),g("div",Nt,[n(U,{class:"iconfont icon-delete",onClick:_=>s(T)("documentList",a.id)},null,8,["onClick"])])):Y("",!0)]),e("div",jt,[n(F,{label:c.$t("app.intlPassengerForm.agencyFrequentFlyer.idNoType"),prop:`documentList.${q}.documentTypeCode`,rules:s(A).documentTypeCode},{default:r(()=>[n(L,{modelValue:a.documentTypeCode,"onUpdate:modelValue":_=>a.documentTypeCode=_,onChange:_=>s(O)(q)},{default:r(()=>[(d(!0),g(J,null,X(s(D),_=>(d(),j(N,{key:_.value,label:_.label,value:_.value},{default:r(()=>[s(D).some(M=>M.value===a.documentTypeCode)?(d(),g("span",kt,[_.value===a.documentTypeCode?(d(),j(U,{key:0,size:12,class:"iconfont icon-right-line"})):(d(),g("span",Dt))])):Y("",!0),se(" "+l(_.label),1)]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","onChange"])]),_:2},1032,["label","prop","rules"]),n(F,{label:c.$t("app.intlPassengerForm.agencyFrequentFlyer.passportNumber"),prop:`documentList.${q}.documentNumber`,rules:s(A).documentNumber},{default:r(()=>[R(n(f,{modelValue:a.documentNumber,"onUpdate:modelValue":_=>a.documentNumber=_,disabled:!1},null,8,["modelValue","onUpdate:modelValue"]),[[u]])]),_:2},1032,["label","prop","rules"]),s(B).includes(a.documentTypeCode)?Y("",!0):(d(),j(F,{key:0,label:c.$t("app.intlPassengerForm.agencyFrequentFlyer.certificateExpirationDate"),prop:`documentList.${q}.expiryDate`,rules:s(H)(a.documentTypeCode)},{default:r(()=>[n(he,{modelValue:a.expiryDate,"onUpdate:modelValue":_=>a.expiryDate=_,type:"date","value-format":"YYYY-MM-DD","prefix-icon":s(G),"disabled-date":s(Je)},null,8,["modelValue","onUpdate:modelValue","prefix-icon","disabled-date"])]),_:2},1032,["label","prop","rules"])),s(B).includes(a.documentTypeCode)?Y("",!0):(d(),j(F,{key:1,label:c.$t("app.intlPassengerForm.issueCountry"),prop:`documentList.${q}.issueCountryCode`,rules:s(A).issueCountryCode},{default:r(()=>[R(n(f,{modelValue:a.issueCountryCode,"onUpdate:modelValue":_=>a.issueCountryCode=_},null,8,["modelValue","onUpdate:modelValue"]),[[u]])]),_:2},1032,["label","prop","rules"])),s(B).includes(a.documentTypeCode)?Y("",!0):(d(),j(F,{key:2,label:c.$t("app.intlPassengerForm.nationality"),prop:`documentList.${q}.nationalityCode`,rules:s(A).nationalityCode},{default:r(()=>[R(n(f,{modelValue:a.nationalityCode,"onUpdate:modelValue":_=>a.nationalityCode=_},null,8,["modelValue","onUpdate:modelValue"]),[[u]])]),_:2},1032,["label","prop","rules"]))])]))),128)),e("div",Tt,[n(U,{class:"iconfont icon-plus-square primary-color",onClick:i[8]||(i[8]=a=>s(V)("documentList"))}),e("div",{class:"btn-text",onClick:i[9]||(i[9]=a=>s(V)("documentList"))},l(c.$t("app.intlPassengerForm.agencyFrequentFlyer.addCertificate")),1)])])]),e("div",wt,[e("div",Vt,[e("div",Lt,l(c.$t("app.intlPassengerForm.agencyFrequentFlyer.remarkInfo")),1),It]),e("div",Et,[(d(!0),g(J,null,X(s(y).remarkList,a=>(d(),g("div",{key:a.id,class:"remark-item"},[n(F,{label:"RMK",prop:"remark.remark"},{default:r(()=>[R(n(f,{modelValue:a.remark,"onUpdate:modelValue":q=>a.remark=q,disabled:!1},null,8,["modelValue","onUpdate:modelValue"]),[[u]])]),_:2},1024),e("div",At,[n(U,{class:"iconfont icon-delete",onClick:q=>s(T)("remarkList",a.id)},null,8,["onClick"])])]))),128)),e("div",Ut,[n(U,{class:"iconfont iconfont icon-plus-square primary-color",onClick:i[10]||(i[10]=a=>s(V)("remarkList"))}),e("div",{class:"btn-text",onClick:i[11]||(i[11]=a=>s(V)("remarkList"))},l(c.$t("app.intlPassengerForm.agencyFrequentFlyer.addRemark")),1)])])]),e("div",Rt,[e("div",Mt,[e("div",St,l(c.$t("app.intlPassengerForm.agencyFrequentFlyer.frequentGuestCard")),1),Ot]),e("div",Bt,[(d(!0),g(J,null,X(s(y).cardList,(a,q)=>(d(),g("div",{key:a.id,class:"frequent-flyer-item"},[n(F,{label:c.$t("app.intlPassengerForm.frequentTraveler.airline"),prop:`cardList.${q}.airlineCode`,rules:s(A).airlineCode},{default:r(()=>[R(n(f,{modelValue:a.airlineCode,"onUpdate:modelValue":_=>a.airlineCode=_,disabled:!1},null,8,["modelValue","onUpdate:modelValue"]),[[u]])]),_:2},1032,["label","prop","rules"]),n(F,{label:c.$t("app.intlPassengerForm.agencyFrequentFlyer.cardNo"),prop:`cardList.${q}.frequentFlyerNumber`,rules:s(A).frequentFlyerNumber},{default:r(()=>[R(n(f,{modelValue:a.frequentFlyerNumber,"onUpdate:modelValue":_=>a.frequentFlyerNumber=_,disabled:!1},null,8,["modelValue","onUpdate:modelValue"]),[[u]])]),_:2},1032,["label","prop","rules"]),e("div",Yt,[n(U,{class:"iconfont icon-delete",onClick:_=>s(T)("cardList",a.id)},null,8,["onClick"])])]))),128)),e("div",Gt,[n(U,{class:"iconfont iconfont icon-plus-square primary-color",onClick:i[12]||(i[12]=a=>s(V)("cardList"))}),e("div",{class:"btn-text",onClick:i[13]||(i[13]=a=>s(V)("cardList"))},l(c.$t("app.intlPassengerForm.agencyFrequentFlyer.addFrequentGuestCard")),1)])])])]),_:1},8,["model","rules"])]),e("div",zt,[n(m,{class:"footer-btn",type:"primary",onClick:s(Q)},{default:r(()=>[se(l(c.$t("app.intlPassengerForm.batchPassenger.confirm")),1)]),_:1},8,["onClick"]),n(m,{class:"footer-btn",plain:"",onClick:i[14]||(i[14]=a=>s(z)(!1))},{default:r(()=>[se(l(c.$t("app.intlPassengerForm.batchPassenger.cancel")),1)]),_:1})])])),[[P,s(C)]])]),_:1},8,["modelValue","onClose"])}}});const Jt=st(Ht,[["__scopeId","data-v-98f718b6"]]),Qt={class:"self-stretch flex justify-between items-center mb-2"},Xt={class:"justify-start text-color-gray-1 text-lg font-bold leading-normal"},Zt={class:"self-stretch h-7 flex justify-start items-center gap-1.5"},Wt={class:"min-w-[64px] flex justify-start items-center"},Kt={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},es={class:"flex justify-start items-center gap-0.5"},ts={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ss={"data-length":"unfixed","data-size":"default","data-type":"gray",class:"px-1 bg-cyan-100 rounded-sm flex justify-center items-center"},as={class:"text-center justify-start text-sky-700 text-xs font-normal leading-[20px]"},ls={class:"dialog-body"},ns={class:"self-stretch flex flex-col justify-start items-start gap-1"},rs={class:"self-stretch inline-flex justify-start items-center gap-2.5 my-1.5"},os={class:"flex justify-start items-center gap-1.5"},is={class:"justify-start text-color-gray-1 text-xs font-bold leading-tight"},ds=e("div",{class:"w-full grow shrink basis-0 h-[0px] rotate-180 border-t-[1.25px] border-brand-3 border-dashed"},null,-1),us={class:"self-stretch inline-flex justify-start items-start gap-3.5"},cs={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},ms={class:"min-w-[64px] flex justify-start items-center"},ps={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},fs={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},gs={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ys={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},_s={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},hs={class:"min-w-[64px] flex justify-start items-center"},vs={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},bs={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},xs={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Fs={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},$s={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},qs={class:"min-w-[64px] flex justify-start items-center"},Cs={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Ps={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ns={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},js={class:"min-w-[64px] flex justify-start items-center"},ks={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Ds={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ts={class:"self-stretch inline-flex justify-start items-start gap-3.5"},ws={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Vs={class:"min-w-[64px] flex justify-start items-center"},Ls={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Is={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},Es={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},As={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Us={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Rs={class:"min-w-[64px] flex justify-start items-center"},Ms={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Ss={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight truncate ... max-w-[150px]"},Os={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Bs={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ys={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Gs={class:"min-w-[64px] flex justify-start items-center"},zs={class:"justify-start text-color-gray-2 text-xs font-normal leading-tight"},Hs={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},Js={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Qs={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Xs={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Zs={class:"min-w-[64px] flex justify-start items-center"},Ws={class:"justify-start text-color-gray-2 text-xs font-normal leading-tight"},Ks={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight truncate ... max-w-[150px]"},ea={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ta={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},sa={class:"self-stretch flex justify-start items-center gap-2.5 my-1.5"},aa={class:"flex justify-start items-center gap-1.5"},la={class:"justify-start text-color-gray-1 text-xs font-bold leading-tight"},na=e("div",{class:"w-full grow shrink basis-0 h-[0px] rotate-180 border-t-[1.25px] border-brand-3 border-dashed"},null,-1),ra={class:"self-stretch inline-flex justify-start items-start gap-3.5"},oa={class:"flex-1 h-7 inline-flex justify-start items-center gap-1.5"},ia={"data-length":"unfixed","data-size":"default","data-type":"blue",class:"min-w-[64px] px-1 bg-brand-3 rounded-sm flex justify-center items-center"},da={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[64px]"},ua={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ca={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ma={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight truncate ... max-w-[175px]"},pa={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},fa={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},ga={key:0,class:"flex-1 h-7 flex justify-start items-center gap-1.5"},ya={class:"min-w-[64px] flex justify-start items-center"},_a={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},ha={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},va={key:1,class:"flex-1 h-7 flex justify-start items-center gap-1.5"},ba={class:"flex justify-start items-center"},xa={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Fa={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},$a={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},qa={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ca={key:2,class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Pa={class:"flex justify-start items-center"},Na={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},ja={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[150px]"},ka={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Da={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Ta={class:"self-stretch flex flex-col justify-start items-start gap-1"},wa={class:"self-stretch flex justify-start items-center gap-2.5 my-1.5"},Va={class:"flex justify-start items-center gap-1.5"},La={class:"justify-start text-color-gray-1 text-xs font-bold leading-tight"},Ia=e("div",{class:"w-full grow shrink basis-0 h-[0px] rotate-180 border-t-[1.25px] border-brand-3 border-dashed"},null,-1),Ea={key:0,class:"self-stretch inline-flex justify-start items-start gap-3.5"},Aa={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ua=e("div",{class:"min-w-[64px] flex justify-start items-center"},[e("div",{class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},"RMK")],-1),Ra={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[900px]"},Ma={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Sa={key:1,class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Oa={class:"self-stretch flex flex-col justify-start items-start gap-1"},Ba={class:"self-stretch flex justify-start items-center gap-2.5 my-1.5"},Ya={class:"flex justify-start items-center gap-1.5"},Ga={class:"justify-start text-color-gray-1 text-xs font-bold leading-tight"},za=e("div",{class:"w-full grow shrink basis-0 h-[0px] rotate-180 border-t-[1.25px] border-brand-3 border-dashed"},null,-1),Ha={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Ja={class:"min-w-[64px] flex justify-start items-center"},Qa={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},Xa={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},Za={class:"flex-1 h-7 flex justify-start items-center gap-1.5"},Wa={class:"min-w-[64px] flex justify-start items-center"},Ka={class:"justify-start text-color-gray-2 text-xs font-normal leading-none"},el={class:"justify-start text-color-gray-2 text-xs font-bold leading-tight"},tl=e("div",{class:"flex-1 self-stretch"},null,-1),sl=e("div",{class:"flex-1 self-stretch"},null,-1),al={class:"dialog-footer"},ll=fe({__name:"RegularDetailsDialog",props:{detailData:{}},emits:["update:modelValue"],setup(p,{emit:k}){const v=k,x=()=>v("update:modelValue",!1);return(o,w)=>{const C=at,y=ge,E=Ce;return d(),j(E,{width:"1040px",center:"","close-on-click-modal":!1,class:"agencyDetails-dialog",onClose:w[1]||(w[1]=D=>x())},{header:r(({})=>{var D;return[e("div",Qt,[e("div",Xt,l(o.$t("app.intlPassengerForm.frequentTraveler.details")),1)]),e("div",Zt,[e("div",Wt,[e("div",Kt,l(o.$t("app.intlPassengerForm.frequentTraveler.travelerID")),1)]),e("div",es,[e("div",ts,l((D=o.detailData)==null?void 0:D.csmIdentifier),1),e("div",ss,[e("div",as,l(o.$t("app.intlPassengerForm.agencyFrequentFlyer.regularCustomer")),1)])])])]}),footer:r(()=>[e("div",al,[n(y,{onClick:w[0]||(w[0]=D=>x())},{default:r(()=>[se(l(o.$t("app.intlPassengerForm.frequentTraveler.close")),1)]),_:1})])]),default:r(()=>{var D,O,T,V,G,z,H,Q,A,B,c,i,F,b,f,N,L,U,K,m,$,u,P,a,q,_,M,ee,I;return[e("div",ls,[e("div",ns,[e("div",rs,[e("div",os,[e("div",is,l(o.$t("app.intlPassengerForm.frequentTraveler.basicInfo")),1)]),ds]),e("div",us,[e("div",cs,[e("div",ms,[e("div",ps,l(o.$t("app.intlPassengerForm.frequentTraveler.chineseName")),1)]),e("div",fs,[((O=(D=o.detailData)==null?void 0:D.chineseName)==null?void 0:O.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:(T=o.detailData)==null?void 0:T.chineseName,placement:"top-start"},{default:r(()=>{var t;return[e("span",gs,l(((t=o.detailData)==null?void 0:t.chineseName)??"-"),1)]}),_:1},8,["content"])):(d(),g("span",ys,l(((V=o.detailData)==null?void 0:V.chineseName)??"-"),1))])]),e("div",_s,[e("div",hs,[e("div",vs,l(o.$t("app.intlPassengerForm.frequentTraveler.englishName")),1)]),e("div",bs,[((z=(G=o.detailData)==null?void 0:G.englishName)==null?void 0:z.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:(H=o.detailData)==null?void 0:H.englishName,placement:"top-start"},{default:r(()=>{var t;return[e("span",xs,l(((t=o.detailData)==null?void 0:t.englishName)??"-"),1)]}),_:1},8,["content"])):(d(),g("span",Fs,l(((Q=o.detailData)==null?void 0:Q.englishName)??"-"),1))])]),e("div",$s,[e("div",qs,[e("div",Cs,l(o.$t("app.intlPassengerForm.frequentTraveler.gender")),1)]),e("div",Ps,l(o.$t(`app.intlPassengerForm.frequentTraveler.sex_${((A=o.detailData)==null?void 0:A.genderCode)??""}`)),1)]),e("div",Ns,[e("div",js,[e("div",ks,l(o.$t("app.intlPassengerForm.frequentTraveler.birthday")),1)]),e("div",Ds,l((B=o.detailData)==null?void 0:B.birthDate),1)])]),e("div",Ts,[e("div",ws,[e("div",Vs,[e("div",Ls,l(o.$t("app.intlPassengerForm.frequentTraveler.mobilePhone")),1)]),e("div",Is,[((i=(c=o.detailData)==null?void 0:c.mobilePhone)==null?void 0:i.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:(F=o.detailData)==null?void 0:F.mobilePhone,placement:"top-start"},{default:r(()=>{var t;return[e("span",Es,l(((t=o.detailData)==null?void 0:t.mobilePhone)??"-"),1)]}),_:1},8,["content"])):(d(),g("span",As,l(((b=o.detailData)==null?void 0:b.mobilePhone)??"-"),1))])]),e("div",Us,[e("div",Rs,[e("div",Ms,l(o.$t("app.intlPassengerForm.frequentTraveler.email")),1)]),e("div",Ss,[((N=(f=o.detailData)==null?void 0:f.emailAddress)==null?void 0:N.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:(L=o.detailData)==null?void 0:L.emailAddress,placement:"top-start"},{default:r(()=>{var t;return[e("span",Os,l(((t=o.detailData)==null?void 0:t.emailAddress)??"-"),1)]}),_:1},8,["content"])):(d(),g("span",Bs,l(((U=o.detailData)==null?void 0:U.emailAddress)??"-"),1))])]),e("div",Ys,[e("div",Gs,[e("div",zs,l(o.$t("app.intlPassengerForm.frequentTraveler.clientCode")),1)]),e("div",Hs,[((m=(K=o.detailData)==null?void 0:K.frequentFlyerNumberInfo)==null?void 0:m.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:($=o.detailData)==null?void 0:$.frequentFlyerNumberInfo,placement:"top-start"},{default:r(()=>{var t;return[e("span",Js,l(((t=o.detailData)==null?void 0:t.frequentFlyerNumberInfo)??"-"),1)]}),_:1},8,["content"])):(d(),g("span",Qs,l(((u=o.detailData)==null?void 0:u.frequentFlyerNumberInfo)??"-"),1))])]),e("div",Xs,[e("div",Zs,[e("div",Ws,l(o.$t("app.intlPassengerForm.frequentTraveler.clientLevel")),1)]),e("div",Ks,[((a=(P=o.detailData)==null?void 0:P.frequentFlyerLevelNumberInfo)==null?void 0:a.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:(q=o.detailData)==null?void 0:q.frequentFlyerLevelNumberInfo,placement:"top-start"},{default:r(()=>{var t;return[e("span",ea,l(((t=o.detailData)==null?void 0:t.frequentFlyerLevelNumberInfo)??"-"),1)]}),_:1},8,["content"])):(d(),g("span",ta,l(((_=o.detailData)==null?void 0:_.frequentFlyerLevelNumberInfo)??"-"),1))])])])]),(d(!0),g(J,null,X((M=o.detailData)==null?void 0:M.documentList,(t,te)=>{var Z,ne,re,h;return d(),g("div",{key:te,class:"self-stretch flex flex-col justify-start items-start gap-1 mt-2"},[e("div",sa,[e("div",aa,[e("div",la,l(o.$t("app.intlPassengerForm.frequentTraveler.idCard"))+l(te+1),1)]),na]),e("div",ra,[e("div",oa,[e("div",ia,[e("div",da,[((Z=o.$t(`app.intlPassengerForm.PP_${(t==null?void 0:t.documentTypeCode)||"empty"}`))==null?void 0:Z.length)>5?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:o.$t(`app.intlPassengerForm.PP_${(t==null?void 0:t.documentTypeCode)||"empty"}`),placement:"top-start"},{default:r(()=>[e("span",ua,l(o.$t(`app.intlPassengerForm.PP_${(t==null?void 0:t.documentTypeCode)||"empty"}`)),1)]),_:2},1032,["content"])):(d(),g("span",ca,l(o.$t(`app.intlPassengerForm.PP_${(t==null?void 0:t.documentTypeCode)||"empty"}`)),1))])]),e("div",ma,[((ne=t==null?void 0:t.documentNumber)==null?void 0:ne.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:t==null?void 0:t.documentNumber,placement:"top-start"},{default:r(()=>[e("span",pa,l((t==null?void 0:t.documentNumber)??"-"),1)]),_:2},1032,["content"])):(d(),g("span",fa,l((t==null?void 0:t.documentNumber)??"-"),1))])]),t!=null&&t.expiryDate?(d(),g("div",ga,[e("div",ya,[e("div",_a,l(o.$t("app.intlPassengerForm.frequentTraveler.idValidity")),1)]),e("div",ha,l(t==null?void 0:t.expiryDate),1)])):Y("",!0),t!=null&&t.issueCountryCode?(d(),g("div",va,[e("div",ba,[e("div",xa,l(o.$t("app.intlPassengerForm.frequentTraveler.issueCountry")),1)]),e("div",Fa,[((re=t==null?void 0:t.issueCountryCode)==null?void 0:re.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:t==null?void 0:t.issueCountryCode,placement:"top-start"},{default:r(()=>[e("span",$a,l((t==null?void 0:t.issueCountryCode)??"-"),1)]),_:2},1032,["content"])):(d(),g("span",qa,l((t==null?void 0:t.issueCountryCode)??"-"),1))])])):Y("",!0),t!=null&&t.nationalityCode?(d(),g("div",Ca,[e("div",Pa,[e("div",Na,l(o.$t("app.intlPassengerForm.frequentTraveler.nationality")),1)]),e("div",ja,[((h=t==null?void 0:t.nationalityCode)==null?void 0:h.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:t==null?void 0:t.nationalityCode,placement:"top-start"},{default:r(()=>[e("span",ka,l((t==null?void 0:t.nationalityCode)??"-"),1)]),_:2},1032,["content"])):(d(),g("span",Da,l((t==null?void 0:t.nationalityCode)??"-"),1))])])):Y("",!0)])])}),128)),e("div",Ta,[e("div",wa,[e("div",Va,[e("div",La,l(o.$t("app.intlPassengerForm.frequentTraveler.remarks")),1)]),Ia]),(d(!0),g(J,null,X((ee=o.detailData)==null?void 0:ee.remarkList,(t,te)=>{var Z;return d(),g(J,{key:te},[t?(d(),g("div",Ea,[e("div",Aa,[Ua,e("div",Ra,[((Z=t.remark)==null?void 0:Z.length)>20?(d(),j(C,{key:0,class:"box-item",effect:"dark",content:t.remark,placement:"top-start"},{default:r(()=>[e("span",Ma,l(t.remark??"-"),1)]),_:2},1032,["content"])):(d(),g("span",Sa,l(t.remark??"-"),1))])])])):Y("",!0)],64)}),128))]),e("div",Oa,[e("div",Ba,[e("div",Ya,[e("div",Ga,l(o.$t("app.intlPassengerForm.frequentTraveler.loyaltyCard")),1)]),za]),(d(!0),g(J,null,X(((I=o.detailData)==null?void 0:I.cardList)??1,t=>(d(),g("div",{key:t,class:"self-stretch inline-flex justify-start items-start gap-3.5"},[e("div",Ha,[e("div",Ja,[e("div",Qa,l(o.$t("app.intlPassengerForm.frequentTraveler.airline")),1)]),e("div",Xa,l(t==null?void 0:t.airlineCode),1)]),e("div",Za,[e("div",Wa,[e("div",Ka,l(o.$t("app.intlPassengerForm.frequentTraveler.phone")),1)]),e("div",el,l(t==null?void 0:t.frequentFlyerNumber),1)]),tl,sl]))),128))])])]}),_:1})}}});const nl=()=>{const{t:p}=ve(),k=S(!1),v=S(),x=S(),o=S(W.close),w=S(""),C=S({passengerName:"",documentNumber:"",mobilePhone:"",csmIdentifier:""}),y=S([]),E=S(!1),D=S({}),O=(b,f,N)=>{Xe.test(f)||Ze.test(f)?N():N(new Error(p("app.pnrManagement.validate.nameInputError")))},T={documentNumber:[{pattern:Ue,message:p("app.pnrManagement.validate.formatErr"),trigger:"blur"}],mobilePhone:[{pattern:Re,message:p("app.pnrManagement.validate.formatErr"),trigger:"blur"}],passengerName:[{validator:O,trigger:"blur"}],csmIdentifier:[{pattern:Me,message:p("app.pnrManagement.validate.formatErr"),trigger:"blur"}]},V=async()=>{var b,f;y.value=[];try{k.value=!0;const{data:N}=await rt(C.value,"081L0133");y.value=(f=(b=N.value)==null?void 0:b.result)!=null&&f.length?[...N.value.result]:[]}finally{k.value=!1}},G=()=>{var f;if(Object.values(C.value).every(N=>N==="")){ie.warning(p("app.intlPassengerForm.frequentTraveler.queryAgentErrorTips"));return}(f=v.value)==null||f.validate(async N=>{N&&V()})},z=()=>{C.value={passengerName:"",documentNumber:"",mobilePhone:"",csmIdentifier:""}},H=()=>{o.value=W.add,w.value=p("app.intlPassengerForm.frequentTraveler.create")},Q=b=>{var f,N;o.value=W.close,b&&(C.value={passengerName:"",documentNumber:"",mobilePhone:"",csmIdentifier:((N=(f=x.value)==null?void 0:f.csmIdentifier)==null?void 0:N.toString())??""},V())},A=b=>{D.value=b,E.value=!0},B=b=>{x.value=Se(b),o.value=W.edit,w.value=p("app.intlPassengerForm.agencyFrequentFlyer.edit")},c=async b=>{k.value=!0;try{const{data:f}=await ot(b,"081L0134");f.value==="OK"&&(ie({message:p("app.qMessage.deleteSuccess"),type:"success"}),V())}finally{k.value=!1}};return{FORM_RULES:T,formRef:v,showAgencyFrequentFlyerDialogType:o,editForm:x,closeAgencyFrequentFlyerDialog:Q,headerTitle:w,queryForm:C,resetForm:z,loading:k,search:G,create:H,customData:y,viewDetails:A,editDetails:B,deleteDetails:async b=>{const f=b.chineseName?b.chineseName:b.englishName??"";await Oe.confirm(pe("div",{class:"batch-delete-tip-box"},p("app.intlPassengerForm.frequentTraveler.deleteAgentTips",{delName:f})),{icon:pe("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"crs-btn-ui crs-btn-message-ui",confirmButtonText:p("app.button.ensure"),cancelButtonText:p("app.pnrManagement.flight.cancel"),showClose:!1}).then(()=>{c(b.csmIdentifier)})},viewAgentRegularData:D,isShowDetailsDialog:E,getPhoneList:b=>b.match(/[^,，]+/g)}},rl=nl,ol={class:"agent-regulars"},il={class:"justify-start items-center flex"},dl={class:"form-box w-full h-[52px] justify-start items-center gap-1.5 flex p-2.5 rounded-lg shadow"},ul={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},cl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[140px]"},ml={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},pl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[180px]"},fl={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},gl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[180px]"},yl={class:"w-full self-stretch inline-flex justify-start items-center gap-1 overflow-hidden"},_l={class:"justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[80px]"},hl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[140px]"},vl={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},bl={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[180px]"},xl={class:"self-stretch inline-flex justify-start items-center gap-1 overflow-hidden"},Fl={"data-type":"cell-text-tag",class:"self-stretch h-12 px-1.5 inline-flex justify-start items-center gap-1 overflow-hidden"},$l={class:"flex-1 justify-start text-color-gray-1 text-sm font-normal leading-snug truncate ... max-w-[180px]"},ql=e("em",{class:"primary-color iconfont icon-eye"},null,-1),Cl=e("em",{class:"primary-color iconfont icon-edit"},null,-1),Pl=e("em",{class:"primary-color iconfont icon-delete"},null,-1),en=fe({__name:"AgentRegulars",setup(p){const{formRef:k,FORM_RULES:v,queryForm:x,resetForm:o,loading:w,create:C,search:y,isShowDetailsDialog:E,customData:D,viewAgentRegularData:O,viewDetails:T,editDetails:V,deleteDetails:G,editForm:z,showAgencyFrequentFlyerDialogType:H,headerTitle:Q,closeAgencyFrequentFlyerDialog:A,getPhoneList:B}=rl();return(c,i)=>{const F=xe,b=$e,f=ge,N=qe,L=lt,U=nt,K=Fe;return R((d(),g("div",ol,[e("div",il,[e("div",dl,[n(N,{ref_key:"formRef",ref:k,inline:!0,model:s(x),rules:s(v),class:"frequent-traveler w-full inline-flex","require-asterisk-position":"right",onSubmit:i[11]||(i[11]=Be(()=>{},["prevent"]))},{default:r(()=>[n(b,{label:c.$t("app.intlPassengerForm.frequentTraveler.travelerID"),prop:"csmIdentifier"},{default:r(()=>[n(F,{modelValue:s(x).csmIdentifier,"onUpdate:modelValue":i[0]||(i[0]=m=>s(x).csmIdentifier=m),modelModifiers:{trim:!0},class:"pnr-input input-uppercase ml-1",clearable:"",onInput:i[1]||(i[1]=m=>{var $;return s(x).csmIdentifier=($=s(x).csmIdentifier)==null?void 0:$.toUpperCase()})},null,8,["modelValue"])]),_:1},8,["label"]),n(b,{label:c.$t("app.intlPassengerForm.frequentTraveler.travelerName"),prop:"passengerName"},{default:r(()=>[n(F,{modelValue:s(x).passengerName,"onUpdate:modelValue":i[2]||(i[2]=m=>s(x).passengerName=m),modelModifiers:{trim:!0},class:"pnr-input input-uppercase ml-1",clearable:"",onInput:i[3]||(i[3]=m=>{var $;return s(x).passengerName=($=s(x).passengerName)==null?void 0:$.toUpperCase()})},null,8,["modelValue"])]),_:1},8,["label"]),n(b,{label:c.$t("app.intlPassengerForm.frequentTraveler.travelerCard"),prop:"documentNumber"},{default:r(()=>[n(F,{modelValue:s(x).documentNumber,"onUpdate:modelValue":i[4]||(i[4]=m=>s(x).documentNumber=m),modelModifiers:{trim:!0},class:"pnr-input input-uppercase ml-1",clearable:"",onInput:i[5]||(i[5]=m=>{var $;return s(x).documentNumber=($=s(x).documentNumber)==null?void 0:$.toUpperCase()})},null,8,["modelValue"])]),_:1},8,["label"]),n(b,{label:c.$t("app.intlPassengerForm.frequentTraveler.travelerTel"),prop:"mobilePhone"},{default:r(()=>[n(F,{modelValue:s(x).mobilePhone,"onUpdate:modelValue":i[6]||(i[6]=m=>s(x).mobilePhone=m),modelModifiers:{trim:!0},class:"pnr-input input-uppercase ml-1",clearable:"",onInput:i[7]||(i[7]=m=>{var $;return s(x).mobilePhone=($=s(x).mobilePhone)==null?void 0:$.toUpperCase()})},null,8,["modelValue"])]),_:1},8,["label"]),n(b,null,{default:r(()=>[n(f,{type:"primary",onClick:i[8]||(i[8]=m=>s(y)())},{default:r(()=>[se(l(c.$t("app.pnrManagement.btnGroups.search"))+" Enter ",1)]),_:1}),n(f,{onClick:i[9]||(i[9]=m=>s(C)())},{default:r(()=>[se(l(c.$t("app.intlPassengerForm.frequentTraveler.create")),1)]),_:1}),n(f,{onClick:i[10]||(i[10]=m=>s(o)())},{default:r(()=>[se(l(c.$t("app.pnrManagement.btnGroups.reset")),1)]),_:1})]),_:1})]),_:1},8,["model","rules"])])]),n(U,{data:s(D),stripe:""},{default:r(()=>[n(L,{label:c.$t("app.intlPassengerForm.frequentTraveler.travelerID"),"min-width":"80"},{default:r(({row:m})=>[e("div",ul,[e("div",cl,[n(le,{"show-val":m.csmIdentifier},{default:r(()=>[e("span",null,l(m.csmIdentifier??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),n(L,{label:c.$t("app.intlPassengerForm.frequentTraveler.chineseName"),"min-width":"80"},{default:r(({row:m})=>[e("div",ml,[e("div",pl,[n(le,{"show-val":m.chineseName},{default:r(()=>[e("span",null,l(m.chineseName??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),n(L,{label:c.$t("app.intlPassengerForm.frequentTraveler.englishName"),"min-width":"160"},{default:r(({row:m})=>[e("div",fl,[e("div",gl,[n(le,{"show-val":m.englishName},{default:r(()=>[e("span",null,l(m.englishName??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),n(L,{label:c.$t("app.intlPassengerForm.frequentTraveler.documentInfo"),"min-width":"220"},{default:r(({row:m})=>[(d(!0),g(J,null,X(m.documentList,($,u)=>(d(),g("div",{key:u,"data-type":"cell-text",class:"w-full self-stretch inline-flex justify-start items-center overflow-hidden"},[e("div",yl,[e("div",_l,[n(le,{"show-val":c.$t(`app.intlPassengerForm.PP_${$.documentTypeCode||"empty"}`)},{default:r(()=>[e("span",null,l(c.$t(`app.intlPassengerForm.PP_${$.documentTypeCode||"empty"}`)),1)]),_:2},1032,["show-val"])]),e("div",hl,[n(le,{"show-val":$.documentNumberDes},{default:r(()=>[e("span",null,l($.documentNumberDes??"-"),1)]),_:2},1032,["show-val"])])])]))),128))]),_:1},8,["label"]),n(L,{label:c.$t("app.intlPassengerForm.frequentTraveler.gender"),"min-width":"80"},{default:r(({row:m})=>[e("div",null,l(m!=null&&m.genderCode?c.$t(`app.intlPassengerForm.frequentTraveler.sex_${(m==null?void 0:m.genderCode)??""}`):"-"),1)]),_:1},8,["label"]),n(L,{label:c.$t("app.intlPassengerForm.frequentTraveler.birthday"),"min-width":"110"},{default:r(({row:m})=>[e("div",vl,[e("div",bl,[n(le,{"show-val":m.birthDate},{default:r(()=>[e("span",null,l(m.birthDate??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),n(L,{label:c.$t("app.intlPassengerForm.frequentTraveler.mobilePhone"),"min-width":"110"},{default:r(({row:m})=>[(d(!0),g(J,null,X(s(B)(m.mobilePhoneDes),($,u)=>(d(),g("div",{key:u,"data-type":"cell-text",class:"self-stretch inline-flex justify-start items-center overflow-hidden"},[e("div",xl,l($),1)]))),128))]),_:1},8,["label"]),n(L,{label:c.$t("app.intlPassengerForm.frequentTraveler.email"),"min-width":"160"},{default:r(({row:m})=>[e("div",Fl,[e("div",$l,[n(le,{"show-val":m.emailAddress},{default:r(()=>[e("span",null,l(m.emailAddress??"-"),1)]),_:2},1032,["show-val"])])])]),_:1},8,["label"]),n(L,{label:c.$t("app.qMessage.operation"),width:"130"},{default:r(({row:m})=>[n(f,{type:"primary",link:"",onClick:$=>s(T)(m)},{default:r(()=>[ql]),_:2},1032,["onClick"]),n(f,{type:"primary",link:"",onClick:$=>s(V)(m)},{default:r(()=>[Cl]),_:2},1032,["onClick"]),n(f,{type:"primary",link:"",onClick:$=>s(G)(m)},{default:r(()=>[Pl]),_:2},1032,["onClick"])]),_:1},8,["label"])]),_:1},8,["data"]),s(E)?(d(),j(ll,{key:0,modelValue:s(E),"onUpdate:modelValue":i[12]||(i[12]=m=>be(E)?E.value=m:null),"detail-data":s(O)},null,8,["modelValue","detail-data"])):Y("",!0),s(H)?(d(),j(Jt,{key:1,"show-agency-frequent-flyer-dialog-type":s(H),"header-title":s(Q),"edit-form":s(z),onClose:s(A)},null,8,["show-agency-frequent-flyer-dialog-type","header-title","edit-form","onClose"])):Y("",!0)])),[[K,s(w)]])}}});export{en as default};
