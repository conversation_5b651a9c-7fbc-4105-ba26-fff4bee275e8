import{e as K,h as J,jD as x,jm as Q,jE as D,jF as G,i as S,jG as M,jp as $,jw as y,jq as X,a as B,ht as Y,hu as Z,jH as W,jl as m,jt as b}from"./index-18f146fc.js";function z(n,e){for(var f=-1,a=n==null?0:n.length;++f<a;)if(e(n[f],f,n))return!0;return!1}var j=1,V=2;function C(n,e,f,a,i,r){var s=f&j,g=n.length,u=e.length;if(g!=u&&!(s&&u>g))return!1;var v=r.get(n),d=r.get(e);if(v&&d)return v==e&&d==n;var A=-1,l=!0,O=f&V?new K:void 0;for(r.set(n,e),r.set(e,n);++A<g;){var t=n[A],T=e[A];if(a)var P=s?a(T,t,A,e,n,r):a(t,T,A,n,e,r);if(P!==void 0){if(P)continue;l=!1;break}if(O){if(!z(e,function(w,p){if(!J(O,p)&&(t===w||i(t,w,f,a,r)))return O.push(p)})){l=!1;break}}else if(!(t===T||i(t,T,f,a,r))){l=!1;break}}return r.delete(n),r.delete(e),l}function c(n){var e=-1,f=Array(n.size);return n.forEach(function(a,i){f[++e]=[i,a]}),f}function h(n){var e=-1,f=Array(n.size);return n.forEach(function(a){f[++e]=a}),f}var o=1,k=2,nn="[object Boolean]",en="[object Date]",rn="[object Error]",an="[object Map]",fn="[object Number]",sn="[object RegExp]",ln="[object Set]",un="[object String]",gn="[object Symbol]",vn="[object ArrayBuffer]",An="[object DataView]",q=x?x.prototype:void 0,R=q?q.valueOf:void 0;function tn(n,e,f,a,i,r,s){switch(f){case An:if(n.byteLength!=e.byteLength||n.byteOffset!=e.byteOffset)return!1;n=n.buffer,e=e.buffer;case vn:return!(n.byteLength!=e.byteLength||!r(new D(n),new D(e)));case nn:case en:case fn:return Q(+n,+e);case rn:return n.name==e.name&&n.message==e.message;case sn:case un:return n==e+"";case an:var g=c;case ln:var u=a&o;if(g||(g=h),n.size!=e.size&&!u)return!1;var v=s.get(n);if(v)return v==e;a|=k,s.set(n,e);var d=C(g(n),g(e),a,i,r,s);return s.delete(n),d;case gn:if(R)return R.call(n)==R.call(e)}return!1}var Tn=1,dn=Object.prototype,On=dn.hasOwnProperty;function Pn(n,e,f,a,i,r){var s=f&Tn,g=G(n),u=g.length,v=G(e),d=v.length;if(u!=d&&!s)return!1;for(var A=u;A--;){var l=g[A];if(!(s?l in e:On.call(e,l)))return!1}var O=r.get(n),t=r.get(e);if(O&&t)return O==e&&t==n;var T=!0;r.set(n,e),r.set(e,n);for(var P=s;++A<u;){l=g[A];var w=n[l],p=e[l];if(a)var I=s?a(p,w,l,e,n,r):a(w,p,l,n,e,r);if(!(I===void 0?w===p||i(w,p,f,a,r):I)){T=!1;break}P||(P=l=="constructor")}if(T&&!P){var L=n.constructor,_=e.constructor;L!=_&&"constructor"in n&&"constructor"in e&&!(typeof L=="function"&&L instanceof L&&typeof _=="function"&&_ instanceof _)&&(T=!1)}return r.delete(n),r.delete(e),T}var wn=1,H="[object Arguments]",N="[object Array]",E="[object Object]",pn=Object.prototype,U=pn.hasOwnProperty;function Ln(n,e,f,a,i,r){var s=S(n),g=S(e),u=s?N:M(n),v=g?N:M(e);u=u==H?E:u,v=v==H?E:v;var d=u==E,A=v==E,l=u==v;if(l&&$(n)){if(!$(e))return!1;s=!0,d=!1}if(l&&!d)return r||(r=new y),s||X(n)?C(n,e,f,a,i,r):tn(n,e,u,f,a,i,r);if(!(f&wn)){var O=d&&U.call(n,"__wrapped__"),t=A&&U.call(e,"__wrapped__");if(O||t){var T=O?n.value():n,P=t?e.value():e;return r||(r=new y),i(T,P,f,a,r)}}return l?(r||(r=new y),Pn(n,e,f,a,i,r)):!1}function F(n,e,f,a,i){return n===e?!0:n==null||e==null||!B(n)&&!B(e)?n!==n&&e!==e:Ln(n,e,f,a,F,i)}function _n(n,e){return n!=null&&e in Object(n)}function En(n,e,f){e=Y(e,n);for(var a=-1,i=e.length,r=!1;++a<i;){var s=Z(e[a]);if(!(r=n!=null&&f(n,s)))break;n=n[s]}return r||++a!=i?r:(i=n==null?0:n.length,!!i&&W(i)&&m(s,i)&&(S(n)||b(n)))}function Rn(n,e){return n!=null&&En(n,e,_n)}function Sn(n,e){return F(n,e)}export{F as b,Rn as h,Sn as i,h as s};
