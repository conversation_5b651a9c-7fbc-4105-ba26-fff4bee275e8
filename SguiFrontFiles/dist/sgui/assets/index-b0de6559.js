import{L as D,M as Q,ez as le,q as F,e9 as K,O as X,m as Z,v as j,r as T,ac as O,aI as ee,eP as oe,x as re,B as ie,D as ce,A as $,C as ge,_ as ue,jz as Ne,jA as Te,w as M,o as de,ed as Ce,G as d,H as V,ij as Pe,ik as we,a1 as Se,V as I,a2 as Ee,W as $e,F as J,hV as xe,eQ as be,eG as ae,ec as Be,eB as ke,Y as Oe,jB as se,b6 as ze,as as Re,a5 as Ae,a6 as Fe,J as Me,K as Le,Z as Ve}from"./index-18f146fc.js";import{c as k}from"./strings-8b86a061.js";import{u as Ie}from"./index-0fa663e2.js";const U=Symbol("tabsRootContextKey"),De=D({tabs:{type:Q(Array),default:()=>le([])}}),ve="ElTabBar",Ke=F({name:ve}),je=F({...Ke,props:De,setup(e,{expose:o}){const p=e,z=K(),c=X(U);c||Z(ve,"<el-tabs><el-tab-bar /></el-tabs>");const s=j("tabs"),b=T(),x=T(),u=()=>{let v=0,f=0;const r=["top","bottom"].includes(c.props.tabPosition)?"width":"height",n=r==="width"?"x":"y",B=n==="x"?"left":"top";return p.tabs.every(S=>{var t,P;const _=(P=(t=z.parent)==null?void 0:t.refs)==null?void 0:P[`tab-${S.uid}`];if(!_)return!1;if(!S.active)return!0;v=_[`offset${k(B)}`],f=_[`client${k(r)}`];const w=window.getComputedStyle(_);return r==="width"&&(p.tabs.length>1&&(f-=Number.parseFloat(w.paddingLeft)+Number.parseFloat(w.paddingRight)),v+=Number.parseFloat(w.paddingLeft)),!1}),{[r]:`${f}px`,transform:`translate${k(n)}(${v}px)`}},h=()=>x.value=u();return O(()=>p.tabs,async()=>{await ee(),h()},{immediate:!0}),oe(b,()=>h()),o({ref:b,update:h}),(v,f)=>(re(),ie("div",{ref_key:"barRef",ref:b,class:ce([$(s).e("active-bar"),$(s).is($(c).props.tabPosition)]),style:ge(x.value)},null,6))}});var Ue=ue(je,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-bar.vue"]]);const qe=D({panes:{type:Q(Array),default:()=>le([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),He={tabClick:(e,o,p)=>p instanceof Event,tabRemove:(e,o)=>o instanceof Event},ne="ElTabNav",We=F({name:ne,props:qe,emits:He,setup(e,{expose:o,emit:p}){const z=K(),c=X(U);c||Z(ne,"<el-tabs><tab-nav /></el-tabs>");const s=j("tabs"),b=Ne(),x=Te(),u=T(),h=T(),v=T(),f=T(),r=T(!1),n=T(0),B=T(!1),S=T(!0),t=M(()=>["top","bottom"].includes(c.props.tabPosition)?"width":"height"),P=M(()=>({transform:`translate${t.value==="width"?"X":"Y"}(-${n.value}px)`})),_=()=>{if(!u.value)return;const l=u.value[`offset${k(t.value)}`],i=n.value;if(!i)return;const a=i>l?i-l:0;n.value=a},w=()=>{if(!u.value||!h.value)return;const l=h.value[`offset${k(t.value)}`],i=u.value[`offset${k(t.value)}`],a=n.value;if(l-a<=i)return;const g=l-a>i*2?a+i:l-i;n.value=g},R=async()=>{const l=h.value;if(!r.value||!v.value||!u.value||!l)return;await ee();const i=v.value.querySelector(".is-active");if(!i)return;const a=u.value,g=["top","bottom"].includes(c.props.tabPosition),N=i.getBoundingClientRect(),y=a.getBoundingClientRect(),E=g?l.offsetWidth-y.width:l.offsetHeight-y.height,C=n.value;let m=C;g?(N.left<y.left&&(m=C-(y.left-N.left)),N.right>y.right&&(m=C+N.right-y.right)):(N.top<y.top&&(m=C-(y.top-N.top)),N.bottom>y.bottom&&(m=C+(N.bottom-y.bottom))),m=Math.max(m,0),n.value=Math.min(m,E)},L=()=>{var l;if(!h.value||!u.value)return;e.stretch&&((l=f.value)==null||l.update());const i=h.value[`offset${k(t.value)}`],a=u.value[`offset${k(t.value)}`],g=n.value;a<i?(r.value=r.value||{},r.value.prev=g,r.value.next=g+a<i,i-g<a&&(n.value=i-a)):(r.value=!1,g>0&&(n.value=0))},pe=l=>{const i=l.code,{up:a,down:g,left:N,right:y}=I;if(![a,g,N,y].includes(i))return;const E=Array.from(l.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)")),C=E.indexOf(l.target);let m;i===N||i===a?C===0?m=E.length-1:m=C-1:C<E.length-1?m=C+1:m=0,E[m].focus({preventScroll:!0}),E[m].click(),te()},te=()=>{S.value&&(B.value=!0)},q=()=>B.value=!1;return O(b,l=>{l==="hidden"?S.value=!1:l==="visible"&&setTimeout(()=>S.value=!0,50)}),O(x,l=>{l?setTimeout(()=>S.value=!0,50):S.value=!1}),oe(v,L),de(()=>setTimeout(()=>R(),0)),Ce(()=>L()),o({scrollToActiveTab:R,removeFocus:q}),O(()=>e.panes,()=>z.update(),{flush:"post",deep:!0}),()=>{const l=r.value?[d("span",{class:[s.e("nav-prev"),s.is("disabled",!r.value.prev)],onClick:_},[d(V,null,{default:()=>[d(Pe,null,null)]})]),d("span",{class:[s.e("nav-next"),s.is("disabled",!r.value.next)],onClick:w},[d(V,null,{default:()=>[d(we,null,null)]})])]:null,i=e.panes.map((a,g)=>{var N,y,E,C;const m=a.uid,H=a.props.disabled,W=(y=(N=a.props.name)!=null?N:a.index)!=null?y:`${g}`,G=!H&&(a.isClosable||e.editable);a.index=`${g}`;const he=G?d(V,{class:"is-icon-close",onClick:A=>p("tabRemove",a,A)},{default:()=>[d(Se,null,null)]}):null,ye=((C=(E=a.slots).label)==null?void 0:C.call(E))||a.props.label,_e=!H&&a.active?0:-1;return d("div",{ref:`tab-${m}`,class:[s.e("item"),s.is(c.props.tabPosition),s.is("active",a.active),s.is("disabled",H),s.is("closable",G),s.is("focus",B.value)],id:`tab-${W}`,key:`tab-${m}`,"aria-controls":`pane-${W}`,role:"tab","aria-selected":a.active,tabindex:_e,onFocus:()=>te(),onBlur:()=>q(),onClick:A=>{q(),p("tabClick",a,W,A)},onKeydown:A=>{G&&(A.code===I.delete||A.code===I.backspace)&&p("tabRemove",a,A)}},[ye,he])});return d("div",{ref:v,class:[s.e("nav-wrap"),s.is("scrollable",!!r.value),s.is(c.props.tabPosition)]},[l,d("div",{class:s.e("nav-scroll"),ref:u},[d("div",{class:[s.e("nav"),s.is(c.props.tabPosition),s.is("stretch",e.stretch&&["top","bottom"].includes(c.props.tabPosition))],ref:h,style:P.value,role:"tablist",onKeydown:pe},[e.type?null:d(Ue,{ref:f,tabs:[...e.panes]},null),i])])])}}}),Ge=D({type:{type:String,values:["card","border-card",""],default:""},activeName:{type:[String,Number]},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:Q(Function),default:()=>!0},stretch:Boolean}),Y=e=>Be(e)||ke(e),Ye={[be]:e=>Y(e),tabClick:(e,o)=>o instanceof Event,tabChange:e=>Y(e),edit:(e,o)=>["remove","add"].includes(o),tabRemove:e=>Y(e),tabAdd:()=>!0},Je=F({name:"ElTabs",props:Ge,emits:Ye,setup(e,{emit:o,slots:p,expose:z}){var c,s;const b=j("tabs"),{children:x,addChild:u,removeChild:h}=Ie(K(),"ElTabPane"),v=T(),f=T((s=(c=e.modelValue)!=null?c:e.activeName)!=null?s:"0"),r=async(t,P=!1)=>{var _,w,R;if(!(f.value===t||ae(t)))try{await((_=e.beforeLeave)==null?void 0:_.call(e,t,f.value))!==!1&&(f.value=t,P&&(o(be,t),o("tabChange",t)),(R=(w=v.value)==null?void 0:w.removeFocus)==null||R.call(w))}catch{}},n=(t,P,_)=>{t.props.disabled||(r(P,!0),o("tabClick",t,_))},B=(t,P)=>{t.props.disabled||ae(t.props.name)||(P.stopPropagation(),o("edit",t.props.name,"remove"),o("tabRemove",t.props.name))},S=()=>{o("edit",void 0,"add"),o("tabAdd")};return Ee({from:'"activeName"',replacement:'"model-value" or "v-model"',scope:"ElTabs",version:"2.3.0",ref:"https://element-plus.org/en-US/component/tabs.html#attributes",type:"Attribute"},M(()=>!!e.activeName)),O(()=>e.activeName,t=>r(t)),O(()=>e.modelValue,t=>r(t)),O(f,async()=>{var t;await ee(),(t=v.value)==null||t.scrollToActiveTab()}),$e(U,{props:e,currentName:f,registerPane:u,unregisterPane:h}),z({currentName:f}),()=>{const t=p.addIcon,P=e.editable||e.addable?d("span",{class:b.e("new-tab"),tabindex:"0",onClick:S,onKeydown:R=>{R.code===I.enter&&S()}},[t?J(p,"addIcon"):d(V,{class:b.is("icon-plus")},{default:()=>[d(xe,null,null)]})]):null,_=d("div",{class:[b.e("header"),b.is(e.tabPosition)]},[P,d(We,{ref:v,currentName:f.value,editable:e.editable,type:e.type,panes:x.value,stretch:e.stretch,onTabClick:n,onTabRemove:B},null)]),w=d("div",{class:b.e("content")},[J(p,"default")]);return d("div",{class:[b.b(),b.m(e.tabPosition),{[b.m("card")]:e.type==="card",[b.m("border-card")]:e.type==="border-card"}]},[...e.tabPosition!=="bottom"?[_,w]:[w,_]])}}}),Qe=D({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),Xe=["id","aria-hidden","aria-labelledby"],fe="ElTabPane",Ze=F({name:fe}),et=F({...Ze,props:Qe,setup(e){const o=e,p=K(),z=Oe(),c=X(U);c||Z(fe,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const s=j("tab-pane"),b=T(),x=M(()=>o.closable||c.props.closable),u=se(()=>{var n;return c.currentName.value===((n=o.name)!=null?n:b.value)}),h=T(u.value),v=M(()=>{var n;return(n=o.name)!=null?n:b.value}),f=se(()=>!o.lazy||h.value||u.value);O(u,n=>{n&&(h.value=!0)});const r=ze({uid:p.uid,slots:z,props:o,paneName:v,active:u,index:b,isClosable:x});return de(()=>{c.registerPane(r)}),Re(()=>{c.unregisterPane(r.uid)}),(n,B)=>$(f)?Ae((re(),ie("div",{key:0,id:`pane-${$(v)}`,class:ce($(s).b()),role:"tabpanel","aria-hidden":!$(u),"aria-labelledby":`tab-${$(v)}`},[J(n.$slots,"default")],10,Xe)),[[Fe,$(u)]]):Me("v-if",!0)}});var me=ue(et,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tabs/src/tab-pane.vue"]]);const nt=Le(Je,{TabPane:me}),lt=Ve(me);export{lt as E,nt as a};
