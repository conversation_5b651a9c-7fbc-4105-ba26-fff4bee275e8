import{R as l,bX as N,cg as c,i3 as v,gp as h,i2 as y,i4 as x,i5 as b,i6 as w,bq as O,gy as M,f9 as _,i7 as S}from"./index-18f146fc.js";import{a as L,h as q,q as C,r as V,t as Y,v as B,s as U,n as o}from"./pnrUtils-1bb76fce.js";import{N as T,I as E,T as s,a7 as H,a6 as $,v as A}from"./regular-crs-4d4d60ea.js";const d=(i,t)=>{if(i){if(t==="ADT")return C;if(t==="CHD")return V}return t==="ADT"?Y:t==="CHD"?B:C},z=(i,t)=>{!(t!=null&&t.idCardNumber)&&!(t!=null&&t.documentType)&&(i==="D"?t.documentType="NI_I":t.documentType="PP_P")},ut=i=>({fullName:"",nameSuffix:"",document:{documentType:"",ssrType:"",idCardNumber:"",visaIssueCountry:"CN",passengerNationality:"CN",visaExpiryDate:"",birthday:"",gender:"",docsName:"",holder:""},docaInfoR:{country:"",state:"",city:"",address:"",zip:""},docaInfoD:{country:"",state:"",city:"",address:"",zip:""},osiCtcm:"",ssrCtcm:"",VIPCheck:!1,vipText:"",FOIDCheck:!1,passengerIndex:i+1,foidNICheck:!1,gmjcInfo:{rmk:"",ssrCkinDfmm:!1,ssrCkinDfpp:!1},FQTVText:"",FQTVCheck:!1,segments:[],niForDocs:"",ppForDocs:""}),G=(i,t)=>{const n=(t==null?void 0:t.airlineType)??"D",m=n!=="D";t.airlineType===n?i.document.documentType=t.document.documentType:i.document.documentType=d(m,t.passengerType)[0]},Q=(i,t)=>{i.pasgType=t.passengerType},X=(i,t)=>{i.fullName=(t==null?void 0:t.fullName)??"",i.nameSuffix=(t==null?void 0:t.nameSuffix)??""},J=i=>{var t,n;return(t=i==null?void 0:i.document)!=null&&t.documentType?U(((n=i==null?void 0:i.document)==null?void 0:n.documentType)??""):d(((i==null?void 0:i.airlineType)??"D")!=="D",i.passengerType)[0].value},K=(i,t,n)=>{var m,p,u,a,f,I,D,e,F;i.document={documentType:n||J(t),ssrType:((m=t==null?void 0:t.document)==null?void 0:m.ssrType)??"",idCardNumber:((p=t==null?void 0:t.document)==null?void 0:p.idCardNumber)??"",visaIssueCountry:((u=t==null?void 0:t.document)==null?void 0:u.visaIssueCountry)??"",passengerNationality:((a=t==null?void 0:t.document)==null?void 0:a.passengerNationality)??"",visaExpiryDate:((f=t==null?void 0:t.document)==null?void 0:f.visaExpiryDate)??"",birthday:((I=t==null?void 0:t.document)==null?void 0:I.birthday)??"",gender:((D=t==null?void 0:t.document)==null?void 0:D.gender)??"",holder:((e=t==null?void 0:t.document)==null?void 0:e.holder)??"",docsName:((F=t==null?void 0:t.document)==null?void 0:F.docsName)??""},z((t==null?void 0:t.airlineType)??"D",i.document)},Z=(i,t)=>{i.niForDocs=(t==null?void 0:t.niForDocs)??"",i.ppForDocs=(t==null?void 0:t.ppForDocs)??"",i.FOIDCheck=!!(t!=null&&t.ppForDocs),i.foidNICheck=!!(t!=null&&t.niForDocs)},W=(i,t)=>{t&&t.birthday&&(i.document.birthday=t.birthday)},k=(i,t)=>{var n,m,p,u,a;i.docaInfoR={country:((n=t==null?void 0:t.docaInfoR)==null?void 0:n.country)??"",state:((m=t==null?void 0:t.docaInfoR)==null?void 0:m.state)??"",city:((p=t==null?void 0:t.docaInfoR)==null?void 0:p.city)??"",address:((u=t==null?void 0:t.docaInfoR)==null?void 0:u.address)??"",zip:((a=t==null?void 0:t.docaInfoR)==null?void 0:a.zip)??""}},j=(i,t)=>{var n,m,p,u,a;i.docaInfoD={country:((n=t==null?void 0:t.docaInfoD)==null?void 0:n.country)??"",state:((m=t==null?void 0:t.docaInfoD)==null?void 0:m.state)??"",city:((p=t==null?void 0:t.docaInfoD)==null?void 0:p.city)??"",address:((u=t==null?void 0:t.docaInfoD)==null?void 0:u.address)??"",zip:((a=t==null?void 0:t.docaInfoD)==null?void 0:a.zip)??""}},lt=i=>{const t=i.replace(/，/g,",").split(/[,，]/);return[...new Set(t)].join(",")},g=(i,t)=>{var n;i.osiCtcm=(t==null?void 0:t.osiCtcm)??"",i.ssrCtcm=(t==null?void 0:t.ssrCtcm)??"",i.vipText=((n=t==null?void 0:t.vipTexts)==null?void 0:n.join("、"))??"",i.VIPCheck=!!(t!=null&&t.vipType),i.passengerEmail=((t==null?void 0:t.passengerEmail)??"").replace("//","@")??""},P=(i,t)=>{var m,p;i.gmjcInfo.rmk=(t==null?void 0:t.identityText)??"";const n=((p=(m=(t==null?void 0:t.supplementaryIdentityInfoList)??[])==null?void 0:m[0])==null?void 0:p.text)??"";i.gmjcInfo.ssrCkinDfmm=n==="DFMM",i.gmjcInfo.ssrCkinDfpp=n==="DFPP"},r=(i,t)=>{i.FQTVCheck=!!(t!=null&&t.frequentNumber),i.FQTVText=t==null?void 0:t.frequentNumber,i.segments=((t==null?void 0:t.segments)??[]).filter(n=>!n.openInd&&!n.arnkInd)},at=(i,t)=>{G(i,t),Q(i,t),X(i,t),K(i,t),Z(i,t),W(i,t),k(i,t),j(i,t),g(i,t),P(i,t),r(i,t)},ft=async(i,t)=>{const n=await L(t),m=(t??[]).filter(u=>!u.disabled&&u.segments[0].departureDate);if(!n||!i||!m.length)return!1;const p=l(m[0].segments[0].departureDate).add(6,"month");return l(i).isBefore(p)},ot=i=>{const t=l(l().format("YYYY-MM-DD"));return l(i).isBefore(t)},Nt=()=>(q()??[]).filter(i=>!i.openInd&&!i.arnkInd).map(i=>({...i,frequentNumber:""})),yt=i=>N(i==null?void 0:i.address)&&N(i==null?void 0:i.country)&&N(i==null?void 0:i.state)&&N(i==null?void 0:i.city)&&N(i==null?void 0:i.zip),Tt=i=>{const t=l(l().format("YYYY-MM-DD"));return l(i).isAfter(t)},It=i=>{const t=new Date,n=new Date(i);if(isNaN(n.getTime()))return Number.MAX_VALUE;let m=t.getFullYear()-n.getFullYear();const p=t.getMonth()-n.getMonth();return(p<0||p===0&&t.getDate()<n.getDate())&&m--,m},Et=(i,t,n)=>{(E.test(t)?A:T).test(t)?n():n(new Error(`${c.global.t("app.intlPassengerForm.validate.englishOrChineseOrNumberNameError")}，${c.global.t("app.intlPassengerForm.validate.allEnglishNameError")}`))},R=(i,t)=>{const n={tip:"",min:1,max:0};if(i){const m=v.find(p=>t.split("_")[1]===p.type);m&&(n.min=m.min,n.max=m.max,new RegExp(`^[0-9A-Za-z(~!@#$%^&*]{${m.min},${m.max}}$`).test(i)||((i==null?void 0:i.length)<m.min||(i==null?void 0:i.length)>m.max?n.tip=m.errorTip:n.tip="app.intlPassengerForm.validate.certError"))}return n},tt=i=>{let t="";return(i==null?void 0:i.length)===18&&(t=i.substring(6,14)),t},it=(i,t)=>{if(!_.test(t.document.idCardNumber))return t.document.documentType==="NI_I"?"errBirthDate":"errNIDate";const n=tt(t.document.idCardNumber);return i===o.CHD&&t.document.birthday!==""&&t.document.birthday&&n!==l(t.document.birthday).format("YYYYMMDD")?"niBirthDateDiff":""},nt=(i,t)=>{if(t.document.documentType.includes("NI"))return c.global.t(y.get(it(i,t))??"");if(t.document.documentType.includes("UU")){if(_.test(t.document.idCardNumber))return c.global.t(y.get("documentTypeTip")??"");if(!S.test(t.document.idCardNumber))return c.global.t(y.get("uuNumberTip")??"")}const n=R(t.document.idCardNumber,t.document.documentType);return n.tip?n.min>1?c.global.t(n.tip,{min:n.min,max:n.max}):c.global.t(n.tip,{max:n.max}):""},Dt=(i,t,n)=>{const m=i.form.value;if(m.document.documentType.includes("NI")&&t&&!h(t)){n(c.global.t(y.get(m.document.documentType==="NI_I"?"errBirthDate":"errHKCard")??""));return}const p=m.document.idCardNumber?nt(i.pasgType,m):"";if(p.length>0){n(p);return}n()},et=(i,t,n)=>{const m=i.form.value,p=i.formRef;if(m.document.idCardNumber&&t===""&&m.document.documentType!=="PP_A"){n(new Error(c.global.t("app.intlPassengerForm.validate.required")));return}m.document.documentType.includes("NI")&&m.document.idCardNumber!==""&&p.value.validateField("passengeIdNo"),n()},Ft=(i,t,n)=>{if(i.form.value.VIPCheck&&t&&!x.test(t)){n(new Error(c.global.t("app.intlPassengerForm.validate.enterCorrectJob")));return}n()},Ct=(i,t,n)=>{if(!i.form.value.foidNICheck){n();return}if(t&&!h(t)){n(c.global.t(y.get("errBirthDate")??""));return}n()},ht=(i,t,n)=>{i.isDocsName&&((E.test(t)?A:T).test(t)||n(new Error(`${c.global.t("app.intlPassengerForm.validate.englishOrChineseOrNumberNameError")}，${c.global.t("app.intlPassengerForm.validate.allEnglishNameError")}`)),n()),T.test(t)?n():n(new Error(c.global.t("app.intlPassengerForm.validate.allEnglishNameError")))},_t=(i,t,n)=>{(i.infSuffix?H:E.test(t)?s:T).test(t)||n(new Error(c.global.t("app.intlPassengerForm.validate.cnFormatError"))),n()},st=(i,t,n)=>{i.fullNameEditDisable&&n(),i.isDocsName&&((E.test(t)?s:T).test(t)||n(new Error(`${c.global.t("app.intlPassengerForm.validate.englishOrChineseOrNumberNameError")}，${c.global.t("app.intlPassengerForm.validate.allEnglishNameError")}`)),n()),b.test(t)?n():n(new Error(c.global.t("app.intlPassengerForm.validate.allEnglishNameError")))},At=(i,t,n)=>{const m=i.form.value,p=i.isInter,u=i.pasgType;!t&&u===o.INF&&!p&&!i.isTeamSchedule&&i.formRef.value.clearValidate(["document.visaIssueCountry","document.passengerNationality","document.visaExpiryDate"]);const a=["PP_P","PP_IP","PP_I","PP_A","PP_F","PP_IN"];u===o.INF&&a.includes(m.document.documentType)&&(t&&!$.test(t)&&n(new Error(c.global.t("app.intlPassengerForm.validate.certError"))),n()),i.isSpecialCardTypeF.value=m.document.documentType==="PP_F"&&w.test(t)&&!p;const f=R(t,m.document.documentType);if(f.tip){const I=f.min>1?c.global.t(f.tip,{min:f.min,max:f.max}):c.global.t(f.tip,{max:f.max});n(I)}n()},dt=(i,t,n)=>{i.form.document.idCardNumber&&!t&&(!["PP_A","PP_I"].includes(i.form.document.documentType)||i.pasgFormAdt)?n(new Error(c.global.t("app.intlPassengerForm.required"))):n()},Rt=(i,t,n)=>{(i.idNoType==="PP_P"||i.idNoType==="PP_IP")&&(t==null?void 0:t.length)>15?n(new Error(c.global.t("app.intlPassengerForm.validate.niNumberTip"))):n()},vt=(i,t,n)=>{i.isInter&&t?i.formRef.value.validateField(["issueAt","international","expiryDate"]):i.formRef.value.clearValidate(["issueAt","international","expiryDate"]),n()},xt=(i,t,n)=>{const m=["PP_A","PP_I"].includes(i.form.value.document.documentType)&&[o.ADT,o.CHD].includes(i.pasgType)&&i.visaExpiryDate;i.isSpecialCardTypeF&&n(),i.isInter?i.form.value.document.idCardNumber&&!t&&!m?n(new Error(c.global.t("app.intlPassengerForm.validate.required"))):n():i.pasgType===o.INF&&!i.isInter?i.form.value.document.idCardNumber&&!t?n(new Error(c.global.t("app.intlPassengerForm.validate.required"))):n():t?n():i.form.value.document.idCardNumber&&!t&&!m?n(new Error(c.global.t("app.intlPassengerForm.validate.required"))):n()},bt=(i,t,n)=>{if(t&&!(i.documentTypeList??[]).find(p=>p.value===t)){n(new Error(c.global.t("app.intlPassengerForm.validate.docTypeError")));return}n()},wt=(i,t,n)=>{if(i.form.value.FOIDCheck){if(t.trim()===""){n(new Error(c.global.t("app.intlPassengerForm.validate.required")));return}if(!O.test(t)){n(new Error(c.global.t("app.intlPassengerForm.validate.enterFOIDPPtips")));return}}n()},Ot=(i,t,n)=>{if(t&&!M.test(t)){n(new Error(c.global.t("app.intlPassengerForm.validate.allEnglishNameError")));return}n()},Mt=(i,t,n)=>{if(!t){n(new Error(c.global.t("app.intlPassengerForm.selectDateBirth")));return}if(i.currentData&&l(i.currentData).diff(l(t),"year")>=2&&t){n(new Error(c.global.t("app.intlPassengerForm.validate.babyBirthdayError")));return}n()};export{K as A,Z as B,W as C,k as D,j as E,g as F,P as G,r as H,R as I,ut as a,at as b,st as c,Mt as d,At as e,vt as f,Rt as g,xt as h,Nt as i,ft as j,ot as k,It as l,yt as m,Et as n,bt as o,Dt as p,et as q,Ft as r,ht as s,dt as t,lt as u,_t as v,Ot as w,wt as x,Ct as y,Tt as z};
