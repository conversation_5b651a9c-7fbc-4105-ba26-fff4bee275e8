import{L as T,iC as w,q as b,eN as N,v as M,w as P,x as l,B as V,P as t,F as m,D as o,A as a,y as c,z as i,G as k,a1 as g,E as C,H as y,J as h,C as v,T as $,_ as F,K as I}from"./index-18f146fc.js";const K=T({type:{type:String,values:["success","info","warning","danger",""],default:""},closable:Boolean,disableTransitions:Boolean,hit:Boolean,color:{type:String,default:""},size:{type:String,values:w,default:""},effect:{type:String,values:["dark","light","plain"],default:"light"},round:Boolean}),q={close:n=>n instanceof MouseEvent,click:n=>n instanceof MouseEvent},A=b({name:"ElTag"}),D=b({...A,props:K,emits:q,setup(n,{emit:r}){const _=n,B=N(),s=M("tag"),u=P(()=>{const{type:e,hit:f,effect:E,closable:S,round:z}=_;return[s.b(),s.is("closable",S),s.m(e),s.m(B.value),s.m(E),s.is("hit",f),s.is("round",z)]}),p=e=>{r("close",e)},d=e=>{r("click",e)};return(e,f)=>e.disableTransitions?(l(),V("span",{key:0,class:o(a(u)),style:v({backgroundColor:e.color}),onClick:d},[t("span",{class:o(a(s).e("content"))},[m(e.$slots,"default")],2),e.closable?(l(),c(a(y),{key:0,class:o(a(s).e("close")),onClick:C(p,["stop"])},{default:i(()=>[k(a(g))]),_:1},8,["class","onClick"])):h("v-if",!0)],6)):(l(),c($,{key:1,name:`${a(s).namespace.value}-zoom-in-center`,appear:""},{default:i(()=>[t("span",{class:o(a(u)),style:v({backgroundColor:e.color}),onClick:d},[t("span",{class:o(a(s).e("content"))},[m(e.$slots,"default")],2),e.closable?(l(),c(a(y),{key:0,class:o(a(s).e("close")),onClick:C(p,["stop"])},{default:i(()=>[k(a(g))]),_:1},8,["class","onClick"])):h("v-if",!0)],6)]),_:3},8,["name"]))}});var G=F(D,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/tag/src/tag.vue"]]);const J=I(G);export{J as E,K as t};
