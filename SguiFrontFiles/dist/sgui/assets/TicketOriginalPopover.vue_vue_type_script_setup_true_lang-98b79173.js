import{fH as A,aY as P,b0 as h,ab as W,b1 as U,r as B,w as b,ac as Q,b2 as K,bn as L,f8 as V,f9 as $,q as j,x as k,y as Y,z as q,B as v,G as x,P as g,Q as I,A as a,fb as S,E as J,D as z,J as O,ak as G,b9 as X,al as Z}from"./index-18f146fc.js";import{b as ee}from"./TicketOriginalItem-ed3e7677.js";import{E as te}from"./index-5035a026.js";import{E as se}from"./index-e3a5adf8.js";var oe=(e=>(e.SK_QUERY="SKQuery",e.NFD_QUERY="queryNetPrice",e.FD_QUERY="netFare",e.FSD_QUERY="InternationalPublishedRatesQuery",e))(oe||{}),ie=(e=>(e.AD="adult",e.CH="child",e.IN="baby",e))(ie||{}),re=(e=>(e.PREMIUM_FIRST="PREMIUM_FIRST",e.FIRST="FIRST",e.PREMIUM_BUSINESS="PREMIUM_BUSINESS",e.BUSINESS="BUSINESS",e.PREMIUM_ECONOMY="PREMIUM_ECONOMY",e.ECONOMY="ECONOMY",e))(re||{}),ne=(e=>(e.L="L",e.H="H",e))(ne||{}),M=(e=>(e.SEARCH="SEARCH",e.TAB_TO_SEARCH="TAB_TO_SEARCH",e))(M||{});const ae=A("fastQuery",{state:()=>({historyAirline:{domesticHistory:[],internationalHistory:[]},queryHistory:new Map,targetInfo:{},showManualRefund:!1,rtktDetailInfoWindows:{active:"",list:[]},isOpened:!1}),getters:{getHistoryAirline(e){return e.historyAirline},getRtktDetailInfoWindows:e=>e.rtktDetailInfoWindows},actions:{setHistoryAirLine(e){this.historyAirline=e,localStorage.setItem("agentHistoryAirline",JSON.stringify(e))},setIsOpened(e){this.isOpened=e},setQueryHistory(e,o,i){var n,f;const r=P(o),c=h(r);if((n=this.queryHistory.get(e))!=null&&n.activeTagKey){i||(this.queryHistory.get(e).activeTagKey=c);const y=((f=this.queryHistory.get(e))==null?void 0:f.list.findIndex(T=>h(T)===c))??-1;if(y>-1){this.queryHistory.get(e).list.splice(y,1,r);return}this.queryHistory.get(e).list.push(r);return}const l={activeTagKey:c,list:[r]};this.queryHistory.set(e,l)},setQueryHistoryBySk(e,o){var l,n;const i=P(o.queryForm),r=h(i);if((l=this.queryHistory.get(e))!=null&&l.activeTagKey){if(this.queryHistory.get(e).activeTagKey=r,o.queryType===M.TAB_TO_SEARCH)return;const f=((n=this.queryHistory.get(e))==null?void 0:n.list.findIndex(y=>h(y)===r))??-1;f>-1&&this.queryHistory.get(e).list.splice(f,1),this.queryHistory.get(e).list.unshift(i);return}const c={activeTagKey:r,list:[i]};this.queryHistory.set(e,c)},delQueryHistory(e,o){var r,c,l;const i=((r=this.queryHistory.get(e))==null?void 0:r.list.findIndex(n=>h(n)===h(o)))??-1;if(i>-1){if(((c=this.queryHistory.get(e))==null?void 0:c.activeTagKey)===h(o)&&this.queryHistory.get(e).list.length>1){const n=i+1<(((l=this.queryHistory.get(e))==null?void 0:l.list.length)??0)?i+1:0;this.queryHistory.get(e).activeTagKey=h(this.queryHistory.get(e).list[n])}this.queryHistory.get(e).list.splice(i,1),this.queryHistory.get(e).list.length||(this.queryHistory.get(e).activeTagKey="")}},setFastQueryTargetInfo(e){this.targetInfo={queryFlag:!0,targetInfo:e}},closeFastQuery(){this.targetInfo={queryFlag:!1,targetInfo:{}},this.setIsOpened(!1)},setShowManualRefund(e){this.showManualRefund=e},setActiveRtktDetailInfoWindows(e){this.rtktDetailInfoWindows.active=e},setRtktDetailInfoWindowsList(e){const o=e.id;this.setActiveRtktDetailInfoWindows(o),this.rtktDetailInfoWindows.list.push({...e,id:o,isShow:!0})},delRtktDetailInfoWindowsList(e){this.rtktDetailInfoWindows.list=this.rtktDetailInfoWindows.list.filter(o=>o.id!==e)}}}),He=ae,le=(e,o)=>{var p,R;const{t:i}=W(),{copy:r,isSupported:c}=U({legacy:!0}),l=B((p=e.tktInfo)==null?void 0:p.showTktPopover),n=B(),f=(R=navigator==null?void 0:navigator.userAgent)==null?void 0:R.toLowerCase(),y=b(()=>f==null?void 0:f.includes("electron/")),T=b(()=>{var t;return typeof((t=window.electronAPI)==null?void 0:t.openTicketOriginalWindow)=="function"}),w=b(()=>F(e.ticketNumber)),m=b(()=>e.secondFactor),C=()=>{if(!T.value)return;const t=[{ticketNo:w.value??"",secondFactor:m.value??{}}];window.electronAPI.openTicketOriginalWindow(JSON.stringify(t))},F=t=>{const d=P(t),u=d[3]!=="-"?`${d.slice(0,3)}-${d.slice(3)}`:d;return u.length>14?u.slice(0,14):d},N=()=>{l.value=!1,o("close-popover",e.tktIndex)},_=()=>{const t=e.formTicket;if(t.secondFactorValue){if(t.secondFactorType==="PNR"&&!L.test(t.secondFactorValue))return!1;if(t.secondFactorType==="name"&&!V.test(t.secondFactorValue))return!1;if(t.secondFactorType==="certificate"&&t.secondFactorCode==="NI"&&!$.test(t.secondFactorValue))return!1}else return t.secondFactorType==="PNR"||t.secondFactorType==="name",!1;return!0},H=async()=>{if(e.isOldFare&&(o("fare-vaild"),!_()))return N(),!1;setTimeout(()=>{var t,d,u;(u=(d=(t=n.value)==null?void 0:t.popperRef)==null?void 0:d.popperInstanceRef)==null||u.forceUpdate()},100)},s=t=>{c&&(r(t),K({message:i("app.batchRefund.copySuccess"),type:"success"}))};return Q(()=>{var t;return(t=e.tktInfo)==null?void 0:t.showTktPopover},()=>{var t;l.value=(t=e.tktInfo)==null?void 0:t.showTktPopover},{immediate:!0,deep:!0}),{ticketNo:w,secondFactor:m,showTktRef:n,showTktPopover:l,closePopover:N,openPopover:H,copyInfo:s,isClient:y,isRequiredClientVersion:T,openTicketOriginalWindow:C}},ce={key:0},ue={class:"font-bold text-base"},fe={key:1,class:"text-brand-2 text-base font-bold leading-normal cursor-pointer"},de={key:2,class:"text-brand-2 text-xs font-bold leading-tight cursor-pointer w-[125px]"},pe={key:3,class:"text-brand-2 text-xs font-normal leading-tight cursor-pointer","data-gid":"02080114"},ye={class:"flex flex-col"},he={class:"flex justify-between items-center"},ke={class:"relative h-[20px] flex items-center justify-center"},ge={class:"text-gray-1 font-bold text-[16px] leading-snug"},ve=g("i",{class:"iconfont icon-windowed mr-[2px]"},null,-1),Ie={key:0,class:"w-full max-h-[600px] overflow-x-hidden overflow-y-auto mt-2.5"},Re=j({__name:"TicketOriginalPopover",props:{tktInfo:{},isInternational:{type:Boolean},tktIndex:{},ticketNumber:{},secondFactor:{},outClass:{default:""},refundClassType:{},level:{default:1},queryType:{},conjunctionTicketNos:{},ticketNumberColorClass:{},prefix:{},title:{type:Boolean},isCdsTicket:{type:Boolean},formTicket:{},isOldFare:{type:Boolean,default:!1}},emits:["close-popover"],setup(e,{emit:o}){const i=B(!1),r=()=>{i.value=!0},c=()=>{i.value=!1},l=e,n=o,{ticketNo:f,secondFactor:y,showTktRef:T,showTktPopover:w,closePopover:m,openPopover:C,copyInfo:F,isClient:N,isRequiredClientVersion:_,openTicketOriginalWindow:H}=le(l,n);return(s,p)=>{const R=Z,t=te,d=se;return k(),Y(d,{ref_key:"showTktRef",ref:T,visible:a(w),"onUpdate:visible":p[2]||(p[2]=u=>X(w)?w.value=u:null),placement:"right",teleported:!0,"popper-class":`tkt-crs-popper ${s.outClass}tkt-crs-popper${s.tktIndex} max-h-[calc(100vh_-_20px)]`,"popper-options":{modifiers:[{name:"preventOverflow",options:{padding:10,rootBoundary:"viewport"}}]},width:578,trigger:"click",onBeforeLeave:a(m),onBeforeEnter:a(C),onShow:r,onHide:c},{reference:q(()=>{var u;return[(u=s.tktInfo)!=null&&u.etNumber?(k(),v("span",ce,[x(R,{link:"",type:"primary"},{default:q(()=>[g("span",ue,I(a(S)(s.tktInfo.etNumber)),1)]),_:1}),g("em",{class:"iconfont icon-copy text-brand-2 relative top-[2px] cursor-pointer",onClick:p[0]||(p[0]=J(D=>{var E;return a(F)(((E=s.tktInfo)==null?void 0:E.etNumber)??"")},["stop"]))})])):s.refundClassType==="0"?(k(),v("span",fe,I(a(S)(s.ticketNumber??"")),1)):s.refundClassType==="1"?(k(),v("span",de,I(a(S)(s.ticketNumber??"")),1)):(k(),v("span",pe,[s.prefix?(k(),v("span",{key:0,class:z(["prefix",s.ticketNumberColorClass])},I(s.prefix),3)):O("",!0),G(" "+I(s.title&&s.ticketNumber?s.$t("app.fareQuery.freightate.details"):a(S)(s.ticketNumber??"")),1)]))]}),default:q(()=>[g("div",ye,[g("div",he,[g("div",ke,[g("div",ge,I(s.$t("app.original.ticket"))+" [DETR]",1)]),a(N)&&a(_)?(k(),v("div",{key:0,class:"text-brand-2 flex flex-inline text-xs font-normal leading-tight cursor-pointer",onClick:p[1]||(p[1]=(...u)=>a(H)&&a(H)(...u))},[ve,g("div",null,I(s.$t("app.fastQuery.windowing")),1)])):O("",!0)]),x(t,{"border-style":"dashed"})]),i.value?(k(),v("div",Ie,[x(ee,{"ticket-info":{ticketNo:a(f)??"",secondFactor:a(y)??{}},"is-query":!0},null,8,["ticket-info"])])):O("",!0)]),_:1},8,["visible","popper-class","onBeforeLeave","onBeforeEnter"])}}});export{re as C,oe as F,M as I,ie as P,ne as S,Re as _,He as u};
