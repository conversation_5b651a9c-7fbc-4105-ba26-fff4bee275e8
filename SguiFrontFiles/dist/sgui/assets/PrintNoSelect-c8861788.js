import{fH as Q,r as y,ab as R,a9 as F,aF as U,w as E,ac as $,av as z,i0 as A,aY as N,q as j,ae as q,a5 as G,x as k,y as D,z as w,B as C,ai as H,aj as L,A as u,J,ak as K,Q as Y,b9 as W,D as X,H as Z}from"./index-18f146fc.js";import{K as ee}from"./ticketOperationApi-fe1536da.js";import{E as te,a as ae}from"./index-d7d71e18.js";import{_ as ne}from"./_plugin-vue_export-helper-c27b6911.js";const le=Q("printNo",()=>{const n=y(new Map);return{printNosByOffice:n,setPrintNosByOffice:(f,c)=>{n.value.set(f,c)},deletePrintNosExceptOffice:f=>{const c=[];for(const[i]of n.value)i!==f&&c.push(i);for(const i of c)n.value.delete(i)}}}),ie=(n,g)=>{const{t:o}=R(),f=le(),c=F(),{printNosByOffice:i}=U(f),p=y(n.modelValue),d=y([]),t=y([]),I=y([]),m=y(n.ticketType??""),P=y(!1),O=E(()=>{var e;return((e=c.state.user)==null?void 0:e.crsSystem)??!1}),b=E(()=>`printNo-select ${n.selectClass??""}`),h=(e,s)=>{var l,v,_;const a=((l=c.state.user)==null?void 0:l[e])??"";return a||(((_=(((v=c.state.user)==null?void 0:v[s])??"").split(";"))==null?void 0:_[0])??"")},S=e=>{const s=(i.value.get(e)??[]).filter(a=>a.type)??[];d.value=s.map(a=>({value:a.devno,label:a.devno,type:`${a.devno} ${o("app.agentTicketQuery.ticketMachine.type_"+a.type)}`})),t.value=N(d.value),I.value=N(t.value),r(n.ticketType??""),n.needDistinguish&&(n.isInter?t.value=t.value.filter(a=>!a.type.includes(o("app.agentTicketQuery.ticketMachine.domestic"))):t.value=t.value.filter(a=>a.type.includes(o("app.agentTicketQuery.ticketMachine.domestic"))))},V=async()=>{var s;if(!O.value)return;const e=h("defaultOffice","office");if(!i.value.get(e)||((s=i.value.get(e))==null?void 0:s.length)===0)try{P.value=!0;const a=z("10270401"),l=(await ee({office:e},a,!0)).data.value;await f.setPrintNosByOffice((l==null?void 0:l.office.office)??e,(l==null?void 0:l.ticketMachines)??[])}finally{P.value=!1}await f.deletePrintNosExceptOffice(e),await S(e)},T=()=>{n.isInter?t.value=t.value.filter(e=>!e.type.includes(o("app.agentTicketQuery.ticketMachine.domestic"))):t.value=t.value.filter(e=>e.type.includes(o("app.agentTicketQuery.ticketMachine.domestic")))},x=e=>{var s,a,l;if(e){let v=[];A.test(e.trim())?v=(d.value??[]).filter(_=>_.value===e):v=(d.value??[]).filter(_=>_.type.includes(e)),t.value=N(v),p.value=((s=t.value)==null?void 0:s.length)>0?(l=(a=t.value)==null?void 0:a[0])==null?void 0:l.value:e.toUpperCase()}else t.value=N(d.value);n.needDistinguish&&T(),t.value=(t.value??[]).filter(v=>v.type.includes(m.value))},r=e=>{e&&(p.value=n.modelValue,m.value=N(e),e==="ARL"&&(m.value=o("app.pnrManagement.paymentMethod.currentTicketReal")),n.needDistinguish&&(t.value=N(d.value),T()),t.value=(t.value??[]).filter(s=>s.type.includes(m.value)))},B=()=>{var s;const e=t.value.find(a=>{var l;return a.value===((l=p.value)==null?void 0:l.trim())});return e?(s=e.type)!=null&&s.includes(o("app.agentTicketQuery.ticketMachine.domestic"))?"D":"I":""},M=()=>{g("update:modelValue",p.value),g("deliverPrintType",B())};return $(()=>n.modelValue,()=>{p.value=n.modelValue}),{printNo:p,printNos:t,loading:P,selectClass:b,filterPrintNo:x,setPrintNo:M,filterPrintToTicketType:r,init:V}},se=ie,oe={key:0},ce={key:1,class:"inline-block w-[12px]"},re=j({__name:"PrintNoSelect",props:{modelValue:{},selectClass:{},isInter:{type:Boolean},needDistinguish:{type:Boolean},ticketType:{}},emits:["update:modelValue","deliverPrintType"],setup(n,{expose:g,emit:o}){const f=n,c=o,{printNo:i,printNos:p,loading:d,selectClass:t,filterPrintNo:I,setPrintNo:m,filterPrintToTicketType:P,init:O}=se(f,c);return g({filterPrintToTicketType:P}),(b,h)=>{const S=Z,V=te,T=ae,x=q("trimUpper");return G((k(),D(T,{modelValue:u(i),"onUpdate:modelValue":h[0]||(h[0]=r=>W(i)?i.value=r:null),class:X(u(t)),"popper-class":"printNo-select-option",placeholder:" ",loading:u(d),filterable:"",clearable:"","filter-method":u(I),onFocus:u(O),onBlur:u(m)},{default:w(()=>[(k(!0),C(H,null,L(u(p),r=>(k(),D(V,{key:r.value,label:r.label,value:r.value},{default:w(()=>[u(p).some(B=>B.value===u(i))?(k(),C("span",oe,[r.value===u(i)?(k(),D(S,{key:0,size:12,class:"iconfont icon-right-line"})):(k(),C("span",ce))])):J("",!0),K(" "+Y(r.type),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","class","loading","filter-method","onFocus","onBlur"])),[[x]])}}});const ve=ne(re,[["__scopeId","data-v-62e257c7"]]);export{ve as P};
