import{cg as Ae,ab as Ce,a9 as je,w as ce,r as D,b6 as Ve,o as $e,ac as Fe,q as re,ae as Ie,x as m,y as E,z as y,P as e,Q as d,A as a,B as k,G as g,ai as q,aj as Y,D as R,a$ as Oe,J as X,a5 as O,ak as U,ag as Pe,H as Me,ah as Ee,al as fe,am as me,an as ge,bb as Ue,C as Be,s as le,aI as ze,fp as He,av as be,bQ as _e,b2 as Le,b9 as ke,b3 as Ge}from"./index-18f146fc.js";import{P as qe}from"./PrintNoSelect-c8861788.js";import{p as De,f as Ye,h as Qe}from"./refundUtil-51abb28e.js";import{D as Z,b as ue}from"./common-d870312b.js";import{a5 as Je,I as Ke,l as we,D as Xe,P as We,f as Ze,g as Re,h as Te,i as et,j as tt,k as at,d as st,e as nt,b as it,c as Ne,S as ot}from"./regular-crs-4d4d60ea.js";import{E as lt,a as rt}from"./index-d7d71e18.js";import{E as dt,a as ut}from"./index-c5503643.js";import{E as ct}from"./index-6ea30548.js";import{E as Se,a as pt}from"./index-9b639e2a.js";import{E as ft}from"./index-2494e7da.js";import{_ as he}from"./_plugin-vue_export-helper-c27b6911.js";import{E as xe}from"./index-385c3d86.js";import{t as mt}from"./throttle-39cac876.js";import{E as gt}from"./index-5035a026.js";import{d as ht,J as xt}from"./ticketOperationApi-fe1536da.js";const H=Ae.global.t,ci=[{label:H("app.agentTicketRefund.pay.cash"),value:"CASH"},{label:H("app.agentTicketRefund.pay.tc"),value:"TC"},{label:H("app.agentTicketRefund.pay.check"),value:"CHECK"},{label:H("app.agentTicketRefund.pay.gr"),value:"GR"},{label:H("app.agentTicketRefund.pay.ef"),value:"EF"}];H("app.agentTicketRefund.passenger.adt"),H("app.agentTicketRefund.passenger.chd"),H("app.agentTicketRefund.passenger.chd"),H("app.agentTicketRefund.passenger.yth"),H("app.agentTicketRefund.passenger.inf");const pi=[{label:H("app.pnrManagement.paymentMethod.domestic"),value:"D"},{label:H("app.pnrManagement.paymentMethod.international"),value:"I"}],fi="TYN202",mi="UP6225819300614137",yt=(o,c,v)=>{(Je.test(c)||Ke.test(c))&&v(H("app.agentTicketRefund.refundPsgNameError")),v()},vt=o=>{const{t:c}=Ce(),v=je(),t=ce(()=>{var n;return(n=v.state.user)==null?void 0:n.entityType}),b=D(),i=Ve({...o.data}),_=D([]),l=D([]),x=ce(()=>!["CDS","GPCDS"].includes(i.ticketManagementOrganizationCode??""));!o.disabled&&we.test(i.name)&&(i.name="");const T=(n,u,s)=>{i.payType==="TC"&&(u.length===0?s(c("app.agentTicketRefund.creditCardNotEmpty")):!o.isDragonBoatOffice&&!et.test(u)?s(c("app.agentTicketRefund.creditCardInput")):o.isDragonBoatOffice&&!tt.test(u)&&s(c("app.agentTicketRefund.dragonBoatOfficeInput"))),s()},P=(n,u,s)=>{var f;(u??"")!==""?((f=b.value)==null||f.validateField("remarkCode"),(i.remarkCode??"")==="IC"?at.test(`${i.remarkCode}${u}`)?s():s(c("app.agentTicketRefund.remarkIC")):(i.remarkCode??"")!==""&&!Te.test(`${i.remarkCode}${u}`)?s(c("app.agentTicketRefund.remarkHint")):s()):i.remarkCode?s(c("app.agentTicketRefund.remarkHint")):s()},C=(n,u,s)=>{(i.remark??"")!==""&&!Re.test(u??"")?s(c("app.agentTicketRefund.formatError")):s()},S=(n,u,s)=>{var h;const f=n.field.split(".")[1];i.taxs[Number(f)].taxAmount!==""&&u===""?s(c("app.agentTicketRefund.taxes")):(i.taxs[Number(f)].taxAmount===""&&u===""&&((h=b.value)==null||h.clearValidate(`taxs.${f}.taxAmount`)),s())},A=(n,u,s)=>{var h;const f=n.field.split(".")[1];i.taxs[Number(f)].taxCode!==""&&i.currency==="CNY"&&!st.test(u)?s(c("app.agentTicketRefund.cnyTaxAmount")):i.taxs[Number(f)].taxCode!==""&&i.currency!=="CNY"&&!nt.test(u)?s(c("app.agentTicketRefund.taxAmount")):(i.taxs[Number(f)].taxCode===""&&u===""&&((h=b.value)==null||h.clearValidate(`taxs.${f}.taxCode`)),s())},I=(n,u,s)=>{i.currency==="CNY"&&u&&!it.test(u)?s(c("app.agentTicketRefund.cnyTip")):i.currency!=="CNY"&&u&&!Ne.test(u)?s(c("app.agentTicketRefund.onlySupportDigits")):s()},j={ticketManagementOrganizationCode:[{required:!0,message:c("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:["change","blur"]}],prntNo:[{required:!0,message:c("app.ticketStatus.deviceNumNull"),trigger:["change","blur"]},{pattern:Xe,trigger:["change","blur"],message:c("app.ticketStatus.deviceError")}],currency:[{required:!0,message:c("app.agentTicketRefund.currencyNotEmpty"),trigger:"change"}],payType:[{required:!0,message:c("app.agentTicketRefund.paymentSel"),trigger:"change"},{pattern:We,message:c("app.agentTicketRefund.paymentInput"),trigger:"change"}],price:[{validator:I,trigger:"change"}],taxValue:[{validator:A,trigger:"change"}],taxName:[{pattern:Ze,message:c("app.agentTicketRefund.taxes"),trigger:"change"},{validator:S,trigger:"change"}],rate:[{validator:I,trigger:"change"}],commision:[{validator:I,trigger:"change"}],psgName:[{validator:yt,trigger:"change"}],creditCard:[{validator:T,required:!0,trigger:"change"}],remarkCode:[{pattern:Re,message:c("app.agentTicketRefund.formatError"),trigger:["change","blur"]},{validator:C,trigger:["change","blur"]}],remark:[{validator:P,trigger:["change","blur"]}],remarkInfo:[{pattern:Te,message:c("app.agentTicketRefund.remarkHint"),trigger:["change","blur"]}],totalAmount:[{validator:I,trigger:"change"}],netRefund:[{required:!0,message:c("app.agentTicketRefund.prntNoNotEmpty"),trigger:["change","blur"]},{validator:I,trigger:"change"}]},N=()=>{const{totalAmount:n,totalTaxs:u,otherDeduction:s,commision:f,commisionRate:h}=i;if(!J())if(f){const F=new Z(ue(Number(n),Number(u),"+")).minus(ue(Number(s),Number(f),"+")).toNumber();i.netRefund=Number(F)}else{const F=new Z(Number(n)).times(Number(h)).div(100).toNumber(),te=new Z(ue(Number(n),Number(u),"+")).minus(ue(Number(s),Number(F),"+")).toNumber();i.netRefund=Number(te)}},$=()=>{i.commisionRate||(i.commision="")},M=()=>{let n=new Z(0);i.taxs.forEach((u,s)=>{var f;(f=b.value)==null||f.validateField(`taxs.${s}.taxAmount`).then(h=>{h&&u.taxAmount?(i.taxs[s].taxAmount=u.taxAmount,n=n.add(new Z(u.taxAmount))):h&&!u.taxAmount&&(n=n.add(new Z(0))),i.totalTaxs=n.toString(),N()})})},B=async()=>{const n=[];i.taxs.forEach((u,s)=>{var f,h;n.push((f=b.value)==null?void 0:f.validateField(`taxs.${s}.taxCode`)),n.push((h=b.value)==null?void 0:h.validateField(`taxs.${s}.taxAmount`))}),await Promise.all(n),M()},G=n=>n&&!Ne.test(n.toString()),J=()=>{const{totalAmount:n,otherDeductionRate:u,otherDeduction:s,commision:f,commisionRate:h}=i;return G(n??"")||G(u??"")||G(s??"")||G(f??"")||G(h??"")},r=n=>{J()||(n==="otherDeductionRate"&&(i.otherDeductionRate??"")!==""&&(i.otherDeductionRate=Number(i.otherDeductionRate),i.otherDeduction=new Z(Number(i.totalAmount)).times(Number(i.otherDeductionRate)).div(100).toString()),n==="commisionRate"&&i.commisionRate&&(i.commision=new Z(Number(i.totalAmount)).times(Number(i.commisionRate)).div(100).toString()),N())},p=n=>{n.target.value!==""&&!De.some(u=>u.label===n.target.value)&&(i.payType=n.target.value)},z=n=>{var u;return(((u=i.checkedSeg)==null?void 0:u.filter(s=>s.etSegIndex===n.etSegIndex))??[]).length>0},W=async()=>{b.value&&(await b.value.resetFields(),i.etTagNew=o.data.etTagNew,i.checkedSeg=o.data.checkedSeg,i.couponNos=o.couponNoHistory?JSON.parse(JSON.stringify(o.couponNoHistory)):[],i.taxs=JSON.parse(JSON.stringify(o.taxsHistory)),_.value=[],M(),r(""))},K=async()=>{if(!b.value)return!1;try{return await b.value.validate()}catch{return!1}},w=()=>{i.taxs.length!==27&&(i.taxs=i.taxs.length>=25&&i.taxs.length<27?i.taxs.concat(new Array(27-i.taxs.length).fill({taxCode:"",taxAmount:""})).map(n=>({...n})):i.taxs.concat(new Array(5).fill({taxCode:"",taxAmount:""})).map(n=>({...n})))},ee=()=>i,V=()=>(_.value=new Array(4).fill(""),(i.couponNos??[]).some(n=>n)?(i.couponNos??[]).forEach((n,u)=>{n&&!ot.test(n)&&(_.value[u]=c("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:4}))}):_.value[0]=c("app.pnrManagement.validate.required"),_.value.every(n=>!n)),se=()=>{x.value||(i.prntNo="")},ne=n=>{var s,f;return(l.value??[]).some(h=>n===h.value)&&n?n:((f=(s=l.value)==null?void 0:s[0])==null?void 0:f.value)??""},L={BSP:{label:c("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:c("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:c("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:c("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:c("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:c("app.agentTicketQuery.OWNTicket"),value:"ARL"}},ie=()=>{var n,u,s,f,h,F,te,de,ye,ve;((n=t.value)!=null&&n.includes("$$$")||(u=t.value)!=null&&u.includes("BSP"))&&(l.value.push(L.BSP),l.value.push(L.GPBSP)),!((s=t.value)!=null&&s.includes("BSP"))&&((f=t.value)!=null&&f.includes("GP"))&&l.value.push(L.GPBSP),((h=t.value)!=null&&h.includes("$$$")||(F=t.value)!=null&&F.includes("BOP"))&&l.value.push(L.BOPBSP),((te=t.value)!=null&&te.includes("$$$")||(de=t.value)!=null&&de.includes("CDS"))&&(l.value.push(L.CDS),l.value.push(L.GPCDS)),((ye=t.value)!=null&&ye.includes("$$$")||(ve=t.value)!=null&&ve.includes("本票"))&&l.value.push(L.ARL),i.ticketManagementOrganizationCode=ne(i.ticketManagementOrganizationCode??"")};$e(async()=>{ie()});const oe=()=>{var n;(n=b.value)==null||n.validate(async u=>{})};return Fe(()=>o.disabled,()=>{!o.disabled&&we.test(i.name)&&(i.name="")}),{segmentErrorMessage:_,formData:i,formRef:b,rules:j,ticketOrganizationList:l,ticketOrganizationListEnum:L,checkTax:B,countAmount:r,bindPaymentValue:p,isCheckSeg:z,resetForm:W,validate:K,getFormDate:ee,addTax:w,commisionRateChange:$,validSegment:V,isShowPrintNo:x,changeTicketManagementOrganizationCode:se,getCurrency:oe}},bt=vt,ae=o=>(me("data-v-cdcb159c"),o=o(),ge(),o),_t={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},kt={class:"self-stretch justify-start items-start gap-5 inline-flex"},wt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Rt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Tt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Nt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ct={class:"w-[84px] text-gray-3 text-xs shrink-0"},$t={key:0,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},It={key:1,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Et={key:0,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Dt={class:"w-[84px] text-gray-3 text-xs shrink-0"},St={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},At={key:1,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},jt={class:"carType-option-panel"},Vt={key:2,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ft={class:"w-[84px] text-gray-3 text-xs shrink-0"},Ot={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Pt={key:3,class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Mt={class:"self-stretch justify-start items-center gap-5 inline-flex"},Ut={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Bt={class:"w-[84px] text-gray-3 text-xs shrink-0"},zt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Ht={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Lt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Gt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},qt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Yt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Qt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Jt={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Kt={class:"w-[84px] text-gray-3 text-xs shrink-0"},Xt={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Wt={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},Zt={class:"self-stretch justify-start items-start gap-5 inline-flex"},ea={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ta={class:"w-[84px] text-gray-3 text-xs shrink-0"},aa={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},sa={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},na={class:"w-[84px] text-gray-3 text-xs shrink-0"},ia={class:"text-gray-2 text-xs font-bold whitespace-nowrap w-[150px]"},oa=ae(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),la={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},ra={class:"w-[84px] text-gray-3 text-xs shrink-0"},da={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},ua={class:"self-stretch justify-start items-start gap-5 inline-flex"},ca={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},pa={key:0,class:"grow shrink basis-0 min-h-[32px] justify-start flex mb-[10px]"},fa={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0 refund-segment"},ma={key:1,class:"grow shrink basis-0 min-h-[32px] justify-start gap-1 flex mb-[10px]"},ga={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0"},ha={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},xa={class:"justify-start items-center gap-4 flex"},ya={class:"text-gray-2 text-xs leading-tight"},va={key:1,class:"flex-col"},ba={class:"flex items-center h-[20px] text-gray-3 font-normal text-xs leading-tight"},_a={class:"justify-start items-center gap-4 flex h-[20px]"},ka={class:"text-gray-2 text-xs leading-tight"},wa={class:"self-stretch justify-start items-start gap-5 inline-flex"},Ra={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ta={key:0,class:"not-required-tip"},Na={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ca={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},$a=ae(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},[e("em")],-1)),Ia={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},Ea={class:"self-stretch justify-start items-start gap-5 inline-flex"},Da={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Sa={class:"w-full mb-[10px]"},Aa={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},ja={class:"ml-[20px]"},Va={class:"text-gray-2 font-[700]"},Fa={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},Oa={class:"w-[20px] text-gray-3 text-xs shrink-0"},Pa={class:"w-[40px] mr-[6px] shrink-0"},Ma={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},Ua={class:"self-stretch justify-start items-start gap-5 inline-flex"},Ba={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},za={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},Ha=ae(()=>e("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),La={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},Ga=ae(()=>e("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px] min-w-[11px]"},"%",-1)),qa={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Ya={class:"self-stretch justify-start items-center gap-5 inline-flex"},Qa={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},Ja={class:"w-[84px] text-gray-3 text-xs shrink-0"},Ka={class:"justify-start items-center flex text-gray-2 text-xs relative"},Xa=ae(()=>e("span",{class:"iconfont icon-info-circle-line absolute left-[-40px]"},null,-1)),Wa={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},Za={class:"text-gray-2 font-[700]"},es=ae(()=>e("span",null,[e("i",{class:"iconfont icon-info-circle-line text-[20px] font-normal ml-[10px] text-gray-4"})],-1)),ts={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] print:basis-1/4"},as={key:1},ss=ae(()=>e("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] print:hidden"},[e("em")],-1)),ns=re({__name:"RefundForm",props:{data:{},refundTickets:{},disabled:{type:Boolean,default:!1},isSupplementRefund:{type:Boolean,default:!1},taxsHistory:{},couponNoHistory:{}},setup(o,{expose:c}){const v=o,{formData:t,formRef:b,rules:i,ticketOrganizationList:_,ticketOrganizationListEnum:l,checkTax:x,countAmount:T,bindPaymentValue:P,isCheckSeg:C,resetForm:S,validate:A,getFormDate:I,addTax:j,commisionRateChange:N,segmentErrorMessage:$,validSegment:M,isShowPrintNo:B,changeTicketManagementOrganizationCode:G,getCurrency:J}=bt(v);return c({resetForm:S,validate:A,getFormDate:I,validSegment:M}),(r,p)=>{const z=Me,W=lt,K=rt,w=dt,ee=ct,V=Ee,se=Se,ne=pt,L=ft,ie=fe,oe=ut,n=Ie("trimUpper");return m(),E(oe,{ref_key:"formRef",ref:b,model:a(t),class:"refund-form","label-position":"left","require-asterisk-position":"right"},{default:y(()=>{var u;return[e("div",_t,[e("div",kt,[e("div",wt,[e("div",Rt,d(r.$t("app.agentTicketRefund.refundTicketNumber")),1),e("div",Tt,d(a(t).refundNo),1)]),e("div",Nt,[e("div",Ct,d(r.$t("app.agentTicketRefund.rtType")),1),r.isSupplementRefund?(m(),k("div",$t,d(a(t).refundType),1)):(m(),k("div",It,d(a(t).international??"-"),1))]),r.isSupplementRefund?(m(),k("div",At,[g(w,{prop:"ticketManagementOrganizationCode",rules:a(i).ticketManagementOrganizationCode,label:r.$t("app.agentTicketQuery.ticketOrganization")},{default:y(()=>[g(K,{modelValue:a(t).ticketManagementOrganizationCode,"onUpdate:modelValue":p[0]||(p[0]=s=>a(t).ticketManagementOrganizationCode=s),class:"ticket-management-organization",disabled:a(t).ticketManagementOrganizationCode==="",placeholder:a(t).ticketManagementOrganizationCode===""?r.$t("app.agentTicketQuery.noData"):"",onChange:a(G)},{default:y(()=>[(m(!0),k(q,null,Y(a(_),s=>(m(),E(W,{key:s.value,label:s.label,value:s.value},{default:y(()=>[e("div",jt,[e("div",{class:R(a(t).ticketManagementOrganizationCode===s.value?"show-select":"hidden-select")},[g(z,null,{default:y(()=>[g(a(Oe))]),_:1})],2),e("span",null,d(s.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["rules","label"])])):(m(),k("div",Et,[e("div",Dt,d(r.$t("app.agentTicketQuery.ticketOrganization")),1),e("div",St,d(((u=a(l)[a(t).ticketManagementOrganizationCode??""])==null?void 0:u.label)||"-"),1)])),r.isSupplementRefund?(m(),k("div",Pt,[a(B)?(m(),E(w,{key:0,prop:"prntNo",rules:a(i).prntNo,label:r.$t("app.ticketStatus.deviceNum")},{default:y(()=>[g(qe,{modelValue:a(t).prntNo,"onUpdate:modelValue":[p[1]||(p[1]=s=>a(t).prntNo=s),p[2]||(p[2]=s=>a(b).validateField("prntNo"))],"select-class":""},null,8,["modelValue"])]),_:1},8,["rules","label"])):X("",!0)])):(m(),k("div",Vt,[e("div",Ft,d(r.$t("app.agentTicketRefund.prntNo")),1),e("div",Ot,d(["CDS","GPCDS"].includes(a(t).ticketManagementOrganizationCode??"")?"-":a(t).prntNo),1)]))]),e("div",Mt,[e("div",Ut,[e("div",Bt,d(r.$t("app.agentTicketRefund.refundAgent")),1),e("div",zt,d(a(t).agent),1)]),e("div",Ht,[e("div",Lt,d(r.$t("app.agentTicketRefund.refundIataNo")),1),e("div",Gt,d(a(t).iata),1)]),e("div",qt,[e("div",Yt,d(r.$t("app.agentTicketRefund.refundOffice")),1),e("div",Qt,d(a(t).office),1)]),e("div",Jt,[e("div",Kt,d(r.$t("app.agentTicketRefund.refundDate")),1),e("div",Xt,d(a(t).refundDate),1)])])]),e("div",Wt,[e("div",Zt,[e("div",ea,[e("div",ta,d(r.$t("app.agentTicketRefund.refundAirlineSettlementCode")),1),e("div",aa,d(a(t).airline),1)]),e("div",sa,[e("div",na,d(r.$t("app.agentTicketRefund.refundTicketNo")),1),g(ee,{placement:"top",trigger:"hover",content:a(t).ticketNoView},{default:y(()=>[e("div",ia,d(a(t).ticketNoView),1)]),_:1},8,["content"])]),oa,e("div",la,[e("div",ra,d(r.$t("app.agentTicketRefund.numberOfCombinedTickets")),1),e("div",da,d(a(t).conjunction),1)])]),e("div",ua,[e("div",ca,[g(w,{label:r.$t("app.agentTicketRefund.passName"),prop:"name",rules:a(i).psgName},{default:y(()=>[O(g(V,{modelValue:a(t).name,"onUpdate:modelValue":p[3]||(p[3]=s=>a(t).name=s),disabled:r.disabled,clearable:"",onInput:p[4]||(p[4]=s=>a(t).name=a(t).name.toUpperCase())},null,8,["modelValue","disabled"]),[[n]])]),_:1},8,["label","rules"])]),r.isSupplementRefund?(m(),k("div",ma,[e("div",ga,d(r.$t("app.agentTicketRefund.refundSeg")),1),e("div",ha,[a(t).conjunction===1?(m(!0),k(q,{key:0},Y(r.refundTickets,(s,f)=>(m(),E(w,{key:f,prop:`segment[${f}]`},{default:y(()=>[g(ne,{modelValue:a(t).checkedSeg,"onUpdate:modelValue":p[5]||(p[5]=h=>a(t).checkedSeg=h),disabled:r.disabled},{default:y(()=>[e("div",xa,[(m(!0),k(q,null,Y(s,(h,F)=>(m(),k("div",{key:F+"segmet",class:"justify-start items-center gap-[2px] flex"},[g(se,{disabled:!h.select,label:h},{default:y(()=>[e("div",ya,d(h.deptCity)+"-"+d(h.arrivalCity),1)]),_:2},1032,["disabled","label"]),e("div",{class:R([!h.select||!a(C)(h)?"text-gray-5":"text-brand-2","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},d(r.$t(`app.queryRefunds.number_${F+1}`)),3)]))),128))])]),_:2},1032,["modelValue","disabled"])]),_:2},1032,["prop"]))),128)):(m(),k("div",va,[(m(!0),k(q,null,Y(r.refundTickets,(s,f)=>(m(),k("div",{key:f,class:R({"mb-[10px]":f<r.refundTickets.length-1})},[e("div",ba,d(r.$t("app.agentTicketRefund.couponNo",{a:a(Ye)(Number(s[0].conjunctionIndex)-1)})),1),g(w,{prop:`segment[${f}]`},{default:y(()=>[g(ne,{modelValue:a(t).checkedSeg,"onUpdate:modelValue":p[6]||(p[6]=h=>a(t).checkedSeg=h),disabled:r.disabled},{default:y(()=>[e("div",_a,[(m(!0),k(q,null,Y(s,(h,F)=>(m(),k("div",{key:F+"segmet",class:"justify-start items-center gap-[2px] flex h-[20px]"},[g(se,{disabled:!h.select,label:h},{default:y(()=>[e("div",ka,d(h.deptCity)+"-"+d(h.arrivalCity),1)]),_:2},1032,["disabled","label"]),e("div",{class:R([!h.select||!a(C)(h)?"text-gray-5":"text-brand-2","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},d(r.$t(`app.queryRefunds.number_${F+1}`)),3)]))),128))])]),_:2},1032,["modelValue","disabled"])]),_:2},1032,["prop"])],2))),128))]))])])):(m(),k("div",pa,[e("div",fa,d(r.$t("app.agentTicketRefund.refundSeg")),1),(m(!0),k(q,null,Y(a(t).couponNos,(s,f)=>(m(),E(w,{key:"couponNo"+f,prop:"couponNos."+f,error:a($)[f],class:"mr-[10px] coupon-no"},{default:y(()=>[O(g(V,{modelValue:a(t).couponNos[f],"onUpdate:modelValue":h=>a(t).couponNos[f]=h,disabled:r.disabled,clearable:"",onBlur:a(M)},null,8,["modelValue","onUpdate:modelValue","disabled","onBlur"]),[[n]])]),_:2},1032,["prop","error"]))),128))]))]),e("div",wa,[e("div",Ra,[g(w,{label:r.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",rules:a(i).totalAmount,class:R({"not-required-container":!a(t).totalAmount})},{default:y(()=>[O(g(V,{modelValue:a(t).totalAmount,"onUpdate:modelValue":p[7]||(p[7]=s=>a(t).totalAmount=s),disabled:r.disabled,clearable:"",onBlur:p[8]||(p[8]=s=>a(T)(""))},null,8,["modelValue","disabled"]),[[n]]),a(t).totalAmount?X("",!0):(m(),k("div",Ta,d(r.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),e("div",Na,[g(w,{label:r.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:a(i).payType},{default:y(()=>[r.disabled?(m(),k("div",{key:1,class:R([{"bg-brand-7":!r.disabled},"bg-gray-7 w-full h-[32px] px-[12px] py-4[px] flex items-center text-xs font-bold cursor-not-allowed text-[--bkc-el-text-color-placeholder] border-solid border-[1px] border-[--bkc-el-disabled-border-color] rounded-[4px]"])},[U(d(a(Qe)(a(t).payType))+" ",1),g(z,{class:"refundForm-icon brand text-gray-7"},{default:y(()=>[g(a(Pe))]),_:1})],2)):(m(),E(K,{key:0,modelValue:a(t).payType,"onUpdate:modelValue":p[9]||(p[9]=s=>a(t).payType=s),modelModifiers:{trim:!0},disabled:r.disabled,class:"pay",filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:r.$t("app.agentTicketRefund.paymentSel"),clearable:"",onBlur:a(P)},{default:y(()=>[(m(!0),k(q,null,Y(a(De),(s,f)=>(m(),E(W,{key:f,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onBlur"]))]),_:1},8,["label","rules"])]),e("div",Ca,[g(w,{label:r.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:a(i).currency},{default:y(()=>[O(g(V,{modelValue:a(t).currency,"onUpdate:modelValue":p[10]||(p[10]=s=>a(t).currency=s),disabled:r.disabled,clearable:"",onBlur:a(J),onInput:p[11]||(p[11]=s=>a(t).currency=a(t).currency.toUpperCase())},null,8,["modelValue","disabled","onBlur"]),[[n]])]),_:1},8,["label","rules"])]),$a])]),e("div",Ia,[e("div",Ea,[e("div",Da,[g(w,{label:r.$t("app.agentTicketRefund.etTag")},{default:y(()=>[g(L,{modelValue:a(t).etTagNew,"onUpdate:modelValue":p[12]||(p[12]=s=>a(t).etTagNew=s),disabled:r.disabled||r.isSupplementRefund,"inline-prompt":"","active-text":"Y","inactive-text":"N"},null,8,["modelValue","disabled"])]),_:1},8,["label"])])]),e("div",Sa,[e("div",Aa,[e("div",null,[e("span",null,d(r.$t("app.agentTicketRefund.refundTax")),1),e("span",ja,d(r.$t("app.fare.singleFare.totalTax")),1),e("span",Va," "+d(a(t).currency)+" "+d(a(t).totalTaxs),1)]),r.disabled?X("",!0):(m(),E(ie,{key:0,link:"",type:"primary",size:"small",disabled:a(t).taxs.length===27,onClick:a(j)},{default:y(()=>[U(d(r.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"]))]),e("div",Fa,[(m(!0),k(q,null,Y(a(t).taxs,(s,f)=>(m(),k("div",{key:f+new Date,class:"grow shrink-0 basis-0 h-[32px] justify-start items-center flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[e("div",Oa,d(f+1),1),e("div",Pa,[g(w,{prop:"taxs."+f+".taxCode",rules:a(i).taxName},{default:y(()=>[O(g(V,{modelValue:s.taxCode,"onUpdate:modelValue":h=>s.taxCode=h,disabled:r.disabled,onInput:h=>s.taxCode=s.taxCode.toUpperCase(),onBlur:a(x)},null,8,["modelValue","onUpdate:modelValue","disabled","onInput","onBlur"]),[[n]])]),_:2},1032,["prop","rules"])]),g(w,{prop:"taxs."+f+".taxAmount",rules:a(i).taxValue},{default:y(()=>[O(g(V,{modelValue:s.taxAmount,"onUpdate:modelValue":h=>s.taxAmount=h,disabled:r.disabled,onBlur:a(x)},null,8,["modelValue","onUpdate:modelValue","disabled","onBlur"]),[[n]])]),_:2},1032,["prop","rules"])]))),128))])])]),e("div",Ma,[e("div",Ua,[e("div",Ba,[g(w,{label:r.$t("app.agentTicketRefund.commision"),prop:"commision",rules:a(i).price},{default:y(()=>[O(g(V,{modelValue:a(t).commision,"onUpdate:modelValue":p[13]||(p[13]=s=>a(t).commision=s),disabled:r.disabled,clearable:"",placeholder:"0.00",onBlur:p[14]||(p[14]=s=>a(T)(""))},null,8,["modelValue","disabled"]),[[n]])]),_:1},8,["label","rules"])]),e("div",za,[g(w,{label:r.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:a(i).rate},{default:y(()=>[O(g(V,{modelValue:a(t).commisionRate,"onUpdate:modelValue":p[15]||(p[15]=s=>a(t).commisionRate=s),disabled:r.disabled,clearable:"",placeholder:"0.00",onBlur:p[16]||(p[16]=s=>a(T)("commisionRate")),onInput:a(N)},null,8,["modelValue","disabled","onInput"]),[[n]]),Ha]),_:1},8,["label","rules"])]),e("div",La,[g(w,{label:r.disabled?r.$t("app.agentTicketRefund.otherDeductionRate"):r.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",rules:a(i).rate},{default:y(()=>[O(g(V,{modelValue:a(t).otherDeductionRate,"onUpdate:modelValue":p[17]||(p[17]=s=>a(t).otherDeductionRate=s),class:"min-width-42",disabled:r.disabled,clearable:"",placeholder:"1-100",onBlur:p[18]||(p[18]=s=>a(T)("otherDeductionRate"))},null,8,["modelValue","disabled"]),[[n]]),Ga]),_:1},8,["label","rules"])]),e("div",qa,[g(w,{label:r.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",rules:a(i).price},{default:y(()=>[O(g(V,{modelValue:a(t).otherDeduction,"onUpdate:modelValue":p[19]||(p[19]=s=>a(t).otherDeduction=s),disabled:r.disabled,clearable:"",placeholder:"0.00",onBlur:p[20]||(p[20]=s=>a(T)(""))},null,8,["modelValue","disabled"]),[[n]])]),_:1},8,["label","rules"])])]),e("div",Ya,[e("div",Qa,[e("div",Ja,d(r.$t("app.agentTicketRefund.remark")),1),e("div",Ka,[g(w,{prop:"remarkInfo",rules:a(i).remarkInfo},{default:y(()=>[O(g(V,{modelValue:a(t).remarkInfo,"onUpdate:modelValue":p[21]||(p[21]=s=>a(t).remarkInfo=s),disabled:r.disabled,clearable:"",placeholder:r.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:p[22]||(p[22]=s=>{var f;return a(t).remarkInfo=(f=a(t).remarkInfo)==null?void 0:f.toUpperCase()})},null,8,["modelValue","disabled","placeholder"]),[[n]])]),_:1},8,["rules"]),g(ee,{placemant:"top",content:r.$t("app.agentTicketRefund.remarkTips")},{default:y(()=>[Xa]),_:1},8,["content"])])]),e("div",Wa,[r.disabled?(m(),E(w,{key:0,label:r.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund",rules:a(i).netRefund},{default:y(()=>[O(g(V,{modelValue:a(t).netRefund,"onUpdate:modelValue":p[23]||(p[23]=s=>a(t).netRefund=s),disabled:r.disabled,clearable:""},null,8,["modelValue","disabled"]),[[n]])]),_:1},8,["label","rules"])):(m(),E(w,{key:1,label:r.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund"},{default:y(()=>[e("span",Za,d(a(t).currency)+" "+d(a(t).netRefund),1),g(ee,{placement:"top",effect:"dark"},{content:y(()=>[U(d(r.$t("app.agentTicketRefund.netRefundTip")),1)]),default:y(()=>[es]),_:1})]),_:1},8,["label"]))]),e("div",ts,[a(t).payType==="TC"?(m(),E(w,{key:0,label:r.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:a(i).creditCard},{default:y(()=>[O(g(V,{modelValue:a(t).creditCard,"onUpdate:modelValue":p[24]||(p[24]=s=>a(t).creditCard=s),disabled:r.disabled,clearable:"",onInput:p[25]||(p[25]=s=>a(t).creditCard=a(t).creditCard.toUpperCase())},null,8,["modelValue","disabled"]),[[n]])]),_:1},8,["label","rules"])):(m(),k("em",as))]),ss])])]}),_:1},8,["model"])}}});const is=he(ns,[["__scopeId","data-v-cdcb159c"]]),os=o=>(me("data-v-03eefa76"),o=o(),ge(),o),ls={class:"w-[460px] h-12 justify-start items-center gap-4 inline-flex mb-[10px]"},rs=os(()=>e("em",{class:"iconfont icon-info-circle-line text-brand-2"},null,-1)),ds={class:"h-[50px] grow shrink basis-0 text-gray-1 text-lg font-normal leading-normal whitespace-normal"},us={class:"w-[206px] h-8 justify-end items-center gap-2.5 inline-flex"},cs=re({__name:"DeleteConfirm",props:{airlineCode:{},etTag:{},tktType:{}},emits:["update:modelValue","confirm"],setup(o,{emit:c}){const v=c,t=()=>{v("update:modelValue",!1)},b=o,i=D(b.airlineCode!=="784"&&b.etTag==="Y"&&b.tktType==="I"||b.tktType==="D"&&b.airlineCode!=="784");return(_,l)=>{const x=Se,T=fe,P=xe;return m(),E(P,{class:"rval-dialog",width:"500px","close-on-click-modal":!1,"show-close":!1,onClose:l[2]||(l[2]=C=>t())},{footer:y(()=>[e("span",us,[_.airlineCode!=="784"?(m(),E(x,{key:0,modelValue:i.value,"onUpdate:modelValue":l[0]||(l[0]=C=>i.value=C),label:_.$t("app.refundForm.restore"),size:"large"},null,8,["modelValue","label"])):X("",!0),g(T,{type:"primary",onClick:l[1]||(l[1]=C=>_.$emit("confirm",i.value))},{default:y(()=>[U(d(_.$t("app.refundForm.confirmBtn")),1)]),_:1}),g(T,{plain:"",onClick:t},{default:y(()=>[U(d(_.$t("app.refundForm.cancelBtn")),1)]),_:1})])]),default:y(()=>[e("div",ls,[rs,e("div",ds,d(_.$t("app.refundForm.wainMsg")),1)])]),_:1})}}});const ps=he(cs,[["__scopeId","data-v-03eefa76"]]),pe=function(o,c){if(!(this instanceof pe))return new pe(o,c);this.options=this.extend({noPrint:"",style:"",paging:!1},c),typeof o=="string"?this.dom=document.querySelector(o):(this.isDOM(o),this.dom=this.isDOM(o)?o:o.$el),this.init()};pe.prototype={init:function(){var o=this.getStyle()+this.getHtml();this.writeIframe(o)},extend:function(o,c){for(var v in c)o[v]=c[v];return o},getStyle:function(){for(var o="",c=document.querySelectorAll("style,link"),v=0;v<c.length;v++)o+=c[v].outerHTML;return o+="<style>"+(this.options.noPrint?this.options.noPrint:".no-print")+"{display:none;}</style>",this.options.paging&&(o+="<style>html,body{padding: 20px},div{height: auto!important;-webkit-print-color-adjust: exact}</style>"),o},getHtml:function(){for(var o=document.querySelectorAll("input"),c=document.querySelectorAll("textarea"),v=document.querySelectorAll("select"),t=0;t<o.length;t++)o[t].type=="checkbox"||o[t].type=="radio"?o[t].checked==!0?o[t].setAttribute("checked","checked"):o[t].removeAttribute("checked"):(o[t].type=="text",o[t].setAttribute("value",o[t].value));for(var b=0;b<c.length;b++)c[b].type=="textarea"&&(c[b].innerHTML=c[b].value);for(var i=0;i<v.length;i++)if(v[i].type=="select-one"){var _=v[i].children;for(var l in _)_[l].tagName=="OPTION"}return this.options.paging?this.dom.outerHTML:this.wrapperRefDom(this.dom).outerHTML},wrapperRefDom:function(o){let c=null,v=o;if(!this.isInBody(v))return v;for(;v;){if(c){let t=v.cloneNode(!1);t.appendChild(c),c=t}else c=v.cloneNode(!0);v=v.parentElement}return c},isInBody:function(o){return o===document.body?!1:document.body.contains(o)},writeIframe:function(o){o=o+"<style>.print{transform: scale(2) !important;}@page {margin-top: 1mm;margin-bottom: 1mm;}</style>"+this.options.style;var c,v,t=document.createElement("iframe"),b=document.body.appendChild(t);t.id="myIframe",t.setAttribute("style","position:absolute;width:0;height:0;top:-10px;left:-10px;"),c=b.contentWindow||b.contentDocument,v=b.contentDocument||b.contentWindow.document,v.open(),v.write(o),v.close();var i=this;t.onload=function(){i.toPrint(c),setTimeout(function(){document.body.removeChild(t)},100)}},toPrint:function(o){try{setTimeout(function(){o.focus();try{o.document.execCommand("print",!1,null)||o.print()}catch{o.print()}o.close()},10)}catch(c){console.log("err",c)}},isDOM:typeof HTMLElement=="object"?function(o){return o instanceof HTMLElement}:function(o){return o&&typeof o=="object"&&o.nodeType===1&&typeof o.nodeName=="string"}};const Q=o=>(me("data-v-8aae7d23"),o=o(),ge(),o),fs=Q(()=>e("div",{class:"w-[1012px] h-6 justify-center items-center gap-2.5 inline-flex relative mb-[10px]"},[e("div",{class:"w-[348px] h-[0px] rotate-180 border border-gray-6"}),e("div",{class:"text-gray-2 text-base font-bold leading-normal"},"AIRLINE / BSP AUTO REFUND FORM"),e("div",{class:"w-[348px] h-[0px] rotate-180 border border-gray-6"})],-1)),ms={class:"w-[1012px] h-[158px] flex-col justify-start items-start gap-2.5 inline-flex"},gs={class:"w-full self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},hs={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},xs={class:"text-gray-2 font-bold leading-tight"},ys={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},vs={class:"text-gray-2 font-bold leading-tight"},bs={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},_s={class:"text-gray-2 font-bold leading-tight"},ks={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},ws={class:"text-gray-2 font-bold leading-tight"},Rs={class:"w-full self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Ts={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Ns={class:"text-gray-2 font-bold leading-tight"},Cs={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},$s={class:"text-gray-2 font-bold leading-tight"},Is={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Es=Q(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"CURRENCY CODE",-1)),Ds=[Es],Ss={class:"text-gray-2 font-bold leading-tight"},As={class:"w-1/4 grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},js=Q(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"FORM PAYMENT",-1)),Vs=[js],Fs={class:"text-gray-2 font-bold leading-tight"},Os={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},Ps={class:"text-gray-2 font-bold leading-tight"},Ms={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Us={class:"grow shrink basis-0 h-5 justify-start items-center gap-2.5 flex"},Bs={class:"text-gray-2 font-bold leading-tight"},zs={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Hs={class:"text-gray-2 font-bold leading-tight"},Ls={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Gs={class:"text-gray-2 font-bold leading-tight"},qs={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Ys={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Qs={class:"text-gray-2 font-bold leading-tight"},Js={class:"w-[324px] self-stretch justify-start items-center gap-2.5 flex"},Ks={class:"text-gray-2 font-bold leading-tight"},Xs={class:"self-stretch h-[18px] justify-start items-start gap-5 inline-flex"},Ws={class:"grow shrink basis-0 self-stretch justify-start items-center gap-2.5 flex"},Zs={class:"text-gray-2 font-bold leading-tight"},en={class:"w-[324px] self-stretch justify-start items-center gap-2.5 flex"},tn={class:"text-gray-2 font-bold leading-tight"},an={class:"w-[1012px]"},sn={class:"w-[1012px] h-[44px] flex-col justify-start items-start gap-2.5 inline-flex"},nn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},on=Q(()=>e("div",{class:"grow shrink basis-0 text-right text-gray-3 font-normal leading-tight"},"GROSS REFUND",-1)),ln=[on],rn={class:"text-gray-2 font-bold leading-tight"},dn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},un={class:"text-gray-2 font-bold leading-tight"},cn={class:"w-[1012px]"},pn={class:"w-[1012px] h-[100px] flex-col justify-start items-start gap-2.5 inline-flex"},fn={class:"self-stretch h-[18px] justify-start items-center gap-5 inline-flex"},mn={class:"h-5 justify-start items-center gap-2.5 flex"},gn=Q(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"-",-1)),hn={class:"text-gray-2 font-bold leading-tight"},xn=Q(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"=",-1)),yn={class:"text-gray-2 font-bold leading-tight"},vn=Q(()=>e("span",{class:"text-gray-2 font-bold leading-tight"},"%",-1)),bn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},_n=Q(()=>e("div",{class:"text-gray-2 font-bold leading-tight"},"-",-1)),kn={class:"text-gray-2 font-bold leading-tight"},wn={class:"self-stretch grow shrink basis-0 justify-start items-center gap-2.5 inline-flex"},Rn={class:"text-gray-2 font-bold leading-tight"},Tn={class:"w-[1012px] h-[18px] justify-start items-center gap-2.5 inline-flex"},Nn=Q(()=>e("div",{class:"text-gray-2 font-bold leading-tight"},"= ",-1)),Cn={class:"text-gray-2 font-bold leading-tight"},$n={class:"text-gray-2 font-bold leading-tight"},In={key:0},En=Q(()=>e("div",{class:"w-full min-w-[20px] h-[0px] rotate-180 border border-gray-6 inline-block"},null,-1)),Dn={key:0,class:"text-gray-2 font-bold inline-block whitespace-nowrap text-base"},Sn=Q(()=>e("div",{class:"w-full min-w-[20px] h-[0px] rotate-180 border border-gray-6 inline-block"},null,-1)),An=re({__name:"PrintRefundForm",props:{refundInfo:{},originalTickets:{}},setup(o,{expose:c}){const v=D(),t=D(!1),b=ce(()=>window.innerHeight-120),i=D(!1),_=D("AIRLINE / BSP AUTO REFUND FORM COMPLETED"),l=mt(x=>{x==="enter"?i.value=!0:i.value=!1},200);return c({captureRef:v,originInfo:t}),(x,T)=>{var A,I,j;const P=gt,C=Ee,S=Ie("trimUpper");return m(),k("div",{ref_key:"captureRef",ref:v,class:"w-full p-[14px]",style:Be({height:b.value+"px"})},[fs,e("div",ms,[e("div",gs,[e("div",hs,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND NUMBER",2),e("span",xs,d(((A=x.refundInfo.refundNo)==null?void 0:A.length)===13?x.refundInfo.refundNo.substring(4):x.refundInfo.refundNo),1)]),e("div",ys,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND TYPE",2),e("span",vs,d(x.refundInfo.tktType==="D"?"DOMESTIC":"INTERNATIONAL"),1)]),e("div",bs,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CITY / OFFICE",2),e("div",_s,d(x.refundInfo.office),1)]),e("div",ks,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"AGENT",2),e("span",ws,d(x.refundInfo.agent),1)])]),e("div",Rs,[e("div",Ts,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"IATA NUMBER",2),e("span",Ns,d(x.refundInfo.iata),1)]),e("div",Cs,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"DATE / TIME",2),e("span",$s,d(x.refundInfo.refundDate),1)]),e("div",Is,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},Ds,2),e("span",Ss,d(x.refundInfo.refundFormCurrency),1)]),e("div",As,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},Vs,2),e("span",Fs,d(x.refundInfo.payType),1)])]),e("div",Os,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"PASSENGER NAM",2),e("span",Ps,d(x.refundInfo.name),1)]),e("div",Ms,[e("div",Us,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"AIRLINE CODE",2),e("span",Bs,d(x.refundInfo.airline),1)]),e("div",zs,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"TICKET NO.",2),e("span",Hs,d(x.refundInfo.ticketNoView),1)]),e("div",Ls,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CONJUNCTION",2),e("span",Gs,d(x.refundInfo.conjunction),1)])]),e("div",qs,[e("div",Ys,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REFUND COUPON-",2),e("div",Qs,[(m(!0),k(q,null,Y(x.refundInfo.couponNos,(N,$)=>(m(),k("span",{key:$},d($+1)+":"+d(N)+"  ",1))),128))])]),e("div",Js,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"ET(Y/N)",2),e("div",Ks,d(x.refundInfo.etTagNew?"Y":"N"),1)])]),e("div",Xs,[e("div",Ws,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"REMARK",2),e("span",Zs,d(x.refundInfo.remarkInfo??""?`${(I=x.refundInfo.remarkInfo)==null?void 0:I.substring(0,2)}-${(j=x.refundInfo.remarkInfo)==null?void 0:j.substring(2)}`:""),1)]),e("div",en,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"CREDIT CARD",2),e("span",tn,d(x.refundInfo.creditCard??""),1)])])]),e("div",an,[g(P,{"border-style":"dashed",class:"m-y-[6px] w-[1012px]"})]),e("div",sn,[e("div",nn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","h-5 justify-start items-center flex"])},ln,2),e("span",rn,d(Number(x.refundInfo.totalAmount).toFixed(2)),1)]),e("div",dn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"ADD TAX(ES)",2),e("span",un,"+ "+d(x.refundInfo.totalTaxs),1)])]),e("div",cn,[g(P,{"border-style":"dashed",class:"m-y-[6px] w-[1012px]"})]),e("div",pn,[e("div",fn,[e("div",mn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"COMMISSION",2),gn,e("span",hn,d(x.refundInfo.commision),1),xn,e("span",yn,d(x.refundInfo.commisionRate),1),vn])]),e("div",bn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"OTHER DEDUCTION",2),_n,e("span",kn,d(x.refundInfo.otherDeduction),1)]),e("div",wn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"NET REFUND",2),e("span",Rn,"= "+d(x.refundInfo.netRefund)+" ("+d(x.refundInfo.payType)+" "+d(x.refundInfo.refundFormCurrency)+")",1)]),e("div",Tn,[e("div",{class:R([t.value?"w-[139px]":"w-[126px]","text-right text-gray-3 font-normal leading-tight"])},"TAX",2),Nn,e("span",Cn,d(x.refundInfo.refundFormCurrency),1),e("div",$n,[(m(!0),k(q,null,Y((x.refundInfo.taxs??[]).filter(N=>N.taxAmount||N.taxCode),(N,$)=>(m(),k("span",{key:$},[U(d(Number(N.taxAmount).toFixed(2))+d(N.taxCode)+" ",1),$!==(x.refundInfo.taxs??[]).filter(M=>M.taxAmount||M.taxCode).length-1?(m(),k("span",In,"+")):X("",!0)]))),128))])])]),e("div",{class:"w-[1012px] h-[auto] items-center gap-2.5 relative mt-[10px] mb-[20px] inline-flex",onMouseenter:T[2]||(T[2]=N=>a(l)("enter")),onMouseleave:T[3]||(T[3]=N=>a(l)("leave"))},[En,i.value?O((m(),E(C,{key:1,modelValue:_.value,"onUpdate:modelValue":T[0]||(T[0]=N=>_.value=N),class:"end-tilte",maxlength:"60","show-word-limit":"",onInput:T[1]||(T[1]=N=>_.value=_.value.toUpperCase())},null,8,["modelValue"])),[[S]]):(m(),k("div",Dn,d(_.value),1)),Sn],32),x.originalTickets.length>0?(m(!0),k(q,{key:0},Y(x.originalTickets,(N,$)=>(m(),k("div",{key:$,class:R([t.value?"text-[18px]":"","w-[1012px] p-[10px] mb-[14px] bg-gray-7 text-gray-1 text-[14px] last:mb-[0px]"])},[e("pre",null,d(a(Ue).decode(N)),1)],2))),128)):X("",!0)],4)}}});const jn=he(An,[["__scopeId","data-v-8aae7d23"]]),Vn={class:"electronic-print-refund-form"},Fn={class:"overflow-y-scroll"},On={class:"flex justify-center mt-5"},Pn=re({__name:"ElectronicPrintRefundForm",props:{modelValue:{type:Boolean},refundInfo:{},originalTickets:{}},emits:["update:modelValue"],setup(o,{emit:c}){const v=o,t=c,b=le(v.modelValue),i=D(),_=D(!1),l=()=>{t("update:modelValue",!1)},x=async()=>{_.value=!0,i.value.originInfo=!0,await ze(),await pe(i.value,{paging:!0,style:`<style>
          .print-refund-form-panel {
            width: 1050px;
            height: 100%;
          }
          * {
            font-size: 15px;
          }
        </style>`}),i.value.originInfo=!1,_.value=!1},T=ce(()=>window.innerHeight-120),P=async()=>{var N,$,M,B;if(!((N=i.value)!=null&&N.captureRef))return;const C=(($=i.value)==null?void 0:$.captureRef.scrollHeight)+20,S=(M=i.value)==null?void 0:M.captureRef.scrollWidth;i.value.captureRef.style.height=C+"px";const A=await He((B=i.value)==null?void 0:B.captureRef,{width:S,height:C,scale:1});i.value.captureRef.style.height=T.value+"px";const I=A.toDataURL("image/png"),j=document.createElement("a");j.href=I,j.download=`${v.refundInfo.ticketNo}.png`,j.click()};return(C,S)=>{const A=fe,I=xe;return m(),k("div",Vn,[g(I,{modelValue:b.value,"onUpdate:modelValue":S[0]||(S[0]=j=>b.value=j),width:"1046","custom-class":"print-refund-form","show-close":!1,"close-on-click-modal":!1,onClose:l},{default:y(()=>[e("div",Fn,[g(jn,{ref_key:"printRef",ref:i,class:R(["print-refund-form-panel",_.value?"":"text-xs"]),"refund-info":C.refundInfo,"original-tickets":C.originalTickets},null,8,["class","refund-info","original-tickets"])]),e("div",On,[g(A,{class:"print-btn",type:"primary",onClick:x},{default:y(()=>[U(d(C.$t("app.refundForm.print")),1)]),_:1}),g(A,{class:"print-btn text-color",onClick:P},{default:y(()=>[U(d(C.$t("app.refundForm.download")),1)]),_:1},8,["onClick"]),g(A,{class:"print-btn text-color",onClick:l},{default:y(()=>[U(d(C.$t("app.refundForm.cancelBtn")),1)]),_:1},8,["onClick"])])]),_:1},8,["modelValue"])])}}});const Mn=(o,c)=>{const{t:v}=Ce(),t=le(!0),b=le(""),i=D(),_=le(!1),l=D({}),x=D({}),T=D({}),P=D([]),C=D(!1),S=le(!1),A=D([]),I=D([]),j=n=>{let u=[];return u=n.map(s=>({taxCode:s.taxCode,taxAmount:s.taxAmount})),u.length<10?u.concat(new Array(10-u.length).fill({taxCode:"",taxAmount:""})).map(s=>({...s})):u},N=()=>{for(const n in l.value.segmentInfos){const u=[...l.value.segmentInfos[n]];P.value.push(u)}},$=()=>{const n=[];return l.value.segmentInfos&&Object.values(l.value.segmentInfos).forEach(u=>{(u??[]).forEach(s=>{s.ticketStatus==="REFUNDED"&&n.push(s)})}),n},M=(n,u,s)=>u?s!=="ARL"||u.startsWith(n)?u:`${n}-${u}`:"-",B=()=>{if(!l.value.ticketNoEnd&&l.value.ticketNoView){const n=l.value.ticketNoView.length,u=l.value.ticketNoView.substring(n-2);return`${l.value.ticketNo}-${u}`}return l.value.ticketNo===l.value.ticketNoEnd?l.value.ticketNo:`${l.value.ticketNo}-${l.value.ticketNoEnd}`},G=()=>{var n,u,s;N(),A.value=j(l.value.taxInfos),I.value=l.value.couponNo?JSON.parse(JSON.stringify(l.value.couponNo)):[],T.value={refundNo:o.isSupplementRefund?"-":M(l.value.airlineCode,l.value.cmdNo,l.value.ticketManagementOrganizationCode??""),volunteer:l.value.cmdOption,createUser:l.value.operator,prntNo:l.value.deviceNum,refundType:o.isSupplementRefund?v("app.refundForm.manualRefundType"):"-",refundDate:l.value.refundDate??"",international:l.value.international==="I"?"INTERNATIONAL":"DOMESTIC",agent:l.value.agent,iata:l.value.iataNo,office:l.value.office,tktType:l.value.ticketType==="D"?"D":"I",ticketManagementOrganizationCode:l.value.ticketManagementOrganizationCode,ticketNo:B(),ticketNoView:l.value.ticketNoView,conjunction:l.value.conjunction,name:l.value.passengerName,psgType:l.value.passengerType,totalAmount:l.value.grossRefund,payType:l.value.payMethod,currency:l.value.currency,etTagNew:l.value.refund==="Y",taxs:j(l.value.taxInfos),totalTaxs:l.value.totalTaxs,commision:l.value.commission,commisionRate:Number(l.value.commissionRate)>0?l.value.commissionRate:"",rate:l.value.commissionRate?"1":"0",otherDeductionRate:"",otherDeduction:l.value.deduction,remark:l.value.remark?l.value.remark.substring(2):"",netRefund:l.value.netRefund,creditCard:l.value.creditCard,airline:l.value.airlineCode,crsPnrNo:"",pnr:"",isCoupon:"",receiptPrinted:"",check:"",remarkCode:l.value.remark?l.value.remark.substring(0,2):"",remarkInfo:l.value.remark,checkedSeg:$(),couponNos:l.value.couponNo??[],printRefundFormTicketNo:`${(n=l.value.ticketNo)==null?void 0:n.substring(3)}-${(u=l.value.ticketNoSecond)==null?void 0:u.substring(3)}`,refundFormCurrency:((s=l.value)==null?void 0:s.refundFormCurrency)??""}},J=()=>{c("update:modelValue",!1)},r=()=>{t.value=!1,i.value.resetForm()},p=async()=>{S.value=!0},z=()=>{C.value=!0},W=async n=>{var s,f,h;C.value=!1;const u={refundNo:(s=i.value)==null?void 0:s.getFormDate().refundNo,ticketNo:o.refundOperationCondition.ticketNo,ticketType:o.printerType?o.printerType:o.refundOperationCondition.ticketType,ticketManagementOrganizationCode:(f=i.value)==null?void 0:f.getFormDate().ticketManagementOrganizationCode,printerNo:(h=i.value)==null?void 0:h.getFormDate().prntNo,resetTicketStatus:n};try{const F=be("091T0105");_.value=!0,await ht(u,F),_.value=!1,await _e(v("app.refundForm.successMsg")),(o==null?void 0:o.isSalesDaily)??!1?c("reSalesDaily"):await c("reQueryTicket"),await J()}finally{_.value=!1}},K=()=>{t.value=!1},w=()=>{t.value=!0},ee=()=>{i.value.resetForm()},V=n=>({refund:n.etTagNew?"Y":"N",currency:n.currency,payMethod:n.payType,remark:n.remarkInfo??"",creditCard:n.creditCard?n.creditCard:"",couponNos:n.couponNos,name:(n.name??"").trim()}),se=n=>{const u=[];return n.forEach(s=>{if(s.taxAmount&&s.taxCode){const f={taxCode:s.taxCode,taxAmount:Number(s.taxAmount)};u.push(f)}}),u},ne=n=>({commission:n.commision&&Number(n.commision)>0?n.commision.toString():"0",commissionRate:n.commisionRate.toString()??"",grossRefund:n.totalAmount.toString(),deduction:n.otherDeduction.toString(),netRefund:n.netRefund.toString(),taxInfos:se(n.taxs)}),L=n=>({refundNo:n.refundNo,ticketNo:n.ticketNo.includes("-")?n.ticketNo.split("-")[0]:n.ticketNo,ticketType:o.printerType?o.printerType:n.tktType,printerNo:n.prntNo,ticketManagementOrganizationCode:n.ticketManagementOrganizationCode??"",refundFormPassengerItem:V(n),refundFormPriceItem:ne(n)}),ie=async n=>{_.value=!0;const u=L(n);let s;try{const f=be("091T0106");s=(await xt(u,f)).data.value,(s==null?void 0:s.code)==="200"&&(await _e(v("app.refundForm.editSuccess")),c("update:modelValue",!1))}finally{_.value=!1}},oe=async()=>{var h,F,te;const n=(h=i.value)==null?void 0:h.getFormDate();if(!n.couponNos.some(de=>de!=="0000")){Le({type:"warning",message:v("app.agentTicketRefund.selSeg")});return}const s=await((F=i.value)==null?void 0:F.validate()),f=(te=i.value)==null?void 0:te.validSegment();s&&f&&ie(n)};return $e(()=>{t.value=o.isSupplementRefund,b.value=t.value?v("app.refundForm.supplementaryRefundInfo"):v("app.refundForm.refundFormInfo"),l.value=o.refundTicketData,o.refundTicketData&&G()}),{taxsHistory:A,refundTicketData:l,renderData:x,showEdit:t,title:b,fullscreenLoading:_,showDeleteConfirm:C,closeDialog:J,deleteRefund:z,editRefund:w,print:p,showRefundInfo:K,refundSegmentInfo:P,formInfo:T,formRef:i,initFormData:ee,handleCommit:oe,cancelEdit:r,onDeleteConfirm:W,showPrintRefundForm:S,couponNoHistory:I}},Un=Mn,Bn=o=>(me("data-v-9c877640"),o=o(),ge(),o),zn=Bn(()=>e("i",{class:"iconfont icon-close"},null,-1)),Hn=[zn],Ln={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},Gn={key:0},qn={key:0,class:"mt-[10px] py-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},Yn={key:1},Qn={class:"mt-[10px] pt-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},Jn=re({__name:"TicketRefundForm",props:{isSupplementRefund:{type:Boolean},printerNo:{},printerType:{},refundOperationCondition:{},refundTicketData:{},isSalesDaily:{type:Boolean},disableOperateButton:{type:Boolean}},emits:["update:modelValue","reQueryTicket","reSalesDaily"],setup(o,{emit:c}){const v=o,t=c,{showEdit:b,title:i,fullscreenLoading:_,closeDialog:l,deleteRefund:x,editRefund:T,print:P,refundSegmentInfo:C,formInfo:S,formRef:A,initFormData:I,handleCommit:j,cancelEdit:N,showDeleteConfirm:$,onDeleteConfirm:M,showPrintRefundForm:B,taxsHistory:G,couponNoHistory:J}=Un(v,t);return(r,p)=>{const z=fe,W=xe,K=Ge;return m(),E(W,{title:a(i),width:"1040px",class:"refund-form-dialog","show-close":!1,"close-on-click-modal":!1,onClose:a(l)},{default:y(()=>[e("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:p[0]||(p[0]=(...w)=>a(l)&&a(l)(...w))},Hn),e("div",null,[e("div",Ln,d(r.$t("app.agentTicketRefund.refundInformationForm")),1),g(is,{ref_key:"formRef",ref:A,data:a(S),"coupon-no-history":a(J),"taxs-history":a(G),"refund-tickets":a(C),disabled:!a(b)},null,8,["data","coupon-no-history","taxs-history","refund-tickets","disabled"])]),r.disableOperateButton?X("",!0):(m(),k("div",Gn,[a(b)?(m(),k("div",Yn,[e("div",Qn,[O((m(),E(z,{type:"primary",onClick:a(j)},{default:y(()=>[U(d(r.$t("app.agentTicketRefund.submit")),1)]),_:1},8,["onClick"])),[[K,a(_),void 0,{fullscreen:!0,lock:!0}]]),g(z,{onClick:a(I)},{default:y(()=>[U(d(r.$t("app.agentTicketRefund.reset")),1)]),_:1},8,["onClick"]),g(z,{onClick:a(N)},{default:y(()=>[U(d(r.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])])])):(m(),k("div",qn,[r.isSalesDaily?X("",!0):(m(),E(z,{key:0,type:"primary",onClick:a(T)},{default:y(()=>[U(d(r.$t("app.refundForm.edit")),1)]),_:1},8,["onClick"])),O((m(),E(z,{onClick:a(x)},{default:y(()=>[U(d(r.$t("app.refundForm.delete")),1)]),_:1},8,["onClick"])),[[K,a(_),void 0,{fullscreen:!0,lock:!0}]]),g(z,{onClick:a(P)},{default:y(()=>[U(d(r.$t("app.refundForm.print")),1)]),_:1},8,["onClick"])]))])),g(ps,{modelValue:a($),"onUpdate:modelValue":p[1]||(p[1]=w=>ke($)?$.value=w:null),"et-tag":r.refundTicketData.refund,"tkt-type":a(S).tktType,"airline-code":r.refundTicketData.airlineCode,onConfirm:a(M)},null,8,["modelValue","et-tag","tkt-type","airline-code","onConfirm"]),a(B)?(m(),E(Pn,{key:1,modelValue:a(B),"onUpdate:modelValue":p[2]||(p[2]=w=>ke(B)?B.value=w:null),"refund-info":a(S),"original-tickets":r.refundTicketData.originalTickets??[]},null,8,["modelValue","refund-info","original-tickets"])):X("",!0)]),_:1},8,["title","onClose"])}}});const gi=he(Jn,[["__scopeId","data-v-9c877640"]]);export{fi as D,pe as P,is as R,gi as T,mi as a,ci as p,pi as t,yt as v};
