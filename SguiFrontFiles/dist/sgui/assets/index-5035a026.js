import{L as c,M as p,q as n,v as u,w as v,x as s,B as o,D as i,A as r,F as m,J as f,C as y,_ as S,K as _}from"./index-18f146fc.js";const h=c({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:p(String),default:"solid"}}),b=n({name:"ElDivider"}),g=n({...b,props:h,setup(a){const l=a,e=u("divider"),d=v(()=>e.cssVar({"border-style":l.borderStyle}));return(t,P)=>(s(),o("div",{class:i([r(e).b(),r(e).m(t.direction)]),style:y(r(d)),role:"separator"},[t.$slots.default&&t.direction!=="vertical"?(s(),o("div",{key:0,class:i([r(e).e("text"),r(e).is(t.contentPosition)])},[m(t.$slots,"default")],2)):f("v-if",!0)],6))}});var k=S(g,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/divider/src/divider.vue"]]);const C=_(k);export{C as E};
