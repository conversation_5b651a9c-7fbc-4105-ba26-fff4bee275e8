import{a as u}from"./FileSaver.min-2ce23ce7.js";import{E as f}from"./exceljs.min-08e2fc65.js";import{el as p,fq as i,aY as x}from"./index-18f146fc.js";function E(e,n){return p(n,function(a){return e[a]})}function b(e){return e==null?[]:E(e,i(e))}function m(e,n,a){const c=x(e);c.forEach(o=>{Object.keys(n).forEach(t=>{Object.prototype.hasOwnProperty.call(n,t)&&(o[n[t]]=o[t]),delete o[t]})});const l=new f.Workbook,r=l.addWorksheet(a);r.addRow(b(n)),c.forEach(o=>{r.addRow(Object.values(o))});const s={size:12};r==null||r.eachRow({includeEmpty:!0},function(o){o.eachCell({includeEmpty:!0},function(t){t.style={font:s}})}),l.xlsx.writeBuffer().then(o=>{const t=new Blob([o],{type:"application/octet-stream"});u.saveAs(t,`${a}.xlsx`)})}export{m as e};
