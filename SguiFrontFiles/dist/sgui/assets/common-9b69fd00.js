import{bJ as D,bb as Y,r as w,w as y,ir as E,is as U,R as S,hD as C,av as T,it as _,cg as d,bG as b,iu as x,dK as R,iv as N,bP as P,au as k,aJ as I}from"./index-18f146fc.js";var u=(t=>(t.ONE_DAYS="ONE_DAY",t.THREE_DAYS="THREE_DAYS",t.SEVEN_DAYS="SEVEN_DAYS",t.FOURTEEN_DAYS="FOURTEEN_DAYS",t.THIRTY_DAYS="THIRTY_DAYS",t.SIXTY_DAYS="SIXTY_DAYS",t))(u||{});const L=[{code:u.ONE_DAYS,min:0,max:1},{code:u.THREE_DAYS,min:1,max:3},{code:u.SEVEN_DAYS,min:3,max:7},{code:u.FOURTEEN_DAYS,min:7,max:14},{code:u.THIRTY_DAYS,min:14,max:30},{code:u.SIXTY_DAYS,min:30,max:60}],v=(t,...e)=>{if(e.length===0)return"";let a=t;for(let n=0;n<e.length;n+=1){const r=new RegExp(`\\{${n}\\}`,"gm");a=a.replace(r,e[n])}return a},H=(t,e)=>{let a=60;const n=w(!1),r=w(v(t,a));let s=-1;return{checkCodeInput:r,startCount:()=>{a=60,n.value=!0,s!==-1&&window.clearInterval(s),s=window.setInterval(()=>{a-=1,r.value=v(t,a),a<0&&(window.clearInterval(s),r.value=e,a=60,n.value=!1)},1e3)},isDisabled:n}},V=()=>{const t=w(!1),e=w(0),a=y(()=>{if(e.value===0)return"0s";const o=Math.floor(e.value/60),c=e.value%60,i=o===0?"":`${o}m`,l=c===0?"":`${c}s`;return`${i}${l}`});let n=null;const r=()=>{n!==null&&(clearInterval(n),n=null)};return{startCountdown:o=>{e.value=o,t.value=!1,n=setInterval(()=>{e.value>0?e.value--:(t.value=!0,e.value=0,r())},1e3)},timeOut:t,countdownText:a,stopCountDownInterval:r}},h=async(t,e,a,n=!1)=>await k.alert(I("div",{class:"text-lg text-gray-1"},t),{icon:I("em",{class:"iconfont icon-info-circle-line text-brand-2 !text-[32px]"}),customClass:"alert-message-common crs-btn-ui button-reverse crs-btn-message-ui",confirmButtonText:e,cancelButtonText:a,showCancelButton:n,showClose:!1}),O=async()=>{var i,l,p,A,f,m;const t=await C.getters.user;let e,a="";try{e=await((i=window.electronAPI)==null?void 0:i.getVersion())??"1.0.1",a=await((p=(l=window==null?void 0:window.electronAPI)==null?void 0:l.getClientInfo)==null?void 0:p.call(l))??""}catch{e="1.0.1"}const n={app:"SGUI",module:"BKC",version:e,timestamp:"",versionAttr:a.toLowerCase().replaceAll("-","_"),userInfo:{office:t.defaultOffice,airline:"",airport:""}},r=T("09300116"),{data:s}=await _(n,r);if(!s.value)return!1;const o=s.value,c=typeof((A=window.electronAPI)==null?void 0:A.checkUpdate)=="function";if(o.upgradeStrategy===2||o.upgradeStrategy===3)try{const g=S().add(o.daysRemaining,"day");return await h(d.global.t(`app.login.upgradeStrategyRemind${o.upgradeStrategy}`,{version:o.targetVersion,year:g.get("year"),month:g.get("month")+1,date:g.get("date")}),d.global.t("app.login.forUpdates"),d.global.t("app.login.notForUpdates"),!0),a.toLowerCase().includes("win")&&c?(m=(f=window==null?void 0:window.electronAPI)==null?void 0:f.checkUpdate)==null||m.call(f):window.open(o.downloadUrl),!0}catch{return!0}else if(o.upgradeStrategy===4){if(b(),await h(d.global.t(`app.login.upgradeStrategyRemind${o.upgradeStrategy}`,{version:o.targetVersion}),d.global.t("app.login.forUpdates"),d.global.t("app.login.notForUpdates"),!1),a.toLowerCase().includes("win")&&c)return window==null||window.electronAPI.checkUpdate(),!1;window.open(o.downloadUrl);try{return window.electronAPI.goDownloadBroswer({closedWindow:!0}),!1}catch{return!1}}return!0},F=async(t,e)=>{const a=navigator.userAgent.toLowerCase();if(E(t),a.includes("electron/")&&!await O())return Promise.reject("ForUpdate error");e.push("/")},M=async()=>{var n,r,s,o,c;const e=(await x("091V0845")).data,a=(r=(n=((e==null?void 0:e.childNodeList)??[]).find(i=>i.currentNode.code==="SYSTEM"))==null?void 0:n.currentNode)==null?void 0:r.id;if(a){const i={pageNumber:1,pageSize:10,content:{typeId:a.toString(),name:"",code:"EXSYSTEM.SAT.EPID.UNUSE",sort:""}};return(((c=(o=(s=(await R(i,"091V0818")).data)==null?void 0:s.content)==null?void 0:o[0])==null?void 0:c.content)??"").toUpperCase()}return"ALL"},G=async(t,e)=>{var c,i;if(!t||(E(e),!(await N("091V0812")).data))return!1;const n=await P("09400109");if(!((c=n==null?void 0:n.data)!=null&&c.crsSystem))return!1;const r=await M(),s=(((i=n==null?void 0:n.data)==null?void 0:i.defaultOffice)??"").toUpperCase(),o=r==="ALL"||r.includes(s);return o||U("afterLoginBeforeCheckChannel"),!o},B=async()=>{const t=navigator.userAgent.toLowerCase();let e="";return t.includes("electron/")&&(e=await window.electronAPI.getAuthInfo()),e},X=async t=>{if(D().includes("icrspsssell")){const a=t??await B();return a==="null"?"":Y.decode(a??"").toUpperCase()??""}return""},j=async(t,e)=>{U(""),await F(t,e)},J=t=>{if(!t)return 0;const e=S(S(t)).diff(new Date,"days"),a=L.find(n=>!(e<n.min)&&e<n.max);return a?a.max:0};export{V as a,X as b,j as c,J as e,M as g,G as i,F as l,H as u};
