var Ee=Object.defineProperty;var Fe=(t,a,i)=>a in t?Ee(t,a,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[a]=i;var P=(t,a,i)=>(Fe(t,typeof a!="symbol"?a+"":a,i),i);import{b6 as Ne,aE as Le,aF as Ye,w as M,aY as Oe,cg as n,R as f,gm as ke,aX as Me,gg as $e,gA as Ue,i6 as He,dq as Re}from"./index-18f146fc.js";import{a as v}from"./passengerSpecialType-f6d133eb.js";class ge{constructor(){P(this,"mealApply",Ne({vegetarianRadioList:[{lable:"AVML",value:"亚洲素食(AVML)"},{lable:"VOML",value:"东方素食(VOML)"},{lable:"VLML",value:"素食(含糖、鸡蛋)(VLML)"},{lable:"VGML",value:"素食(无糖)(VGML)"},{lable:"VJML",value:"耆那教徒素食(VJML)"}],specialRadioList:[{lable:"BBML",value:"婴儿餐(BBML)"},{lable:"CHML",value:"儿童餐(CHML)"},{lable:"HNML",value:"印度餐(HNML)"},{lable:"KSML",value:"犹太教餐(KSML)"},{lable:"MOML",value:"穆斯林餐(MOML)"},{lable:"DBML",value:"糖尿病患者餐(DBML)"}],preferenceRadioList:[{lable:"FPML",value:"鲜水果餐(FPML)"},{lable:"LCML",value:"低卡路里餐(LCML)"},{lable:"SFML",value:"海鲜餐(SFML)"},{lable:"LSML",value:"无盐餐(LSML)"},{lable:"BLML",value:"流食(BLML)"},{lable:"NLML",value:"不含乳糖食品(NLML)"},{lable:"RVML",value:"生菜蔬食品(RVML)"},{lable:"LFML",value:"低胆固醇、低脂肪餐(LFML)"},{lable:"GFML",value:"过重自由食物(GFML)"},{lable:"SPML",value:"禁忌餐食(SPML)"}],mealValue:""}))}}const h=new ge,he={normalType:[{lable:"UMNR",value:"无陪儿童(UMNR)"},{lable:"CHLD",value:"儿童出生日期(CHLD)"},{lable:"CTCE",value:"邮箱(CTCE)"},{lable:"FQTV",value:"常旅客(FQTV)"},{lable:"CTCM",value:"电话(CTCM)"}],specialType:[{lable:"DEPA",value:"被驱逐出境(有人陪伴)(DEPA)"},{lable:"DEPU",value:"被驱逐出境(无人陪伴)(DEPU)"},{lable:"HNML",value:"印度餐(HNML)"},{lable:"STCR",value:"担架旅客(STCR)"},{lable:"GMJC",value:"革命伤残军人(GMJC)"},{lable:"SEMN",value:"船员水手(SEMN)"},{lable:"MEDA",value:"严重疾病旅客(MEDA)"},{lable:"DEAF",value:"聋哑旅客(DEAF)"},{lable:"BLND",value:"盲人旅客(BLND)"},{lable:"WCHR",value:"有自理能力轮椅(WCHR)"},{lable:"WCHS",value:"半自理能力轮椅(WCHS)"},{lable:"WCHC",value:"无自理能力轮椅(WCHC)"},{lable:"STPC",value:"中转休息室(STPC)"},{lable:"OTHER",value:"其他类型"}]};class xe{constructor(){P(this,"baggageFormData",Ne({radio1:"",quantityInput:"",weightInput:"",sizeInput:""}));P(this,"specialRadioList1",new Map([["宠物(PETC)","PETC"],["被约束的动物(AVIH)","AVIH"],["占座行李(CBBG)","CBBG"],["超大行李(BULK)","BULK"],["自行车(BIKE)","BIKE"],["体育设施(SPEQ)","SPEQ"],["易碎行李(FRAG)","FRAG"]]));P(this,"specialRadioList2",new Map([["婴儿摇篮(BSCT)","BSCT"],["被约束的动物(AVIH)","AVIH"]]));P(this,"specialRadioList3",new Map([["无烟靠走廊(NSSA)","NSSA"],["无烟靠窗(NSSW)","NSSW"],["吸烟靠走廊(SMSA)","SMSA"],["吸烟靠窗(SMSW)","SMSW"],["额外占座(EXST)","EXST"],["无烟座位(NSST)","NSST"],["吸烟座位(SMST)","SMST"],["座位(SEAT)","SEAT"]]))}GET_CODE(a){return this.specialRadioList1.get(a)||this.specialRadioList2.get(a)}}const S=new xe,Be=Le(),Ge=/[\u4e00-\u9fa5]+/,{activeTag:A,orderInfo:ve,originPnrData:Ke}=Ye(Be),I=M(()=>ve.value.get(A.value).flight??[]),Se=M(()=>{var t;return Oe(((t=ve.value.get(A.value))==null?void 0:t.passengers)??[])}),we=M(()=>{var t,a;return((a=(t=Ke.value)==null?void 0:t.get(A.value))==null?void 0:a.passengers)??[]}),st=[{label:n.global.t("app.intlPassengerForm.Instructions_IDCarDAndResidence"),value:"NI_I",certificateType:"FOID"},{label:n.global.t("app.intlPassengerForm.Instructions_UnableAndSpecialID"),value:"UU",certificateType:"FOID"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_M"),value:"PP_M",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_G"),value:"PP_G",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_A"),value:"PP_A",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_C"),value:"PP_C",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],pt=[{label:n.global.t("app.intlPassengerForm.Instructions_IDCarDAndResidence"),value:"NI_I",certificateType:"FOID"},{label:n.global.t("app.intlPassengerForm.Instructions_UnableAndSpecialID"),value:"UU",certificateType:"FOID"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_M"),value:"PP_M",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_G"),value:"PP_G",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_A_FRE"),value:"PP_A",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_C"),value:"PP_C",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],dt=[{label:n.global.t("app.intlPassengerForm.Instructions_IDCarDAndResidence"),value:"NI_I",certificateType:"FOID"},{label:n.global.t("app.intlPassengerForm.Instructions_UnableAndSpecialID"),value:"UU",certificateType:"FOID"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_A"),value:"PP_A",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],ft=[{label:n.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"}],Ve=[{label:n.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_AIN"),value:"PP_A",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_M"),value:"PP_M",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_G"),value:"PP_G",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_C"),value:"PP_C",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IN"),value:"PP_IN",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],mt=[{label:n.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_AIN"),value:"PP_A",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IN"),value:"PP_IN",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_T"),value:"PP_T",certificateType:"DOCS"}],Tt=[{label:n.global.t("app.intlPassengerForm.Instructions_PP_P"),value:"PP_P",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IP"),value:"PP_IP",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_A"),value:"PP_A",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_I"),value:"PP_I",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_F"),value:"PP_F",certificateType:"DOCS"},{label:n.global.t("app.intlPassengerForm.Instructions_PP_IN"),value:"PP_IN",certificateType:"DOCS"}],bt=[{label:n.global.t("app.intlPassengerForm.CBBG"),value:"CBBG"},{label:n.global.t("app.intlPassengerForm.STCR"),value:"STCR"},{label:n.global.t("app.intlPassengerForm.EXST"),value:"EXST"}],N=(t,a)=>{for(const[i,l]of t.entries())if(l===a)return i;return""},Pt=t=>{const a=[...h.mealApply.preferenceRadioList,...h.mealApply.specialRadioList,...h.mealApply.vegetarianRadioList],i=[...he.normalType,...he.specialType];let l="";return i.find(e=>e.lable===t)&&(l="iconfont icon-specialpassenger"),a.find(e=>e.lable===t)&&(l="iconfont icon-meal"),(N(S.specialRadioList1,t)||N(S.specialRadioList2,t))&&(l="iconfont icon-suitcase-fill"),N(S.specialRadioList3,t)&&(l="iconfont icon-seat-fill"),l},Dt=t=>{const a=/[\s/\n]/g;return(t==null?void 0:t.replace(a,""))||""},Ct=(t,a,i)=>{const l=[],e=I.value.filter(s=>!s.disabled),o=we.value.find(s=>s.fullName===a);e.forEach(s=>{s.segments.forEach(r=>{var T;const p=t.find(d=>{var b;return`${r.departureAirportCode}-${r.arrivalAirportCode}${f(r.departureDate).format("YYYY-MM-DD")}${r.airlines.airCode}${r.airlines.flightNo} ${(b=r.cabins)==null?void 0:b[0].cabinName}`==`${d.origin}-${d.destination}${d.flightDate}${d.airline}${d.flightNumber} ${d.classId}`}),m=((o==null?void 0:o.segments)??[]).find(d=>{var b;return`${r.departureAirportCode}-${r.arrivalAirportCode}${f(r.departureDate).format("YYYY-MM-DD")}${r.airlines.airCode}${r.airlines.flightNo} ${(b=r.cabins)==null?void 0:b[0].cabinName}`==`${d.origin}-${d.destination}${d.flightDate}${d.airline}${d.flightNumber} ${d.classId}`});if(p)l.push(p);else if(o&&m)l.push(m);else{const d={actionCode:r.actionCode,origin:r.departureAirportCode,originCH:r.departureAirportCN,destination:r.arrivalAirportCode,destinationCH:r.arrivalAirportCN,services:[],arnkInd:r.airlines.flightNo==="ARNK",openInd:s.openFlag,airline:r.airlines.airCode,flightNumber:r.airlines.flightNo,classId:(T=r.cabins)!=null&&T.length?r.cabins[0].cabinName:"",flightDate:f(r.departureDate).format("YYYY-MM-DD"),departureDate:r.departureDate,departureTime:r.departureTime,arrivalTime:r.arrivalTime,ocAirline:r.airlines.ocAirline??"",ocFlightNumber:r.airlines.ocFlightNumber??""};i&&(d.hasInft=!0,d.inftActionCode="NN"),l.push(d)}})});const u=[];return(Qe(!0)??[]).forEach(s=>{const r=(l??[]).find(p=>`${s.origin}-${s.destination}${f(s.flightDate).format("YYYY-MM-DD")}${s.airline}${s.flightNumber} ${s.classId}`==`${p.origin}-${p.destination}${p.flightDate}${p.airline}${p.flightNumber} ${p.classId}`);r&&u.push(r)}),u},It=t=>{const a=[];return I.value.forEach(i=>{i.segments.forEach(l=>{var o;const e=t.find(u=>{var c;return`${l.departureAirportCode}-${l.arrivalAirportCode}${f(l.departureDate).format("YYYY-MM-DD")}${l.airlines.airCode}${l.airlines.flightNo} ${(c=l.cabins)==null?void 0:c[0].cabinName}`==`${u.origin}-${u.destination}${f(u.flightDate).format("YYYY-MM-DD")}${u.airline}${u.flightNumber} ${u.classId}`});a.push({actionCode:l.actionCode,origin:l.departureAirportCode,originCH:l.departureAirportCN,destination:l.arrivalAirportCode,destinationCH:l.arrivalAirportCN,services:(e==null?void 0:e.services)??[],arnkInd:l.airlines.flightNo==="ARNK",openInd:i.openFlag,airline:l.airlines.airCode,flightNumber:l.airlines.flightNo,classId:(o=l.cabins)!=null&&o.length?l.cabins[0].cabinName:"",flightDate:l.departureDate,departureDate:l.departureDate,departureTime:l.departureTime,arrivalTime:l.arrivalTime,ocAirline:l.airlines.ocAirline??"",ocFlightNumber:l.airlines.ocFlightNumber??""})})}),a},Qe=t=>{const a=[];return I.value.forEach(i=>{t&&i.disabled||i.segments.forEach(l=>{var e;a.push({actionCode:l.actionCode,origin:l.departureAirportCode,originCH:l.departureAirportCN,destination:l.arrivalAirportCode,destinationCH:l.arrivalAirportCN,services:[],arnkInd:l.airlines.flightNo==="ARNK",openInd:i.openFlag,airline:l.airlines.airCode,flightNumber:l.airlines.flightNo,classId:(e=l.cabins)!=null&&e.length?l.cabins[0].cabinName:"",flightDate:l.departureDate,departureDate:l.departureDate,departureTime:l.departureTime,arrivalTime:l.arrivalTime,ocAirline:l.airlines.ocAirline??"",ocFlightNumber:l.airlines.ocFlightNumber??""})})}),a},yt=()=>{const t=[];return I.value.forEach(a=>{a.segments.forEach(i=>{t.push({departureAirport:i.departureAirportCode,arrivalAirport:i.arrivalAirportCode})})}),t},ht=t=>{const a=[];return t?Se.value.forEach(i=>{var l;a.push({name:((l=i.infantDetail)==null?void 0:l.fullName)??"",type:"INF"})}):Se.value.forEach(i=>{a.push({name:i.fullName,type:i.passengerType})}),a},St=t=>{if(t&&!t.includes("_"))switch(t){case"NI":return"NI_I";case"UU":return"UU";default:return`PP_${t}`}return t??""},Nt=t=>{let a="";switch(t){case"ADT":a=n.global.t("app.passengerType.ADT");break;case"CHD":a=n.global.t("app.passengerType.CHD");break;case"INF":a=n.global.t("app.passengerType.INF");break;case"UM":a=n.global.t("app.passengerType.UMCHD");break;case"STU":a=n.global.t("app.passengerType.STU");break;case"EMI":a=n.global.t("app.passengerType.EMI");break;case"SEA":a=n.global.t("app.passengerType.SEA");break;case"LBR":a=n.global.t("app.passengerType.LBR");break;case"GM":a=n.global.t("app.passengerType.GM");break;case"JC":a=n.global.t("app.passengerType.JC");break;default:a=n.global.t("app.passengerType.ADT");break}return a},Mt=async()=>{const a=(await v()).filter(i=>i.field1==="CHD"&&i.field2!=="UM").map(i=>({value:i.field2,label:i.field2}));return a.map((i,l)=>{(i.value==="MISS"||i.value==="MSTR")&&a.unshift(a.splice(l,1)[0])}),a},vt=async()=>{const a=(await v()).filter(i=>i.field1==="ADT"&&i.field2!=="JC"&&i.field2!=="GM").map(i=>({checked:!1,value:i.field2,label:i.field2}));return a.map((i,l)=>{(i.value==="MR"||i.value==="MS")&&a.unshift(a.splice(l,1)[0])}),a},At=async()=>{const a=(await v()).filter(i=>i.field1==="INF").map(i=>({checked:!1,value:i.field2,label:i.field2}));return a.map((i,l)=>{(i.value==="MISS"||i.value==="MSTR")&&a.unshift(a.splice(l,1)[0])}),a},_t=(t,a)=>{const i=Ge.test(t);return a?i?"":" ":""};var We=(t=>(t.CONTACT_PERSON="contactPerson",t.ISSUE_LIMIT="issueLimit",t.PASSENGER="passenger",t.FLIGHT="flight",t.SPECIAL_SERVICES="specialServices",t.SALE_FARE="saleFare",t.CHANGE="changeTicket",t.FP="fp",t.CC="cc",t.EI="ei",t.TC="tc",t.OI="oi",t.SVC="svc",t.REMARK="remark",t.TICKET_NUMBERS="ticketNumbers",t.UN_TICKET_NUMBERS="unTicketNumbers",t.SSR_INFT="ssrInft",t.TN_NUMBERS="tn",t.XN_NUMBERS="xn",t.GROUP="group",t.SSR_CHD="ssrChd",t.SSR_ADTK="ssrAdtk",t))(We||{}),qe=(t=>(t.UPDATE="UPDATE",t.DELETE="DELETE",t.ADD="ADD",t))(qe||{}),C=(t=>(t.TKT="TKT",t.QTE="QTE",t.TQ="T&Q",t))(C||{}),Ae=(t=>(t.ADT="ADT",t.CHD="CHD",t.INF="INF",t.JC="JC",t.GM="GM",t.SEA="SEA",t.LBR="LBR",t.ADT_EMI="ADT_EMI",t.ADT_STU="ADT_STU",t.CHD_STU="CHD_STU",t.CHD_EMI="CHD_EMI",t))(Ae||{}),Xe=(t=>(t.M="M",t.F="F",t))(Xe||{});const D=(t,a)=>(t??[]).map(i=>({type:a,text:i.text.trim(),airlineCode:i.airline})),ze=t=>(t??[]).map(a=>{var i,l,e,o,u,c,s;return{type:`SSR ${(l=a==null?void 0:a.text)==null?void 0:l.substring(0,(i=a==null?void 0:a.text)==null?void 0:i.indexOf(" "))}`,text:(u=(o=(e=a==null?void 0:a.text)==null?void 0:e.split(" "))==null?void 0:o.slice(2).join(" "))==null?void 0:u.trim(),airlineCode:((s=(c=a==null?void 0:a.text)==null?void 0:c.split(" "))==null?void 0:s[1])??""}}),Et=t=>t.some(a=>(a.segments??[]).some(i=>i.segmentType==="2")),Ft=t=>t.every(a=>(a.segments??[]).every(i=>{var l;return i.segmentType==="2"||(((l=i==null?void 0:i.airlines)==null?void 0:l.flightNo)??"")==="ARNK"})),Lt=t=>t.every(a=>(a.segments??[]).every(i=>{var l;return i.segmentType!=="2"||(((l=i==null?void 0:i.airlines)==null?void 0:l.flightNo)??"")==="ARNK"})),Yt=async t=>{const a=t.flatMap(i=>i.segments.flatMap(l=>[l.departureAirportCode,l.arrivalAirportCode]));return await ke(a)},Ot=async t=>{const a=await Me("DOMESTIC_AIRLINE_LOCAL_DATA"),i=(a==null?void 0:a.localData)??[];if(i.length===0)return!1;const l=new Set;return t.forEach(e=>e.segments.forEach(o=>{o.airlines&&(o.airlines.airCode&&l.add(o.airlines.airCode),o.airlines.ocAirline&&l.add(o.airlines.ocAirline))})),[...l].some(e=>!i.includes(e))},je=(t,a)=>{const i=[];return t.forEach(l=>{a.forEach(e=>{e===l.code&&l.hostgds==="1E"&&i.push(l)})}),i},kt=async t=>{const a=await Me("AIRLINE_LOCAL_DATA");if(a!=null&&a.localData){const i=JSON.parse((a==null?void 0:a.localData)??"")??[],l=new Set;t.forEach(o=>o.segments.forEach(u=>{var c,s;o.disabled||((c=u.airlines)!=null&&c.airCode&&l.add(u.airlines.airCode),(s=u.airlines)!=null&&s.ocAirline&&l.add(u.airlines.ocAirline))}));const e=je(i,[...l]);return l.size===0?!0:(e==null?void 0:e.length)===l.size}return!0},$t=(t,a)=>{let i=!1;return{flights:t.map(e=>{var u;if(!e.openFlag)return{segments:e.segments.map(c=>{var p,m,T,d;let s={};const r=f(c.departureDate).isValid()?f(c.departureDate).format("YYYY-MM-DD"):"";return c.airlines.flightNo==="ARNK"?(s={segmentType:"ARNK",departureAirport:c.departureAirportCode,arrivalAirport:c.arrivalAirportCode,marketingAirline:"",cabinCode:"",departureDate:r},a&&(s.selectedFlag=(p=c.selectedFlag)!=null&&p.includes(C.TQ)?"T_Q":c.selectedFlag),s):(s={actionCode:$e(((m=c.cabins[0])==null?void 0:m.state)??""),departureDate:r,departureTime:c.departureTime,arrivalDate:f(c.arrivalDate).format("YYYY-MM-DD"),arrivalTime:c.arrivalTime,departureTerminal:c.departureTerminal,arrivalTerminal:c.arrivalTerminal,departureAirport:c.departureAirportCode,arrivalAirport:c.arrivalAirportCode,marketingFlightNumber:c.airlines.flightNo,sharedInfo:c.airlines.isShared,marketingPlaneType:c.airlines.planeType,marketingAirline:c.airlines.airCode,cabinCode:(T=c.cabins)!=null&&T.length?c.cabins[0].cabinName:"",segmentType:c.airlines.flightNo==="ARNK"?"ARNK":"NORMAL"},a&&(s.selectedFlag=(d=c.selectedFlag)!=null&&d.includes(C.TQ)?"T_Q":c.selectedFlag),s)})};i=!0;let o={};return o={segments:[{segmentType:"OPEN",departureAirport:e.segments[0].departureAirportCode,arrivalAirport:e.segments[0].arrivalAirportCode,marketingAirline:e.segments[0].airlines.airCode,cabinCode:e.segments[0].cabins[0].cabinName,departureDate:e.segments[0].departureDate?f(e.segments[0].departureDate).format("YYYY-MM-DD"):""}]},a&&(o.segments[0].selectedFlag=(u=e.segments[0].selectedFlag)!=null&&u.includes(C.TQ)?"T_Q":e.segments[0].selectedFlag),o}),openFlag:i}},Ze=(t,a)=>{const i=[];return a.forEach(l=>l.segments.forEach(e=>{var u;if((t??[]).find(c=>c.hasInft&&`${c.origin}-${c.destination}-${c.departureDate}`==`${e.departureAirportCode}-${e.arrivalAirportCode}-${e.departureDate}`)){const c={departureDate:f(e.departureDate).format("YYYY-MM-DD"),departureTime:e.departureTime,arrivalDate:f(e.arrivalDate).format("YYYY-MM-DD"),arrivalTime:e.arrivalTime,departureTerminal:e.departureTerminal,arrivalTerminal:e.arrivalTerminal,departureAirport:e.departureAirportCode,arrivalAirport:e.arrivalAirportCode,marketingFlightNumber:e.airlines.flightNo,sharedInfo:e.airlines.isShared,marketingPlaneType:e.airlines.planeType,marketingAirline:e.airlines.airCode,cabinCode:(u=e.cabins)!=null&&u.length?e.cabins[0].cabinName:"",segmentType:e.airlines.flightNo==="ARNK"?"ARNK":"NORMAL"};i.push(c)}})),i},Ut=(t,a)=>{if(!t)return!0;const i=Ve.map(l=>l.value);return a.every(l=>{var o;const e=((o=l==null?void 0:l.document)==null?void 0:o.documentType)??"";return e===""?!0:i.includes(e)})},Je=(t,a)=>{var i;(i=t==null?void 0:t.document)!=null&&i.idCardNumber&&!a&&He.test(t.document.idCardNumber)&&(t.document.ssrType="FOID",t.document.documentType="NI_I")},et=(t,a)=>{const i=[];return a.forEach(l=>l.segments.forEach(e=>{var u;const o=(t??[]).find(c=>{const s=f(e.departureDate).format("YYYY-MM-DD"),r=f(c.flightDate).format("YYYY-MM-DD");return c.frequentNumber&&`${c.origin}-${c.destination}-${r}`==`${e.departureAirportCode}-${e.arrivalAirportCode}-${s}`});if(o){const c={departureDate:f(e.departureDate).format("YYYY-MM-DD"),departureTime:e.departureTime,arrivalDate:f(e.arrivalDate).format("YYYY-MM-DD"),arrivalTime:e.arrivalTime,departureTerminal:e.departureTerminal,arrivalTerminal:e.arrivalTerminal,departureAirport:e.departureAirportCode,arrivalAirport:e.arrivalAirportCode,marketingFlightNumber:e.airlines.flightNo,sharedInfo:e.airlines.isShared,marketingPlaneType:e.airlines.planeType,marketingAirline:e.airlines.airCode,cabinCode:(u=e.cabins)!=null&&u.length?e.cabins[0].cabinName:"",segmentType:e.airlines.flightNo==="ARNK"?"ARNK":"NORMAL",frequentNumber:o.frequentNumber};i.push(c)}})),i},tt=t=>{const a=[];return t.osiCtcm&&a.push({type:"OSI CTCM",text:t.osiCtcm??""}),t.ssrCtcm&&a.push({type:"SSR CTCM",text:t.ssrCtcm??""}),a},Ht=t=>t?(t??"").split("、").map(i=>`${i}`)??[]:[],at=t=>t.map(a=>/\/[pP]\d+$/i.test(a)?a.replace(/\/[pP]\d+$/i,""):a),Rt=(t,a,i)=>{const l=[];return t.forEach(e=>{var r,p,m,T,d,b,_,E,F,L,Y,O,k,$,U,H,R,g,x,B,G,K,w,V,Q,W,q,X,z,j,Z,J,ee,te,ae,ie,le,re,ne,oe,ce,ue,se,pe,de,fe,me,Te,be,Pe,De,Ce,Ie,ye;Je(e,i);const o=["JC","GM","UM"];!i&&(e.passengerType.includes("CHD")||o.includes((e==null?void 0:e.specialPassengerType)??""));const u=(r=e==null?void 0:e.document)==null?void 0:r.idCardNumber,c=/\(\s*UM\d+\)/g.test(e.fullName)&&e.passengerType.includes("CHD"),s={infantRef:e.infantDetail?e.infantDetail.id:"",id:e.id,sex:(e==null?void 0:e.document.gender)??"",passengerType:e.passengerType,specialPassengerType:e.specialPassengerType??"",certificateType:u?e==null?void 0:e.document.documentType:"",certificateNo:e==null?void 0:e.document.idCardNumber,certificateIssueCountry:u?((p=e==null?void 0:e.document)==null?void 0:p.visaIssueCountry)??"":"",certificateHolderInternational:u?((m=e==null?void 0:e.document)==null?void 0:m.passengerNationality)??"":"",certificateExpiryDate:u?((T=e==null?void 0:e.document)==null?void 0:T.visaExpiryDate)??"":"",birthday:(e==null?void 0:e.birthday)??"",fullName:c?e.fullName.replace(/\(\s*UM\d+\)/g,"").replace(/\s{2,}/g," ").trim():e.fullName,umnrInd:c,infantChineseName:((d=e==null?void 0:e.infantDetail)==null?void 0:d.chineseName)??"",nameType:e.chineseName?"CN":"EN",contactAndCommunicationList:tt(e),email:(e==null?void 0:e.passengerEmail)??"",vipType:e.vipType,vipTextList:at(e.vipTexts??[]),identityText:e.identityText??"",supplementaryIdentityInfoList:e.supplementaryIdentityInfoList??[],ppForDocs:e.ppForDocs??"",docsName:u?(b=e==null?void 0:e.document)==null?void 0:b.docsName:"",addressInfo:{live:{countryCode:((_=e==null?void 0:e.docaInfoR)==null?void 0:_.country)??"",provinceCode:((E=e==null?void 0:e.docaInfoR)==null?void 0:E.state)??"",cityNameEn:((F=e==null?void 0:e.docaInfoR)==null?void 0:F.city)??"",postcode:((L=e==null?void 0:e.docaInfoR)==null?void 0:L.zip)??"",detailAddress:((Y=e==null?void 0:e.docaInfoR)==null?void 0:Y.address)??""},arrival:{countryCode:((O=e==null?void 0:e.docaInfoD)==null?void 0:O.country)??"",provinceCode:((k=e==null?void 0:e.docaInfoD)==null?void 0:k.state)??"",cityNameEn:(($=e==null?void 0:e.docaInfoD)==null?void 0:$.city)??"",postcode:((U=e==null?void 0:e.docaInfoD)==null?void 0:U.zip)??"",detailAddress:((H=e==null?void 0:e.docaInfoD)==null?void 0:H.address)??""}},niForDocs:(e==null?void 0:e.niForDocs)??"",frequenters:et(e.segments??[],a??[]),holder:((R=e.document)==null?void 0:R.holder)??"",familyId:e.familyId??0};if(e.infantDetail){const y=(x=(g=e.infantDetail)==null?void 0:g.document)==null?void 0:x.idCardNumber,_e={infantRef:"",id:e.infantDetail.id,sex:((B=e.infantDetail)==null?void 0:B.document.gender)??"",passengerType:Ae.INF,certificateType:y?(G=e.infantDetail)==null?void 0:G.document.documentType:"",certificateNo:(K=e.infantDetail)==null?void 0:K.document.idCardNumber,certificateIssueCountry:y?((V=(w=e.infantDetail)==null?void 0:w.document)==null?void 0:V.visaIssueCountry)??"":"",certificateHolderInternational:y?((W=(Q=e.infantDetail)==null?void 0:Q.document)==null?void 0:W.passengerNationality)??"":"",certificateExpiryDate:((q=e.infantDetail)==null?void 0:q.document.visaExpiryDate)??"",birthday:(e.infantDetail.birthday||((X=e.infantDetail)==null?void 0:X.document.birthday))??"",fullName:e.infantDetail.fullName,infantChineseName:((z=e==null?void 0:e.infantDetail)==null?void 0:z.chineseName)??"",nameType:e.infantDetail.chineseName?"CN":"EN",phoneNumber:"",identityText:"",nameSuffix:((j=e.infantDetail)==null?void 0:j.nameSuffix)??"",inftSuffix:((Z=e.infantDetail)==null?void 0:Z.inftSuffix)??"",ppForDocs:"",addressInfo:{live:{countryCode:((ee=(J=e.infantDetail)==null?void 0:J.docaInfoR)==null?void 0:ee.country)??"",provinceCode:((ae=(te=e.infantDetail)==null?void 0:te.docaInfoR)==null?void 0:ae.state)??"",cityNameEn:((le=(ie=e.infantDetail)==null?void 0:ie.docaInfoR)==null?void 0:le.city)??"",postcode:((ne=(re=e.infantDetail)==null?void 0:re.docaInfoR)==null?void 0:ne.zip)??"",detailAddress:((ce=(oe=e.infantDetail)==null?void 0:oe.docaInfoR)==null?void 0:ce.address)??""},arrival:{countryCode:((se=(ue=e.infantDetail)==null?void 0:ue.docaInfoD)==null?void 0:se.country)??"",provinceCode:((de=(pe=e.infantDetail)==null?void 0:pe.docaInfoD)==null?void 0:de.state)??"",cityNameEn:((me=(fe=e.infantDetail)==null?void 0:fe.docaInfoD)==null?void 0:me.city)??"",postcode:((be=(Te=e.infantDetail)==null?void 0:Te.docaInfoD)==null?void 0:be.zip)??"",detailAddress:((De=(Pe=e.infantDetail)==null?void 0:Pe.docaInfoD)==null?void 0:De.address)??""}},holder:((Ie=(Ce=e.infantDetail)==null?void 0:Ce.document)==null?void 0:Ie.holder)??"",inftSelectedSegments:Ze(((ye=e.infantDetail)==null?void 0:ye.segments)??[],a??[])};l.push(_e)}l.push(s)}),l},gt=t=>{const a=[];return a.push(...D(t.remarks??[],"RMK")),a.push(...D(t.remarkOsis??[],"OSI")),a.push(...D(t.others??[],"SSR OTHS")),a.push(...D(t.ckins??[],"SSR CKIN")),a.push(...D(t.clids??[],"SSR CLID")),a.push(...ze(t.ssrContents??[])),a},it=t=>{if(t&&t.length){const a=t.map(i=>Ue(i)?i.ct:i);return a==null?void 0:a.filter(i=>i)}return[]},lt=t=>(t??[]).filter(a=>a.airline&&a.phoneNumber),xt=t=>({ctList:it(t.cts),email:t.contactorEmail,phoneInfoList:lt(t.contact)}),Bt=(t,a)=>({timeLimit:t.issueLimitType==="TL"&&t.issueLimitCrs?f(t.issueLimitCrs).format("YYYY-MM-DD HH:mm"):"",office:t.issueLimitType==="TL"&&t.issueLimitOffice?t.issueLimitOffice:a??"",ticketNumber:t.issueLimitType==="T"||t.issueLimitType==="WC"?(t.issueLimitCrs??"").substring(2):""}),rt=(t,a)=>({actionCode:a??"NN",marketingAirline:t.airline,departureAirport:t.origin,arrivalAirport:t.destination,marketingFlightNumber:t.flightNumber,cabinCode:t.classId,departureDate:f(t.flightDate).format("YYYY-MM-DD")}),Gt=t=>{const a=[];return t.forEach(i=>{i.segments.forEach(l=>{l.services.forEach(e=>{const o={type:e.ssrCode,text:e.text??"",passengerIds:[i.id],specialServiceSegments:[rt(l,e.actionCode)],specialServiceNumber:1};a.push(o)})})}),a},nt=(t,a,i,l)=>(t??[]).map(o=>{var s,r;const u=(o.fareInfo.segInfos??[]).map(p=>({fareBasisCodes:p.fareBasisCodes,airline:p.companyCode})),c=[];return(o.baggageAllowance??[]).forEach((p,m)=>{c.push({baggage:p,segIndex:(m+1).toString()})}),{priceItemId:Re(),specialPassengerType:o.code,sequenceNmbr:o.fareInfo.sequenceNmbr,ticketAmount:o.fareInfo.tktAmount,totalAmount:o.fareInfo.totalAmount,currency:o.fareInfo.currency,fuel:o.fareInfo.fuel,fund:o.fareInfo.fund,fn:o.fareInfo.fn,fc:o.fareInfo.fc,commissionRate:Number((o==null?void 0:o.agentRate)??0),segmentInfos:u,baggageWithSegIndex:c,passengerNum:((s=a==null?void 0:a[o==null?void 0:o.code])==null?void 0:s.passengerNum)??1,passengerTypeUpdate:o.childUseAdultFare?"ADT":l.passengerTypeUpdate?l.passengerTypeUpdate:o.code,passengerAge:((r=a==null?void 0:a[o==null?void 0:o.code])==null?void 0:r.passengerAge)??0,passengerIds:o.pasgId??[],chdUsingAdtPrice:(o==null?void 0:o.childUseAdultFare)??!1}}),Kt=(t,a,i,l)=>{if(!(t!=null&&t.passengerFares))return[];const e=new Map;(t.passengerFares??[]).forEach(c=>{const s=c.advanced,r=JSON.stringify(s);e.has(r)||e.set(r,{...s,priceItem:[]}),e.get(r).priceItem.push(c)});const o=[...e.values()],u=[];return o.forEach((c,s)=>{const r=c.priceItem[0].advanced;u.push({priceId:s+1+"",ticketType:c.ticketType,negotiatedFareCode:r!=null&&r.isGovernment?"GP":(r==null?void 0:r.keyCustomerCode)??"",queryExclusiveNegotiatedFare:(r==null?void 0:r.exclusiveNegotiated)??!1,fareBasic:(r==null?void 0:r.farebasic)??"",fareType:(r==null?void 0:r.fareType)??"",pricingSource:(r==null?void 0:r.classType)??"Both",currencyCode:(r==null?void 0:r.currencyCode)??"CNY",issuingAirline:(r==null?void 0:r.airlines)??"",airportCode:(r==null?void 0:r.placeOfSale)??"",calculateLowestPrice:(r==null?void 0:r.minimumPrice)??"",calculateAllBrand:(r==null?void 0:r.allBrands)??"",payMethod:r!=null&&r.governmentCC?"CC":r!=null&&r.paymentMethod?r==null?void 0:r.paymentMethod:"CASH",priceItems:nt(c.priceItem,i,s,r),groupPassengerType:(r==null?void 0:r.passengerType)??"",discountCode:l,brand:(r==null?void 0:r.brand)??"",baggage:(r==null?void 0:r.baggage)??"",publishCurrencyCode:(r==null?void 0:r.publishCurrencyCode)??"",fareNumber:(r==null?void 0:r.fareNumber)??"",updatePrice:(r==null?void 0:r.updatePrice)??""})}),u},wt=t=>Object.entries(t).filter(i=>i[0]!=="CC").reduce((i,[l,e])=>{const o=e.map(u=>({passengerIds:u.passengerIds,text:u.text,type:l}));return i.concat(o)},[]),Vt=(t,a)=>{var u,c,s,r;const i=((r=(s=(c=(u=t??[])==null?void 0:u[0])==null?void 0:c.segments)==null?void 0:s[0])==null?void 0:r.departureDate)??"",l=(a??[]).filter(p=>p.passengerType.includes("ADT"));if(l.length===0)return!1;const e=(l??[]).every(p=>f(i).subtract(18,"year").isBefore(f((p==null?void 0:p.birthday)??""))),o=(a??[]).some(p=>p.passengerType.includes("CHD")||p.infantDetail);return e&&o},Qt=(t,a)=>{if(!(t??[]).length)return[];const i=[{label:a==="issue"?n.global.t("app.pnrManagement.paymentMethod.bspTicket"):n.global.t("app.pnrManagement.paymentMethod.bspTicketTry"),value:"BSP"},{label:a==="issue"?n.global.t("app.pnrManagement.paymentMethod.bopTicket"):n.global.t("app.pnrManagement.paymentMethod.bopTicketTry"),value:"BOP"},{label:a==="issue"?n.global.t("app.pnrManagement.paymentMethod.cdsTicket"):n.global.t("app.pnrManagement.paymentMethod.cdsTicketTry"),value:"CDS"},{label:a==="issue"?n.global.t("app.pnrManagement.paymentMethod.currentTicket"):n.global.t("app.pnrManagement.paymentMethod.currentTicketTry"),value:"ARL"}];if((t??[]).find(o=>o==="$$$"))return i;const e=[];return(t??[]).forEach(o=>{switch(o){case"BSP":e.push({label:a==="issue"?n.global.t("app.pnrManagement.paymentMethod.bspTicket"):n.global.t("app.pnrManagement.paymentMethod.bspTicketTry"),value:"BSP"});break;case"BOP":e.push({label:a==="issue"?n.global.t("app.pnrManagement.paymentMethod.bopTicket"):n.global.t("app.pnrManagement.paymentMethod.bopTicketTry"),value:"BOP"});break;case"CDS":e.push({label:a==="issue"?n.global.t("app.pnrManagement.paymentMethod.cdsTicket"):n.global.t("app.pnrManagement.paymentMethod.cdsTicketTry"),value:"CDS"});break;case"本票":e.push({label:a==="issue"?n.global.t("app.pnrManagement.paymentMethod.currentTicket"):n.global.t("app.pnrManagement.paymentMethod.currentTicketTry"),value:"ARL"});break}}),e};export{et as A,Qt as B,Et as C,Tt as D,Ot as E,Ut as F,Xe as G,Vt as H,At as I,Rt as J,xt as K,gt as L,Gt as M,Kt as N,Bt as O,We as P,wt as Q,Ft as R,Lt as S,bt as T,he as U,S as V,C as W,pt as X,Yt as a,Dt as b,Pt as c,yt as d,ht as e,qe as f,Nt as g,Qe as h,_t as i,it as j,Ct as k,It as l,Ht as m,Ae as n,kt as o,ft as p,Ve as q,mt as r,St as s,st as t,h as u,dt as v,Mt as w,vt as x,$t as y,Ze as z};
