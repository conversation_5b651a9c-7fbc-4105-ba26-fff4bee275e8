import{jw as O,gA as P,fq as L,jy as E,hu as m,eD as v,em as D,en as I,i as _,eC as w,iq as x}from"./index-18f146fc.js";import{b as C,h as F}from"./isEqual-9c56e106.js";var H=1,T=2;function G(n,e,t,r){var s=t.length,o=s,u=!r;if(n==null)return!o;for(n=Object(n);s--;){var i=t[s];if(u&&i[2]?i[1]!==n[i[0]]:!(i[0]in n))return!1}for(;++s<o;){i=t[s];var a=i[0],c=n[a],l=i[1];if(u&&i[2]){if(c===void 0&&!(a in n))return!1}else{var d=new O;if(r)var p=r(c,l,a,n,e,d);if(!(p===void 0?C(l,c,H|T,r,d):p))return!1}}return!0}function y(n){return n===n&&!P(n)}function S(n){for(var e=L(n),t=e.length;t--;){var r=e[t],s=n[r];e[t]=[r,s,y(s)]}return e}function R(n,e){return function(t){return t==null?!1:t[n]===e&&(e!==void 0||n in Object(t))}}function q(n){var e=S(n);return e.length==1&&e[0][2]?R(e[0][0],e[0][1]):function(t){return t===n||G(t,n,e)}}var B=1,U=2;function K(n,e){return E(n)&&y(e)?R(m(n),e):function(t){var r=v(t,n);return r===void 0&&r===e?F(t,n):C(e,r,B|U)}}function N(n){return function(e){return e==null?void 0:e[n]}}function $(n){return function(e){return D(e,n)}}function J(n){return E(n)?N(m(n)):$(n)}function X(n){return typeof n=="function"?n:n==null?I:typeof n=="object"?_(n)?K(n[0],n[1]):q(n):J(n)}const f=new Map;let A;w&&(document.addEventListener("mousedown",n=>A=n),document.addEventListener("mouseup",n=>{for(const e of f.values())for(const{documentHandler:t}of e)t(n,A)}));function g(n,e){let t=[];return Array.isArray(e.arg)?t=e.arg:x(e.arg)&&t.push(e.arg),function(r,s){const o=e.instance.popperRef,u=r.target,i=s==null?void 0:s.target,a=!e||!e.instance,c=!u||!i,l=n.contains(u)||n.contains(i),d=n===u,p=t.length&&t.some(h=>h==null?void 0:h.contains(u))||t.length&&t.includes(i),M=o&&(o.contains(u)||o.contains(i));a||c||l||d||p||M||e.value(r,s)}}const Y={beforeMount(n,e){f.has(n)||f.set(n,[]),f.get(n).push({documentHandler:g(n,e),bindingFn:e.value})},updated(n,e){f.has(n)||f.set(n,[]);const t=f.get(n),r=t.findIndex(o=>o.bindingFn===e.oldValue),s={documentHandler:g(n,e),bindingFn:e.value};r>=0?t.splice(r,1,s):t.push(s)},unmounted(n){f.delete(n)}};export{Y as C,X as b};
