import{q as I,ab as h,r as y,ay as c,a9 as B,o as C,aA as E,ax as k,x as s,B as l,G as t,z as a,ai as N,aj as M,y as V,ak as d,Q as r,A as _,aB as b,P as Q,ag as S,aC as q,aD as A,H as j}from"./index-18f146fc.js";import{E as z,a as F,b as G}from"./index-a197ff1b.js";import{_ as H}from"./_plugin-vue_export-helper-c27b6911.js";const P={class:"languages"},T={class:"el-dropdown-link"},J=I({__name:"I18n",props:{loginMode:{default:"agent"}},emits:["getQrCode"],setup(p,{emit:i}){const m=p,u=i,{locale:g}=h({useScope:"global"}),n=y(c()),f=B(),w=async e=>{q(e),m.loginMode==="qrCode"&&u("getQrCode",0),n.value=c(e),g.value=e,f.dispatch("setLocaleLang",e),await A("diLocalData")};return C(()=>{E(k())}),(e,K)=>{const L=j,v=z,x=F,D=G;return s(),l("div",P,[t(D,{onCommand:w},{dropdown:a(()=>[t(x,null,{default:a(()=>[(s(!0),l(N,null,M(_(b),o=>(s(),V(v,{key:o.value,class:"i18n-item-li",command:o.value,"data-test":o.name,"data-gid":"081V0101"},{default:a(()=>[d(r(o.name),1)]),_:2},1032,["command","data-test"]))),128))]),_:1})]),default:a(()=>[Q("span",T,[d(r(n.value)+" ",1),t(L,{class:"el-icon--right"},{default:a(()=>[t(_(S))]),_:1})])]),_:1})])}}});const W=H(J,[["__scopeId","data-v-9e0ad533"]]);export{W as I};
