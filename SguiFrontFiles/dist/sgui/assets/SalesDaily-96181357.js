import{_ as la}from"./theme-light_empty-0081a108.js";import{q as ct,x as c,y as be,z as T,B as u,ai as G,aj as te,G as m,A as e,a$ as ra,ak as he,Q as d,H as Ct,ao as fa,h_ as ga,ab as St,r as E,b6 as rt,s as xt,ac as Nt,o as Me,aY as He,aH as ha,b2 as sa,ad as st,R as Ue,av as wt,a5 as $t,P as a,J as Ee,D as xe,ah as ca,al as Et,b3 as Ft,cg as B,a9 as Rt,w as Fe,aJ as Tt,hh as ba,bB as ka,h$ as va,aw as Ta,eA as _a,au as oa,a4 as xa,b9 as _t,am as Na,an as Ra}from"./index-18f146fc.js";import{F as Ce,s as je}from"./FilterSelect-598ae02d.js";import{E as it,a as pt}from"./index-d7d71e18.js";import{E as Dt,a as At}from"./index-c5503643.js";import{E as ia,a as pa}from"./index-3d51361b.js";import{E as ua}from"./index-2610c77e.js";import{E as da}from"./index-385c3d86.js";import{e as Ca}from"./exceljs.min-08e2fc65.js";import{a as Sa}from"./date-b1242c8d.js";import{T as wa}from"./TicketRefundForm-28604069.js";import{P as $a}from"./PrintNoSelect-c8861788.js";import{D as Ea}from"./regular-crs-4d4d60ea.js";import{k as Fa}from"./ticketOperationApi-fe1536da.js";import{_ as Da}from"./Page.vue_vue_type_script_setup_true_lang-ca55cadb.js";import{E as Aa}from"./index-28cc82cf.js";import{E as Ia}from"./index-2494e7da.js";import{_ as Oa}from"./_plugin-vue_export-helper-c27b6911.js";import"./_createMathOperation-2069a064.js";import"./index-9b639e2a.js";import"./isEqual-9c56e106.js";import"./flatten-1142070a.js";import"./index-1c4b8a79.js";import"./index-a197ff1b.js";import"./index-6ea30548.js";import"./isUndefined-aa0326a0.js";import"./dropdown-bbbdd88c.js";import"./castArray-f685dae0.js";import"./refs-649593ac.js";import"./strings-8b86a061.js";import"./index-c5921abf.js";import"./refundUtil-51abb28e.js";import"./common-d870312b.js";import"./throttle-39cac876.js";import"./index-5035a026.js";import"./index-037d7e25.js";import"./index-3a8869fd.js";const Pa={key:1,class:"ml-4"},Va=ct({__name:"TicketTypeSelect",props:{ticketType:{},tktTypes:{}},emits:["update:ticket-type"],setup(b,{emit:g}){const t=g,I=s=>{t("update:ticket-type",s)};return(s,k)=>{const h=Ct,H=it,A=pt;return c(),be(A,{"model-value":s.ticketType,placeholder:s.$t("app.agentReport.selectTicketType"),onChange:I},{default:T(()=>[(c(!0),u(G,null,te(s.tktTypes,x=>(c(),be(H,{key:x.value,value:x.value,label:s.$t(`app.agentReport.${x.label}`)},{default:T(()=>[s.ticketType===x.value?(c(),be(h,{key:0,class:"bg-inherit"},{default:T(()=>[m(e(ra))]),_:1})):(c(),u("span",Pa)),he(" "+d(s.$t(`app.agentReport.${x.label}`)),1)]),_:2},1032,["value","label"]))),128))]),_:1},8,["model-value","placeholder"])}}}),lt=(b,g)=>fa(`${ga}/crs/apiReport/crsDailySales`,{headers:{gid:g}}).post(b).json(),Ua=/^[1-9][0-9]{0,4}$/,ja=(b,g)=>{const{t}=St(),I=E(),s=E({saleStatusCode:["ISSU","RFND"],deviceNumber:"",tktType:"BSP"}),k=E(["ISSU","RFND"]),h=E(),H=rt({saleStatusCode:[{required:!0,message:t("app.agentReport.selectStatus"),trigger:"change"}],deviceNumber:[{required:!0,message:t("app.agentReport.printTicketNumber"),trigger:"change"},{pattern:Ua,message:t("app.agentReport.deviceNumberErr"),trigger:["change","blur"]}]}),A=E([]),x=E({}),L=E([]),U=xt(!1),z=E(!1);let v={};const ae=E([]);let y=[];const J=l=>!l||l==="",Q=async l=>{const i=document.createElement("input");i.value=l,document.body.appendChild(i),i.select(),document.execCommand("Copy"),i.style.display="none",sa({message:t("app.agentReport.tip"),type:"success",duration:2*1e3})},p=async l=>{A.value[l].showDropDown(!A.value[l].getVisibleValue())},M=l=>{C(),st.setLink(`/v2/crs/pnrManagement?pnrNo=${l}`)},j=(l,i)=>{C(),st.setLink(`/v2/crs/ticketOperation?ticketNumber=${l}&secondFactorCode=CN&secondFactorValue=${i??""}`)},P=l=>{var V;const i=[];return(((V=x.value)==null?void 0:V.items)??[]).forEach(Z=>{Z[l]&&!i.includes(Z[l])&&i.push(Z[l])}),i},f=l=>l.ticketType==="4"&&l.passengerType==="Y"?t("app.ticketType.GPDomesticTicket"):l.ticketType==="2"&&l.passengerType==="Y"?t("app.ticketType.GPInternationalTicket"):t("app.ticketType.BSPDomesticTicket"),w=l=>{switch(l==null?void 0:l.ticketType){case"1":return t("app.ticketType.internationalAirlineTickets");case"2":return t("app.ticketType.BSPInternationalTicket");case"3":return t("app.ticketType.domesticAirlineTickets");case"4":return f(l);case"7":return t("app.ticketType.BOPTicket");default:return l==null?void 0:l.ticketType}},O=()=>{const l={};l.ticketTypes=P("saleStatusCode"),l.jobNos=P("agent"),l.ticketKinds=P("ticketType"),l.payTypes=P("paymentTypeCode"),x.value.filter=l},K=()=>{v={},h.value={},ae.value=[],x.value={},y=[]},W=async()=>{var l;(l=I.value)==null||l.validate(async i=>{var Z,_e,ke,de,ge,_,X,oe,F,q;if(!i)return;z.value=!1,K();const V={deviceNumber:s.value.deviceNumber,saleStatusCodes:s.value.saleStatusCode,tktType:s.value.tktType,value:"2",date:Ue().format("YYYY-MM-DD"),outerSign:!1};U.value=!0;try{const Se=wt("09300109"),ve=(await lt(V,Se)).data.value;ve.items=(Z=ve==null?void 0:ve.items)==null?void 0:Z.map(Oe=>({...Oe,ticketType:w(Oe)})),x.value=ve,h.value={...ve.queryTSLHeader,ticketingDate:(_e=ve==null?void 0:ve.queryTSLHeader)!=null&&_e.ticketingDate?Ue(ve.queryTSLHeader.ticketingDate).format("YYYY-MM-DD"):""},O(),L.value=He((ke=x.value)==null?void 0:ke.items),y=((de=x.value)==null?void 0:de.items)??[];const Ae=((ge=g==null?void 0:g.salesForm)==null?void 0:ge.prntNo)===V.deviceNumber&&((_=g==null?void 0:g.salesForm)==null?void 0:_.tktType)===V.tktType;(X=g==null?void 0:g.salesForm)!=null&&X.tktType&&Ae&&b("errorNumber",(oe=x==null?void 0:x.value)==null?void 0:oe.totalErrorNumber,(F=x==null?void 0:x.value)==null?void 0:F.issueErrorNumber,(q=x==null?void 0:x.value)==null?void 0:q.refundErrorNumber)}finally{U.value=!1}})},se=(l,i)=>{if(i!=null&&i.length)ae.value.includes(l)||ae.value.push(l);else{const V=ae.value.indexOf(l);V!==-1&&ae.value.splice(V,1)}},ne=(l,i,V)=>{var _e,ke,de,ge,_,X,oe,F;se(l,i),z.value=!0;const Z=He(i);V&&Z.push(V),v[l]=Z,y=((_e=x.value)==null?void 0:_e.items)??[],((ke=v==null?void 0:v.airline)==null?void 0:ke.length)>0&&(y=y.filter(q=>v.airline.includes(q.airline))),((de=v==null?void 0:v.prntNo)==null?void 0:de.length)>0&&(y=y.filter(q=>v.prntNo.includes(q.pnr))),((ge=v==null?void 0:v.ticketStatus)==null?void 0:ge.length)>0&&(y=y.filter(q=>v.ticketStatus.includes(q.saleStatusCode)),y.sort((q,Se)=>q.salesDateTime.localeCompare(Se.salesDateTime))),((_=v==null?void 0:v.payType)==null?void 0:_.length)>0&&(y=y.filter(q=>v.payType.includes(q.paymentTypeCode))),((X=v==null?void 0:v.jobNo)==null?void 0:X.length)>0&&(y=y.filter(q=>v.jobNo.includes(q.agent))),((oe=v==null?void 0:v.ticketType)==null?void 0:oe.length)>0&&(y=y.filter(q=>v.ticketType.includes(q.ticketType))),((F=v==null?void 0:v.currencyType)==null?void 0:F.length)>0&&(y=y.filter(q=>v.currencyType.includes(q.currency))),L.value=y},C=()=>{b("update:modelValue",!1)};return Nt(()=>g.reQuery,()=>{W()}),Me(()=>{var i,V,Z,_e,ke,de,ge,_,X,oe,F,q;s.value.saleStatusCode=k.value.map(Se=>Se),s.value.deviceNumber=(i=g==null?void 0:g.salesForm)==null?void 0:i.prntNo,s.value.tktType=((V=g==null?void 0:g.salesForm)==null?void 0:V.tktType)??"BSP",((Z=s.value)==null?void 0:Z.tktType)===((_e=g==null?void 0:g.salesForm)==null?void 0:_e.tktType)&&((ke=s.value)==null?void 0:ke.deviceNumber)===((de=g==null?void 0:g.salesForm)==null?void 0:de.prntNo)&&s.value.saleStatusCode.includes("ISSU")&&s.value.saleStatusCode.includes("RFND")&&((X=(_=(ge=g==null?void 0:g.storeTodayError)==null?void 0:ge.querySaleDailyErrorRes)==null?void 0:_.items)==null?void 0:X.length)>0&&(x.value=(oe=g==null?void 0:g.storeTodayError)==null?void 0:oe.querySaleDailyErrorRes,L.value=He((F=x.value)==null?void 0:F.items),y=((q=x.value)==null?void 0:q.items)??[])}),ha(()=>{H.deviceNumber[0].required=["BSP","CDS","PYN"].includes(s.value.tktType)}),{todayExceptionsFormRef:I,todayExceptionsForm:s,saleStatusCodeList:k,todayExceptionsRules:H,isLoading:U,todayExceptionsList:L,filterRptRef:A,filterTypeList:ae,querySaleDailyRes:x,headDetail:h,handleSearch:W,buildTicketType:w,isEmptyData:J,filterChange:ne,jumpToPnrEtQuery:M,jumpToTcTicketQuery:j,doCopy:Q,openFilter:p,closeDialog:C}},Ba=ja,Ma={class:"today-exceptions-dialog-content"},Ha={class:"flex"},La={key:0,class:"h-8 w-full px-2.5 bg-[#f6f6f6] rounded justify-start items-center gap-2.5 flex flex-1"},za=a("span",{class:"text-[#8c8c8c] text-xs font-normal leading-tight"},"OFFICE",-1),Ya={class:"text-neutral-800 text-xs font-normal leading-tight"},Ga=a("span",{class:"text-[#8c8c8c] text-xs font-normal leading-tight"},"IATA NO",-1),Qa={class:"text-neutral-800 text-xs font-normal leading-tight"},qa={class:"text-[#8c8c8c] text-xs font-normal leading-tight"},Xa={class:"text-neutral-800 text-xs font-normal leading-tight"},Ka={key:0,class:"result"},Wa=["onClick"],Ja=["onClick"],Za={key:1},en=["onClick"],tn=["onClick"],an={key:1},nn=["onClick"],on=["onClick"],ln={key:1,class:"empty-info"},rn=a("img",{src:la,alt:"$t('app.agentReport.noErrordata')"},null,-1),sn={class:"main-info"},cn=ct({__name:"TodayExceptionsDialog",props:{salesForm:{},storeTodayError:{},tktTypes:{},reQuery:{type:Boolean}},emits:["update:modelValue","errorNumber","showRefundDialog"],setup(b,{emit:g}){const t=b,I=g,{todayExceptionsFormRef:s,todayExceptionsForm:k,headDetail:h,saleStatusCodeList:H,todayExceptionsRules:A,isLoading:x,todayExceptionsList:L,filterRptRef:U,filterTypeList:z,querySaleDailyRes:v,handleSearch:ae,openFilter:y,filterChange:J,jumpToTcTicketQuery:Q,doCopy:p,isEmptyData:M,jumpToPnrEtQuery:j,closeDialog:P}=Ba(I,t);return(f,w)=>{const O=Dt,K=it,W=pt,se=ca,ne=Et,C=At,l=ia,i=ua,V=pa,Z=da,_e=Ft;return c(),be(Z,{width:"1000",title:f.$t("app.agentReport.abnormalRecords"),"close-on-click-modal":!1,class:"today-exceptions-dialog",onClose:e(P)},{footer:T(()=>[m(ne,{class:"w-[80px]",onClick:e(P)},{default:T(()=>[he(d(f.$t("app.agentReport.close")),1)]),_:1},8,["onClick"])]),default:T(()=>{var ke,de,ge;return[$t((c(),u("div",Ma,[a("div",Ha,[m(C,{ref_key:"todayExceptionsFormRef",ref:s,model:e(k),inline:!0,rules:e(A),"require-asterisk-position":"right"},{default:T(()=>[m(O,{label:f.$t("app.agentReport.tktType"),class:"ticket-type",required:""},{default:T(()=>[m(Va,{"tkt-types":f.tktTypes,"ticket-type":e(k).tktType,"onUpdate:ticketType":w[0]||(w[0]=_=>e(k).tktType=_)},null,8,["tkt-types","ticket-type"])]),_:1},8,["label"]),m(O,{label:f.$t("app.agentReport.saleStatus"),prop:"saleStatusCode",class:"sale-status"},{default:T(()=>[m(W,{modelValue:e(k).saleStatusCode,"onUpdate:modelValue":w[1]||(w[1]=_=>e(k).saleStatusCode=_),multiple:"","collapse-tags":"",placeholder:f.$t("app.agentReport.selectTicketType")},{default:T(()=>[(c(!0),u(G,null,te(e(H),_=>(c(),be(K,{key:_,label:_,value:_},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder"])]),_:1},8,["label"]),m(O,{label:f.$t("app.agentReport.ticketMachineNumber"),prop:"deviceNumber",class:"print-ticket-number"},{default:T(()=>[m(se,{modelValue:e(k).deviceNumber,"onUpdate:modelValue":w[2]||(w[2]=_=>e(k).deviceNumber=_),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:f.$t("app.agentReport.printTicketNumber")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),m(O,null,{default:T(()=>[m(ne,{"data-gid":"11280201",type:"primary",onClick:e(ae)},{default:T(()=>[he(d(f.$t("app.agentReport.search")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model","rules"]),(ke=e(h))!=null&&ke.office?(c(),u("div",La,[a("div",null,[za,a("span",Ya,"："+d(e(h).office),1)]),a("div",null,[Ga,a("span",Qa,"："+d(e(h).iataNumber),1)]),a("div",null,[a("span",qa,d(f.$t("app.agentReport.saleDate")),1),a("span",Xa,"："+d(e(h).ticketingDate),1)])])):Ee("",!0)]),(ge=(de=e(v))==null?void 0:de.items)!=null&&ge.length?(c(),u("div",Ka,[m(V,{ref:"todayExceptionsRef",height:"550px",data:e(L),stripe:"",style:{"min-width":"100%"},class:"sales-daily-table",onFilterChange:e(J)},{default:T(()=>[m(l,{prop:"ticket",label:f.$t("app.agentReport.tktNo"),width:"150px",flexible:"true"},{default:T(({row:_})=>[a("span",{class:"pointer-span",onClick:X=>e(Q)(_.ticket,_.pnr)},d(_.ticket),9,Wa),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:X=>e(p)(_.ticket)},null,8,Ja)]),_:1},8,["label"]),m(l,{prop:"ticketStatus",width:"110px"},{header:T(()=>{var _;return[a("span",{class:xe(["pointer-span-drop",e(z).includes("ticketStatus")?"text-brand-2":""]),onClick:w[3]||(w[3]=X=>e(y)(1))},d(f.$t("app.agentReport.tktStatus")),3),m(Ce,{ref:X=>{X&&(e(U)[1]=X)},filters:(_=e(v).filter)==null?void 0:_.ticketTypes,"column-key":"ticketStatus",onHandleConfrim:e(J)},null,8,["filters","onHandleConfrim"])]}),default:T(({row:_})=>[m(i,{class:"tag"},{default:T(()=>[he(d(_.ticketStatus),1)]),_:2},1024)]),_:1}),m(l,{prop:"jobNo","min-width":"80px"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(z).includes("jobNo")?"text-brand-2":""]),onClick:w[4]||(w[4]=_=>e(y)(3))},d(f.$t("app.agentReport.agent")),3),m(Ce,{ref:_=>{_&&(e(U)[3]=_)},filters:e(v).filter.jobNos,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":f.$t("app.agentReport.agentTip"),"column-key":"jobNo",onHandleConfrim:e(J)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),m(l,{prop:"desArr",label:f.$t("app.agentReport.segSE"),"min-width":"90px"},null,8,["label"]),m(l,{prop:"refundNo",label:f.$t("app.agentReport.refundNo"),"min-width":"125px"},{default:T(({row:_})=>[e(M)(_.refundNo||_.refundFormNumber)?(c(),u(G,{key:0},[],64)):(c(),u("div",Za,[a("span",{class:"pointer-span",onClick:X=>f.$emit("showRefundDialog",_)},d(_.refundNo||_.refundFormNumber),9,en),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:X=>e(p)(_.refundNo||_.refundFormNumber)},null,8,tn)]))]),_:1},8,["label"]),m(l,{prop:"pnr",label:"PNR","min-width":"105px"},{default:T(({row:_})=>[e(M)(_.pnr)?(c(),u(G,{key:0},[],64)):(c(),u("div",an,[a("span",{class:"pointer-span",onClick:X=>e(j)(_.pnr)},d(_.pnr),9,nn),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:X=>e(p)(_.pnr)},null,8,on)]))]),_:1}),m(l,{prop:"ticketType","min-width":"125px"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(z).includes("ticketType")?"text-brand-2":""]),onClick:w[5]||(w[5]=_=>e(y)(6))},d(f.$t("app.agentReport.tktType")),3),m(Ce,{ref:_=>{_&&(e(U)[6]=_)},filters:e(v).filter.ticketKinds,"column-key":"ticketType",onHandleConfrim:e(J)},null,8,["filters","onHandleConfrim"])]),_:1}),m(l,{prop:"payType","min-width":"100px"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(z).includes("payType")?"text-brand-2":""]),onClick:w[6]||(w[6]=_=>e(y)(4))},d(f.$t("app.agentReport.payment")),3),m(Ce,{ref:_=>{_&&(e(U)[4]=_)},filters:e(v).filter.payTypes,"column-key":"payType",onHandleConfrim:e(J)},null,8,["filters","onHandleConfrim"])]),_:1})]),_:1},8,["data","onFilterChange"])])):(c(),u("div",ln,[rn,a("div",sn,d(f.$t("app.agentReport.noErrordata")),1)]))])),[[_e,e(x)]])]}),_:1},8,["title","onClose"])}}});const pn=[{name:B.global.t("app.agentReport.tktNo"),wch:14.75},{name:B.global.t("app.agentReport.segSE"),wch:10.5},{name:B.global.t("app.agentReport.tktSettle"),wch:15.63},{name:B.global.t("app.agentReport.tax"),wch:12},{name:B.global.t("app.agentReport.agency"),wch:12},{name:B.global.t("app.agentReport.agencyRate"),wch:12},{name:"PNR",wch:9.75},{name:B.global.t("app.agentReport.agent"),wch:8.63},{name:B.global.t("app.agentReport.tktType"),wch:13.5},{name:B.global.t("app.agentReport.ticketMachineNumber"),wch:8.75},{name:B.global.t("app.agentReport.payment"),wch:8.75}],un=b=>{const g=(b.items??[]).filter(t=>t.ticketStatus==="ISSU"||t.ticketStatus==="EXCH").map(t=>({ticket:t.ticket,desArr:t.desArr,amount:t.amount,taxAmount:t.taxAmount,agencyFee:t.agencyFee,agencyFeePercent:t.agencyFeePercent,pnr:t.pnr,jobNo:t.jobNo,tktType:t.ticketType,ticketMachineNumber:t.prntNo,payment:t.payType}));return{officeNo:b==null?void 0:b.office,iataNO:b==null?void 0:b.iata,salesDate:b.items[0].salesDate,items:g}},dn=b=>{const g={},t={},I={},s={};return(b??[]).forEach(k=>{g[k.currencyType]=g[k.currencyType]?g[k.currencyType]+Number(k.amount):Number(k.amount),t[k.currencyType]=t[k.currencyType]?t[k.currencyType]+Number(k.taxAmount):Number(k.taxAmount),I[k.currencyType]=I[k.currencyType]?I[k.currencyType]+Number(k.agencyFee):Number(k.agencyFee);const h=je(Number(k.amount),Number(k.agencyFee));s[k.currencyType]=s[k.currencyType]?s[k.currencyType]+h:h}),{totalAmount:g,taxAmount:t,agencyFee:I,carriers:s}},mn=(b,g)=>{var se,ne;const t=g.addWorksheet(B.global.t("app.agentReport.issueChangeReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:K1"),t.getRow(1).height=30;const I=t.getCell("A1");I.value=B.global.t("app.agentReport.issueChangeReport"),I.alignment={vertical:"middle",horizontal:"center"},I.font={size:14,bold:!0};const s=un(b),k=(s==null?void 0:s.officeNo)??"",h=(s==null?void 0:s.iataNO)??"",H=(s==null?void 0:s.salesDate)??"",A=[`OFFICE:${k}`,null,`IATA NO:${h}`,null,null,`日期:${H}`],x=t.addRow(A);x.font={size:14,bold:!0};const L=t.addRow([]);L.height=9;const U=[],z=[];pn.forEach(C=>{U.push(C.name),z.push({wch:C.wch})}),t.addRow(U).eachCell(C=>{C.font={bold:!0}}),t.columns.forEach((C,l)=>{C.width=z[l].wch});const y=[3,4,5,6];(s.items??[]).forEach(C=>{t.addRow(Object.values(C)).eachCell((i,V)=>{y.includes(V)&&(i.value=Number(i.value),i.numFmt="0.00",i.alignment={horizontal:"left"})})});const J=t.addRow([]),{totalAmount:Q,taxAmount:p,agencyFee:M,carriers:j}=dn(b.items.filter(C=>C.ticketStatus==="ISSU"||C.ticketStatus==="EXCH")),P=["TICKETS ISSU",((se=b.total)==null?void 0:se.totalTicket)??"","NORMAL FARE","-- AMOUNT："];for(const C in Q)if(Q[C]){const l=Q[C];P.push(l),P.push(C)}const f=[null,null,"NORMAL TAX","-- AMOUNT："];for(const C in p)if(p[C]){const l=p[C];f.push(l),f.push(C)}const w=[null,null,"NORMAL COMMIT","-- AMOUNT："];for(const C in M)if(M[C]){const l=M[C];w.push(l),w.push(C)}const O=["TICKETS EXCHANGE",((ne=b.total)==null?void 0:ne.totalExchange)??"","CARRIERS","-- AMOUNT："];for(const C in j)if(j[C]){const l=j[C];O.push(l),O.push(C)}t.addRow(P),t.addRow(O),t.addRow(f),t.addRow(w);const K=[1,2,3,5,6],W=J.number+1;for(let C=W;C<=W+3;C++)for(let l=1;l<=11;l++){const i=t.getCell(C,l);K.includes(l)&&(i.alignment={horizontal:"right"}),l==5&&(i.value=Number(i.value),i.numFmt="0.00"),i.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},yn=[{name:B.global.t("app.agentReport.tktNo"),wch:14.75},{name:B.global.t("app.agentReport.refundOrder"),wch:10.5},{name:B.global.t("app.agentReport.refundAmount"),wch:15.63},{name:B.global.t("app.agentReport.handFee"),wch:12},{name:B.global.t("app.agentReport.refundTax"),wch:12},{name:B.global.t("app.agentReport.agency"),wch:9.75},{name:B.global.t("app.agentReport.agencyRate"),wch:9.75},{name:B.global.t("app.agentReport.agent"),wch:8.63},{name:B.global.t("app.agentReport.tktType"),wch:13.5},{name:B.global.t("app.agentReport.ticketMachineNumber"),wch:8.75},{name:B.global.t("app.agentReport.payment"),wch:8.75}],fn=b=>{const g=(b.items??[]).filter(t=>t.ticketStatus==="RFND").map(t=>({ticket:t.ticket,refundNo:!t.refundNo||t.refundNo===""?"-":t.refundNo,amount:t.amount,serviceCharge:t.serviceCharge,taxAmount:t.taxAmount,agencyFee:t.agencyFee,agencyFeePercent:t.agencyFeePercent,jobNo:t.jobNo,tktType:t.ticketType,ticketMachineNumber:t.prntNo,payment:t.payType}));return{officeNo:b==null?void 0:b.office,iataNO:b==null?void 0:b.iata,salesDate:b.items[0].salesDate,items:g}},gn=b=>{const g={},t={},I={},s={},k={};return(b??[]).forEach(h=>{g[h.currencyType]=g[h.currencyType]?g[h.currencyType]+Number(h.amount):Number(h.amount),t[h.currencyType]=t[h.currencyType]?t[h.currencyType]+Number(h.taxAmount):Number(h.taxAmount),I[h.currencyType]=I[h.currencyType]?I[h.currencyType]+Number(h.agencyFee):Number(h.agencyFee),s[h.currencyType]=s[h.currencyType]?s[h.currencyType]+Number(h.serviceCharge):Number(h.serviceCharge);const H=je(Number(h.amount),Number(h.agencyFee));k[h.currencyType]=k[h.currencyType]?k[h.currencyType]+H:H}),{totalAmount:g,taxAmount:t,agencyFee:I,refundAmount:s,carriers:k}},hn=(b,g)=>{var C;const t=g.addWorksheet(B.global.t("app.agentReport.refundReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:K1"),t.getRow(1).height=30;const I=t.getCell("A1");I.value=B.global.t("app.agentReport.refundReport"),I.alignment={vertical:"middle",horizontal:"center"},I.font={size:14,bold:!0};const s=fn(b),k=(s==null?void 0:s.officeNo)??"",h=(s==null?void 0:s.iataNO)??"",H=(s==null?void 0:s.salesDate)??"",A=[`OFFICE:${k}`,null,`IATA NO:${h}`,null,null,`日期:${H}`],x=t.addRow(A);x.font={size:14,bold:!0};const L=t.addRow([]);L.height=9;const U=[],z=[];yn.forEach(l=>{U.push(l.name),z.push({wch:l.wch})}),t.addRow(U).eachCell(l=>{l.font={bold:!0}}),t.columns.forEach((l,i)=>{l.width=z[i].wch});const y=[3,4,5,6,7];(s.items??[]).forEach(l=>{t.addRow(Object.values(l)).eachCell((V,Z)=>{y.includes(Z)&&(V.value=Number(V.value),V.numFmt="0.00",V.alignment={horizontal:"left"})})});const J=t.addRow([]),{totalAmount:Q,taxAmount:p,agencyFee:M,refundAmount:j,carriers:P}=gn(b.items.filter(l=>l.ticketStatus==="RFND")),f=["TICKETS REFUND",((C=b.total)==null?void 0:C.totalRefund)??"","REFUND FARE","-- AMOUNT："];for(const l in Q)if(Q[l]){const i=Q[l];f.push(i),f.push(l)}const w=[null,null,"REFUND TAX","-- AMOUNT："];for(const l in p)if(p[l]){const i=p[l];w.push(i),w.push(l)}const O=[null,null,"DEDUCTION","-- AMOUNT："];for(const l in j)if(j[l]){const i=j[l];O.push(i),O.push(l)}const K=[null,null,"COMMIT","-- AMOUNT："];for(const l in M)if(M[l]){const i=M[l];K.push(i),K.push(l)}const W=[null,null,"CARRIERS","-- AMOUNT："];for(const l in P)if(P[l]){const i=P[l];W.push(i),W.push(l)}t.addRow(f),t.addRow(W),t.addRow(w),t.addRow(O),t.addRow(K);const se=[1,2,3,5,6],ne=J.number+1;for(let l=ne;l<=ne+4;l++)for(let i=1;i<=11;i++){const V=t.getCell(l,i);se.includes(i)&&(V.alignment={horizontal:"right"}),i==5&&(V.value=Number(V.value),V.numFmt="0.00"),V.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},bn=[{name:B.global.t("app.agentReport.tktNo"),wch:14.75},{name:B.global.t("app.agentReport.airline"),wch:8.2},{name:B.global.t("app.agentReport.ticketMachineNumber"),wch:10.5},{name:"PNR",wch:9.75},{name:B.global.t("app.agentReport.agent"),wch:8.63},{name:B.global.t("app.agentReport.tktType"),wch:13.5},{name:B.global.t("app.agentReport.payment"),wch:8.75}],kn=b=>{const g=(b.items??[]).filter(t=>t.ticketStatus==="VOID").map(t=>({ticket:t.ticket,airline:t.airline,deviceNo:t.prntNo,pnr:t.pnr,jobNo:t.jobNo,tktType:t.ticketType,payment:t.payType}));return{officeNo:b==null?void 0:b.office,iataNO:b==null?void 0:b.iata,salesDate:b.items[0].salesDate,items:g}},vn=(b,g)=>{var P;const t=g.addWorksheet(B.global.t("app.agentReport.voidReportSheet"));t.properties.defaultRowHeight=17,t.mergeCells("A1:H1"),t.getRow(1).height=30;const I=t.getCell("A1");I.value=B.global.t("app.agentReport.voidReport"),I.alignment={vertical:"middle",horizontal:"center"},I.font={size:14,bold:!0};const s=kn(b),k=(s==null?void 0:s.officeNo)??"",h=(s==null?void 0:s.iataNO)??"",H=(s==null?void 0:s.salesDate)??"",A=[`OFFICE:${k}`,null,`IATA NO:${h}`,null,null,`日期:${H}`],x=t.addRow(A);x.font={size:14,bold:!0};const L=t.addRow([]);L.height=9;const U=[],z=[];bn.forEach(f=>{U.push(f.name),z.push({wch:f.wch})}),t.addRow(U).eachCell(f=>{f.font={bold:!0}}),t.columns.forEach((f,w)=>{var O;f.width=((O=z[w])==null?void 0:O.wch)??12}),(s.items??[]).forEach(f=>{t.addRow(Object.values(f))});const y=t.addRow([]),J=["TICKETS VOID",((P=b.total)==null?void 0:P.totalVoid)??"","NORMAL FARE","-- AMOUNT：",null,null],Q=[null,null,"NORMAL TAX","-- AMOUNT：",null,null],p=[null,null,"NORMAL COMMIT","-- AMOUNT：",null,null];t.addRow(J),t.addRow([null,null,"CARRIERS","-- AMOUNT：",null,null]),t.addRow(Q),t.addRow(p);const M=[1,2,3,5,6],j=y.number+1;for(let f=j;f<=j+3;f++)for(let w=1;w<=8;w++){const O=t.getCell(f,w);M.includes(w)&&(O.alignment={horizontal:"right"}),w==5&&(O.value=Number(O.value),O.numFmt="0.00"),O.fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFDCE6F1"}}}},Tn=b=>{const g=E(),{t}=St(),I=Rt(),s=Rt(),k=E(""),h=E(),H=E(""),A=E(),x=E(),L=E(!0),U=Fe(()=>{var r;return((r=I.state.user)==null?void 0:r.externalLink)==="SAT"}),z=E(""),v=E(),ae=E([]),y=E(),J=E(!1),Q=rt({querySaleDailyErrorRes:{},totalNumber:0,issueNumber:0,refundNumber:0}),p=E(),M=E(),j=E(0),P=E(0),f=E(0),w=E([]),O=E(),K=E({}),W=E(0),se=xt(!1),ne=E([]),C=E(!1),l=E(!1);let i={};const V=Fe(()=>{var r;return((r=s.state.user)==null?void 0:r.defaultOfficeInternational)??!1}),Z=Fe(()=>s.getters.userPreferences??{}),_e=Fe(()=>{var r;return((r=s.state.user)==null?void 0:r.entityType)??""}),ke=[{label:"BSP",value:"BSP"},{label:"BOP",value:"BOP"},{label:"CDS",value:"CDS"},{label:"PYN",value:"PYN"},{label:"GP_BSP",value:"GP"}],de=E([]),ge=[{label:"AL",value:"AL"},{label:"CA",value:"CA"},{label:"CC",value:"CC"},{label:"CK",value:"CK"}],_=E([]),X=E(b.cmd),oe=E({pageNumber:1,pageSize:20});let F=[];const q=E(!1),Se=E(!1),ve=E(!1),Ae=E({}),Oe=E({}),ut=r=>!r||r==="",S=rt({searchDate:Ue(new Date).format("YYYY-MM-DD"),prntNo:"",ticketNumber:"",isFilterTime:!0,tktType:"",filterPayType:"AL",filterPayTypeCopy:""}),dt=xt({render(){return Tt("em",{class:"iconfont icon-calendar"})}}),Le=Fe(()=>Ue(new Date).format("YYYY-MM-DD")===S.searchDate),mt=Fe(()=>({searchDate:[{required:!0,message:t("app.agentReport.selectDate"),trigger:"change"}],prntNo:[{required:U.value&&(Le.value||J.value)&&!(S.tktType==="BOP"||S.tktType==="GP"||S.tktType==="CDS"),message:t("app.agentReport.printTicketNumber"),trigger:"change"},{pattern:ba,message:t("app.agentReport.deviceNumberErr"),trigger:["change","blur"]}],ticketNum:[{required:!1,message:t("app.agentReport.ticketNumber"),trigger:"change"},{pattern:ka,message:t("app.agentReport.ticketNumTip"),trigger:["change","blur"]}],filterPayType:[{pattern:va,message:t("app.agentReport.payTypeTip"),trigger:["change","blur"]}]})),yt=()=>{C.value||(S.isFilterTime?w.value=we(ne.value):w.value=we(F))};function N(r){return r.ticketNo="",r.passengerName="",r.airlineCode="",r.ticketNoEnd="",r.currency="",r.payMethod="",r.ticketType="",r.segmentInfos={},r.segInfos={},r.cmdNo="",r.cmdOption="",r.operator="",r.office="",r.taxInfo={},r.taxInfos={},r.check="",r.passengerType="",r.conjunction=0,r.couponNo=["0"],r.grossRefund=0,r.refund="Y",r.remark="",r.ticketNoEnd="",r.netRefund=0,r.creditCard="",r.commission=0,r.commissionRate=0,r.deduction=0,r.querySuccess=!1,r.totalTaxs=0,r}const D=r=>{Ae.value=r,q.value=!0},ft=async r=>{const $=document.createElement("input");$.value=r,document.body.appendChild($),$.select(),document.execCommand("Copy"),$.style.display="none",sa({message:t("app.agentReport.tip"),type:"success",duration:2*1e3})},$e=async r=>{O.value=r,Se.value=!0,z.value=r!=null&&r.ticketTypeCode?r.ticketTypeCode:""},ze=async r=>{ae.value[r].showDropDown(!ae.value[r].getVisibleValue())},Pe=async()=>{var r;(r=v.value)==null||r.handleSizeChange(20)},Ye=r=>{st.setLink(`/v2/crs/pnrManagement?pnrNo=${r}`)},Ge=(r,$)=>{st.setLink(`/v2/crs/ticketOperation?ticketNumber=${r}&secondFactorCode=CN&secondFactorValue=${$??""}`)},we=r=>(r??[]).slice((oe.value.pageNumber-1)*oe.value.pageSize,oe.value.pageNumber*oe.value.pageSize),Be=()=>{oe.value.pageNumber=1,i={},_.value=[],K.value={},p.value={},F=[]},Qe=r=>{const $=r??[],Y={},le={},Te={},me={},ce={amount:{},taxAmount:{},agencyFee:{},carriers:{}},ie={amount:{},taxAmount:{},agencyFee:{},carriers:{}},pe={amount:{},taxAmount:{},agencyFee:{},carriers:{}},ue={amount:{},taxAmount:{},agencyFee:{},carriers:{}};return $.filter(o=>o.currencyType).forEach(o=>{if(o.ticketStatus==="ISSU"){ce.amount[o.currencyType]=ce.amount[o.currencyType]?ce.amount[o.currencyType]+Number(o.amount):Number(o.amount),ce.taxAmount[o.currencyType]=ce.taxAmount[o.currencyType]?ce.taxAmount[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),ce.agencyFee[o.currencyType]=ce.agencyFee[o.currencyType]?ce.agencyFee[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee);const ye=je(Number(o.amount),Number(o.agencyFee));ce.carriers[o.currencyType]=ce.carriers[o.currencyType]?ce.carriers[o.currencyType]+ye:ye}if(o.ticketStatus==="RFND"){ie.amount[o.currencyType]=ie.amount[o.currencyType]?ie.amount[o.currencyType]+Number(o.amount):Number(o.amount),ie.taxAmount[o.currencyType]=ie.taxAmount[o.currencyType]?ie.taxAmount[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),ie.agencyFee[o.currencyType]=ie.agencyFee[o.currencyType]?ie.agencyFee[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee);const ye=je(Number(o.amount),Number(o.agencyFee));ie.carriers[o.currencyType]=ie.carriers[o.currencyType]?ie.carriers[o.currencyType]+ye:ye}if(o.ticketStatus==="EXCH"){pe.amount[o.currencyType]=pe.amount[o.currencyType]?pe.amount[o.currencyType]+Number(o.amount):Number(o.amount),pe.taxAmount[o.currencyType]=pe.taxAmount[o.currencyType]?pe.taxAmount[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),pe.agencyFee[o.currencyType]=pe.agencyFee[o.currencyType]?pe.agencyFee[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee);const ye=je(Number(o.amount),Number(o.agencyFee));pe.carriers[o.currencyType]=pe.carriers[o.currencyType]?pe.carriers[o.currencyType]+ye:ye}if(o.ticketStatus==="VOID"){ue.amount[o.currencyType]=ue.amount[o.currencyType]?ue.amount[o.currencyType]+Number(o.amount):Number(o.amount),ue.taxAmount[o.currencyType]=ue.taxAmount[o.currencyType]?ue.taxAmount[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),ue.agencyFee[o.currencyType]=ue.agencyFee[o.currencyType]?ue.agencyFee[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee);const ye=je(Number(o.amount),Number(o.agencyFee));ue.carriers[o.currencyType]=ue.carriers[o.currencyType]?ue.carriers[o.currencyType]+ye:ye}Y[o.currencyType]=Y[o.currencyType]?Y[o.currencyType]+Number(o.amount):Number(o.amount),le[o.currencyType]=le[o.currencyType]?le[o.currencyType]+Number(o.taxAmount):Number(o.taxAmount),Te[o.currencyType]=Te[o.currencyType]?Te[o.currencyType]+Number(o.agencyFee):Number(o.agencyFee),me[o.currencyType]=me[o.currencyType]?me[o.currencyType]+Number(o.serviceCharge):Number(o.serviceCharge)}),{ISSUA:ce,REFUND:ie,EXCHANGE:pe,VOID:ue,amount:Y,taxAmount:le,agencyFee:Te,refundAmount:me,totalTicket:$.filter(o=>o.ticketStatus==="ISSU").length??0,totalVoid:$.filter(o=>o.ticketStatus==="VOID").length??0,totalRefund:$.filter(o=>o.ticketStatus==="RFND").length??0,totalExchange:$.filter(o=>o.ticketStatus==="EXCH").length??0}},Ie=()=>{if(h.value&&h.value.getBoundingClientRect().width!==0){const r=h.value.getBoundingClientRect(),$=w.value.length*50+50,Y=`${document.documentElement.clientHeight-77-45-62-150-20-100}`;$>parseInt(Y,10)?r.left<100?(H.value=`${document.documentElement.clientHeight-70-100+50+78+20}px`,k.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50+50+78+20-50}`):(H.value=`${document.documentElement.clientHeight-70-100+50+20}px`,k.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50+50+20-50}`):(H.value="100%",k.value=$.toString())}else{const r=w.value.length*50+50,$=`${document.documentElement.clientHeight-77-45-62-150-20-100}`;r>parseInt($,10)?(H.value=`${document.documentElement.clientHeight-70-100}px`,k.value=`${document.documentElement.clientHeight-77-45-62-150-20-100-20+50}`):(H.value="100%",k.value=r.toString())}},ee=async r=>{var $;if(r){l.value=!l.value;return}C.value=!1,Pe(),($=A.value)==null||$.validate(async Y=>{var me,ce,ie,pe,ue,o,ye,re,Ve,Ot,Pt,Vt,Ut,jt,Bt,Mt,Ht,Lt,zt,Yt,Gt,Qt,qt,Xt,Kt,Wt,Jt,Zt,ea,ta;if(!Y)return;Be();const le={date:S.searchDate,deviceNumber:S.prntNo,tktType:S.tktType,saleStatusCodes:[],value:"1",outerSign:!1},Te={date:S.searchDate,deviceNumber:S.prntNo,tktType:S.tktType,saleStatusCodes:["ISSU","RFND"],value:"2",outerSign:!0};se.value=!0;try{const kt=wt("09300108");if(S.searchDate!==Ue().format("YYYY-MM-DD")){const Ne=await lt(le,kt);p.value=Ne.data.value}else{const[Ne,Re]=await Promise.allSettled([lt(le,kt),lt(Te,kt)]);Ne.status==="fulfilled"&&(p.value=(ce=(me=Ne==null?void 0:Ne.value)==null?void 0:me.data)==null?void 0:ce.value),Re.status==="fulfilled"&&(M.value=(pe=(ie=Re==null?void 0:Re.value)==null?void 0:ie.data)==null?void 0:pe.value,f.value=(ue=M.value)==null?void 0:ue.totalErrorNumber,j.value=(o=M.value)==null?void 0:o.issueErrorNumber,P.value=(ye=M.value)==null?void 0:ye.refundErrorNumber,Q.querySaleDailyErrorRes=M.value,Q.totalNumber=f.value,Q.issueNumber=j.value,Q.refundNumber=P.value)}if(S.ticketNumber||S.filterPayType){const Ne=[],Re=[],et=[],tt=[],at=[],nt=[],ot=[];S.ticketNumber&&(p.value.items=(re=p==null?void 0:p.value)==null?void 0:re.items.filter(fe=>{var aa,na;return fe.ticket===((aa=S==null?void 0:S.ticketNumber)!=null&&aa.includes("-")?S.ticketNumber:(na=S.ticketNumber)==null?void 0:na.replace(/(\d{3})(\d{7})/,"$1-$2"))}),Ne.push((Ve=p.value.items[0])==null?void 0:Ve.airline),Re.push((Ot=p.value.items[0])==null?void 0:Ot.ticketStatus),et.push((Pt=p.value.items[0])==null?void 0:Pt.prntNo),tt.push((Vt=p.value.items[0])==null?void 0:Vt.jobNo),at.push((Ut=p.value.items[0])==null?void 0:Ut.ticketType),nt.push((jt=p.value.items[0])==null?void 0:jt.payType),ot.push((Bt=p.value.items[0])==null?void 0:Bt.currencyType),p.value.filter.airlines=Ne,p.value.filter.currencyTypes=ot,p.value.filter.jobNos=tt,p.value.filter.ticketKinds=at,p.value.filter.ticketTypes=Re,p.value.filter.prntNos=et,p.value.filter.payTypes=nt),S.filterPayType&&(p.value.items=(S==null?void 0:S.filterPayType)==="AL"?(Mt=p==null?void 0:p.value)==null?void 0:Mt.items:(Ht=p==null?void 0:p.value)==null?void 0:Ht.items.filter(fe=>fe.payType===(S==null?void 0:S.filterPayType)),(S==null?void 0:S.filterPayType)!=="AL"&&(Ne.push(...Array.from(new Set((Lt=p.value.items)==null?void 0:Lt.map(fe=>fe.airline)))),Re.push(...Array.from(new Set((zt=p.value.items)==null?void 0:zt.map(fe=>fe.ticketStatus)))),et.push(...Array.from(new Set((Yt=p.value.items)==null?void 0:Yt.map(fe=>fe.prntNo)))),tt.push(...Array.from(new Set((Gt=p.value.items)==null?void 0:Gt.map(fe=>fe.jobNo)))),at.push(...Array.from(new Set((Qt=p.value.items)==null?void 0:Qt.map(fe=>fe.ticketType)))),nt.push(...Array.from(new Set((qt=p.value.items)==null?void 0:qt.map(fe=>fe.payType)))),ot.push(...Array.from(new Set((Xt=p.value.items)==null?void 0:Xt.map(fe=>fe.currencyType)))),p.value.filter.airlines=Ne,p.value.filter.currencyTypes=ot,p.value.filter.jobNos=tt,p.value.filter.ticketKinds=at,p.value.filter.ticketTypes=Re,p.value.filter.prntNos=et,p.value.filter.payTypes=nt))}K.value=Qe((Kt=p.value)==null?void 0:Kt.items);const vt=He((Wt=p.value)==null?void 0:Wt.items);ne.value=vt==null?void 0:vt.sort((Ne,Re)=>new Date(Ne.salesTime).getTime()-new Date(Re.salesTime).getTime()).reverse(),w.value=S.isFilterTime?we(ne.value):we((Jt=p.value)==null?void 0:Jt.items),W.value=(ea=(Zt=p.value)==null?void 0:Zt.items)==null?void 0:ea.length,F=((ta=p.value)==null?void 0:ta.items)??[],Ie()}finally{se.value=!1}})},gt=(r,$)=>{if($!=null&&$.length)_.value.includes(r)||_.value.push(r);else{const Y=_.value.indexOf(r);Y!==-1&&_.value.splice(Y,1)}},ht=(r,$,Y)=>{var Te,me,ce,ie,pe,ue,o,ye;gt(r,$),C.value=!0;const le=He($);Y&&le.push(Y),i[r]=le,F=((Te=p.value)==null?void 0:Te.items)??[],((me=i==null?void 0:i.airline)==null?void 0:me.length)>0&&(F=F.filter(re=>i.airline.includes(re.airline))),((ce=i==null?void 0:i.prntNo)==null?void 0:ce.length)>0&&(F=F.filter(re=>i.prntNo.includes(re.prntNo))),((ie=i==null?void 0:i.ticketStatus)==null?void 0:ie.length)>0&&(F=F.filter(re=>i.ticketStatus.includes(re.ticketStatus)),F.sort((re,Ve)=>re.salesTime.localeCompare(Ve.salesTime))),((pe=i==null?void 0:i.payType)==null?void 0:pe.length)>0&&(F=F.filter(re=>i.payType.includes(re.payType))),((ue=i==null?void 0:i.jobNo)==null?void 0:ue.length)>0&&(F=F.filter(re=>i.jobNo.includes(re.jobNo))),((o=i==null?void 0:i.ticketType)==null?void 0:o.length)>0&&(F=F.filter(re=>i.ticketType.includes(re.ticketType))),((ye=i==null?void 0:i.currencyType)==null?void 0:ye.length)>0&&(F=F.filter(re=>i.currencyType.includes(re.currencyType))),K.value=Qe(F),F=F.sort((re,Ve)=>new Date(re.salesTime).getTime()-new Date(Ve.salesTime).getTime()).reverse(),oe.value.pageNumber=1,w.value=we(F),W.value=F.length,Ie()},bt=(r,$)=>{oe.value.pageNumber=r===0?1:r,oe.value.pageSize=$,S.isFilterTime&&!C.value?w.value=we(ne.value):w.value=we(F),Ie()},qe=[{name:t("app.agentReport.airline"),wch:17},{name:t("app.agentReport.tktNo"),wch:21},{name:t("app.agentReport.tktStatus"),wch:19},{name:t("app.agentReport.deviceNo"),wch:12},{name:t("app.agentReport.agent"),wch:8},{name:t("app.agentReport.segSE"),wch:12},{name:t("app.agentReport.tktSettle"),wch:14},{name:t("app.agentReport.tax"),wch:10},{name:t("app.agentReport.obTax"),wch:10},{name:t("app.agentReport.agency"),wch:12},{name:t("app.agentReport.agencyRate"),wch:12},{name:t("app.agentReport.handFee"),wch:12},{name:t("app.agentReport.refundNo"),wch:12},{name:"PNR",wch:8},{name:t("app.agentReport.payment"),wch:10},{name:t("app.agentReport.tktSymbol"),wch:10},{name:t("app.agentReport.saleDate"),wch:14},{name:t("app.agentReport.salesTime"),wch:14},{name:t("app.agentReport.curryType"),wch:12},{name:t("app.agentReport.tktType"),wch:18}],Xe=r=>{const $=new Ca.Workbook;mn({...p.value,items:F,total:K.value},$),hn({...p.value,items:F,total:K.value},$),vn({...p.value,items:F,total:K.value},$),$.xlsx.writeBuffer().then(Y=>{const le=new Blob([Y],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),Te=URL.createObjectURL(le),me=document.createElement("a");me.href=Te,me.download=`${r}.xlsx`,me.click(),URL.revokeObjectURL(Te)})},Ke=()=>{const r=[],$=[];qe.forEach(Y=>{r.push(Y.name),$.push({wch:Y.wch})}),Xe(`${S.searchDate}销售日报`)},We=r=>{if(r)switch(r){case"全部":S.filterPayType="AL";break;case"(CA) 现金":S.filterPayType="CA";break;case"(CC) 信用卡":S.filterPayType="CC";break;case"(CK) 支票":S.filterPayType="CK";break;default:S.filterPayType=r;break}else S.filterPayType=S.filterPayTypeCopy;S.filterPayType=S.filterPayType.trim().toUpperCase()},Je=()=>{S.filterPayTypeCopy=S.filterPayType},Ze=async()=>{await oa.confirm(t("app.agentReport.confirmExport"),"",{icon:Tt("em",{class:"iconfont icon-info-circle-line"}),customClass:"sales-daily-msg-box crs-btn-ui",confirmButtonText:t("app.agentReport.confirm"),cancelButtonText:t("app.agentReport.cancel"),showClose:!1,autofocus:!1}),(F??[]).length<1?await oa.alert(t("app.agentReport.dataNoEmpty"),{icon:Tt("em",{class:"iconfont icon-info-circle-line"}),customClass:"iconStyle",showConfirmButton:!1,type:"warning"}):Ke()};Me(()=>{window.addEventListener("resize",()=>{Ie()}),Ie(),Pe(),N(Ae.value)});const n=()=>{var Te,me;if(!X.value||!X.value.includes("-"))return;const r=X.value.split("-")[0],$=((me=(Te=r==null?void 0:r.substring(r.indexOf("TSL:")+4))==null?void 0:Te.trim())==null?void 0:me.split("/"))??[],Y=$[0]??"",le=$[1];S.searchDate=Ue(le?Sa(le):new Date).format("YYYY-MM-DD"),S.prntNo=Y,ee()},R=async()=>{ve.value=!0},ma=(r,$,Y)=>{f.value=r,j.value=$,P.value=Y},ya=(r,$)=>{if($==="")return[];if($==="$$$")return r;const Y=$.split(";").map(le=>le.trim()==="本票"?"PYN":le.trim().toUpperCase());return r.filter(le=>Y.includes(le.value))};Nt(X,()=>{n()}),Nt(Z,()=>{var r;V.value&&(S.prntNo=((r=Z.value)==null?void 0:r.internationalPrinterno)??"")}),Me(async()=>{var $,Y;V.value&&(S.prntNo=(($=Z.value)==null?void 0:$.internationalPrinterno)??""),de.value=ya(ke,_e.value),S.tktType=((Y=de.value[0])==null?void 0:Y.label)??"";const r=await Ta({code:"BUSINESS.DAILYSALES.SWITCH"},"09300114");J.value=((r==null?void 0:r.data)??"")==="1",U.value||(ee(),n())});const It=()=>{g.value=window.innerWidth};return _a(async()=>{window.removeEventListener("resize",It)}),Me(()=>{g.value=window.innerWidth,window.addEventListener("resize",It)}),{isSatLink:U,salesForm:S,filterTypeList:_,querySaleDailyRes:p,salesDaliyDatas:w,rowSalesDaliyData:O,salesRules:mt,handleSearch:ee,reQueryTodyExceptionsFlag:l,salesRef:A,daliyTableRef:y,handleChangePage:bt,totalInfo:K,handleExport:Ze,filterChange:ht,pageTotal:W,isEmptyData:ut,pageInfo:oe,refundTicket:Ae,showDialog:q,showTodayExceptionsDialog:ve,showDialogFun:$e,showPrintNoDialog:Se,jumpToPnrEtQuery:Ye,jumpToTcTicketQuery:Ge,doCopy:ft,params:Oe,innerHeight:k,wrapHeight:H,salesDailyRef:h,openFilter:ze,filterRptRef:ae,updatePageSizeNum:Pe,pageRef:v,isSalesDaily:L,datePrefix:dt,isLoading:se,filterSaleTime:yt,handleTodayException:R,tktTypes:de,isToday:Le,totalNumber:f,issueNumber:j,refundNumber:P,getErrorNumber:ma,storeTodayError:Q,payTypes:ge,filterPayTypeRef:x,selectPayTypeBlur:We,selectPayTypeFocus:Je,setRefundData:D,refundTicketType:z,hasDailySalesAuth:J,innerWidth:g}},_n=(b,g)=>{const{t}=St(),I=Rt(),s=Fe(()=>{var p;return(p=I.state.user)==null?void 0:p.entityType}),k=E(),h=Fe(()=>g.rowSalesDaliyData),H=E(!1),A=rt({printerNo:"",ticketOrganization:""}),x={ticketOrganization:[{required:!0,message:t("app.ticketStatus.deviceNumNull"),trigger:"blur"}],printerNo:[{required:!0,message:t("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:Ea,trigger:"blur",message:t("app.ticketStatus.deviceError")}]},L=E([]),U={BSP:{label:t("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:t("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:t("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:t("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:t("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:t("app.agentTicketQuery.OWNTicket"),value:"ARL"}},z=Fe(()=>!["CDS","GPCDS"].includes(A.ticketOrganization)),v=()=>{k.value.validate(p=>{!p&&h.value||y("",h.value.refundNo,h.value.ticketTypeCode=="D",A.printerNo,A.ticketOrganization)})},ae=(p,M,j,P,f)=>({ticketNo:p,refundNo:M,ticketType:j?"D":"I",printerNo:P,ticketManagementOrganizationCode:f}),y=async(p,M,j,P,f)=>{var w;try{H.value=!0;const O=wt("09200108"),{data:K}=await Fa(ae(p,M,j,P,f),O);b("setRefundData",(w=K.value)==null?void 0:w.data),b("update:modelValue",!1),H.value=!1}finally{H.value=!1}},J=()=>{b("update:modelValue",!1)},Q=()=>{var p,M,j,P,f,w,O,K,W,se,ne,C;((p=s.value)!=null&&p.includes("$$$")||(M=s.value)!=null&&M.includes("BSP"))&&(L.value.push(U.BSP),L.value.push(U.GPBSP)),!((j=s.value)!=null&&j.includes("BSP"))&&((P=s.value)!=null&&P.includes("GP"))&&L.value.push(U.GPBSP),((f=s.value)!=null&&f.includes("$$$")||(w=s.value)!=null&&w.includes("BOP"))&&L.value.push(U.BOPBSP),((O=s.value)!=null&&O.includes("$$$")||(K=s.value)!=null&&K.includes("CDS"))&&(L.value.push(U.CDS),L.value.push(U.GPCDS)),((W=s.value)!=null&&W.includes("$$$")||(se=s.value)!=null&&se.includes("本票"))&&L.value.push(U.ARL),A.ticketOrganization=((C=(ne=L.value)==null?void 0:ne[0])==null?void 0:C.value)??""};return Me(()=>{Q(),A.printerNo=h.value.prntNo}),{formDate:k,printNoFrom:A,PRINTER_NO_RULES:x,ticketOrganizationList:L,isShowPrintNo:z,confirmPrinterNo:v,closeDialog:J,loading:H}},xn=_n,Nn=a("i",{class:"iconfont icon-close"},null,-1),Rn=[Nn],Cn={class:"carType-option-panel"},Sn=ct({__name:"RefundParameterDialog",props:{rowSalesDaliyData:{}},emits:["update:modelValue","setRefundData"],setup(b,{emit:g}){const t=g,I=b,{formDate:s,printNoFrom:k,PRINTER_NO_RULES:h,ticketOrganizationList:H,isShowPrintNo:A,confirmPrinterNo:x,closeDialog:L,loading:U}=xn(t,I);return(z,v)=>{const ae=Ct,y=it,J=pt,Q=Dt,p=At,M=Et,j=da,P=Ft;return c(),be(j,{title:z.$t("app.queryRefunds.selectPrint"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(L)},{footer:T(()=>[a("div",null,[$t((c(),be(M,{type:"primary","data-gid":"091T0107",onClick:v[4]||(v[4]=f=>e(x)())},{default:T(()=>[he(d(z.$t("app.ticketStatus.confirmBtn")),1)]),_:1})),[[P,e(U),void 0,{fullscreen:!0,lock:!0}]]),m(M,{onClick:e(L)},{default:T(()=>[he(d(z.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:T(()=>[a("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:v[0]||(v[0]=(...f)=>e(L)&&e(L)(...f))},Rn),m(p,{ref_key:"formDate",ref:s,model:e(k),rules:e(h),"label-position":"left","require-asterisk-position":"right"},{default:T(()=>[m(Q,{prop:"ticketOrganization",label:z.$t("app.agentTicketQuery.ticketOrganization")},{default:T(()=>[m(J,{modelValue:e(k).ticketOrganization,"onUpdate:modelValue":v[1]||(v[1]=f=>e(k).ticketOrganization=f),class:"ticket-management-organization",disabled:e(k).ticketOrganization==="",placeholder:e(k).ticketOrganization===""?z.$t("app.agentTicketQuery.noData"):""},{default:T(()=>[(c(!0),u(G,null,te(e(H),f=>(c(),be(y,{key:f.value,label:f.label,value:f.value},{default:T(()=>[a("div",Cn,[a("div",{class:xe(e(k).ticketOrganization===f.value?"show-select":"hidden-select")},[m(ae,null,{default:T(()=>[m(e(ra))]),_:1})],2),a("span",null,d(f.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label"]),e(A)?(c(),be(Q,{key:0,prop:"printerNo",label:z.$t("app.ticketStatus.deviceNum")},{default:T(()=>[m($a,{modelValue:e(k).printerNo,"onUpdate:modelValue":[v[2]||(v[2]=f=>e(k).printerNo=f),v[3]||(v[3]=f=>e(s).validateField("printerNo"))],"select-class":"w-[340px]"},null,8,["modelValue"])]),_:1},8,["label"])):Ee("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const De=b=>(Na("data-v-31a80230"),b=b(),Ra(),b),wn={class:"sales-daily-flex"},$n={key:0,class:"card-checked"},En={key:1,class:"card-no-checked"},Fn={key:0,class:"card-checked"},Dn={key:1,class:"card-no-checked"},An={class:"inline-block todayAbnormal"},In={key:1,class:"text-red-1 inline-block bg-red-3 leading-8 text-sm height-[32px] error-tip"},On={key:0,class:"result"},Pn={class:"search-info min-w-[380px]"},Vn=De(()=>a("span",{class:"search-info-title"},"OFFICE：",-1)),Un=De(()=>a("span",{class:"search-info-title space-span"},"IATA NO：",-1)),jn={class:"search-info-title space-span"},Bn=["onClick"],Mn=["onClick"],Hn={key:1},Ln=["onClick"],zn=["onClick"],Yn={key:1},Gn=["onClick"],Qn=["onClick"],qn={class:"gap-5 shadow-[0px_-2px_7px_0px_rgba(0,0,0,0.04)] bottom-wrap"},Xn={class:"bottom-wrap-title"},Kn={class:"bottom-wrap-title-item"},Wn={class:"min-w-[220px] h-[22px] bg-[#f3ffe9] rounded-sm justify-start items-start gap-1 flex"},Jn=De(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#d9f7be] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#237804] text-sm font-normal"},"TICKETS ISSU ")],-1)),Zn={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},eo={class:"min-w-[220px] h-[22px] bg-[#fff7f7] rounded-sm justify-start items-start gap-1 flex"},to=De(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#fce6e6] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#ff3636] text-sm font-normal"},"TICKETS REFUND ")],-1)),ao={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},no={class:"min-w-[220px] h-[22px] bg-[#fff7de] rounded-sm justify-start items-start gap-1 flex"},oo=De(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#feeeb9] rounded-sm justify-start items-center flex"},[a("div",{class:"text-center text-[#d48806] text-sm font-normal"},"TICKETS EXCHANGE ")],-1)),lo={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},ro={class:"min-w-[220px] h-[22px] bg-[#ebf2ff] rounded-sm justify-start items-start gap-1 flex"},so=De(()=>a("div",{class:"w-[150px] h-[22px] px-1.5 bg-[#d6e4ff] rounded-sm justify-end items-center flex"},[a("div",{class:"text-center text-[#143889] text-sm font-normal"},"TICKETS VOID")],-1)),co={class:"h-[22px] text-neutral-800 text-sm font-normal items-center flex"},io={class:"bottom-wrap-desc gap-[30px]"},po={class:"bottom-wrap-desc-item"},uo={class:"text-[#8c8c8c] text-sm font-normal"},mo={class:"!gap-2.5"},yo={key:0,class:"text-neutral-800 text-sm font-normal"},fo={class:"text-[#8c8c8c] text-sm font-normal"},go={class:"!gap-2.5"},ho={key:0,class:"text-neutral-800 text-sm font-normal"},bo={class:"text-[#8c8c8c] text-sm font-normal"},ko={class:"!gap-2.5"},vo={key:0,class:"text-neutral-800 text-sm font-normal"},To={class:"text-[#8c8c8c] text-sm font-normal"},_o={class:"!gap-2.5"},xo={key:0,class:"text-neutral-800 text-sm font-normal"},No={class:"bottom-wrap-desc-item"},Ro={class:"text-[#8c8c8c] text-sm font-normal"},Co={class:"!gap-2.5"},So={key:0,class:"text-neutral-800 text-sm font-normal"},wo={class:"text-[#8c8c8c] text-sm font-normal"},$o={class:"!gap-2.5"},Eo={key:0,class:"text-neutral-800 text-sm font-normal"},Fo={class:"text-[#8c8c8c] text-sm font-normal"},Do={class:"!gap-2.5"},Ao={key:0,class:"text-neutral-800 text-sm font-normal"},Io={class:"text-[#8c8c8c] text-sm font-normal"},Oo={class:"!gap-2.5"},Po={key:0,class:"text-neutral-800 text-sm font-normal"},Vo={class:"bottom-wrap-desc-item"},Uo={class:"text-[#8c8c8c] text-sm font-normal"},jo={class:"!gap-2.5"},Bo={key:0,class:"text-neutral-800 text-sm font-normal"},Mo={class:"text-[#8c8c8c] text-sm font-normal"},Ho={class:"!gap-2.5"},Lo={key:0,class:"text-neutral-800 text-sm font-normal"},zo={class:"text-[#8c8c8c] text-sm font-normal"},Yo={class:"!gap-2.5"},Go={key:0,class:"text-neutral-800 text-sm font-normal"},Qo={class:"text-[#8c8c8c] text-sm font-normal"},qo={class:"!gap-2.5"},Xo={key:0,class:"text-neutral-800 text-sm font-normal"},Ko={class:"bottom-wrap-desc-item"},Wo={class:"text-[#8c8c8c] text-sm font-normal"},Jo={class:"!gap-2.5"},Zo={key:0,class:"text-neutral-800 text-sm font-normal"},el={class:"text-[#8c8c8c] text-sm font-normal"},tl={class:"!gap-2.5"},al={key:0,class:"text-neutral-800 text-sm font-normal"},nl={class:"text-[#8c8c8c] text-sm font-normal"},ol={class:"!gap-2.5"},ll={key:0,class:"text-neutral-800 text-sm font-normal"},rl={class:"text-[#8c8c8c] text-sm font-normal"},sl={class:"!gap-2.5"},cl={key:0,class:"text-neutral-800 text-sm font-normal"},il={class:"bottom-wrap-desc-item"},pl=De(()=>a("div",null,null,-1)),ul={class:"text-[#8c8c8c] text-sm font-normal"},dl={class:"!gap-2.5"},ml={key:0,class:"text-neutral-800 text-sm font-normal"},yl={key:1,class:"empty-info"},fl=De(()=>a("img",{src:la,alt:"$t('app.agentReport.nodata')"},null,-1)),gl={class:"main-info"},hl={name:"SalesDaily"},bl=ct({...hl,props:{cmd:{}},setup(b){const g=b,{innerWidth:t,isSatLink:I,salesForm:s,salesRef:k,filterTypeList:h,daliyTableRef:H,querySaleDailyRes:A,salesDaliyDatas:x,rowSalesDaliyData:L,salesRules:U,handleSearch:z,filterChange:v,handleChangePage:ae,totalInfo:y,handleExport:J,pageTotal:Q,salesDailyRef:p,filterSaleTime:M,showPrintNoDialog:j,isEmptyData:P,pageInfo:f,refundTicket:w,showDialog:O,handleTodayException:K,showTodayExceptionsDialog:W,doCopy:se,params:ne,openFilter:C,filterRptRef:l,pageRef:i,jumpToPnrEtQuery:V,jumpToTcTicketQuery:Z,isLoading:_e,isSalesDaily:ke,datePrefix:de,showDialogFun:ge,tktTypes:_,isToday:X,reQueryTodyExceptionsFlag:oe,totalNumber:F,issueNumber:q,refundNumber:Se,getErrorNumber:ve,storeTodayError:Ae,payTypes:Oe,filterPayTypeRef:ut,selectPayTypeBlur:S,selectPayTypeFocus:dt,setRefundData:Le,refundTicketType:mt,hasDailySalesAuth:yt}=Tn(g);return(N,D)=>{var qe,Xe,Ke,We,Je,Ze;const ft=Aa,$e=Dt,ze=xa("Select"),Pe=Ct,Ye=it,Ge=pt,we=ca,Be=Et,Qe=Ia,Ie=At,ee=ia,gt=ua,ht=pa,bt=Ft;return $t((c(),u("div",{ref_key:"salesDailyRef",ref:p,class:"crs-new-ui-init-cls sales-daily crs-btn-ui h-[100%]"},[a("div",wn,[m(Ie,{ref_key:"salesRef",ref:k,model:e(s),inline:!0,rules:e(U),"require-asterisk-position":"right",class:"sale-form"},{default:T(()=>[m($e,{label:N.$t("app.agentReport.date"),prop:"searchDate",class:"title-label"},{default:T(()=>[m(ft,{modelValue:e(s).searchDate,"onUpdate:modelValue":D[0]||(D[0]=n=>e(s).searchDate=n),editable:!1,type:"date",clearable:!1,"prefix-icon":e(de),"value-format":"YYYY-MM-DD"},null,8,["modelValue","prefix-icon"])]),_:1},8,["label"]),e(I)&&(e(X)||e(yt))?(c(),be($e,{key:0,label:N.$t("app.agentReport.tktType")},{default:T(()=>[m(Ge,{modelValue:e(s).tktType,"onUpdate:modelValue":D[1]||(D[1]=n=>e(s).tktType=n),class:"card-type-select","popper-class":"select_card_popper"},{default:T(()=>[(c(!0),u(G,null,te(e(_),n=>(c(),be(Ye,{key:n.value,label:N.$t(`app.agentReport.${n.label}`),value:n.value},{default:T(()=>[e(s).tktType===n.value?(c(),u("span",$n,[m(Pe,{size:14},{default:T(()=>[m(ze)]),_:1})])):(c(),u("span",En)),a("span",null,d(N.$t(`app.agentReport.${n.label}`)),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["label"])):Ee("",!0),m($e,{label:N.$t("app.agentReport.ticketMachineNumber"),prop:"prntNo",class:"print-ticket-number"},{default:T(()=>[m(we,{modelValue:e(s).prntNo,"onUpdate:modelValue":D[2]||(D[2]=n=>e(s).prntNo=n),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:N.$t("app.agentReport.printTicketNumber")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),m($e,{label:N.$t("app.agentReport.ticketNumber"),prop:"ticketNum",class:"print-ticket-number"},{default:T(()=>[m(we,{modelValue:e(s).ticketNumber,"onUpdate:modelValue":D[3]||(D[3]=n=>e(s).ticketNumber=n),modelModifiers:{trim:!0},size:"default",clearable:"",placeholder:N.$t("app.agentReport.ticketNumHolder")},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),m($e,{label:N.$t("app.queryRefunds.payType"),prop:"filterPayType",class:"print-ticket-number"},{default:T(()=>[m(Ge,{ref_key:"filterPayTypeRef",ref:ut,modelValue:e(s).filterPayType,"onUpdate:modelValue":D[4]||(D[4]=n=>e(s).filterPayType=n),modelModifiers:{trim:!0},"popper-class":"select_card_popper",clearable:"",filterable:"","allow-create":"","default-first-option":"","reserve-keyword":!1,style:{width:"240px"},onChange:e(S),onVisibleChange:e(dt)},{default:T(()=>[(c(!0),u(G,null,te(e(Oe),n=>(c(),be(Ye,{key:n.value,label:N.$t(`app.agentReport.${n.label}`),value:n.value},{default:T(()=>[e(s).filterPayType===n.value?(c(),u("span",Fn,[m(Pe,{size:14},{default:T(()=>[m(ze)]),_:1})])):(c(),u("span",Dn)),a("span",null,d(N.$t(`app.agentReport.${n.label}`)),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","onChange","onVisibleChange"])]),_:1},8,["label"]),m($e,null,{default:T(()=>[m(Be,{"data-gid":"11280201",type:"primary",onClick:D[5]||(D[5]=n=>e(z)())},{default:T(()=>[he(d(N.$t("app.agentReport.search")),1)]),_:1})]),_:1}),a("div",An,[e(I)?(c(),be(Be,{key:0,class:"today-error",type:"error",onClick:e(K)},{default:T(()=>[he(d(N.$t("app.agentReport.todayAbnormal")),1)]),_:1},8,["onClick"])):Ee("",!0),e(I)?(c(),u("span",In,d(N.$t("app.agentReport.totalError",{total:e(F),issueError:e(q),refundError:e(Se)})),1)):Ee("",!0)]),m($e,{label:N.$t("app.agentReport.sortedTime")},{default:T(()=>[m(Qe,{modelValue:e(s).isFilterTime,"onUpdate:modelValue":D[6]||(D[6]=n=>e(s).isFilterTime=n),"inline-prompt":"","active-text":N.$t("app.queryRefunds.yes"),"inactive-text":N.$t("app.queryRefunds.no"),onChange:e(M)},null,8,["modelValue","active-text","inactive-text","onChange"])]),_:1},8,["label"])]),_:1},8,["model","rules"]),(qe=e(A))!=null&&qe.office?(c(),u("div",On,[a("div",Pn,[Vn,he(d((Xe=e(A))==null?void 0:Xe.office)+" ",1),Un,he(d((Ke=e(A))==null?void 0:Ke.iata)+" ",1),a("span",jn,d(N.$t("app.agentReport.saleDate"))+"：",1),he(d((We=e(A))!=null&&We.items[0]?(Je=e(A))==null?void 0:Je.items[0].salesDate:e(s).searchDate),1)])])):Ee("",!0),m(Be,{class:"export-btn","data-gid":"11280202",onClick:e(J)},{default:T(()=>[he(d(N.$t("app.agentReport.export")),1)]),_:1},8,["onClick"])]),(Ze=e(A))!=null&&Ze.office?(c(),u("div",{key:0,class:xe([e(t)<=1600?"height-min":"height-max"])},[m(ht,{ref_key:"daliyTableRef",ref:H,data:e(x),stripe:"",style:{"min-width":"100%"},height:"100%",class:"sales-daily-table",onFilterChange:e(v)},{default:T(()=>[m(ee,{prop:"airline",width:"60px",fixed:"left"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(h).includes("airline")?"text-brand-2":""]),onClick:D[7]||(D[7]=n=>e(C)(0))},d(N.$t("app.agentReport.airline")),3),m(Ce,{ref:n=>{n&&(e(l)[0]=n)},filters:e(A).filter.airlines,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":N.$t("app.agentReport.agentTip"),"column-key":"airline",onHandleConfrim:e(v)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),m(ee,{prop:"ticket",label:N.$t("app.agentReport.tktNo"),width:"150px",flexible:"true",fixed:"left"},{default:T(({row:n})=>[a("span",{class:"pointer-span",onClick:R=>e(Z)(n.ticket,n.pnr)},d(n.ticket),9,Bn),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:R=>e(se)(n.ticket)},null,8,Mn)]),_:1},8,["label"]),m(ee,{prop:"ticketStatus",width:"88px",fixed:"left"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(h).includes("ticketStatus")?"text-brand-2":""]),onClick:D[8]||(D[8]=n=>e(C)(1))},d(N.$t("app.agentReport.tktStatus")),3),m(Ce,{ref:n=>{n&&(e(l)[1]=n)},filters:e(A).filter.ticketTypes,"column-key":"ticketStatus",onHandleConfrim:e(v)},null,8,["filters","onHandleConfrim"])]),default:T(({row:n})=>[m(gt,{class:xe(`${n.ticketStatus}-tag`)},{default:T(()=>[he(d(n.ticketStatus),1)]),_:2},1032,["class"])]),_:1}),m(ee,{prop:"prntNo",width:"84px",fixed:""},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(h).includes("prntNo")?"text-brand-2":""]),onClick:D[9]||(D[9]=n=>e(C)(2))},d(N.$t("app.agentReport.ticketMachineNumber")),3),m(Ce,{ref:n=>{n&&(e(l)[2]=n)},filters:e(A).filter.prntNos,"column-key":"prntNo",onHandleConfrim:e(v)},null,8,["filters","onHandleConfrim"])]),_:1}),m(ee,{prop:"jobNo","min-width":"80px"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(h).includes("jobNo")?"text-brand-2":""]),onClick:D[10]||(D[10]=n=>e(C)(3))},d(N.$t("app.agentReport.agent")),3),m(Ce,{ref:n=>{n&&(e(l)[3]=n)},filters:e(A).filter.jobNos,"show-filter-input":!0,"not-disabled":!0,"filter-input-placeholder":N.$t("app.agentReport.agentTip"),"column-key":"jobNo",onHandleConfrim:e(v)},null,8,["filters","filter-input-placeholder","onHandleConfrim"])]),_:1}),m(ee,{prop:"desArr",label:N.$t("app.agentReport.segSE"),"min-width":"90px"},null,8,["label"]),m(ee,{prop:"amount",label:N.$t("app.agentReport.tktSettle"),"min-width":"80px"},null,8,["label"]),m(ee,{prop:"taxAmount",label:N.$t("app.agentReport.tax"),"min-width":"80px"},null,8,["label"]),m(ee,{prop:"obTax",label:N.$t("app.agentReport.obTax"),"min-width":"80px"},{default:T(({row:n})=>[n.obTax==="0"?(c(),u(G,{key:0},[he("0.00")],64)):(c(),u(G,{key:1},[he(d(n.obTax),1)],64))]),_:1},8,["label"]),m(ee,{prop:"agencyFee",label:N.$t("app.agentReport.agency"),"min-width":"75px"},null,8,["label"]),m(ee,{prop:"agencyFeePercent",label:N.$t("app.agentReport.agencyRate"),"min-width":"75px"},null,8,["label"]),m(ee,{prop:"serviceCharge",label:N.$t("app.agentReport.handFee"),"min-width":"75px"},null,8,["label"]),m(ee,{prop:"refundNo",label:N.$t("app.agentReport.refundNo"),"min-width":"120px"},{default:T(({row:n})=>[e(P)(n.refundNo)?(c(),u(G,{key:0},[he(" - ")],64)):(c(),u("div",Hn,[a("span",{class:"pointer-span",onClick:R=>e(ge)(n)},d(n.refundNo),9,Ln),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:R=>e(se)(n.refundNo)},null,8,zn)]))]),_:1},8,["label"]),m(ee,{prop:"pnr",label:"PNR","min-width":"105px"},{default:T(({row:n})=>[e(P)(n.pnr)?(c(),u(G,{key:0},[],64)):(c(),u("div",Yn,[a("span",{class:"pointer-span",onClick:R=>e(V)(n.pnr)},d(n.pnr),9,Gn),a("em",{class:"pointer-span iconfont icon-copy copy-em",onClick:R=>e(se)(n.pnr)},null,8,Qn)]))]),_:1}),m(ee,{prop:"ticketType","min-width":"125px"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(h).includes("ticketType")?"text-brand-2":""]),onClick:D[11]||(D[11]=n=>e(C)(6))},d(N.$t("app.agentReport.tktType")),3),m(Ce,{ref:n=>{n&&(e(l)[6]=n)},filters:e(A).filter.ticketKinds,"column-key":"ticketType",onHandleConfrim:e(v)},null,8,["filters","onHandleConfrim"])]),_:1}),m(ee,{prop:"payType","min-width":"100px"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(h).includes("payType")?"text-brand-2":""]),onClick:D[12]||(D[12]=n=>e(C)(4))},d(N.$t("app.agentReport.payment")),3),m(Ce,{ref:n=>{n&&(e(l)[4]=n)},filters:e(A).filter.payTypes,"column-key":"payType",onHandleConfrim:e(v)},null,8,["filters","onHandleConfrim"])]),_:1}),m(ee,{prop:"couponNo",label:N.$t("app.agentReport.tktSymbol")},null,8,["label"]),m(ee,{prop:"currencyType","min-width":"75px"},{header:T(()=>[a("span",{class:xe(["pointer-span-drop",e(h).includes("currencyType")?"text-brand-2":""]),onClick:D[13]||(D[13]=n=>e(C)(5))},d(N.$t("app.agentReport.curryType")),3),m(Ce,{ref:n=>{n&&(e(l)[5]=n)},filters:e(A).filter.currencyTypes,"column-key":"currencyType",onHandleConfrim:e(v)},null,8,["filters","onHandleConfrim"])]),_:1})]),_:1},8,["data","onFilterChange"]),m(e(Da),{ref:n=>{n&&(i.value=n)},class:"sales-daily-page crs-pagination-ui",total:e(Q),"current-page":e(f).pageNumber,"page-size":20,onHandleChange:e(ae)},null,8,["total","current-page","onHandleChange"]),a("div",qn,[a("div",Xn,[a("div",Kn,[a("div",null,[a("div",Wn,[Jn,a("div",Zn,d(e(y).totalTicket),1)])]),a("div",null,[a("div",eo,[to,a("div",ao,d(e(y).totalRefund),1)])]),a("div",null,[a("div",no,[oo,a("div",lo,d(e(y).totalExchange),1)])]),a("div",null,[a("div",ro,[so,a("div",co,d(e(y).totalVoid),1)])])])]),a("div",io,[a("div",po,[a("div",null,[a("label",uo,d(N.$t("app.agentReport.tktSettleBottom")),1),a("div",mo,[e(x).length<1||Object.keys(e(y).ISSUA.amount).length===0?(c(),u("span",yo,"0")):(c(!0),u(G,{key:1},te(e(y).ISSUA.amount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",fo,d(N.$t("app.agentReport.tktSettleBottom")),1),a("div",go,[e(x).length<1||Object.keys(e(y).REFUND.amount).length===0?(c(),u("span",ho,"0")):(c(!0),u(G,{key:1},te(e(y).REFUND.amount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",bo,d(N.$t("app.agentReport.tktSettleBottom")),1),a("div",ko,[e(x).length<1||Object.keys(e(y).EXCHANGE.amount).length===0?(c(),u("span",vo,"0")):(c(!0),u(G,{key:1},te(e(y).EXCHANGE.amount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",To,d(N.$t("app.agentReport.tktSettleBottom")),1),a("div",_o,[e(x).length<1||Object.keys(e(y).VOID.amount).length===0?(c(),u("span",xo,"0")):(c(!0),u(G,{key:1},te(e(y).VOID.amount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])])]),a("div",No,[a("div",null,[a("label",Ro,d(N.$t("app.agentReport.taxBottom")),1),a("div",Co,[e(x).length<1||Object.keys(e(y).ISSUA.taxAmount).length===0?(c(),u("span",So,"0")):(c(!0),u(G,{key:1},te(e(y).ISSUA.taxAmount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",wo,d(N.$t("app.agentReport.taxBottom")),1),a("div",$o,[e(x).length<1||Object.keys(e(y).REFUND.taxAmount).length===0?(c(),u("span",Eo,"0")):(c(!0),u(G,{key:1},te(e(y).REFUND.taxAmount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Fo,d(N.$t("app.agentReport.taxBottom")),1),a("div",Do,[e(x).length<1||Object.keys(e(y).EXCHANGE.taxAmount).length===0?(c(),u("span",Ao,"0")):(c(!0),u(G,{key:1},te(e(y).EXCHANGE.taxAmount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Io,d(N.$t("app.agentReport.taxBottom")),1),a("div",Oo,[e(x).length<1||Object.keys(e(y).VOID.taxAmount).length===0?(c(),u("span",Po,"0")):(c(!0),u(G,{key:1},te(e(y).VOID.taxAmount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])])]),a("div",Vo,[a("div",null,[a("label",Uo,d(N.$t("app.agentReport.agencyBottom")),1),a("div",jo,[e(x).length<1||Object.keys(e(y).ISSUA.agencyFee).length===0?(c(),u("span",Bo,"0")):(c(!0),u(G,{key:1},te(e(y).ISSUA.agencyFee,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Mo,d(N.$t("app.agentReport.agencyBottom")),1),a("div",Ho,[e(x).length<1||Object.keys(e(y).REFUND.agencyFee).length===0?(c(),u("span",Lo,"0")):(c(!0),u(G,{key:1},te(e(y).REFUND.agencyFee,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",zo,d(N.$t("app.agentReport.agencyBottom")),1),a("div",Yo,[e(x).length<1||Object.keys(e(y).EXCHANGE.agencyFee).length===0?(c(),u("span",Go,"0")):(c(!0),u(G,{key:1},te(e(y).EXCHANGE.agencyFee,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",Qo,d(N.$t("app.agentReport.agencyBottom")),1),a("div",qo,[e(x).length<1||Object.keys(e(y).VOID.agencyFee).length===0?(c(),u("span",Xo,"0")):(c(!0),u(G,{key:1},te(e(y).VOID.agencyFee,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])])]),a("div",Ko,[a("div",null,[a("label",Wo,d(N.$t("app.agentReport.carriers")),1),a("div",Jo,[e(x).length<1||Object.keys(e(y).ISSUA.carriers).length===0?(c(),u("span",Zo,"0")):(c(!0),u(G,{key:1},te(e(y).ISSUA.carriers,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",el,d(N.$t("app.agentReport.carriers")),1),a("div",tl,[e(x).length<1||Object.keys(e(y).REFUND.carriers).length===0?(c(),u("span",al,"0")):(c(!0),u(G,{key:1},te(e(y).REFUND.carriers,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",nl,d(N.$t("app.agentReport.carriers")),1),a("div",ol,[e(x).length<1||Object.keys(e(y).EXCHANGE.carriers).length===0?(c(),u("span",ll,"0")):(c(!0),u(G,{key:1},te(e(y).EXCHANGE.carriers,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])]),a("div",null,[a("label",rl,d(N.$t("app.agentReport.carriers")),1),a("div",sl,[e(x).length<1||Object.keys(e(y).VOID.carriers).length===0?(c(),u("span",cl,"0")):(c(!0),u(G,{key:1},te(e(y).VOID.carriers,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])])]),a("div",il,[pl,a("div",null,[a("label",ul,d(N.$t("app.agentReport.handFee")),1),a("div",dl,[e(x).length<1||Object.keys(e(y).refundAmount).length===0?(c(),u("span",ml,"0")):(c(!0),u(G,{key:1},te(e(y).refundAmount,(n,R)=>(c(),u("span",{key:R,class:"text-neutral-800 text-sm font-normal"},d(`${R} ${n.toFixed(2)}`),1))),128))])])])])]),e(O)?(c(),be(wa,{key:0,modelValue:e(O),"onUpdate:modelValue":D[14]||(D[14]=n=>_t(O)?O.value=n:null),"printer-no":e(ne).printerNo,"printer-type":e(mt),"is-supplement-refund":!1,"refund-operation-condition":e(ne),"refund-ticket-data":e(w),"is-sales-daily":e(ke),onReSalesDaily:D[15]||(D[15]=n=>e(z)(e(W)))},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data","is-sales-daily"])):Ee("",!0)],2)):(c(),u("div",yl,[a("div",null,[fl,a("div",gl,d(N.$t("app.agentReport.nodata")),1)])])),e(W)?(c(),be(cn,{key:2,modelValue:e(W),"onUpdate:modelValue":D[16]||(D[16]=n=>_t(W)?W.value=n:null),"re-query":e(oe),"sales-form":e(s),"store-today-error":e(Ae),"tkt-types":e(_),onErrorNumber:e(ve),onShowRefundDialog:D[17]||(D[17]=n=>e(ge)(n))},null,8,["modelValue","re-query","sales-form","store-today-error","tkt-types","onErrorNumber"])):Ee("",!0),e(j)?(c(),be(Sn,{key:3,modelValue:e(j),"onUpdate:modelValue":D[18]||(D[18]=n=>_t(j)?j.value=n:null),"row-sales-daliy-data":e(L),onSetRefundData:e(Le)},null,8,["modelValue","row-sales-daliy-data","onSetRefundData"])):Ee("",!0)])),[[bt,e(_e)]])}}});const ar=Oa(bl,[["__scopeId","data-v-31a80230"]]);export{ar as default};
