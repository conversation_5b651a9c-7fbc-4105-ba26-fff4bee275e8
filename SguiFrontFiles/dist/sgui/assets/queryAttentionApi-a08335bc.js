import{em as Fo,ht as Xa,hu as <PERSON>,ft as No,el as wo,hv as Io,hw as Eo,hx as <PERSON>,L as Ro,hy as Oo,hz as eo,q as Ye,Y as Qo,v as <PERSON>,r as B,w as Ee,x as u,y as Se,z as x,a5 as Ct,P as o,D as ye,A as t,H as je,bf as Vo,J as ge,B as $,F as yt,ak as me,Q as g,ai as he,G as f,a6 as va,T as Bo,_ as Yo,hA as qo,K as Uo,eb as Ho,m as jo,aI as Tt,hB as Go,ao as Ne,b4 as at,dP as to,c3 as Ko,hC as Je,cd as Zt,b6 as ha,dr as Mt,ac as Xe,aX as pt,hD as zo,o as bt,aY as it,gn as Wo,aj as Ie,C as da,b9 as rt,ab as gt,aH as Jo,am as It,an as Et,ah as Pt,E as Vt,b7 as oa,cg as s,hE as zt,hF as _e,bg as Zo,R as se,hG as ya,aE as kt,aF as Ft,aK as ao,af as Bt,ag as Wt,fH as Xo,e0 as en,X as tn,av as oo,a9 as Xt,b5 as Pa,s as ct,aJ as ht,aU as no,hH as Ra,ar as Jt,fC as qe,e3 as an,as as Rt,hI as on,aT as St,aZ as Da,a$ as nn,al as sa,bd as Oa,be as Qa,b1 as rn,b2 as $a,bX as sn,b3 as la,fY as ln,ax as cn,fj as io,hJ as pa,az as un,a8 as dn,fx as vt,gb as pn,gc as gn,fZ as ga,ce as fn,gK as mn,eY as ro,fE as vn,bw as hn,hK as yn,hL as bn,fG as Ma,au as so,eX as lo,dq as xa,g6 as _n,c4 as qt}from"./index-18f146fc.js";import{b as Sa,g as Ta}from"./time-e33ed60f.js";import{a3 as co,a2 as Cn,_ as An,a1 as Dn,a4 as Va}from"./regular-crs-4d4d60ea.js";import{E as $n}from"./index-cb25ab55.js";import{E as xn}from"./index-93952dc4.js";import{_ as et}from"./_plugin-vue_export-helper-c27b6911.js";import{E as ka}from"./index-5035a026.js";import{E as _t}from"./index-6ea30548.js";import{_ as Sn}from"./theme-light_empty-0081a108.js";import{g as Ba,C as Fa,c as ea,a as Tn,b as kn}from"./CustomDatePicker-0bb1d9a1.js";import{u as ca}from"./TicketOriginalPopover.vue_vue_type_script_setup_true_lang-98b79173.js";import{E as na,a as uo}from"./index-b0de6559.js";import{c as Fn}from"./_createAggregator-40d30c91.js";import{C as po}from"./index-c5921abf.js";import{E as At}from"./index-e3a5adf8.js";import{e as Ln}from"./index-3d51361b.js";import{f as Nn,E as ia,a as go}from"./index-9b639e2a.js";import{E as Ut,a as Ht}from"./index-c5503643.js";import{E as wn,a as In,b as En}from"./index-a197ff1b.js";import{t as ba}from"./throttle-39cac876.js";import{E as fo}from"./index-28cc82cf.js";import{E as mo}from"./index-1c4b8a79.js";import{E as Pn}from"./index-24b42935.js";import{u as Rn}from"./usePersonalization-956f86ae.js";import{_ as On}from"./Personalization.vue_vue_type_script_setup_true_lang-e733350b.js";import{E as Qn}from"./index-35960f7d.js";import{u as Mn}from"./useTemporaryOrderUtils-73fa067b.js";import{g as Vn}from"./position-a4b17c53.js";import{E as Bn}from"./index-2494e7da.js";function Yn(e,a,i){var r=-1,c=e.length;a<0&&(a=-a>c?0:c+a),i=i>c?c:i,i<0&&(i+=c),c=a>i?0:i-a>>>0,a>>>=0;for(var n=Array(c);++r<c;)n[r]=e[r+a];return n}function qn(e,a){return a.length<2?e:Fo(e,Yn(a,0,-1))}function Un(e,a){return a=Xa(a,e),e=qn(e,a),e==null||delete e[Lo(No(a))]}function Hn(e){return Ln(e)?void 0:e}var jn=1,Gn=2,Kn=4,zn=Nn(function(e,a){var i={};if(e==null)return i;var r=!1;a=wo(a,function(n){return n=Xa(n,e),r||(r=n.length>1),n}),Io(e,Eo(e),i),r&&(i=Po(i,jn|Gn|Kn,Hn));for(var c=a.length;c--;)Un(i,a[c]);return i});const Wn=zn;var Jn=Fn(function(e,a,i){e[i?0:1].push(a)},function(){return[[],[]]});const Ot=Jn,Zn=["light","dark"],Xn=Ro({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:Oo(eo),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:Zn,default:"light"}}),ei={close:e=>e instanceof MouseEvent},ti=Ye({name:"ElAlert"}),ai=Ye({...ti,props:Xn,emits:ei,setup(e,{emit:a}){const i=e,{Close:r}=qo,c=Qo(),n=Mo("alert"),l=B(!0),v=Ee(()=>eo[i.type]),d=Ee(()=>[n.e("icon"),{[n.is("big")]:!!i.description||!!c.default}]),y=Ee(()=>({[n.is("bold")]:i.description||c.default})),p=h=>{l.value=!1,a("close",h)};return(h,S)=>(u(),Se(Bo,{name:t(n).b("fade"),persisted:""},{default:x(()=>[Ct(o("div",{class:ye([t(n).b(),t(n).m(h.type),t(n).is("center",h.center),t(n).is(h.effect)]),role:"alert"},[h.showIcon&&t(v)?(u(),Se(t(je),{key:0,class:ye(t(d))},{default:x(()=>[(u(),Se(Vo(t(v))))]),_:1},8,["class"])):ge("v-if",!0),o("div",{class:ye(t(n).e("content"))},[h.title||h.$slots.title?(u(),$("span",{key:0,class:ye([t(n).e("title"),t(y)])},[yt(h.$slots,"title",{},()=>[me(g(h.title),1)])],2)):ge("v-if",!0),h.$slots.default||h.description?(u(),$("p",{key:1,class:ye(t(n).e("description"))},[yt(h.$slots,"default",{},()=>[me(g(h.description),1)])],2)):ge("v-if",!0),h.closable?(u(),$(he,{key:2},[h.closeText?(u(),$("div",{key:0,class:ye([t(n).e("close-btn"),t(n).is("customed")]),onClick:p},g(h.closeText),3)):(u(),Se(t(je),{key:1,class:ye(t(n).e("close-btn")),onClick:p},{default:x(()=>[f(t(r))]),_:1},8,["class"]))],64)):ge("v-if",!0)],2)],2),[[va,l.value]])]),_:3},8,["name"]))}});var oi=Yo(ai,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/alert/src/alert.vue"]]);const ni=Uo(oi),mt="ElInfiniteScroll",ii=50,ri=200,si=0,li={delay:{type:Number,default:ri},distance:{type:Number,default:si},disabled:{type:Boolean,default:!1},immediate:{type:Boolean,default:!0}},La=(e,a)=>Object.entries(li).reduce((i,[r,c])=>{var n,l;const{type:v,default:d}=c,y=e.getAttribute(`infinite-scroll-${r}`);let p=(l=(n=a[y])!=null?n:y)!=null?l:d;return p=p==="false"?!1:p,p=v(p),i[r]=Number.isNaN(p)?d:p,i},{}),vo=e=>{const{observer:a}=e[mt];a&&(a.disconnect(),delete e[mt].observer)},ci=(e,a)=>{const{container:i,containerEl:r,instance:c,observer:n,lastScrollTop:l}=e[mt],{disabled:v,distance:d}=La(e,c),{clientHeight:y,scrollHeight:p,scrollTop:h}=r,S=h-l;if(e[mt].lastScrollTop=h,n||v||S<0)return;let k=!1;if(i===e)k=p-(y+h)<=d;else{const{clientTop:T,scrollHeight:V}=e,b=Vn(e,r);k=h+y>=b+T+V-d}k&&a.call(c)};function fa(e,a){const{containerEl:i,instance:r}=e[mt],{disabled:c}=La(e,r);c||i.clientHeight===0||(i.scrollHeight<=i.clientHeight?a.call(r):vo(e))}const ui={async mounted(e,a){const{instance:i,value:r}=a;Ho(r)||jo(mt,"'v-infinite-scroll' binding value must be a function"),await Tt();const{delay:c,immediate:n}=La(e,i),l=Go(e,!0),v=l===window?document.documentElement:l,d=ba(ci.bind(null,e,r),c);if(l){if(e[mt]={instance:i,container:l,containerEl:v,delay:c,cb:r,onScroll:d,lastScrollTop:v.scrollTop},n){const y=new MutationObserver(ba(fa.bind(null,e,r),ii));e[mt].observer=y,y.observe(e,{childList:!0,subtree:!0}),fa(e,r)}l.addEventListener("scroll",d)}},unmounted(e){const{container:a,onScroll:i}=e[mt];a==null||a.removeEventListener("scroll",i),vo(e)},async updated(e){if(!e[mt])await Tt();else{const{containerEl:a,cb:i,observer:r}=e[mt];a.clientHeight&&r&&fa(e,i)}}},_a=ui;_a.install=e=>{e.directive("InfiniteScroll",_a)};const di=_a,ho=(e,a)=>Ne(`${at}/flight/queryFlights`,{headers:{gid:a}}).post(e).json(),pi=(e,a)=>Ne(`${at}/flight/queryFlights`,{headers:{gid:a}},{ignoreError:!0}).post(e).json(),gi=(e,a)=>Ne(`${at}/flight/queryMultiInfos`,{headers:{gid:a}}).post(e).json(),fi=e=>Ne(`${to}/commonConfiguration/terminalSelectInfo`,{headers:{gid:e}}).get().json(),mi=e=>Ne(`${to}/commonConfiguration/querySeamlessOrDa`,{headers:{gid:e}}).get().json(),nf=(e,a)=>Ne(`${at}/apiAvSearch/airRetrievePor`,{headers:{gid:e}},{onFetchError:a}).post().json(),rf=(e,a)=>Ne(`${at}/apiAvSearch/airPreOccupy`,{headers:{gid:a}}).post(e).json(),sf=e=>Ko.post(`${at}/apiAvSearch/airRetrievePor`,{},{headers:{gid:e}}),lf=(e,a,i)=>Ne(`${at}/apiAvSearch/airCancelPor`,{headers:{gid:a}},{onFetchError:i}).post(e).json(),cf=e=>Ne(`${at}/apiAvSearch/airIgnorePor`,{headers:{gid:e}}).post().json(),uf=(e,a)=>Ne(`${at}/apiAvSearch/airIgnorePor`,{headers:{gid:e}},{onFetchError:a}).post().json(),df=(e,a)=>Ne(`${at}/flight/segment/deleteSegment`,{headers:{gid:a}}).post(e).json(),pf=(e,a)=>Ne(`${at}/flight/segment/modifyStatus`,{headers:{gid:a}}).post(e).json(),gf=(e,a)=>Ne(`${at}/flight/segment/updateSegmentOrder`,{headers:{gid:a}}).post(e).json(),ff=(e,a)=>Ne(`${at}/flight/segment/selectSegment`,{headers:{gid:a}}).post(e).json(),yo=e=>Ne(`${at}/flight/segment/addSegment`).post(e).json(),mf=(e,a)=>Ne(`${at}/flight/segment/unmark`,{headers:{gid:a}}).post(e).json(),vi=(e,a)=>Ne(`${at}/flight/quota`,{headers:{gid:a}}).post(e).json(),vf=(e,a)=>Ne(`${Je}/apiAvSearch/qtbRebook`,{headers:{gid:a}}).post(e).json(),hf=(e,a)=>Ne(`${at}/flight/changeSegmentTime`,{headers:{gid:a}}).post(e).json();var aa=(e=>(e.A="A",e.B="B",e.H="H",e))(aa||{});const hi=(e,a)=>{const i=ca(),r="国内",c="国际",n=B(""),l=B(i.historyAirline),v=B({}),d=B({}),y=B([]),p=B([]),h=B(),S=B(!1),k=B(!1),T=B(!1),V=B(Zt()),b=ha({}),G=ha({}),w=B([]);let E=!1;const D=B(""),I=B(),m=B(),C=(X,re)=>{const z=[];if(re[0]){const j=re[0].airportCode;re[0].airportCode=`${X}#${j}`}for(let j=0;j<re.length;)z.push(re.slice(j,j+=4));return z},A=X=>{const re=[];return(X??[]).forEach(z=>{const j=[];z.alphabeticalOrderValue.forEach(ue=>{const Le={};Le.type=ue.alphabet,Le.airPort=C(ue.alphabet,ue.alphabetValue),j.push(Le)});const W={};W.group=z.alphabeticalOrder,W.groupValue=j,re.push(W)}),re},_=X=>{const re=[aa.A,aa.B,aa.H];w.value=X.map(z=>{const j={...z,type:z.firstLevel===r,airportEnName:z.airportEnName||z.airportCode,airportFullName:"",airPortFormatName:"",cityName:"",cityEnName:z.cityEnName||z.cityCode,searchName:[...z.nameArray,...z.namePYArray].toString(),searchCity:[...z.cityArray,...z.cityPYArray].toString()};return V.value==="en"?(j.cityName=j.cityEnName,j.airportFullName=j.airportEnName===j.airportCode?j.airportCode:`${j.airportEnName}${j.airportCode}`,j.airPortFormatName=j.airportEnName):(j.cityName=j.secondLevel,j.airportFullName=`${j.airportCnName}${j.airportCode}`,j.airPortFormatName=j.airportCnName),j}).sort((z,j)=>{const W=z.firstLevel===r,ue=j.firstLevel===r;if(W&&!ue)return-1;if(!W&&ue)return 1;const Le=z.codeType,$e=j.codeType,Oe=re.indexOf(Le)===-1?re.length:re.indexOf(Le),Ae=re.indexOf($e)===-1?re.length:re.indexOf($e);return Oe-Ae})},P=()=>{var re;const X={};X.data=JSON.parse((re=I.value)==null?void 0:re.localData),b.aggregate=A(X.data.domestic),G.aggregate=A(X.data.international)},U=()=>{var z;const X={};X.data=JSON.parse((z=m.value)==null?void 0:z.localData);const re=it(X.data);_(re)},K=X=>{if(X==="local"){const re=setInterval(async()=>{I.value=await pt("diLocalData"),I.value&&(P(),clearInterval(re))},5e3)}else{const re=setInterval(async()=>{m.value=await pt("searchLocalData"),m.value&&(U(),clearInterval(re))},5e3)}},Y=async()=>{if(I.value=await pt("diLocalData"),!I.value){K("local");return}P()},ne=async()=>{if(m.value=await pt("searchLocalData"),!m.value){K("all");return}U()},oe=(X,re)=>((X??[]).some(j=>{const W=V.value==="en"?j.airportEnName===re.airportEnName:j.airportCnName===re.airportCnName;return j.airportCode===re.airportCode&&W})||((X??[]).length===8&&X.shift(),(X??[]).push(re)),X),Z=(X,re,z,j)=>{var ue,Le;const W={airportCode:X,airportCnName:re,airportEnName:z,airPortFullName:"",airPortFormatName:""};j?l.value.domesticHistory=oe((ue=l.value)==null?void 0:ue.domesticHistory,W):l.value.internationalHistory=oe((Le=l.value)==null?void 0:Le.internationalHistory,W),i.setHistoryAirLine(l.value)},H=(X,re,z,j,W)=>{n.value=X,a("update:modelValue",X),a("update:name",V.value==="en"?z:re),a("change",V.value==="en"?z:re,X,e.segIndex),S.value=!1,h.value=W??""},O=Mt(async X=>{X.length!==0?(S.value=!1,k.value=!1,T.value=!0,await Tt(),E=!1,D.value=X,w.value.some(z=>`${z.airportCnName}${z.airportCode}`===X||`${z.cityName}${z.cityCode}`===X)&&(T.value=!1),S.value=!0):T.value=!1},200),L=Ee({get:()=>(n.value=e.modelValue.toLocaleUpperCase(),e.modelValue===e.name?e.modelValue:V.value==="en"&&e.name?`${e.modelValue} ${e.name??""}`:`${e.modelValue}${e.name??""}`),set:X=>{n.value=X.toLocaleUpperCase(),a("update:modelValue",X.toLocaleUpperCase()),a("update:name",""),a("change",X,e.segIndex),O(X)}}),R=()=>{S.value=!0,k.value=!0,T.value=!1,be()},N=B({}),F=(X,re)=>!X||(Wo.test(X)?X.length<3:X.length<2)?!1:re.some(j=>j.airportCode===X||j.cityCode===X||j.airportCnName===X||j.cityName===X?(h.value=j.cityCode===X||j.cityName.includes(X)?"city":"airport",N.value=j,!0):!1),ae=(X,re,z,j,W)=>{E=!0,Z(X,re,z,j),H(X,re,z,j,W)},le=(X,re,z,j,W)=>{E=!0,Z(X,re,z,j),H(X,re,z,j,W)},de=()=>{if(E)return;const X=e.modelValue.toUpperCase(),re=F(X,w.value);let z="",j=X,W="";re&&(h.value==="airport"?(z=N.value.airportCnName,W=N.value.airportEnName,j=N.value.airportCode):(z=N.value.cityName,W=N.value.cityEnName,j=N.value.cityCode),Z(j,z,W,N.value.type)),n.value=j,a("update:name",V.value==="en"?W:z),a("update:modelValue",j),a("change",j,e.segIndex)},fe=X=>{X.keyCode===9&&de()},te=()=>{S.value&&(S.value=!1),T.value=!1,k.value=!1},Te=async()=>{var re,z;const X=await pt("HOT_CITIES");if(X){const j=JSON.parse(X==null?void 0:X.localData);v.value=((re=j.filter(W=>W.field3===c))==null?void 0:re[0])??{},d.value=((z=j.filter(W=>W.field3===r))==null?void 0:z[0])??{}}},be=async()=>{const X=await pt("COMMON_CITIES");if(!X)return;const re=JSON.parse(X==null?void 0:X.localData);y.value=re.domesticCommonCities??[],p.value=re.internationalCommonCities??[]},Re=()=>{a("triggerAirBlur")};return Xe(()=>{var X;return(X=zo.state.user)==null?void 0:X.switchAirline},async()=>{I.value=await pt("diLocalData"),P()}),Xe(()=>e.isAshingTransitTerminal,()=>{e.isAshingTransitTerminal&&(k.value=!1)}),bt(async()=>{await Y(),await ne(),await Promise.all([Te(),be()])}),{visible:S,hotCitiesOfDomestic:d,hotCitiesOfInter:v,tabShow:k,showInputModel:L,searchShow:T,domesticData:b,internationalData:G,searchAllData:w,inputValue:D,inputClick:R,keyChange:O,getPortValue:ae,getSearchValue:le,clickOutside:te,popoverHide:de,updateCityCnName:fe,airBlurClick:Re,locale:V,threeCharacterCodeInput:n,comCitiesDomestic:y,comCitiesInter:p}},yi=hi,bi=(e,a)=>{const i=ca(),r=B(""),c=B(""),n=B([]),l=B([]),v=Ee(()=>{const C=i.historyAirline,A=e.difference?C.domesticHistory:C.internationalHistory;return it(A).map(P=>{var U;return{...P,airPortFullName:e.locale==="en"?P.airportEnName||P.airportCode:P.airportCnName||P.airportCode,airPortFormatName:S(P),tooltipDisabled:e.locale==="en"?((U=P.airportEnName)==null?void 0:U.length)<14:P.airportCnName.length<7}})}),d=async C=>{c.value=C.airportCode;const A=C.airportCode,_=A.includes("#")?A.slice(2,A.length):A;a("sendPortValue",_,C.airportCnName,C.airportEnName,e.difference)},y=async C=>{c.value=C.code;const A=C.code,_=A.includes("#")?A.slice(2,A.length):A;a("sendPortValue",_,C.cnName,C.enName,e.difference)},p=C=>{c.value=C.code,a("sendPortValue",C.code,C.city,C.city,e.difference,"city")},h=C=>C.includes("#")?C.slice(2,C.length):C,S=C=>e.locale==="en"?C.airportEnName:C.airportCnName,k=C=>C[0].airportCode.includes("#"),T=B(32),V=B(0),b=B(0),G=B(10),w=B(0),E=B([]),D=C=>{V.value=C.target.scrollTop,b.value=Math.floor(C.target.scrollTop/T.value),G.value=b.value+10},I=async C=>{if(C.paneName==="hotHistory")return;V.value=0,b.value=0,G.value=10;const A=n.value.filter(P=>P.group===C.paneName),_=[];A[0].groupValue.forEach(P=>{P.airPort.forEach(U=>{_.push(U)})}),E.value=_,w.value=E.value.length};Xe(()=>e.modelValue,()=>{c.value=e.modelValue.slice(0,3)},{immediate:!0});const m=Ee(()=>E.value.slice(b.value,G.value));return bt(async()=>{n.value=e.resData.aggregate,r.value="hotHistory"}),{tabActive:r,isChecked:c,paneData:n,history:v,getRealCode:h,checkContent:d,transformText:S,includeMark:k,submitHotCities:p,itemHeight:T,scrollTop:V,curryDataLength:w,scroll:D,tabClick:I,visibleList:m,scrollRef:l,checkComCity:y}},_i=bi,Ci={class:"main flex flex-row flex-wrap w-[537px] pt-3"},Ai={key:0,class:"more flex w-full"},Di={class:"h-[22px] leading-[22px] text-red-1 whitespace-nowrap text-xs pr-5"},$i={class:"map flex flex-row flex-wrap"},xi=["onClick"],Si={class:"airport-info-box"},Ti={class:"airport-code-box"},ki={class:"airport-code-text"},Fi={class:"airport-name-text focus:outline-none"},Li={key:1,class:"more flex w-full"},Ni={class:"h-[22px] leading-[22px] text-red-1 whitespace-nowrap text-xs pr-5"},wi={class:"map flex flex-row flex-wrap"},Ii=["onClick"],Ei={class:"airport-info-box"},Pi={class:"airport-code-box"},Ri={class:"airport-code-text"},Oi={class:"focus:outline-none"},Qi={class:"more flex w-full"},Mi={key:0,class:"h-[22px] leading-[22px] text-xs text-red-1 pr-5 whitespace-nowrap"},Vi={class:"map flex flex-row flex-wrap"},Bi=["onClick"],Yi={class:"airport-info-box"},qi={class:"airport-code-box"},Ui={class:"airport-code-text"},Hi={class:"airport-name-text"},ji={key:0,class:"h-[22px] leading-[22px] text-xs text-red-1 w-2.5 pr-6"},Gi={key:1,class:"h-[22px] mr-6 leading-4"},Ki=["onClick"],zi={class:"airport-info-box"},Wi={class:"airport-code-box"},Ji={class:"airport-code-text"},Zi={class:"airport-name-text"},Xi=Ye({__name:"AgentPort",props:{resData:{},difference:{type:Boolean},hotCities:{},comCities:{},locale:{},modelValue:{}},emits:["sendPortValue"],setup(e,{emit:a}){const i=e,r=a,c=Zt(),{tabActive:n,isChecked:l,paneData:v,history:d,checkContent:y,transformText:p,includeMark:h,submitHotCities:S,getRealCode:k,itemHeight:T,scrollTop:V,curryDataLength:b,scroll:G,tabClick:w,visibleList:E,scrollRef:D,checkComCity:I}=_i(i,r);return(m,C)=>(u(),Se(t(uo),{modelValue:t(n),"onUpdate:modelValue":C[1]||(C[1]=A=>rt(n)?n.value=A:null),type:"border-card",class:"portTabs w-[536px] px-5 box-border",onTabClick:t(w)},{default:x(()=>[f(t(na),{label:m.$t("app.fastQuery.headerQuery.hotHistory"),name:"hotHistory",lazy:!0},{default:x(()=>{var A,_;return[o("div",Ci,[m.comCities.length>0?(u(),$("div",Ai,[o("span",Di,g(m.$t("app.fastQuery.headerQuery.common")),1),o("div",$i,[(u(!0),$(he,null,Ie(m.comCities,P=>(u(),$("div",{key:P.code,class:ye(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(l)===P.code}]),onClick:U=>t(I)(P)},[f(t(_t),{effect:"dark",content:t(c)==="en"?P.enName:P.cnName,placement:"top"},{default:x(()=>[o("div",Si,[o("div",Ti,[o("span",ki,g(t(k)(P.code)),1)]),o("div",Fi,g(t(c)==="en"?P.enName:P.cnName),1)])]),_:2},1032,["content"])],10,xi))),128))])])):ge("",!0),(A=m.hotCities)!=null&&A.field4?(u(),$("div",Li,[o("span",Ni,g(m.$t("app.fastQuery.headerQuery.hot")),1),o("div",wi,[(u(!0),$(he,null,Ie(JSON.parse(m.hotCities.field4.toString()),P=>(u(),$("div",{key:P.code,class:ye(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(l)===P.code}]),onClick:U=>t(S)(P)},[f(t(_t),{effect:"dark",content:P.city,placement:"top"},{default:x(()=>[o("div",Ei,[o("div",Pi,[o("span",Ri,g(t(k)(P.code)),1)]),o("div",Oi,g(P.city),1)])]),_:2},1032,["content"])],10,Ii))),128))])])):ge("",!0),o("div",Qi,[((_=t(d))==null?void 0:_.length)!==0?(u(),$("span",Mi,g(m.$t("app.fastQuery.headerQuery.history")),1)):ge("",!0),o("div",Vi,[(u(!0),$(he,null,Ie(t(d),P=>(u(),$("div",{key:P.airportCode,class:ye(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(l)===P.airportCode}]),onClick:U=>t(y)(P)},[f(t(_t),{effect:"dark",content:P.airPortFormatName,placement:"top"},{default:x(()=>[o("div",Yi,[o("div",qi,[o("span",Ui,g(t(k)(P.airportCode)),1)]),o("div",Hi,g(P.airPortFormatName),1)])]),_:2},1032,["content"])],10,Bi))),128))])])])]}),_:1},8,["label"]),(u(!0),$(he,null,Ie(t(v),(A,_)=>(u(),Se(t(na),{key:A.group,label:A.group,name:A.group,lazy:!0},{default:x(()=>[o("div",{ref_for:!0,ref:P=>{P&&(t(D)[_]=P)},class:"scroll overflow-auto w-full h-[350px]",onScroll:C[0]||(C[0]=(...P)=>t(G)&&t(G)(...P))},[o("div",{class:"overflow-hidden",style:da({height:(t(b)+1)*t(T)+"px"})},[o("ul",{class:"list-none m-0",style:da({"margin-top":`${t(V)}px`})},[(u(!0),$(he,null,Ie(t(E),(P,U)=>{var K;return u(),$("li",{key:U,class:"flex flex-nowrap",style:da({height:t(T)+"px","margin-top":t(h)(P)?"10px":"0px"})},[t(h)(P)?(u(),$("span",ji,g((K=P[0])==null?void 0:K.airportCode.slice(0,1)),1)):(u(),$("span",Gi)),(u(!0),$(he,null,Ie(P,Y=>(u(),$("div",{key:Y.airportCode,class:ye(["content mr-1 text-ellipsis hover:text-[var(--bkc-el-color-primary)] hover:bg-brand-7 hover:rounded-sm",{active:t(l)===Y.airportCode}]),onClick:ne=>t(y)(Y)},[f(t(_t),{effect:"dark",content:t(p)(Y),placement:"top"},{default:x(()=>[o("div",zi,[o("div",Wi,[o("span",Ji,g(t(k)(Y.airportCode)),1)]),o("div",Zi,g(t(p)(Y)),1)])]),_:2},1032,["content"])],10,Ki))),128))],4)}),128))],4)],4)],544)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","onTabClick"]))}});const Ya=et(Xi,[["__scopeId","data-v-12f64a04"]]),er=(e,a)=>{const{t:i}=gt(),r=Ee(()=>e.searchData),c=B([]),n=B([]),l=B(),v=B(""),d=B(0),y=B(),p=B(),h=350,S=3,k=24,T=h/k+S,V=B(),b=U=>{const K={};return U.forEach(Y=>{const{firstLevel:ne,secondLevel:oe,cityCode:Z,cityName:H,country:O}=Y,L=ne+oe;K[L]||(K[L]={firstLevel:ne,secondLevel:H,secondLevelFullName:H===Z?Z:`${H}${Z}`,country:O,cityCode:Z,list:[]}),K[L].list.push(Y)}),Object.values(K)},G=(U,K,Y,ne)=>{const oe=e.locale==="en"?(U==null?void 0:U.split(" "))??"":K,Z=oe==null?void 0:oe.map(H=>H[0]).join("").toUpperCase().startsWith(Y);return ne?Z:(U==null?void 0:U.toUpperCase().startsWith(Y))||Z},w=(U,K)=>{if(!K)return[];const Y=K.toUpperCase(),[ne,oe]=Ot(U,fe=>fe.airportCode.toUpperCase().startsWith(Y)),[Z,H]=Ot(oe,fe=>fe.cityCode.toUpperCase().startsWith(Y)),[O,L]=Ot(H,fe=>G(fe.cityName,fe.cityPYArray,Y,!0)),[R,N]=Ot(L,fe=>fe.cityName.toUpperCase().startsWith(Y)),[F,ae]=Ot(N,fe=>G(fe.airPortFormatName,fe.namePYArray,Y,!0)),[le,de]=Ot(ae,fe=>fe.airPortFormatName.toUpperCase().startsWith(Y));return[...new Set([...ne,...Z,...O,...R,...F,...le,...de])]},E=U=>{const K=new Set,Y=new Set,ne=new Set,oe=U.toUpperCase(),Z=r.value;for(let O=Z.length-1;O>=0;O--){const L=Z[O].airportCode,R=Z[O].cityCode;if(L.startsWith(oe)||e.type!=="1"&&R.startsWith(oe))K.add(Z[O]);else{const N=G(Z[O].airPortFormatName,Z[O].namePYArray,oe),F=U.length>3&&(Z[O].airportCnName+Z[O].airportCode).includes(U);N||F?Y.add(Z[O]):G(Z[O].cityName,Z[O].cityPYArray,oe)&&ne.add(Z[O])}}return b(w([...K,...Y,...ne],U))},D=U=>{const K=[],Y=[],ne=[];return U.forEach(oe=>oe.firstLevel==="国际"?Y.push(oe):ne.push(oe)),U.length&&(U[0].firstLevel==="国际"?(Y.length&&K.push({code:"I",list:Y}),ne.length&&K.push({code:"D",list:ne})):(ne.length&&K.push({code:"D",list:ne}),Y.length&&K.push({code:"I",list:Y}))),K},I=U=>new Promise(Y=>{const ne=D(E(U));Y(ne)}),m=U=>{l.value=U.airportCode;const K=U.firstLevel==="国内";a("sendSearchValue",U.airportCode,U.airportCnName,U.airportEnName,K,"airport",U.airportEnName)},C=U=>{l.value=U.cityCode;const K=U.firstLevel==="国内";a("sendSearchValue",U.cityCode,U.secondLevel,U.secondLevel,K,"city")},A=(U,K)=>U.length+K.length>9?`${U.substr(0,6)}...${K}`:U+K,_=U=>{const ne=Math.floor((U||0)/k)+T;let oe=0,Z=-1;const H=n.value.findIndex((L,R)=>(Z=L.list.findIndex((N,F)=>{const ae=oe+N.list.length;return ne<ae&&!(ne<oe)||R===n.value.length-1&&F===L.list.length-1&&!(ae>ne)?!0:(oe=ae,!1)}),Z>-1)),O=[];if(H===-1){c.value=O;return}n.value.slice(0,H+1).forEach((L,R)=>{if(R>H)return;if(R<H){O.push(L);return}const N=L.list.slice(0,Z+1);O.push({...L,list:N})}),(O??[]).forEach(L=>{((L==null?void 0:L.list)??[]).forEach(R=>{((R==null?void 0:R.list)??[]).sort((N,F)=>{const ae=["A","B","H"],le=ae.indexOf(N.codeType),de=ae.indexOf(F.codeType);return le===de?0:le-de})})}),c.value=O},P=U=>{y.value||(y.value=!0,requestAnimationFrame(()=>{y.value=!1}),_(U.target.scrollTop))};return Jo(async()=>{v.value=i("app.fastQuery.headerQuery.searching"),V.value=!0,n.value=await I(e.input),await _(),V.value=!1,c.value.length<=0&&(v.value=i("app.fastQuery.headerQuery.noAirportsFound"))}),{allData:r,currySearchRes:c,isChecked:l,text:v,checkMemory:m,transformText:A,containerRef:p,handleScroll:P,totalHeight:d,searching:V,checkCity:C}},tr=er,ar=e=>(It("data-v-70c79d4c"),e=e(),Et(),e),or={class:"type-text-box"},nr={class:"text-name"},ir=ar(()=>o("div",{class:"name-line"},null,-1)),rr={class:"flex cursor-pointer"},sr=["textContent"],lr={key:0,class:"list"},cr={class:"airport-info-box"},ur={class:"airport-code-box"},dr={class:"airport-code-text"},pr={class:"airport-name-text"},gr={key:1,class:"list"},fr={class:"airport-info-box hover:bg-brand-7 hover:text-[var(--bkc-el-color-primary)]"},mr={class:"airport-code-box"},vr={class:"airport-code-text"},hr=["onClick"],yr={key:2,class:"list"},br=["onClick"],_r={class:"airport-info-box hover:bg-brand-7 hover:text-[var(--bkc-el-color-primary)]"},Cr={class:"airport-code-box"},Ar={class:"airport-code-text"},Dr={class:"w-[248px] shadow bg-gray-0 rounded-sm border border-gray-6 text-gray-6"},$r={class:"h-[22px] leading-[22px] inline-block my-2 pl-2 w-[240px] text-xs bg-gray-7 text-gray-2"},xr=Ye({__name:"AgentSearch",props:{type:{},input:{},searchData:{},locale:{}},emits:["sendSearchValue"],setup(e,{emit:a}){const i=e,r=a,{currySearchRes:c,isChecked:n,text:l,checkMemory:v,handleScroll:d,containerRef:y,searching:p,checkCity:h}=tr(i,r);return(S,k)=>(u(),$(he,null,[Ct(o("div",{class:ye(["agent-search",S.locale==="en"?"en-agent-search":""]),onScroll:k[0]||(k[0]=(...T)=>t(d)&&t(d)(...T))},[o("div",null,[o("div",{ref_key:"containerRef",ref:y},[(u(!0),$(he,null,Ie(t(c),(T,V)=>(u(),$("div",{key:V,class:"panel"},[o("div",or,[o("div",nr,g(T.code==="I"?S.$t("app.avSearch.Intl"):S.$t("app.avSearch.Dom")),1),ir]),(u(!0),$(he,null,Ie(T.list,(b,G)=>(u(),$("div",{key:G},[o("div",rr,[o("span",{class:"country-code-text",textContent:g(b.country)},null,8,sr),S.type===0?(u(),$("div",lr,[f(t(_t),{effect:"dark",content:b.secondLevel,placement:"top"},{default:x(()=>[o("div",cr,[o("div",ur,[o("div",dr,g(b.cityCode.includes("#")?b.cityCode.slice(2,b.cityCode.length):b.cityCode),1)]),o("div",pr,g(b.secondLevel),1)])]),_:2},1032,["content"])])):(u(),$("div",gr,[f(t(_t),{effect:"dark",content:b.secondLevel,placement:"top"},{default:x(()=>[o("div",fr,[o("div",mr,[o("div",vr,g(b.cityCode.includes("#")?b.cityCode.slice(2,b.cityCode.length):b.cityCode),1)]),o("div",{class:ye(["airport-name-text",t(n)===b.cityCode?"bg-brand-7 text-[var(--bkc-el-color-primary)]":""]),onClick:w=>t(h)(b)},g(b.secondLevel),11,hr)])]),_:2},1032,["content"])])),S.type!==1?(u(),$("div",yr,[(u(!0),$(he,null,Ie(b.list,(w,E)=>(u(),$("div",{key:E,class:ye(["content text-ellipsis",{"bg-brand-7 text-[var(--bkc-el-color-primary)]":t(n)===w.airportCode}]),onClick:D=>t(v)(w)},[f(t(_t),{effect:"dark",content:w.airPortFormatName,placement:"top"},{default:x(()=>[o("div",_r,[o("div",Cr,[o("div",Ar,g(w.airportCode.includes("#")?w.cityCode.slice(2,w.airportCode.length):w.airportCode),1)]),o("div",{class:ye(["airport-name-text",t(n)===w.airportCode?"bg-brand-7 text-[var(--bkc-el-color-primary)]":""])},g(w.airPortFormatName),3)])]),_:2},1032,["content"])],10,br))),128))])):ge("",!0)])]))),128))]))),128))],512)])],34),[[va,!t(p)&&t(c).length>0]]),Ct(o("div",Dr,[o("span",$r,g(t(l)),1)],512),[[va,t(p)||!(t(c).length>0)]])],64))}});const bo=et(xr,[["__scopeId","data-v-70c79d4c"]]),Sr={key:0,class:"svg-icon iconfont icon-nav-booking"},Tr={key:1,class:"svg-icon iconfont icon-booking-to"},kr={key:0},Fr=Ye({__name:"AgentAirportContainer",props:{position:{},prefixTitle:{},type:{},modelValue:{},name:{},segIndex:{},isAshingTransitTerminal:{type:Boolean},isAgentCity:{}},emits:["update:modelValue","update:name","change","triggerAirBlur"],setup(e,{emit:a}){const i=e,r=a,{tabShow:c,searchShow:n,domesticData:l,internationalData:v,searchAllData:d,inputValue:y,locale:p,inputClick:h,getPortValue:S,getSearchValue:k,showInputModel:T,clickOutside:V,visible:b,hotCitiesOfDomestic:G,hotCitiesOfInter:w,popoverHide:E,airBlurClick:D,threeCharacterCodeInput:I,comCitiesDomestic:m,comCitiesInter:C}=yi(i,r);return(A,_)=>(u(),Se(t(At),{visible:t(b),"onUpdate:visible":_[3]||(_[3]=P=>rt(b)?b.value=P:null),trigger:"click",placement:A.position,"popper-class":"airportClass","show-arrow":!1,onHide:t(E)},{reference:x(()=>[f(t(Pt),{modelValue:t(T),"onUpdate:modelValue":_[0]||(_[0]=P=>rt(T)?T.value=P:null),disabled:A.isAshingTransitTerminal,placeholder:A.prefixTitle,clearable:"",class:"middlePosition",onClick:Vt(t(h),["stop"]),onKeydown:oa(t(V),["tab"]),onKeyup:oa(t(h),["tab"]),onBlur:t(D)},{prefix:x(()=>[A.type!==""?(u(),$(he,{key:0},[A.type==="takeOff"?(u(),$("em",Sr)):ge("",!0),A.type==="arrive"?(u(),$("em",Tr)):ge("",!0)],64)):yt(A.$slots,"default",{key:1},void 0,!0)]),_:3},8,["modelValue","disabled","placeholder","onClick","onKeydown","onKeyup","onBlur"])]),default:x(()=>[t(c)||t(n)?(u(),$("div",kr,[t(c)?(u(),Se(t(uo),{key:0,type:"card",class:"diTabs"},{default:x(()=>[f(t(na),{label:A.$t("app.fastQuery.headerQuery.domestic")},{default:x(()=>[f(Ya,{modelValue:t(I),"onUpdate:modelValue":_[1]||(_[1]=P=>rt(I)?I.value=P:null),"res-data":t(l),"hot-cities":t(G),"com-cities":t(m),difference:!0,locale:t(p),onSendPortValue:t(S)},null,8,["modelValue","res-data","hot-cities","com-cities","locale","onSendPortValue"])]),_:1},8,["label"]),f(t(na),{label:A.$t("app.fastQuery.headerQuery.International")},{default:x(()=>[f(Ya,{modelValue:t(I),"onUpdate:modelValue":_[2]||(_[2]=P=>rt(I)?I.value=P:null),"res-data":t(v),"hot-cities":t(w),"com-cities":t(C),difference:!1,locale:t(p),onSendPortValue:t(S)},null,8,["modelValue","res-data","hot-cities","com-cities","locale","onSendPortValue"])]),_:1},8,["label"])]),_:1})):ge("",!0),t(n)?Ct((u(),Se(bo,{key:1,type:A.isAgentCity,"search-data":t(d),input:t(y),locale:t(p),onSendSearchValue:t(k)},null,8,["type","search-data","input","locale","onSendSearchValue"])),[[t(po),t(V)]]):ge("",!0)])):ge("",!0)]),_:3},8,["visible","placement","onHide"]))}});const Yt=et(Fr,[["__scopeId","data-v-4b22c934"]]),Lr={class:"transit-terminal-airport-box custom-focus-tip-input"},Nr=o("div",{class:"hidden bkc-el-form-item__error"},"A/B/..",-1),wr={class:"airport-box"},Ir=Ye({__name:"TransitTerminalAirport",props:{modelValue:{default:""},isAgentCity:{}},emits:["update:modelValue"],setup(e,{emit:a}){const i=e,r=a,c=B(Zt()),n=B(!1),l=B(),v=B(!1),d=B([]),y=B(""),p=Ee({get:()=>i.modelValue.toUpperCase(),set:D=>{n.value=!!D,T(D)}}),h=()=>{T(p.value)},S=D=>{r("update:modelValue",D),v.value=!D},k=D=>{var Y,ne,oe;const I=(Y=l.value.input)!=null&&Y.selectionStart?l.value.input.selectionStart-1:-1,m=D.split(""),C=D.slice(0,I+1),A=D.slice(I+1,D.length);let _=-1,P=-1;if((ne=m[I])!=null&&ne.includes("/")&&(_=I+1),(oe=m[I+1])!=null&&oe.includes("/")&&(P=I+1),_===-1){const Z=C.lastIndexOf("/");Z>-1&&(_=Z+1)}if(P===-1){const Z=A.indexOf("/");Z>-1&&(P=Z+C.length)}const U=_===-1?0:_,K=P===-1?D.length:P;return{start:U,end:K}},T=D=>{if(S(D),!D)return;const I=D==null?void 0:D.toUpperCase(),{start:m,end:C}=k(D);y.value=I.slice(m,C),n.value=!!y.value},V=D=>{d.value=D.map(I=>{const m={...I,type:I.firstLevel==="国内",airportEnName:I.airportEnName||I.airportCode,airportFullName:"",airPortFormatName:"",cityName:"",cityEnName:I.cityEnName||I.cityCode,searchName:[...I.nameArray,...I.namePYArray].toString(),searchCity:[...I.cityArray,...I.cityPYArray].toString()};return c.value==="en"?(m.cityName=m.cityEnName,m.airportFullName=m.airportEnName===m.airportCode?m.airportCode:`${m.airportEnName}${m.airportCode}`,m.airPortFormatName=m.airportEnName):(m.cityName=m.secondLevel,m.airportFullName=`${m.airportCnName}${m.airportCode}`,m.airPortFormatName=m.airportCnName),m}).sort((I,m)=>+(I.firstLevel==="国内")-+(m.firstLevel==="国内"))},b=async()=>{if(d.value.length)return;const D=await pt("searchLocalData");if(D&&(D!=null&&D.localData)){const I=JSON.parse(D==null?void 0:D.localData);V(I)}},G=()=>{n.value=!1},w=D=>{const{start:I,end:m}=k(p.value),C=p.value.slice(0,I),A=p.value.slice(m);S(`${C}${D}${A}`),G()},E=async D=>{[37,38,39,40].includes(D.keyCode)&&(await Tt(),T(p.value))};return bt(()=>{b()}),(D,I)=>{const m=Pt,C=At;return u(),$("div",Lr,[f(C,{visible:n.value,persistent:!1,trigger:"click","popper-class":"transit-terminal-airport-popover","show-arrow":!1,width:"248"},{reference:x(()=>[o("div",{class:ye(["input-focus-tip",v.value?"input-focus":""])},[f(m,{ref_key:"inputRef",ref:l,modelValue:p.value,"onUpdate:modelValue":I[0]||(I[0]=A=>p.value=A),placeholder:D.$t("app.avSearch.transitTerminal"),onKeyup:E,onBlur:I[1]||(I[1]=A=>v.value=!1),onClick:Vt(h,["stop"])},null,8,["modelValue","placeholder"]),Nr],2)]),default:x(()=>[Ct((u(),$("div",wr,[f(bo,{locale:c.value,type:D.isAgentCity,"search-data":d.value,input:y.value,onSendSearchValue:w},null,8,["locale","type","search-data","input"])])),[[t(po),G]])]),_:1},8,["visible"])])}}});const ma=[{code:"Y8",icon:"icon-y8-c",logo:"#icon-y8-c",name:s.global.t("app.airlineList.JinpengAirlines")},{code:"TW",icon:"icon-tw-c",logo:"#icon-tw-c",name:s.global.t("app.airlineList.DulwichAir")},{code:"7C",icon:"icon-7c-c",logo:"#icon-7c-c",name:s.global.t("app.airlineList.JejuAir")},{code:"EK",icon:"icon-ek-c",logo:"#icon-ek-c",name:s.global.t("app.airlineList.Emirates")},{code:"EN",icon:"icon-en-c",logo:"#icon-en-c",name:s.global.t("app.airlineList.AirDolomites")},{code:"ET",icon:"icon-et-c",logo:"#icon-et-c",name:s.global.t("app.airlineList.EthiopianAirlines")},{code:"EU",icon:"icon-eu-c",logo:"#icon-eu-c",name:s.global.t("app.airlineList.ChengduAirlines")},{code:"EY",icon:"icon-ey-c",logo:"#icon-ey-c",name:s.global.t("app.airlineList.EtihadAirways")},{code:"F7",icon:"icon-f7-c",logo:"#icon-f7-c",name:s.global.t("app.airlineList.AirFrye")},{code:"F9",icon:"icon-f9-c",logo:"#icon-f9-c",name:s.global.t("app.airlineList.FrontierAirlines")},{code:"FB",icon:"icon-fb-c",logo:"#icon-fb-c",name:s.global.t("app.airlineList.BulgarianAirlines")},{code:"FE",icon:"icon-fe-c",logo:"#icon-fe-c",name:s.global.t("app.airlineList.FarEastAirlines")},{code:"FI",icon:"icon-fi-c",logo:"#icon-fi-c",name:s.global.t("app.airlineList.Icelandair")},{code:"FJ",icon:"icon-fj-c",logo:"#icon-fj-c",name:s.global.t("app.airlineList.PacificAirlines")},{code:"FM",icon:"icon-fm-c",logo:"#icon-fm-c",name:s.global.t("app.airlineList.ShanghaiAirlines")},{code:"FQ",icon:"icon-fq-c",logo:"#icon-fq-c",name:s.global.t("app.airlineList.BrindabellaAir")},{code:"FV",icon:"icon-fv-c",logo:"#icon-fv-c",name:s.global.t("app.airlineList.RussianNationalAviation")},{code:"G3",icon:"icon-g3-c",logo:"#icon-g3-c",name:s.global.t("app.airlineList.GowerTransportAir")},{code:"G5",icon:"icon-g5-c",logo:"#icon-g5-c",name:s.global.t("app.airlineList.HuaAirlines")},{code:"GA",icon:"icon-ga-c",logo:"#icon-ga-c",name:s.global.t("app.airlineList.GarudaIndonesia")},{code:"GE",icon:"icon-ge-c",logo:"#icon-ge-c",name:s.global.t("app.airlineList.TransasiaAirways")},{code:"GF",icon:"icon-gf-c",logo:"#icon-gf-c",name:s.global.t("app.airlineList.GulfAir")},{code:"GJ",icon:"icon-gj-c",logo:"#icon-gj-c",name:s.global.t("app.airlineList.YoloAir")},{code:"GR",icon:"icon-gr-c",logo:"#icon-gr-c",name:s.global.t("app.airlineList.AirOrigny")},{code:"GS",icon:"icon-gs-c",logo:"#icon-gs-c",name:s.global.t("app.airlineList.TianjinAirlines")},{code:"HA",icon:"icon-ha-c",logo:"#icon-ha-c",name:s.global.t("app.airlineList.HawaiianAirlines")},{code:"HF",icon:"icon-hf-c",logo:"#icon-hf-c",name:s.global.t("app.airlineList.LloydAir")},{code:"HM",icon:"icon-hm-c",logo:"#icon-hm-c",name:s.global.t("app.airlineList.AirSeychelles")},{code:"HO",icon:"icon-ho-c",logo:"#icon-ho-c",name:s.global.t("app.airlineList.JuneyaoAirlines")},{code:"HU",icon:"icon-hu-c",logo:"#icon-hu-c",name:s.global.t("app.airlineList.HainanAirlines")},{code:"HX",icon:"icon-hx-c",logo:"#icon-hx-c",name:s.global.t("app.airlineList.HongKongAirlines")},{code:"HY",icon:"icon-hy-c",logo:"#icon-hy-c",name:s.global.t("app.airlineList.UzbekistanAirways")},{code:"HZ",icon:"icon-hz-c",logo:"#icon-hz-c",name:s.global.t("app.airlineList.SakhalinAir")},{code:"IB",icon:"icon-ib-c",logo:"#icon-ib-c",name:s.global.t("app.airlineList.Spainair")},{code:"IC",icon:"icon-ic-c",logo:"#icon-ic-c",name:s.global.t("app.airlineList.AirIndia")},{code:"IE",icon:"icon-ie-c",logo:"#icon-ie-c",name:s.global.t("app.airlineList.SolomonAirlines")},{code:"IG",icon:"icon-ig-c",logo:"#icon-ig-c",name:s.global.t("app.airlineList.AirMeridian")},{code:"IR",icon:"icon-ir-c",logo:"#icon-ir-c",name:s.global.t("app.airlineList.IranAir")},{code:"IT",icon:"icon-it-c",logo:"#icon-it-c",name:s.global.t("app.airlineList.KingfisherAirlines")},{code:"IY",icon:"icon-iy-c",logo:"#icon-iy-c",name:s.global.t("app.airlineList.YemeniaAirways")},{code:"IZ",icon:"icon-iz-c",logo:"#icon-iz-c",name:s.global.t("app.airlineList.Ehang")},{code:"J2",icon:"icon-j2-c",logo:"#icon-j2-c",name:s.global.t("app.airlineList.AzerbaijanAirlines")},{code:"JD",icon:"icon-jd-c",logo:"#icon-jd-c",name:s.global.t("app.airlineList.CapitalAirlines")},{code:"JH",icon:"icon-jh-c",logo:"#icon-jh-c",name:s.global.t("app.airlineList.NortheastAirlines")},{code:"JJ",icon:"icon-jj-c",logo:"#icon-jj-c",name:s.global.t("app.airlineList.SouthAmericanAirlines")},{code:"JK",icon:"icon-jk-c",logo:"#icon-jk-c",name:s.global.t("app.airlineList.Spanair")},{code:"JL",icon:"icon-jl-c",logo:"#icon-jl-c",name:s.global.t("app.airlineList.JapanAirlines")},{code:"JP",icon:"icon-jp-c",logo:"#icon-jp-c",name:s.global.t("app.airlineList.AdriaticAir")},{code:"JQ",icon:"icon-jq-c",logo:"#icon-jq-c",name:s.global.t("app.airlineList.JetstarAirways")},{code:"JR",icon:"icon-jr-c",logo:"#icon-jr-c",name:s.global.t("app.airlineList.HappyAir")},{code:"JS",icon:"icon-js-c",logo:"#icon-js-c",name:s.global.t("app.airlineList.Headingforthesea")},{code:"JU",icon:"icon-ju-c",logo:"#icon-ju-c",name:s.global.t("app.airlineList.YugoslavAirlines")},{code:"K6",icon:"icon-k6-c",logo:"#icon-k6-c",name:s.global.t("app.airlineList.AngkorAir")},{code:"KA",icon:"icon-ka-c",logo:"#icon-ka-c",name:s.global.t("app.airlineList.Dragonair")},{code:"KC",icon:"icon-kc-c",logo:"#icon-kc-c",name:s.global.t("app.airlineList.KazakhAirlines")},{code:"KE",icon:"icon-ke-c",logo:"#icon-ke-c",name:s.global.t("app.airlineList.KoreanAir")},{code:"KF",icon:"icon-kf-c",logo:"#icon-kf-c",name:s.global.t("app.airlineList.PortniaBlueFirstAir")},{code:"KK",icon:"icon-kk-c",logo:"#icon-kk-c",name:s.global.t("app.airlineList.AtlasAir")},{code:"KL",icon:"icon-kl-c",logo:"#icon-kl-c",name:s.global.t("app.airlineList.KLM")},{code:"KM",icon:"icon-km-c",logo:"#icon-km-c",name:s.global.t("app.airlineList.AirMalta")},{code:"KN",icon:"icon-kn-c",logo:"#icon-kn-c",name:s.global.t("app.airlineList.ChinaUnitedAirlines")},{code:"KP",icon:"icon-kp-c",logo:"#icon-kp-c",name:s.global.t("app.airlineList.CapeAir")},{code:"KQ",icon:"icon-kq-c",logo:"#icon-kq-c",name:s.global.t("app.airlineList.KenyaAirways")},{code:"KS",icon:"icon-ks-c",logo:"#icon-ks-c",name:s.global.t("app.airlineList.PencilAviation")},{code:"KU",icon:"icon-ku-c",logo:"#icon-ku-c",name:s.global.t("app.airlineList.KuwaitAirways")},{code:"KY",icon:"icon-ky-c",logo:"#icon-ky-c",name:s.global.t("app.airlineList.KunmingAirlines")},{code:"LA",icon:"icon-la-c",logo:"#icon-la-c",name:s.global.t("app.airlineList.AirChile")},{code:"LG",icon:"icon-lg-c",logo:"#icon-lg-c",name:s.global.t("app.airlineList.LuxembourgAir")},{code:"LH",icon:"icon-lh-c",logo:"#icon-lh-c",name:s.global.t("app.airlineList.Lufthansa")},{code:"LI",icon:"icon-li-c",logo:"#icon-li-c",name:s.global.t("app.airlineList.LeewardAviation")},{code:"LN",icon:"icon-ln-c",logo:"#icon-ln-c",name:s.global.t("app.airlineList.LibyanAirlines")},{code:"LO",icon:"icon-lo-c",logo:"#icon-lo-c",name:s.global.t("app.airlineList.WaveNavigation")},{code:"LP",icon:"icon-lp-c",logo:"#icon-lp-c",name:s.global.t("app.airlineList.PeruvianAirlines")},{code:"LR",icon:"icon-lr-c",logo:"#icon-lr-c",name:s.global.t("app.airlineList.LascaAir")},{code:"9W",icon:"icon-9w-c",logo:"#icon-9w-c",name:s.global.t("app.airlineList.JetAirways")},{code:"A3",icon:"icon-a3-c",logo:"#icon-a3-c",name:s.global.t("app.airlineList.AegeanAir")},{code:"A5",icon:"icon-a5-c",logo:"#icon-a5-c",name:s.global.t("app.airlineList.LinelAir")},{code:"A9",icon:"icon-a9-c",logo:"#icon-a9-c",name:s.global.t("app.airlineList.AirGeorgia")},{code:"AA",icon:"icon-aa-c",logo:"#icon-aa-c",name:s.global.t("app.airlineList.AmericanAirlines")},{code:"AB",icon:"icon-ab-c",logo:"#icon-ab-c",name:s.global.t("app.airlineList.AirBerlin")},{code:"AC",icon:"icon-ac-c",logo:"#icon-ac-c",name:s.global.t("app.airlineList.AirCanada")},{code:"AD",icon:"icon-ad-c",logo:"#icon-ad-c",name:s.global.t("app.airlineList.IndonesianParadiseAirlines")},{code:"AE",icon:"icon-ae-c",logo:"#icon-ae-c",name:s.global.t("app.airlineList.CefcAirlines")},{code:"AF",icon:"icon-af-c",logo:"#icon-af-c",name:s.global.t("app.airlineList.AirFrance")},{code:"AH",icon:"icon-ah-c",logo:"#icon-ah-c",name:s.global.t("app.airlineList.AirAlgerie")},{code:"AI",icon:"icon-ai-c",logo:"#icon-ai-c",name:s.global.t("app.airlineList.IndiaAir")},{code:"AM",icon:"icon-am-c",logo:"#icon-am-c",name:s.global.t("app.airlineList.AirMexico")},{code:"AP",icon:"icon-ap-c",logo:"#icon-ap-c",name:s.global.t("app.airlineList.AirOne")},{code:"AR",icon:"icon-ar-c",logo:"#icon-ar-c",name:s.global.t("app.airlineList.AerolineasArgentinas")},{code:"AS",icon:"icon-as-c",logo:"#icon-as-c",name:s.global.t("app.airlineList.AlaskaAirlines")},{code:"AT",icon:"icon-at-c",logo:"#icon-at-c",name:s.global.t("app.airlineList.AirMaroc")},{code:"AV",icon:"icon-av-c",logo:"#icon-av-c",name:s.global.t("app.airlineList.ColumbiaAirlines")},{code:"AW",icon:"icon-aw-c",logo:"#icon-aw-c",name:s.global.t("app.airlineList.DecantaraAir")},{code:"AY",icon:"icon-ay-c",logo:"#icon-ay-c",name:s.global.t("app.airlineList.Finnair")},{code:"AZ",icon:"icon-az-c",logo:"#icon-az-c",name:s.global.t("app.airlineList.Alitalia")},{code:"B2",icon:"icon-b2-c",logo:"#icon-b2-c",name:s.global.t("app.airlineList.BelarusianAirlines")},{code:"B6",icon:"icon-b6-c",logo:"#icon-b6-c",name:s.global.t("app.airlineList.JetblueAirways")},{code:"B7",icon:"icon-b7-c",logo:"#icon-b7-c",name:s.global.t("app.airlineList.LirongAir")},{code:"BA",icon:"icon-ba-c",logo:"#icon-ba-c",name:s.global.t("app.airlineList.BritishAirways")},{code:"BD",icon:"icon-bd-c",logo:"#icon-bd-c",name:s.global.t("app.airlineList.MidlandAirlines")},{code:"BE",icon:"icon-be-c",logo:"#icon-be-c",name:s.global.t("app.airlineList.FlybyAir")},{code:"BG",icon:"icon-bg-c",logo:"#icon-bg-c",name:s.global.t("app.airlineList.BanglaAirlines")},{code:"BI",icon:"icon-bi-c",logo:"#icon-bi-c",name:s.global.t("app.airlineList.BruneiAirlines")},{code:"BK",icon:"icon-bk-c",logo:"#icon-bk-c",name:s.global.t("app.airlineList.OkAirways")},{code:"BL",icon:"icon-bl-c",logo:"#icon-bl-c",name:s.global.t("app.airlineList.PacificAirlines")},{code:"BM",icon:"icon-bm-c",logo:"#icon-bm-c",name:s.global.t("app.airlineList.PrayuthAirIndonesia")},{code:"BP",icon:"icon-bp-c",logo:"#icon-bp-c",name:s.global.t("app.airlineList.AirBotswana")},{code:"BR",icon:"icon-br-c",logo:"#icon-br-c",name:s.global.t("app.airlineList.EvaAir")},{code:"BT",icon:"icon-bt-c",logo:"#icon-bt-c",name:s.global.t("app.airlineList.AirBaltic")},{code:"BU",icon:"icon-bu-c",logo:"#icon-bu-c",name:s.global.t("app.airlineList.BrassensAir")},{code:"BV",icon:"icon-bv-c",logo:"#icon-bv-c",name:s.global.t("app.airlineList.BluePanoramaAviation")},{code:"BW",icon:"icon-bw-c",logo:"#icon-bw-c",name:s.global.t("app.airlineList.Tedosi")},{code:"BX",icon:"icon-bx-c",logo:"#icon-bx-c",name:s.global.t("app.airlineList.OceanfrontAir")},{code:"CA",icon:"icon-ca-c",logo:"#icon-ca-c",name:s.global.t("app.airlineList.AirChina")},{code:"CI",icon:"icon-ci-c",logo:"#icon-ci-c",name:s.global.t("app.airlineList.ChinaAirlines")},{code:"CM",icon:"icon-cm-c",logo:"#icon-cm-c",name:s.global.t("app.airlineList.PanamaAirlift")},{code:"CN",icon:"icon-cn-c",logo:"#icon-cn-c",name:s.global.t("app.airlineList.GrandChinaAirlines")},{code:"CU",icon:"icon-cu-c",logo:"#icon-cu-c",name:s.global.t("app.airlineList.AirCubana")},{code:"CX",icon:"icon-cx-c",logo:"#icon-cx-c",name:s.global.t("app.airlineList.CathayPacific")},{code:"CY",icon:"icon-cy-c",logo:"#icon-cy-c",name:s.global.t("app.airlineList.CyprusAirways")},{code:"CZ",icon:"icon-cz-c",logo:"#icon-cz-c",name:s.global.t("app.airlineList.ChinaSouthernAirlines")},{code:"DC",icon:"icon-dc-c",logo:"#icon-dc-c",name:s.global.t("app.airlineList.GoldenAir")},{code:"DE",icon:"icon-de-c",logo:"#icon-de-c",name:s.global.t("app.airlineList.CondorAir")},{code:"DJ",icon:"icon-dj-c",logo:"#icon-dj-c",name:s.global.t("app.airlineList.VirginAirlines")},{code:"DL",icon:"icon-dl-c",logo:"#icon-dl-c",name:s.global.t("app.airlineList.DeltaAirLines")},{code:"DN",icon:"icon-dn-c",logo:"#icon-dn-c",name:s.global.t("app.airlineList.AirDeccan")},{code:"DR",icon:"icon-dr-c",logo:"#icon-dr-c",name:s.global.t("app.airlineList.LinkAir")},{code:"DT",icon:"icon-dt-c",logo:"#icon-dt-c",name:s.global.t("app.airlineList.AirAngola")},{code:"DY",icon:"icon-dy-c",logo:"#icon-dy-c",name:s.global.t("app.airlineList.NorwegianAir")},{code:"DZ",icon:"icon-dz-c",logo:"#icon-dz-c",name:s.global.t("app.airlineList.DonghaiAirlines")},{code:"E3",icon:"icon-e3-c",logo:"#icon-e3-c",name:s.global.t("app.airlineList.DomodedovoAir")},{code:"EF",icon:"icon-ef-c",logo:"#icon-ef-c",name:s.global.t("app.airlineList.FarEastAir")},{code:"EI",icon:"icon-ei-c",logo:"#icon-ei-c",name:s.global.t("app.airlineList.AerLingus")},{code:"2P",icon:"icon-2p-c",logo:"#icon-2p-c",name:s.global.t("app.airlineList.AirPhilippine")},{code:"3K",icon:"icon-3k-c",logo:"#icon-3k-c",name:s.global.t("app.airlineList.JetstarAirways")},{code:"4D",icon:"icon-4d-c",logo:"#icon-4d-c",name:s.global.t("app.airlineList.SinaiAir")},{code:"4U",icon:"icon-4u-c",logo:"#icon-4u-c",name:s.global.t("app.airlineList.Germanwings")},{code:"5L",icon:"icon-5l-c",logo:"#icon-5l-c",name:s.global.t("app.airlineList.AlsoAir")},{code:"7B",icon:"icon-7b-c",logo:"#icon-7b-c",name:s.global.t("app.airlineList.KrasnoyarskAir")},{code:"8C",icon:"icon-8c-c",logo:"#icon-8c-c",name:s.global.t("app.airlineList.EastStarAirlines")},{code:"8L",icon:"icon-8l-c",logo:"#icon-8l-c",name:s.global.t("app.airlineList.LuckyAir")},{code:"8M",icon:"icon-8m-c",logo:"#icon-8m-c",name:s.global.t("app.airlineList.MyanmarAirlines")},{code:"8U",icon:"icon-8u-c",logo:"#icon-8u-c",name:s.global.t("app.airlineList.AfrikiaAir")},{code:"9B",icon:"icon-9b-c",logo:"#icon-9b-c",name:s.global.t("app.airlineList.NorwegianStateRailways")},{code:"9K",icon:"icon-9k-c",logo:"#icon-9k-c",name:s.global.t("app.airlineList.CapeCodAir")},{code:"9U",icon:"icon-9u-c",logo:"#icon-9u-c",name:s.global.t("app.airlineList.AirMoldova")},{code:"LX",icon:"icon-lx-c",logo:"#icon-lx-c",name:s.global.t("app.airlineList.SwissAir")},{code:"LY",icon:"icon-ly-c",logo:"#icon-ly-c",name:s.global.t("app.airlineList.Ehang")},{code:"MA",icon:"icon-ma-c",logo:"#icon-ma-c",name:s.global.t("app.airlineList.Malev")},{code:"MD",icon:"icon-md-c",logo:"#icon-md-c",name:s.global.t("app.airlineList.AirMadagascar")},{code:"ME",icon:"icon-me-c",logo:"#icon-me-c",name:s.global.t("app.airlineList.MiddleEastAirlines")},{code:"MF",icon:"icon-mf-c",logo:"#icon-mf-c",name:s.global.t("app.airlineList.XiamenAirlines")},{code:"MH",icon:"icon-mh-c",logo:"#icon-mh-c",name:s.global.t("app.airlineList.MalaysiaAirlines")},{code:"MI",icon:"icon-mi-c",logo:"#icon-mi-c",name:s.global.t("app.airlineList.Silkair")},{code:"UQ",icon:"icon-uq-c",logo:"#icon-uq-c",name:s.global.t("app.airlineList.UrumqiAir")},{code:"MO",icon:"icon-mo-c",logo:"#icon-mo-c",name:s.global.t("app.airlineList.StaticAir")},{code:"MP",icon:"icon-mp-c",logo:"#icon-mp-c",name:s.global.t("app.airlineList.MartinAir")},{code:"MJ",icon:"icon-mj-c",logo:"#icon-mj-c",name:s.global.t("app.airlineList.ARGAviation")},{code:"MK",icon:"icon-mk-c",logo:"#icon-mk-c",name:s.global.t("app.airlineList.AirMauritius")},{code:"MR",icon:"icon-mr-c",logo:"#icon-mr-c",name:s.global.t("app.airlineList.AirMauritania")},{code:"MS",icon:"icon-ms-c",logo:"#icon-ms-c",name:s.global.t("app.airlineList.Egyptair")},{code:"MU",icon:"icon-mu-c",logo:"#icon-mu-c",name:s.global.t("app.airlineList.ChinaEasternAirlines")},{code:"MX",icon:"icon-mx-c",logo:"#icon-mx-c",name:s.global.t("app.airlineList.Aeromexico")},{code:"NE",icon:"icon-ne-c",logo:"#icon-ne-c",name:s.global.t("app.airlineList.SkyEurope")},{code:"NF",icon:"icon-nf-c",logo:"#icon-nf-c",name:s.global.t("app.airlineList.AirVanuatu")},{code:"NH",icon:"icon-nh-c",logo:"#icon-nh-c",name:s.global.t("app.airlineList.AllNipponAirways")},{code:"NS",icon:"icon-ns-c",logo:"#icon-ns-c",name:s.global.t("app.airlineList.HebeiAirlines")},{code:"NX",icon:"icon-nx-c",logo:"#icon-nx-c",name:s.global.t("app.airlineList.AirMacau")},{code:"NZ",icon:"icon-nz-c",logo:"#icon-nz-c",name:s.global.t("app.airlineList.AirNewZealand")},{code:"OA",icon:"icon-oa-c",logo:"#icon-oa-c",name:s.global.t("app.airlineList.OlympicAir")},{code:"OD",icon:"icon-od-c",logo:"#icon-od-c",name:s.global.t("app.airlineList.AirNatalco")},{code:"OK",icon:"icon-ok-c",logo:"#icon-ok-c",name:s.global.t("app.airlineList.CzechAirlines")},{code:"OL",icon:"icon-ol-c",logo:"#icon-ol-c",name:s.global.t("app.airlineList.OLTAviation")},{code:"OM",icon:"icon-om-c",logo:"#icon-om-c",name:s.global.t("app.airlineList.MongolianAirlines")},{code:"OQ",icon:"icon-oq-c",logo:"#icon-oq-c",name:s.global.t("app.airlineList.ChongqingAirlines")},{code:"OS",icon:"icon-os-c",logo:"#icon-os-c",name:s.global.t("app.airlineList.OlympicAirlines")},{code:"OT",icon:"icon-ot-c",logo:"#icon-ot-c",name:s.global.t("app.airlineList.PelicanAir")},{code:"OU",icon:"icon-ou-c",logo:"#icon-ou-c",name:s.global.t("app.airlineList.CroatianAirlines")},{code:"OV",icon:"icon-ov-c",logo:"#icon-ov-c",name:s.global.t("app.airlineList.EstonianAir")},{code:"OZ",icon:"icon-oz-c",logo:"#icon-oz-c",name:s.global.t("app.airlineList.AsianaAirlines")},{code:"PG",icon:"icon-pg-c",logo:"#icon-pg-c",name:s.global.t("app.airlineList.BangkokAirways")},{code:"PK",icon:"icon-pk-c",logo:"#icon-pk-c",name:s.global.t("app.airlineList.PakistanAirlines")},{code:"PN",icon:"icon-pn-c",logo:"#icon-pn-c",name:s.global.t("app.airlineList.WesternAir")},{code:"PR",icon:"icon-pr-c",logo:"#icon-pr-c",name:s.global.t("app.airlineList.PhilippineAirlines")},{code:"PS",icon:"icon-ps-c",logo:"#icon-ps-c",name:s.global.t("app.airlineList.UkrainianAirlines")},{code:"PU",icon:"icon-pu-c",logo:"#icon-pu-c",name:s.global.t("app.airlineList.AirUruguay")},{code:"PW",icon:"icon-pw-c",logo:"#icon-pw-c",name:s.global.t("app.airlineList.PrecisionAviation")},{code:"PX",icon:"icon-px-c",logo:"#icon-px-c",name:s.global.t("app.airlineList.AirNewGuinea")},{code:"PY",icon:"icon-py-c",logo:"#icon-py-c",name:s.global.t("app.airlineList.SurinameAir")},{code:"PZ",icon:"icon-pz-c",logo:"#icon-pz-c",name:s.global.t("app.airlineList.AirParaguay")},{code:"QF",icon:"icon-qf-c",logo:"#icon-qf-c",name:s.global.t("app.airlineList.QantasAirways")},{code:"QR",icon:"icon-qr-c",logo:"#icon-qr-c",name:s.global.t("app.airlineList.QatarAirlines")},{code:"QV",icon:"icon-qv-c",logo:"#icon-qv-c",name:s.global.t("app.airlineList.LaoAir")},{code:"RA",icon:"icon-ra-c",logo:"#icon-ra-c",name:s.global.t("app.airlineList.NepalAirlines")},{code:"RC",icon:"icon-rc-c",logo:"#icon-rc-c",name:s.global.t("app.airlineList.FaroeIslandsAir")},{code:"RG",icon:"icon-rg-c",logo:"#icon-rg-c",name:s.global.t("app.airlineList.BrazilianAirlines")},{code:"RJ",icon:"icon-rj-c",logo:"#icon-rj-c",name:s.global.t("app.airlineList.JordanAviation")},{code:"RO",icon:"icon-ro-c",logo:"#icon-ro-c",name:s.global.t("app.airlineList.RomanianAirlines")},{code:"RQ",icon:"icon-rq-c",logo:"#icon-rq-c",name:s.global.t("app.airlineList.CAMAir")},{code:"S2",icon:"icon-s2-c",logo:"#icon-s2-c",name:s.global.t("app.airlineList.SaharaAir")},{code:"S7",icon:"icon-s7-c",logo:"#icon-s7-c",name:s.global.t("app.airlineList.SiberianAirlines")},{code:"SA",icon:"icon-sa-c",logo:"#icon-sa-c",name:s.global.t("app.airlineList.SouthAfricanAirways")},{code:"SB",icon:"icon-sb-c",logo:"#icon-sb-c",name:s.global.t("app.airlineList.AirCaledonia")},{code:"SC",icon:"icon-sc-c",logo:"#icon-sc-c",name:s.global.t("app.airlineList.ShandongAirlines")},{code:"SK",icon:"icon-sk-c",logo:"#icon-sk-c",name:s.global.t("app.airlineList.ScandinavianAirlines")},{code:"SN",icon:"icon-sn-c",logo:"#icon-sn-c",name:s.global.t("app.airlineList.BrusselsAirlines")},{code:"SQ",icon:"icon-sq-c",logo:"#icon-sq-c",name:s.global.t("app.airlineList.SingaporeAirlines")},{code:"SU",icon:"icon-su-c",logo:"#icon-su-c",name:s.global.t("app.airlineList.Aeroflot")},{code:"SV",icon:"icon-sv-c",logo:"#icon-sv-c",name:s.global.t("app.airlineList.SaudiAirlines")},{code:"SW",icon:"icon-sw-c",logo:"#icon-sw-c",name:s.global.t("app.airlineList.AirNamibia")},{code:"T5",icon:"icon-t5-c",logo:"#icon-t5-c",name:s.global.t("app.airlineList.TurkmenistanAir")},{code:"TA",icon:"icon-ta-c",logo:"#icon-ta-c",name:s.global.t("app.airlineList.TakaInternationalAirlines")},{code:"TF",icon:"icon-tf-c",logo:"#icon-tf-c",name:s.global.t("app.airlineList.MalmoAir")},{code:"TG",icon:"icon-tg-c",logo:"#icon-tg-c",name:s.global.t("app.airlineList.ThaiAirways")},{code:"TK",icon:"icon-tk-c",logo:"#icon-tk-c",name:s.global.t("app.airlineList.TurkishAirlines")},{code:"TM",icon:"icon-tm-c",logo:"#icon-tm-c",name:s.global.t("app.airlineList.RamAir")},{code:"TN",icon:"icon-tn-c",logo:"#icon-tn-c",name:s.global.t("app.airlineList.AirTahiti")},{code:"TO",icon:"icon-to-c",logo:"#icon-to-c",name:s.global.t("app.airlineList.PresidentialAviation")},{code:"TP",icon:"icon-tp-c",logo:"#icon-tp-c",name:s.global.t("app.airlineList.AirPortugal")},{code:"TU",icon:"icon-tu-c",logo:"#icon-tu-c",name:s.global.t("app.airlineList.TunisAir")},{code:"TV",icon:"icon-tv-c",logo:"#icon-tv-c",name:s.global.t("app.airlineList.TibetAirlines")},{code:"U6",icon:"icon-u6-c",logo:"#icon-u6-c",name:s.global.t("app.airlineList.UralAirlines")},{code:"UA",icon:"icon-ua-c",logo:"#icon-ua-c",name:s.global.t("app.airlineList.UnitedAirlines")},{code:"UL",icon:"icon-ul-c",logo:"#icon-ul-c",name:s.global.t("app.airlineList.SiHang")},{code:"UM",icon:"icon-um-c",logo:"#icon-um-c",name:s.global.t("app.airlineList.AirZimbabwe")},{code:"UN",icon:"icon-un-c",logo:"#icon-un-c",name:s.global.t("app.airlineList.AnnulusAviation")},{code:"UO",icon:"icon-uo-c",logo:"#icon-uo-c",name:s.global.t("app.airlineList.HongKongExpress")},{code:"UP",icon:"icon-up-c",logo:"#icon-up-c",name:s.global.t("app.airlineList.BahamasAirlines")},{code:"US",icon:"icon-us-c",logo:"#icon-us-c",name:s.global.t("app.airlineList.AmericanAir")},{code:"UU",icon:"icon-uu-c",logo:"#icon-uu-c",name:s.global.t("app.airlineList.AuslarAir")},{code:"UX",icon:"icon-ux-c",logo:"#icon-ux-c",name:s.global.t("app.airlineList.SpanishAirEurope")},{code:"V2",icon:"icon-v2-c",logo:"#icon-v2-c",name:s.global.t("app.airlineList.KaratAir")},{code:"VA",icon:"icon-va-c",logo:"#icon-va-c",name:s.global.t("app.airlineList.VirginAustralia")},{code:"VD",icon:"icon-vd-c",logo:"#icon-vd-c",name:s.global.t("app.airlineList.HenanAirlines")},{code:"VF",icon:"icon-vf-c",logo:"#icon-vf-c",name:s.global.t("app.airlineList.HuitravelAir")},{code:"VN",icon:"icon-vn-c",logo:"#icon-vn-c",name:s.global.t("app.airlineList.VietnamAirlines")},{code:"VR",icon:"icon-vr-c",logo:"#icon-vr-c",name:s.global.t("app.airlineList.CapeVerdeAirlines")},{code:"VS",icon:"icon-vs-c",logo:"#icon-vs-c",name:s.global.t("app.airlineList.VirginAtlantic")},{code:"VV",icon:"icon-vv-c",logo:"#icon-vv-c",name:s.global.t("app.airlineList.UkrainianAirlines")},{code:"VW",icon:"icon-vw-c",logo:"#icon-vw-c",name:s.global.t("app.airlineList.GreatLakesAir")},{code:"VX",icon:"icon-vx-c",logo:"#icon-vx-c",name:s.global.t("app.airlineList.WebbardAir")},{code:"VY",icon:"icon-vy-c",logo:"#icon-vy-c",name:s.global.t("app.airlineList.WellingAir")},{code:"WF",icon:"icon-wf-c",logo:"#icon-wf-c",name:s.global.t("app.airlineList.WidroAir")},{code:"WN",icon:"icon-wn-c",logo:"#icon-wn-c",name:s.global.t("app.airlineList.SouthwestAirlines")},{code:"WS",icon:"icon-ws-c",logo:"#icon-ws-c",name:s.global.t("app.airlineList.WesternJetAviation")},{code:"WY",icon:"icon-wy-c",logo:"#icon-wy-c",name:s.global.t("app.airlineList.OmanAir")},{code:"XF",icon:"icon-xf-c",logo:"#icon-xf-c",name:s.global.t("app.airlineList.AirVladivostok")},{code:"XK",icon:"icon-xk-c",logo:"#icon-xk-c",name:s.global.t("app.airlineList.CorsairAirlines")},{code:"YV",icon:"icon-yv-c",logo:"#icon-yv-c",name:s.global.t("app.airlineList.MesaAir")},{code:"ZH",icon:"icon-zh-c",logo:"#icon-zh-c",name:s.global.t("app.airlineList.ShenzhenAirlines")},{code:"ZI",icon:"icon-zi-c",logo:"#icon-zi-c",name:s.global.t("app.airlineList.AigleAzur")},{code:"ZK",icon:"icon-zk-c",logo:"#icon-zk-c",name:s.global.t("app.airlineList.GreatLakesAirlines")},{code:"ZL",icon:"icon-zl-c",logo:"#icon-zl-c",name:s.global.t("app.airlineList.RegionalExpressAirlines")},{code:"3U",icon:"icon-3u-c",logo:"#icon-3u-c",name:s.global.t("app.airlineList.SichuanAirlines")},{code:"QW",icon:"icon-qw-c",logo:"#icon-qw-c",name:s.global.t("app.airlineList.QingdaoAirlines")},{code:"KR",icon:"icon-kr-c",logo:"#icon-kr-c",name:s.global.t("app.airlineList.CambodiaAirways")},{code:"QD",icon:"icon-qd-c",logo:"#icon-qd-c",name:s.global.t("app.airlineList.JCAirlinesCambodia")},{code:"TR",icon:"icon-tr-c",logo:"#icon-tr-c",name:s.global.t("app.airlineList.Tigerair")},{code:"FU",icon:"icon-fu-c",logo:"#icon-fu-c",name:s.global.t("app.airlineList.FuzhouAirlines")},{code:"XW",icon:"icon-xw-c",logo:"#icon-xw-c",name:s.global.t("app.airlineList.CoolBirdAir")},{code:"GY",icon:"icon-gy-c",logo:"#icon-gy-c",name:s.global.t("app.airlineList.GuizhouAirlines")},{code:"9D",icon:"icon-9d-c",logo:"#icon-9d-c",name:s.global.t("app.airlineList.PermAir")},{code:"H9",icon:"icon-h9-c",logo:"#icon-h9-c",name:s.global.t("app.airlineList.HimalayaAirlines")},{code:"9H",icon:"icon-9h-c",logo:"#icon-9h-c",name:s.global.t("app.airlineList.ChanganAirlines")},{code:"GT",icon:"icon-gt-c",logo:"#icon-gt-c",name:s.global.t("app.airlineList.GuilinAir")},{code:"GX",icon:"icon-gx-c",logo:"#icon-gx-c",name:s.global.t("app.airlineList.BeibuGulfAir")},{code:"LT",icon:"icon-lt-c",logo:"#icon-lt-c",name:s.global.t("app.airlineList.LongjiangAirlines")},{code:"A6",icon:"icon-a6-c",logo:"#icon-a6-c",name:s.global.t("app.airlineList.RedEarthAir")},{code:"RY",icon:"icon-ry-c",logo:"#icon-ry-c",name:s.global.t("app.airlineList.JiangxiAirlines")}],Er={class:"rounded bg-brand-7 flex flex-col border-t mt-[10px]"},Pr={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},Rr={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},Or={class:"font-normal text-xs leading-[20px] items-center text-gray-2"},yf=Ye({__name:"AirRoute",props:{routingSegList:{},needCloseIcon:{type:Boolean}},setup(e){return(a,i)=>(u(),$("div",Er,[a.needCloseIcon?yt(a.$slots,"default",{key:0}):ge("",!0),(u(!0),$(he,null,Ie(a.routingSegList,(r,c)=>(u(),$("div",{key:c,class:"flex flex-col p-[10px] font-normal text-xs leading-[20px] break-all"},[(u(!0),$(he,null,Ie(r.routingRestrictions,(n,l)=>(u(),$("div",{key:l,class:"font-normal text-xs leading-[20px] text-gray-1"},[o("span",Pr,g(`*${n}`),1)]))),128)),(u(!0),$(he,null,Ie(r.routingPaths,(n,l)=>(u(),$("div",{key:l,class:"font-normal text-xs leading-[20px] text-gray-1"},[o("span",Rr,g(`${l+1}*`),1),o("span",Or,g(`${n}`),1)]))),128))]))),128))]))}}),bf=(e,a)=>Ne(`${Je}/apiAvSearch/computeInterAirPrice`,{headers:{gid:a}}).post(e).json(),_f=(e,a)=>Ne(`${Je}/fare/domestic`,{headers:{gid:a}}).post(e).json(),Cf=(e,a)=>Ne(`${Je}/fare/queryInterReprice`,{headers:{gid:a}}).post(e).json(),Af=(e,a)=>Ne(`${Je}/fare/queryDomesticReprice`,{headers:{gid:a}}).post(e).json(),Df=(e,a)=>Ne(`${Je}/apiAvSearch/queryCrsFareOrder`,{headers:{gid:a}}).post(e).json(),$f=(e,a)=>Ne(`${Je}/apiAvSearch/batchInterAirPriceFormat`,{headers:{gid:a}}).post(e).json(),xf=(e,a)=>Ne(`${Je}/fare/route/query`,{headers:{gid:a}}).post(e).json(),Sf=(e,a)=>Ne(`${Je}/apiAvSearch/fareRuleBaggage`,{headers:{gid:a}}).post(e).json(),Tf=(e,a)=>Ne(`${Je}/apiAvSearch/domesticFreeBaggage`,{headers:{gid:a}}).post(e).json(),kf=(e,a)=>Ne(`${Je}/apiAvSearch/historyAndNewPriceCompute`,{headers:{gid:a}}).post(e).json(),Ff=(e,a)=>Ne(`${Je}/apiAvSearch/computeInterAirPriceBytTicket`,{headers:{gid:a}}).post(e).json(),Lf=(e,a)=>Ne(`${Je}/fare/domestic`,{headers:{gid:a}}).post(e).json(),Nf=(e,a)=>Ne(`${Je}/apiAvSearch/fareIntoAndEtdz`,{headers:{gid:a}}).post(e).json(),wf=(e,a)=>Ne(`${Je}/fare/queryRTKTDetail`,{headers:{gid:a}},{originalValue:!0,ignoreError:!0}).post(e).json(),If=(e,a)=>Ne(`${Je}/fare/cancelCreditCardAuth`,{headers:{gid:a}}).post(e).json(),Ef=(e,a)=>Ne(`${Je}/cpay/crsCdsPay`,{headers:{gid:a}}).post(e).json(),Pf=(e,a)=>Ne(`${Je}/bopPay/crsBopPay`,{headers:{gid:a}}).post(e).json(),Rf=(e,a)=>Ne(`${Je}/bopPay/cancelPayment`,{headers:{gid:a}}).post(e).json(),Of=(e,a)=>Ne(`${Je}/fare/queryTicketsDetail`,{headers:{gid:a}},{ignoreError:!0}).post(e).json(),Qr=[{code:"<",offset:"-1"},{code:">",offset:"+1"},{code:"\\",offset:"+2"},{code:"+",offset:"+2"}],qa=(e,a)=>!e||!a||!ya.test(a)?"":`${e}T${a.slice(0,2)}:${a.slice(2)}`,Mr=e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),Vr=(e,a,i,r)=>{if(co.test(e)){const p=se(new Date).format("YYYY").slice(0,2),[h,S,k]=e.match(/(\d{2})([A-Za-z]{3})(\d{2})/).slice(1),T=`${p}${k}`,V=se(`${T}-${Mr(S)}-${h}`,"YYYY-MMM-DD");if(!V.isValid())throw new Error;return V.format("YYYY-MM-DD")}let c=i,n=r;(c??"")===""&&(c=se(new Date).format("YYYY-MM-DD"),n=se(new Date).format("HHmm"));const l=c.substring(0,4);let v,d;const y=e?Sa(e,l):`${l}-${c.substring(5)}`;return n!==""&&a!==""?(v=se(`${l}-${c.substring(5)}:${n}`),d=se(`${y}:${a}`)):(v=se(`${l}-${c.substring(5)}`),d=se(`${y}`)),d.isBefore(v)?d.add(1,"year").format("YYYY-MM-DD"):d.format("YYYY-MM-DD")},Br=e=>({[_e.FLIGHT_STATUS]:{value:"",required:!0},[_e.COMPANY_CODE]:{value:"",required:!0},[_e.FLIGHT_NUMBER]:{value:"",required:e},[_e.CABIN_CODE]:{value:"",required:!0},[_e.DEPARTURE_DATE]:{value:"",required:e},[_e.DEPARTURE_AIRPORT]:{value:"",required:!0},[_e.DATE_TIME_RANGE]:{value:"",required:e},[_e.ARRIVAL_AIRPORT]:{value:"",required:!0},[_e.STOP_QUANTITY]:{value:"",required:!0},[_e.STOP_FLAG]:{value:"",required:!0,unorderedCode:[_e.GLOBAL_INDICATOR]},[_e.AIR_EQUIPMENT_TYPE]:{value:""},[_e.OC_AIRLINE]:{value:""},[_e.OPEN_AND_CLOSE_CABINS]:{value:""}}),Yr=()=>({[_e.GLOBAL_INDICATOR]:{value:""}}),qr=e=>{const a=e[0]!=="O";let i=e.replaceAll(/ */gi,"");const r=Yr(),c=Br(a);try{if(Object.keys(c).forEach(l=>{var y;const v=zt.get(l),d=c[l];if(v){const p=v.exec(i);if(!p||p.index){if(d!=null&&d.required)throw new Error;return}d.value=p[0],i=i.slice(p[0].length),(y=d.unorderedCode)!=null&&y.length&&d.unorderedCode.forEach(h=>{const S=r[h],k=new RegExp(zt.get(h),"g"),T=i.match(k);if(T!=null&&T.length){if(T.length>1)throw new Error;S.value=T[0],i=i.replace(k,"")}})}}),i)throw new Error;return{valid:!0,flightInfoForm:{...c,...r}}}catch{return{valid:!1,flightInfoForm:{...c,...r}}}},Ur=(e,a,i,r)=>{var S;if(!e&&!a)return{departureDateTime:"",arrivalDateTime:""};const c=a,n=c.slice(0,4),l=Vr(e,n,i,r),v=((S=Qr.find(k=>c.includes(k.code)))==null?void 0:S.offset)??0,d=se(l).add(Number(v),"day").format("YYYY-MM-DD"),y=v?c.slice(5):c.slice(4),p=qa(l,n||"0000"),h=qa(d,y||"0000");return{departureDateTime:p,arrivalDateTime:h}},Hr=e=>e.slice(2),jr=e=>{if(!e)return{openCabins:[],closeCabins:[]};let a=e,i=[],r=[];const c=zt.get(_e.OPEN_CABINS),n=c.exec(a);if(c&&n){const d=n[0];if(i=d.replace("#D","").split("")??[],n.index)return{openCabins:i,closeCabins:[]};a=a.slice(d.length)}return zt.get(_e.CLOSE_CABINS).exec(a)&&(r=a.replace("#C","").split("")??[]),{openCabins:i,closeCabins:r}},Gr=(e,a,i)=>{const r=e[_e.FLIGHT_STATUS].value==="O",{departureDateTime:c,arrivalDateTime:n}=Ur(e[_e.DEPARTURE_DATE].value,e[_e.DATE_TIME_RANGE].value,a,i),{openCabins:l,closeCabins:v}=jr(e[_e.OPEN_AND_CLOSE_CABINS].value),d=Hr(e[_e.OC_AIRLINE].value),y=e[_e.GLOBAL_INDICATOR].value.slice(2);return{openFlag:r,globalIndicator:y,flightStatus:e[_e.FLIGHT_STATUS].value.replace("*",""),companyCode:e[_e.COMPANY_CODE].value,flightNumber:e[_e.FLIGHT_NUMBER].value,cabinCode:e[_e.CABIN_CODE].value,departureAirport:e[_e.DEPARTURE_AIRPORT].value,departureDateTime:c,arrivalDateTime:n,arrivalAirport:e[_e.ARRIVAL_AIRPORT].value,stopQuantity:e[_e.STOP_QUANTITY].value,stopFlag:e[_e.STOP_FLAG].value,airEquipmentType:e[_e.AIR_EQUIPMENT_TYPE].value,operatingAirline:d,openCabins:l,closeCabins:v,flightType:""}},Qf=(e,a,i)=>{const r=[];return e.some(l=>{const{valid:v,flightInfoForm:d}=qr(l);return r.push(d),!v})?[]:r.map(l=>[Gr(l,a??"",i??"")])??[]},Mf=e=>{const a=[],i=[];return e.forEach(r=>{Zo.some(c=>c.value.toUpperCase()===r.toUpperCase())?i.push({passengerType:r==="CNN"?"CHD":r,selectedNumber:1}):a.push({passengerType:r,selectedNumber:1})}),{selectedPassengers:i,groupPassengers:a}},Vf=e=>{switch(e){case"RR":case"HK":return"S";case"HL":case"HN":return"L";default:return"U"}},Bf=e=>e?` #O${e}`:"",Kr=e=>(It("data-v-2f9e66d0"),e=e(),Et(),e),zr={class:"min-h-[50px]"},Wr={key:0,class:"brand text-xs"},Jr=Kr(()=>o("div",{class:"brand-line border-b mt-[10px]"},null,-1)),Zr={key:1,className:"text-neutral-800 text-xs font-bold leading-tight"},Xr={key:2,className:"w-[100%] h-[30px] px-1.5 py-[7px] bg-[#f5f9ff] rounded-tl-md justify-start items-center inline-flex mt-[6px]"},es={key:3,className:"w-[100%] h-[30px] px-1.5 py-[7px] bg-[#f5f9ff] rounded-tl-md justify-start items-center inline-flex mt-[6px]"},ts={class:"row-base th ml-[-7px]"},as={key:4,class:"cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex flex-col"},os={key:6,class:"cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex flex-col"},ns=Ye({__name:"priceBasis",props:{priceBasis:{},ticketingTime:{},computingTime:{},priceFc:{},fcc:{},needCloseIcon:{type:Boolean}},setup(e){return(a,i)=>{var n,l,v,d;const r=$n,c=xn;return u(),$("div",zr,[a.priceBasis?(u(),$("div",Wr,[Jr,a.needCloseIcon?yt(a.$slots,"default",{key:0},void 0,!0):(u(),$("div",Zr,g(a.$t("app.fareQuery.freightate.ticketPriceBasis")),1)),a.priceFc?(u(),$("div",Xr,g(a.priceFc),1)):ge("",!0),(l=(n=a.priceBasis)==null?void 0:n.segmentFare[0])!=null&&l.rate?(u(),$("div",es,[o("span",ts,g(a.$t("app.fareQuery.freightate.rate")),1),o("span",null,g((d=(v=a.priceBasis)==null?void 0:v.segmentFare[0])==null?void 0:d.rate),1)])):ge("",!0),f(c,{class:"row-base th mt-[2px]"},{default:x(()=>[f(r,{span:4},{default:x(()=>[me("PU")]),_:1}),f(r,{span:4},{default:x(()=>[me("Farebasis")]),_:1}),f(r,{span:4},{default:x(()=>[me(g(a.$t("app.fareQuery.freightate.currency")),1)]),_:1}),f(r,{span:4},{default:x(()=>[me("NUC")]),_:1}),f(r,{span:4},{default:x(()=>[me(g(a.$t("app.fareQuery.freightate.desc")),1)]),_:1}),f(r,{span:4},{default:x(()=>[me(g(a.$t("app.fareQuery.freightate.globalIndicator")),1)]),_:1})]),_:1}),a.priceBasis.segmentFare&&a.priceBasis.segmentFare.length>0?(u(),$("div",as,[(u(!0),$(he,null,Ie(a.priceBasis.segmentFare,(y,p)=>(u(),Se(c,{key:p,class:"row-base"},{default:x(()=>[f(r,{span:4},{default:x(()=>[me(g(y.pu??"--"),1)]),_:2},1024),f(r,{span:4},{default:x(()=>[me(g(y.fareBasis??"--"),1)]),_:2},1024),f(r,{span:4},{default:x(()=>[me(g(y.currency??"--"),1)]),_:2},1024),f(r,{span:4},{default:x(()=>[me(g(y.nuc??"--"),1)]),_:2},1024),f(r,{span:4},{default:x(()=>[me(g(y.description??"--"),1)]),_:2},1024),f(r,{span:4},{default:x(()=>[me(g(y.globalIndicator??"--"),1)]),_:2},1024)]),_:2},1024))),128))])):(u(),Se(c,{key:5,class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md"},{default:x(()=>[f(r,{span:4},{default:x(()=>[me("--")]),_:1}),f(r,{span:4},{default:x(()=>[me("--")]),_:1}),f(r,{span:4},{default:x(()=>[me("--")]),_:1}),f(r,{span:4},{default:x(()=>[me("--")]),_:1}),f(r,{span:4},{default:x(()=>[me("--")]),_:1}),f(r,{span:4},{default:x(()=>[me("--")]),_:1})]),_:1})),f(c,{class:"row-base mt-[10px] th"},{default:x(()=>[f(r,{span:4},{default:x(()=>[me("CNY TAX")]),_:1}),f(r,{span:4},{default:x(()=>[me("LST "+g(a.$t("app.fareQuery.freightate.currency")),1)]),_:1}),f(r,{span:4},{default:x(()=>[me("CODES TYP")]),_:1}),f(r,{span:8},{default:x(()=>[me(g(a.$t("app.fareQuery.freightate.desc")),1)]),_:1}),f(r,{span:4},{default:x(()=>[me("TAX")]),_:1})]),_:1}),a.priceBasis.taxFare&&a.priceBasis.taxFare.length>0?(u(),$("div",os,[(u(!0),$(he,null,Ie(a.priceBasis.taxFare,(y,p)=>(u(),Se(c,{key:p,class:"row-base"},{default:x(()=>[f(r,{span:4},{default:x(()=>[me(g(y.cnyTax??"--"),1)]),_:2},1024),f(r,{span:4},{default:x(()=>[me(g(y.lstCurrency??"--"),1)]),_:2},1024),f(r,{span:4},{default:x(()=>[me(g(y.codesTyp??"--"),1)]),_:2},1024),f(r,{span:8},{default:x(()=>[me(g(y.description??"--"),1)]),_:2},1024),f(r,{span:4},{default:x(()=>[me(g(y.tax??"--"),1)]),_:2},1024)]),_:2},1024))),128))])):(u(),Se(c,{key:7,class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md"},{default:x(()=>[f(r,{span:4},{default:x(()=>[me("--")]),_:1}),f(r,{span:4},{default:x(()=>[me("--")]),_:1}),f(r,{span:4},{default:x(()=>[me("--")]),_:1}),f(r,{span:8},{default:x(()=>[me("--")]),_:1}),f(r,{span:4},{default:x(()=>[me("--")]),_:1})]),_:1})),f(c,{class:"row-base mt-[10px] th"},{default:x(()=>[f(r,{span:8},{default:x(()=>[me(g(a.$t("app.fareQuery.freightate.computingTime")),1)]),_:1}),f(r,{span:8},{default:x(()=>[me(g(a.$t("app.fareQuery.freightate.ticketingTime")),1)]),_:1}),f(r,{span:4},{default:x(()=>[me("FCC")]),_:1})]),_:1}),f(c,{class:"row-base cell rounded-bl-md rounded-tr-md rounded-tl-md rounded-br-md flex"},{default:x(()=>[f(r,{span:8},{default:x(()=>[me(g(a.computingTime??"--"),1)]),_:1}),f(r,{span:8},{default:x(()=>[me(g(a.ticketingTime??"--"),1)]),_:1}),f(r,{span:4},{default:x(()=>[me(g(a.fcc??"--"),1)]),_:1})]),_:1})])):ge("",!0)])}}});const Yf=et(ns,[["__scopeId","data-v-2f9e66d0"]]),is={class:"cursor-default"},rs={class:"w-full flex justify-between"},ss={class:"text-xs text-gray-2 mb-1.5"},ls={class:"text-xs text-gray-2 font-bold mb-1.5"},cs={class:"text-xs rounded bg-brand-7 mt-[5px]"},us={class:"pr-[40px] text-gray-2"},ds={class:"basis-[100px] text-gray-2"},ps={class:"text-gray-2"},gs={key:0,class:"mb-2.5"},fs={class:"flex justify-between items-center"},ms={key:0,class:"w-full overflow-x-hidden text-xs mr-5 bg-brand-7"},vs={class:"overflow-x-auto"},hs={class:"flex p-0 mb-1 bg-[#FFFFFF] text-[#8C8C8C] whitespace-nowrap h-[30px] leading-[30px]"},ys={class:"w-60 px-2"},bs={class:"w-40 px-2"},_s={class:"min-w-[120px] px-2"},Cs={class:"w-[60px] px-2"},As={class:"w-60 px-2 overflow-hidden whitespace-nowrap text-ellipsis bg-brand-7"},Ds={class:"w-40 bg-brand-7"},$s={class:"min-w-[125px] px-2"},xs={class:"w-[60px] px-2"},Ss=Ye({__name:"Package",props:{baggageAllowance:{},international:{type:Boolean},needCloseIcon:{type:Boolean},doBaggageApi:{type:Boolean}},emits:["closePanel"],setup(e){const a=B(!1),i=d=>d.index?`${d.baggageCurrency}${d.baggageAmout}${d.baggageType==="4"?`/${d.baggageUnit}`:""}`:"-",r=d=>d.baggageType??"-",c=kt(),{activeTag:n}=Ft(c),l=Ee(()=>c.getOrderInfo),v=Ee(()=>{var d;return((d=l.value.get(n.value))==null?void 0:d.type)==="2"});return(d,y)=>{var T,V,b;const p=ka,h=je,S=ni,k=_t;return u(),$("div",is,[f(p),o("div",rs,[o("p",ss,g(d.international?d.$t("app.fareQuery.freightate.baggageAllowanceTips"):""),1),d.needCloseIcon?(u(),Se(h,{key:0,class:"cursor-pointer mr-[10px]",size:"16px",onClick:y[0]||(y[0]=G=>d.$emit("closePanel"))},{default:x(()=>[f(t(ao))]),_:1})):ge("",!0)]),o("div",{class:ye(["mb-[2px]",d.international&&v.value?"hidden":""])},[o("p",ls,g(d.$t("app.fareQuery.freightate.freeBaggageAllowance")),1),!d.international&&d.doBaggageApi?(u(),Se(S,{key:0,title:d.$t("app.fareQuery.freightate.domBaggageAllowanceTips"),type:"warning",closable:!1,"show-icon":""},null,8,["title"])):ge("",!0),o("div",cs,[(u(!0),$(he,null,Ie(((T=d.baggageAllowance)==null?void 0:T.freeBaggageSegInfo)??[],(G,w)=>(u(),$("p",{key:w,class:"flex px-2 h-[30px] leading-[30px]"},[o("span",us,g(d.$t("app.fareQuery.freightate.freeBaggageAllowance")),1),o("span",ds,g(`${G.departurePlace}-${G.arrivalPlace}`),1),o("span",ps,g(G.freeBaggageFare),1)]))),128))])],2),d.international?(u(),$("div",gs,[o("div",fs,[o("p",{class:"flex items-center text-xs text-gray-1 font-bold mb-1.5",onClick:y[1]||(y[1]=G=>a.value=!a.value)},[o("span",null,g(d.$t("app.fareQuery.freightate.excessBaggageAllowance")),1),a.value?(u(),Se(h,{key:0,size:16},{default:x(()=>[f(t(Bt))]),_:1})):(u(),Se(h,{key:1,size:16},{default:x(()=>[f(t(Wt))]),_:1}))])]),a.value?(u(),$("div",ms,[o("div",vs,[o("p",hs,[o("span",ys,g(d.$t("app.fareQuery.freightate.baggageDescription")),1),o("span",bs,g(d.$t("app.fareQuery.freightate.segment")),1),(u(!0),$(he,null,Ie(((V=d.baggageAllowance)==null?void 0:V.payBaggageTitle)??[],(G,w)=>(u(),$(he,{key:w},[o("span",_s,g(G),1),o("span",Cs,g(d.$t("app.fareQuery.freightate.baggageType")),1)],64))),128))]),(u(!0),$(he,null,Ie(((b=d.baggageAllowance)==null?void 0:b.payBaggageInfo)??[],(G,w)=>(u(),$(he,{key:w},[(u(!0),$(he,null,Ie(G.payBaggageSegInfo,(E,D)=>(u(),$("div",{key:D,class:"rounded flex p-0 h-[30px] leading-[30px]"},[f(k,{effect:"dark",placement:"top",content:G.payBaggageStatement},{default:x(()=>[o("span",As,g(G.payBaggageStatement),1)]),_:2},1032,["content"]),o("span",Ds,g(`${E.departurePlace}-${E.arrivalPlace}`),1),(u(!0),$(he,null,Ie(E.payBaggageFares,(I,m)=>(u(),$(he,{key:m},[o("span",$s,g(i(I)),1),o("span",xs,g(r(I)),1)],64))),128))]))),128))],64))),128))])])):ge("",!0)])):ge("",!0)])}}});const qf=et(Ss,[["__scopeId","data-v-66ec95ac"]]),Ts={class:"min-h-[50px] border-t mt-[10px]"},ks={key:1,class:"brand text-xs flex flex-col"},Fs={class:"brand-title font-bold text-gray-2 mb-1 mt-0.5"},Ls={key:0,class:"brand-info bg-brand-7 rounded-[1px] p-[8px] flex flex-col"},Ns={class:"brand-free brand-one text-green-2 mr-4 font-bold h-5"},ws={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},Is={class:"min-w-[30px]"},Es=o("div",{class:"border-b border-dashed border-gray-300 mb-1 mt-1"},null,-1),Ps={class:"brand-pay brand-one text-yellow-1 mr-4 font-bold h-5"},Rs={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},Os={class:"min-w-[30px]"},Qs={key:0,class:"border-b border-dashed border-gray-300 mb-1 mt-1"},Ms=o("div",{class:"brand-provide brand-one text-gray-1 mr-4 font-bold h-5"},"DISPALY AS NOT OFFERED",-1),Vs={key:0,class:"brand-content text-gray-2 text-xs font-normal leading-tight flex h-5 items-center"},Bs={class:"min-w-[30px]"},Ys={key:2,class:"min-h-[50px]"},Uf=Ye({__name:"Brand",props:{brandInfoData:{},needCloseIcon:{type:Boolean}},setup(e){return(a,i)=>{var r;return u(),$("div",Ts,[a.needCloseIcon?yt(a.$slots,"default",{key:0}):ge("",!0),(r=a.brandInfoData)!=null&&r.length?(u(),$("div",ks,[(u(!0),$(he,null,Ie(a.brandInfoData,(c,n)=>(u(),$(he,{key:n},[o("div",Fs,g(c.brandName)+" "+g(c.tierCode),1),c.equityClassify.length>0?(u(),$("div",Ls,[(u(!0),$(he,null,Ie(c.equityClassify,(l,v)=>(u(),$("div",{key:v,class:"mb-1"},[l.offeredForFree.length>0?(u(),$(he,{key:0},[o("div",Ns,g(a.$t("app.fareQuery.freightate.free")),1),(u(!0),$(he,null,Ie(l.offeredForFree,(d,y)=>(u(),$(he,{key:y},[d.subCode||d.title?(u(),$("div",ws,[o("div",Is,g(d.subCode??""),1),me(g(d.title),1)])):ge("",!0)],64))),128)),Es],64)):ge("",!0),l.offeredForCharge.length>0?(u(),$(he,{key:1},[o("div",Ps,g(a.$t("app.fareQuery.freightate.charge")),1),(u(!0),$(he,null,Ie(l.offeredForCharge,(d,y)=>(u(),$(he,{key:y},[d.subCode||d.title?(u(),$("div",Rs,[o("div",Os,g(d.subCode??""),1),me(g(d.title),1)])):ge("",!0)],64))),128)),l.displayAsNotOffered.length>0?(u(),$("div",Qs)):ge("",!0)],64)):ge("",!0),l.displayAsNotOffered.length>0?(u(),$(he,{key:2},[Ms,(u(!0),$(he,null,Ie(l.displayAsNotOffered,(d,y)=>(u(),$(he,{key:y},[d.subCode||d.title?(u(),$("div",Vs,[o("div",Bs,g(d.subCode??""),1),me(g(d.title),1)])):ge("",!0)],64))),128))],64)):ge("",!0)]))),128))])):ge("",!0)],64))),128))])):(u(),$("div",Ys,g(a.$t("app.fareQuery.freightate.noData")),1))])}}}),qs=kt(),{activeTag:Us}=Ft(qs),Ua=()=>({flightDataList:new Map,clickFlightSearchFlag:!1,seizeSeatInfoFlight:[],clickHistoryFlag:{},isInterSegment:!1,refrashFlight:!1,activeFlightIndex:0}),ta=Xo("flight",{state:()=>({flightByDate:{[Us.value]:Ua()},simEtermIsOpen:!1}),actions:{initAgentSell(e){this.flightByDate[e]=Ua()},deleteAgentSell(e){delete this.flightByDate[e]},setActiveFlightIndex(e,a){this.flightByDate[e].activeFlightIndex=a},setFlightDataList(e,a,i,r,c){var l,v;i.currentPage=c||1,i.queryForm=a;const n=new Map([[r,i]]);(l=this.flightByDate[e])==null||l.flightDataList.forEach((d,y)=>{n.has(y)||n.set(y,d)}),(v=this.flightByDate[e])==null||v.flightDataList.clear(),this.flightByDate[e].flightDataList=n},setFlightSort(e,a){var i;(i=this.flightByDate[e])==null||i.flightDataList.clear(),this.flightByDate[e].flightDataList=a},delFlightDataList(e,a){this.flightByDate[e].flightDataList.delete(a)},updateFlightDataListKey(e,a,i,r){const c=[...this.flightByDate[e].flightDataList.keys()].indexOf(i),n=this.flightByDate[e].flightDataList.get(i);if(!n)return;n.queryForm=r;const l=[...this.flightByDate[e].flightDataList];l.splice(c,1,[a,n]);const v=new Map(l);this.flightByDate[e].flightDataList=v},delFlightByPnr(e){this.flightByDate=Wn(this.flightByDate,[e])},setHistoryQueryForm(e,a){this.flightByDate[e].clickHistoryFlag.flag=!this.flightByDate[e].clickHistoryFlag.flag,this.flightByDate[e].clickHistoryFlag.queryForm=a},setClickFlightSearchFlag(e,a){this.flightByDate[e]||this.initAgentSell(e),this.flightByDate[e].clickFlightSearchFlag=!!a},delSeizeSeatInfoFlight(e,a){this.flightByDate[e].seizeSeatInfoFlight=this.flightByDate[e].seizeSeatInfoFlight.filter(i=>i.key!==a)},setSeizeSeatInfoFlight(e,a){this.flightByDate[e].seizeSeatInfoFlight.push(a)},setRefrashFlight(e,a){this.flightByDate[e].refrashFlight=a},setSimEtermOpenFlag(e){this.simEtermIsOpen=e}}}),Hs={class:"custom-focus-tip-input"},js={class:"hidden bkc-el-form-item__error"},Gs=Ye({__name:"CustomFocusTipInput",props:{modelValue:{default:""},tipText:{}},emits:["update:modelValue"],setup(e,{emit:a}){const i=e,r=a,c=Ee({get:()=>i.modelValue.toUpperCase(),set:v=>{n.value=!v,r("update:modelValue",v.toUpperCase())}}),n=B(!1),l=v=>{if(v){n.value=!c.value;return}n.value=v};return(v,d)=>{const y=Pt;return u(),$("div",Hs,[o("div",{class:ye(["input-focus-tip",n.value?"input-focus":""])},[f(y,tn({ref:"inputRef",modelValue:c.value,"onUpdate:modelValue":d[0]||(d[0]=p=>c.value=p)},v.$attrs,{onBlur:d[1]||(d[1]=p=>l(!1)),onFocus:d[2]||(d[2]=p=>l(!0))}),en({_:2},[Ie(v.$slots,(p,h)=>({name:h,fn:x(()=>[yt(v.$slots,h,{},void 0,!0)])}))]),1040,["modelValue"]),o("div",js,g(v.tipText),1)],2)])}}});const Ha=et(Gs,[["__scopeId","data-v-6bab0367"]]);var Qt=(e=>(e[e.DIRECT=0]="DIRECT",e[e.STOP=-1]="STOP",e))(Qt||{});const wt="FAST_QUERY_AV_QUERY",Ks=()=>se().format("YYYY-MM-DD"),zs=e=>se(e).add(1,"day").format("YYYY-MM-DD"),Ws=e=>se(e).subtract(1,"day").format("YYYY-MM-DD"),_o=(e,a)=>{var n;let i=[];const r=Array.from(a.keys()),c=r==null?void 0:r.find(l=>{var v,d;return l!==e&&((d=(v=a.get(l))==null?void 0:v.occupySpecialContent)==null?void 0:d.includes(s.global.t("app.basic.occupy")))});if(c){const l=a.get(c);i=((l==null?void 0:l.flight)??[]).filter(v=>v.segments.some(d=>d.segmentType==="2"))}else i=(((n=a.get(e))==null?void 0:n.flight)??[]).filter(l=>l.segments.some(v=>v.segmentType==="2"));return i},Js=e=>{var a,i;return!!(e!=null&&e.notchPath)||((i=(a=e==null?void 0:e.segments[0])==null?void 0:a.airlines)==null?void 0:i.flightNo)==="ARNK"},Zs=e=>{var a,i;return!!(e!=null&&e.openFlag)||((i=(a=e==null?void 0:e.segments[0])==null?void 0:a.airlines)==null?void 0:i.flightNo)==="OPEN"},Xs=e=>{const a=e==null?void 0:e.match(/[a-zA-Z]+/g);return a?a==null?void 0:a.join(""):""},ra=(e,a)=>{let i=[],r=it(a);e&&(r=(r??[]).filter(c=>!Zs(c)&&!Js(c)));try{i=r.flatMap(c=>c.segments.map(n=>{var l,v,d,y,p;return{airline:n.airlines.airCode,flightNumber:parseInt((v=((l=n.airlines)==null?void 0:l.flightNo)??"")==null?void 0:v.replace(/[a-zA-Z]/g,"")),flightSuffix:Xs((d=n.airlines)==null?void 0:d.flightNo),cls:n.cabins[0].cabinName,departureDate:n.departureDate?se(n.departureDate).format("YYYY-MM-DD"):"",origin:n.departureAirportCode,destination:n.arrivalAirportCode,seats:(n==null?void 0:n.tktNum)??0,action:e?n.actionCode??"":n.seatTag??"",departureTime:(y=n.departureTime)==null?void 0:y.replace(/:/g,""),arrivalTime:(p=n.arrivalTime)==null?void 0:p.replace(/:/g,""),married:n.marriedSegmentNumber?parseInt(n.marriedSegmentNumber):0}}))}catch{i=[]}return i},el=async(e,a,i,r,c,n,l)=>{var k,T,V;const v={departureDate:se(a.departureDate).format("YYYY-MM-DD"),queryType:"REAL_TIME_AV",flightNo:e.toUpperCase()},d=(((k=c.get(i))==null?void 0:k.type)??"")==="2",y=_o(i,c),p=((T=r==null?void 0:r.get(i))==null?void 0:T.flight)??[];d&&y.length&&(v.preOccupySegmentInfoList=ra(!1,y)),!d&&p.length&&(v.preOccupySegmentInfoList=ra(!0,p));const h=await pi(v,n),S=((V=h==null?void 0:h.data.value)==null?void 0:V.flightInfoList)??[];if(S.length){const b=G=>{const{departureAirportCode:w,departureAirportCN:E,arrivalAirportCode:D,arrivalAirportCN:I,departureDate:m,departureTime:C,arrivalDate:A,arrivalTime:_,departureTerminal:P,arrivalTerminal:U,airlines:K,cabins:Y,etInd:ne}=G;if(l!=null&&l.length){const Z=l.find(O=>O.airportCode.toUpperCase()===w.toUpperCase()),H=l.find(O=>O.airportCode.toUpperCase()===D.toUpperCase());Z&&H&&(a.departureAirportCode.toUpperCase()===Z.cityCode.toUpperCase()&&(a.departureAirportCode=Z.airportCode),a.arrivalAirportCode.toUpperCase()===H.cityCode.toUpperCase()&&(a.arrivalAirportCode=H.airportCode))}const oe=`${a.departureAirportCode}${a.arrivalAirportCode}`.toUpperCase()===`${w}${D}`.toUpperCase();if(oe){const Z=Y==null?void 0:Y.find(O=>{var L;return(O==null?void 0:O.cabinName.toUpperCase())===((L=a==null?void 0:a.cabin)==null?void 0:L.toUpperCase())});let H="";Z&&(H=Z.state??""),Object.assign(a,{isRealFlightNo:oe,departureAirportCode:w,departureAirportCN:E,arrivalAirportCode:D,arrivalAirportCN:I,departureDate:m,departureTime:C,arrivalDate:A,arrivalTime:_,departureTerminal:P,arrivalTerminal:U,airCN:K.airCN,isShared:K.isShared,state:H,etInd:ne})}return oe};return S.some(G=>{if(G.segments.find(E=>b(E)))return!0;if(G.segments.length>1){const E=G.segments[0],D=G.segments[G.segments.length-1],{arrivalAirportCode:I,arrivalDate:m,arrivalTime:C,arrivalTerminal:A}=D;return Object.assign(E,{arrivalAirportCode:I,arrivalDate:m,arrivalTime:C,arrivalTerminal:A}),b(E)}})}return!1},tl=async(e,a,i,r,c)=>{const n=e.map(async l=>{if(l.isOpen)return l.isRealFlightNo=!0,!0;const v=l.flightNumber.startsWith(l.airlines)?l.flightNumber:`${l.airlines}${l.flightNumber}`;return await el(v,l,i,r,c,oo("01010215"),a),!!l.isRealFlightNo});await Promise.all(n)},al=e=>(e??[]).map(a=>{const i=[],r=a.segments;return r.some(n=>Number((n==null?void 0:n.stopCity)??0))&&(i.includes(Qt.STOP)||i.push(Qt.STOP),r.length-1===0)?{...a,flyType:i}:(i.includes(r.length-1)||i.push(r.length-1),{...a,flyType:i})}),ol=()=>{const e=Xt();return{cacheHistoryCondition:async r=>{var d;const c=await pt("QUERY_HISTORY"),{userName:n}=await e.getters.user;if(c===null){await Pa("QUERY_HISTORY",JSON.stringify([{userName:n,qureyParam:[r]}]));return}const l=JSON.parse(c==null?void 0:c.localData).findIndex(y=>y.userName===n),v=JSON.parse(c==null?void 0:c.localData);if(l<0)v.push({userName:n,qureyParam:[r]});else{const y=JSON.parse(c==null?void 0:c.localData)[l];if((d=y==null?void 0:y.qureyParam)==null?void 0:d.map(h=>JSON.stringify(h)).some(h=>h===JSON.stringify(r)))return;y.qureyParam.length<5||y.qureyParam.shift(),y.qureyParam.push(r),v[l]=y}await Pa("QUERY_HISTORY",JSON.stringify(v))},getHistory:async()=>{var l;const r=await pt("QUERY_HISTORY"),{userName:c}=await e.getters.user;if(!(r!=null&&r.localData))return null;const n=(l=JSON.parse(r==null?void 0:r.localData))==null?void 0:l.find(v=>v.userName===c);return(n==null?void 0:n.qureyParam)??null}}},ja=()=>({destName:"",originName:"",origin:"",destination:"",departureDate:"",departureDateTime:"",departureDateTimes:"",flightNumber:"",onlyDirectFlight:!1,airlines:"",onlyCompany:"",seamlessOrDa:"",unsharedFlight:!1,carrierFlight:!1,lowestPrice:!1,timeSequence:!1,transitTerminal:"",transitTerminalName:"",selectPassengers:{adult:{num:0,min:0,max:1},children:{num:0,min:0,max:1},baby:{num:0,min:0,max:1},chdOverseasStudent:{num:0,min:0,max:1},overseasStudent:{num:0,min:0,max:1},migrate:{num:0,min:0,max:1},chdMigrate:{num:0,min:0,max:1},seafarer:{num:0,min:0,max:1},labourer:{num:0,min:0,max:1}}}),nl=(e,a)=>{const{t:i}=gt(),{cacheHistoryCondition:r,getHistory:c}=ol(),n=B(),l=Xt(),v=Ee(()=>l.getters.userPreferences),d=ca(),y=kt(),{activeTag:p,orderInfo:h}=Ft(y),S=a.avQueryFromFastQuery?wt:p.value,k=B([]),T=ta(),V=Ee(()=>{var M;return((M=h.value.get(p.value))==null?void 0:M.commandsAVCheckSuccess)??!1}),b=Ee(()=>{var q;const M=(q=h.value.get(p.value))==null?void 0:q.commands;return JSON.stringify(M?M.get("AV"):"")}),G=B(!1),w=B(""),E=B(),D=B(),I=B(),m=B(!1),C=B(!0),A=B(!1),_=B(ja()),P=B({S:!1,T:!1,W:!1}),U=B([]),K=B([]),Y=B(!1),ne=["H","E","P","O"].sort(),oe=B(!1),Z=ct({render(){return ht("em",{class:"iconfont icon-calendar"})}}),H=B(),O=B([`${i("app.fastQuery.headerQuery.notHistory")}`]),L=(M,q,J)=>{if(C.value){if(q.length>3||!St.test(q)){J(new Error(i("app.fastQuery.headerQuery.formatError")));return}if(_.value.destination&&q===_.value.destination){J(new Error(i("app.fastQuery.headerQuery.identical")));return}}J()},R=(M,q,J)=>{if(C.value&&!q){J(new Error(i("app.fastQuery.headerQuery.must")));return}J()},N=(M,q,J)=>{if(C.value){if(q.length>3||!St.test(q)){J(new Error(i("app.fastQuery.headerQuery.formatError")));return}if(_.value.origin&&q===_.value.origin){J(new Error(i("app.fastQuery.headerQuery.identical")));return}}J()},F=(M,q,J)=>{if(C.value&&!q){J(new Error(i("app.fastQuery.headerQuery.must")));return}J()},ae=M=>M.some(q=>{var J;return q.includes("*")?!q.startsWith("*")||(((J=q.match(/\*/g))==null?void 0:J.length)??0)>1:!1}),le=M=>M!=null&&M.some(q=>q.length===1)?!0:M.some((q,J,Me)=>J<Me.length-1&&q.length===2&&Me[J+1].length===2),de=(M,q,J)=>{var tt,nt,Ze;const Me=new RegExp("^(?!\\/)(?!.*\\/\\/)(?!.*[^/]{4})(?!(?:[^/]{2}\\/){2}|[^/]{2}\\/[^/]{2}(?:\\/|$))[a-zA-Z0-9*]{2,3}(?:\\/[a-zA-Z0-9*]{2,3})*$(?<!\\/)"),Ce=(tt=_.value.transitTerminal??"")==null?void 0:tt.split("/").filter(lt=>lt!==""),Ke=(Ze=(nt=_.value.transitTerminal??"")==null?void 0:nt.replace(/\*/g,""))==null?void 0:Ze.split("/").filter(lt=>lt!=="");q?Ke.some(lt=>lt.length===3)?Me.test(_.value.transitTerminal??"")?le(Ke)?J(new Error(i("app.pnrManagement.validate.formatErr"))):ae(Ce)?J(new Error(i("app.pnrManagement.validate.formatErr"))):J():J(new Error(i("app.pnrManagement.validate.formatErr"))):J(new Error(i("app.pnrManagement.validate.formatErr"))):J()},fe=(M,q,J)=>{A.value&&!q&&J(new Error(i("app.fastQuery.headerQuery.must"))),J()},te={origin:[{validator:R,trigger:"change"},{validator:L,trigger:"blur"}],destination:[{validator:F,trigger:"change"},{validator:N,trigger:"blur"}],departureDate:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"change"}],airlines:[{validator:fe,trigger:"change"},{pattern:no,message:new Error(i("app.fastQuery.headerQuery.correctAirlineNum")),trigger:"change"}],flightNumber:[{pattern:Ra,message:new Error(i("app.avSearch.validateFltNo")),trigger:"change"}],departureDateTime:[{pattern:ya,message:new Error(i("app.avSearch.timeFormatTip")),trigger:"change"}],transitTerminal:[{validator:de,trigger:"change"}]},Te=M=>{switch(M){case"S":P.value.T=!1,P.value.W=!1;break;case"T":P.value.S=!1,P.value.W=!1;break;case"W":P.value.S=!1,P.value.T=!1;break}},be=async()=>{const{data:M}=await mi("08100116");H.value=M.value},Re=()=>{var M,q;_.value.carrierFlight=((M=v.value)==null?void 0:M.unshared)??!1,_.value.onlyDirectFlight=((q=v.value)==null?void 0:q.nonstop)??!1},X=()=>{const M=_.value.origin;_.value.origin=_.value.destination,_.value.destination=M;const q=_.value.originName;_.value.originName=_.value.destName,_.value.destName=q},re=M=>{const q=Ta();return M.getTime()<q.getTime()},z=M=>{_.value.seamlessOrDa===M?_.value.seamlessOrDa="":_.value.seamlessOrDa=M},j=async(M,q,J)=>{var Ke,tt,nt,Ze;const Me=a.avQueryFromFastQuery?wt:p.value;T.setClickFlightSearchFlag(Me,!((Ke=T.flightByDate[Me])!=null&&Ke.clickFlightSearchFlag));const Ce=JSON.parse(JSON.stringify(_.value));return Ce.airlines=((tt=Ce.airlines)==null?void 0:tt.toUpperCase())??"",Ce.flightNumber=((nt=Ce.flightNumber)==null?void 0:nt.toUpperCase())??"",Ce.transitTerminal=((Ze=Ce.transitTerminal)==null?void 0:Ze.toUpperCase())??"",(M&&q||Ce.flightNumber)&&await r(Ce),{..._.value,origin:M,destination:q,departureDate:se(J)?se(J).format("YYYY-MM-DD"):""}},W=(M,q,J)=>`${M}${q}${J}`,ue=async()=>{var q,J;w.value="",G.value=!1;let M=_.value.departureDate;if(Y.value){M=((q=_.value.departureDate)==null?void 0:q[0])??"";const Me=((J=_.value.departureDate)==null?void 0:J[1])??"";w.value=`${W(_.value.origin,_.value.destination,M)}/${W(_.value.destination,_.value.origin,Me)}`;const Ce=await j(_.value.origin,_.value.destination,M),Ke=await j(_.value.destination,_.value.origin,Me);await ce(),e("searchRoundClick",Ce,Ke)}else{const Me=await j(_.value.origin,_.value.destination,M);await ce(),e("searchClick",Me)}e("getRoundTripFlag",w.value),G.value=!0},Le=()=>{if(P.value.S){_.value.onlyCompany="S";return}if(P.value.T){_.value.onlyCompany="T";return}if(P.value.W){_.value.onlyCompany="W";return}_.value.onlyCompany=""},$e=Mt(()=>{var M;(M=E.value)==null||M.validate(async q=>{q&&(_.value.flightNumber&&(Ae(!1),_.value.transitTerminal=""),Le(),ue())})},300),Oe=(M,q)=>M.length!==q.length?!1:M.every(J=>q.includes(J))&&q.every(J=>M.includes(J)),Ae=M=>{M&&(_.value.flightNumber="",_.value.departureDate=""),P.value={S:!1,T:!1,W:!1},_.value.carrierFlight=!1,_.value.lowestPrice=!1,_.value.timeSequence=!1,_.value.onlyDirectFlight=!1,_.value.airlines="",_.value.origin="",_.value.originName="",_.value.destination="",_.value.destName="",_.value.seamlessOrDa="",_.value.departureDateTime="",Y.value=!1},Pe=M=>{const q=M.filter(J=>St.test(J));return _.value.transitTerminal=q.join("/"),M.filter(J=>J&&!St.test(J))},Ge=M=>{if(!M)return M;const q=M.match(/^(\d{1,2})([A-Za-z]{3})$/);if(!q)return M;const J=q[1],Me=q[2].toUpperCase();return J.padStart(2,"0")+Me},ot=M=>{let q=M;return q.length>5?Ba(se(q).format("YYYY-MM-DD"),!0):(q.length<5&&(q=Ge(q)),Ba(Sa(q),!0))},st=M=>M&&(_.value.lowestPrice=M),Be=M=>{const q=M[0].split("").sort();return M[0].length<=4&&q.every(J=>ne.includes(J))&&(q.includes("E")&&(_.value.timeSequence=!0),q.includes("O")&&(_.value.carrierFlight=!0),st(q.includes("P")),M.shift()),M},ut=M=>{let q=it(M);Ae(!0),q=Be(q),q=Pe(q),q.forEach(J=>{if(J==="D"){_.value.onlyDirectFlight=!0;return}if(Cn.test(J)){J.startsWith("*")?(_.value.seamlessOrDa="DA",_.value.airlines=J.slice(1)):(_.value.seamlessOrDa="seamless",_.value.airlines=J);return}if(An.test(J)){_.value.origin=J.slice(0,3),_.value.destination=J.slice(3);return}if(Dn.test(J)){_.value.departureDate=ot(J);return}Ra.test(J)&&(_.value.flightNumber=J),ya.test(J)&&(_.value.departureDateTime=J)})},Ue=()=>{n.value=void 0,_.value=ja(),P.value={S:!1,T:!1,W:!1},_.value.carrierFlight=!1,_.value.lowestPrice=!1,_.value.timeSequence=!1,C.value=!0,E.value.resetFields()},ee=M=>{let q="";switch(M){case"S":q=`${i("app.fastQuery.headerQuery.onlyStar")}`;break;case"T":q=`${i("app.fastQuery.headerQuery.onlySkyteam")}`;break;case"W":q=`${i("app.fastQuery.headerQuery.onlyOneworld")}`;break}return q},ce=async()=>{const M=await c();(M??[]).length<1||(k.value=[],k.value=JSON.parse(JSON.stringify(M)),U.value=JSON.parse(JSON.stringify(O.value)),O.value=(M??[]).map(q=>q.dept&&q.arrivel?`${q.dept}-${q.arrivel}`:!q.origin&&q.flightNumber?`${q.flightNumber} ${q.departureDate}`:`${q.origin??""}-${q.destination??""} ${q.departureDate??""} ${q.seamlessOrDa==="DA"?"*":""}${q.airlines} ${q.departureDateTime??""}
              ${q.transitTerminal??""} ${q.onlyDirectFlight?i("app.avSearch.onlyFight"):""} ${q.carrierFlight?i("app.avSearch.nonSharedFlights"):""}
              ${q.lowestPrice?i("app.avSearch.lowestPrice"):""} ${q.timeSequence?i("app.avSearch.timeSequence"):""} ${ee(q.onlyCompany)}`),K.value=JSON.parse(JSON.stringify(O.value)),!Oe(U.value,K.value)&&U.value[0]!==`${i("app.fastQuery.headerQuery.notHistory")}`&&(n.value=K.value.length-1))},pe=M=>{T.simEtermIsOpen||M.key==="Enter"&&(d.isOpened?S===wt:p.value===S)&&$e()},ke=async M=>{if(n.value=M,k.value[M].dept&&k.value[M].arrivel){const q=k.value[M];q.origin=q.dept??"",q.destination=q.arrivel??"",q.dept="",q.arrivel="",q.transitTerminal=""}_.value=it(k.value[M]),De(_.value.onlyCompany),$e()};Jt.on(`${qe.QUERY_MARRIED_FLIGHT}${p.value}`,async M=>{_.value=M,await Tt()});const De=M=>{switch(M){case"S":P.value.S=!0,P.value.T=!1,P.value.W=!1;break;case"T":P.value.T=!0,P.value.S=!1,P.value.W=!1;break;case"W":P.value.W=!0,P.value.S=!1,P.value.T=!1;break;default:P.value.W=!1,P.value.S=!1,P.value.T=!1;break}};return Xe(()=>_.value.onlyDirectFlight,()=>{_.value.onlyDirectFlight?(_.value.transitTerminal="",oe.value=!0):oe.value=!1}),Xe(()=>_.value.transitTerminal,()=>{_.value.transitTerminal!==""?(_.value.onlyDirectFlight=!1,m.value=!0):m.value=!1}),Xe(()=>_.value.flightNumber,()=>{_.value.flightNumber?(C.value=!1,E.value.clearValidate("origin"),E.value.clearValidate("destination")):C.value=!0}),Xe(()=>_.value.seamlessOrDa,async()=>{_.value.seamlessOrDa?A.value=!0:A.value=!1,await E.value.validateField("airlines")}),Xe([()=>V.value,()=>b.value],async()=>{var M,q;if(V.value&&(b.value??[]).length){const J=(q=(M=JSON.parse(b.value)[0])==null?void 0:M.replace(/\s+/g," ").slice(3))==null?void 0:q.split("/").filter(Me=>Me);await ut(J),await $e(),y.setCommandsAvCheckSuccess(p.value,!1)}}),Xe(()=>a.historyQueryForm,async M=>{M&&!Y.value&&(De(M.onlyCompany??""),_.value=it(M))},{immediate:!0,deep:!0}),Xe(()=>v.value,M=>{M&&(_.value.carrierFlight=(M==null?void 0:M.unshared)??!1,_.value.onlyDirectFlight=(M==null?void 0:M.nonstop)??!1)}),bt(async()=>{Re(),await be(),await ce(),window.addEventListener("keydown",pe,!0)}),an(()=>{window.addEventListener("keydown",pe,!0)}),Rt(()=>{Jt.off(`${qe.QUERY_MARRIED_FLIGHT}${p.value}`),window.removeEventListener("keydown",pe,!0)}),on(()=>{window.removeEventListener("keydown",pe,!0)}),{queryFormRef:E,FORM_RULES:te,queryForm:_,datePrefix:Z,isAshingTransitTerminal:oe,onlyDirectFlightAshing:m,agentAirportOriginRef:D,agentAirportDestinationRef:I,revertFromTo:X,disabledDate:re,seamlessOrDaClick:z,search:$e,resetClick:Ue,chooseItems:O,roundTripSwitch:Y,selectedItem:n,handleSelectItem:ke,checkCompany:P,changeCompanyRadio:Te}},Na=e=>(It("data-v-850c0a23"),e=e(),Et(),e),il={class:"flex"},rl={class:"flex mr-[10px] self-stretch"},sl=Na(()=>o("div",{class:"airlines-item bkc-el-input__wrapper"},"*",-1)),ll=[sl],cl={class:"inline-flex"},ul={class:"carrier-only-direct"},dl={class:"h-full pt-[2px] flex flex-col items-start mr-2.5"},pl={class:"lowest-price-box flex items-start"},gl={class:"lowest-price-box flex items-start"},fl={class:"h-full pt-[2px] flex flex-col items-start mr-2.5"},ml={class:"company-box flex items-start"},vl={class:"company-box flex items-start"},hl={class:"h-full pt-[2px] flex flex-col items-start mr-2.5"},yl={class:"company-box flex items-start"},bl={class:"flex-col justify-start items-start gap-5 inline-flex ml-[10px]"},_l={class:"inline-flex items-center justify-center gap-1"},Cl=Na(()=>o("span",{class:"iconfont icon-time-circle2"},null,-1)),Al={class:"text-xs"},Dl={key:0,class:"flex items-center"},$l={class:"text-brand-2 text-xs font-normal leading-tight"},xl={key:1,class:"flex items-center"},Sl=Na(()=>o("div",{class:"w-[19px]"},null,-1)),Tl={class:"text-gray-2 text-xs font-normal leading-tight"},kl=Ye({__name:"HeaderAvQuery",props:{flightSearchFlag:{},cityOrAirport:{},historyQueryForm:{},avQueryFromFastQuery:{type:Boolean}},emits:["searchClick","searchRoundClick","isPnrExtraction","getRoundTripFlag"],setup(e,{expose:a,emit:i}){const r=e,c=i,{queryFormRef:n,queryForm:l,FORM_RULES:v,revertFromTo:d,datePrefix:y,resetClick:p,roundTripSwitch:h,seamlessOrDaClick:S,search:k,onlyDirectFlightAshing:T,agentAirportOriginRef:V,agentAirportDestinationRef:b,chooseItems:G,selectedItem:w,handleSelectItem:E,checkCompany:D,changeCompanyRadio:I}=nl(c,r);return a({search:k}),(m,C)=>{const A=Ut,_=je,P=Pt,U=ia,K=sa,Y=wn,ne=In,oe=En,Z=Ht;return u(),$("div",il,[f(Z,{ref_key:"queryFormRef",ref:n,class:"header-av-query-form","label-position":"top",model:t(l),rules:t(v)},{default:x(()=>[f(A,{class:ye(["agent-airport-item",m.avQueryFromFastQuery?"fast-query-airport-item":"airport-item"]),prop:"origin"},{default:x(()=>[f(Yt,{ref_key:"agentAirportOriginRef",ref:V,modelValue:t(l).origin,"onUpdate:modelValue":C[0]||(C[0]=H=>t(l).origin=H),name:t(l).originName,"onUpdate:name":C[1]||(C[1]=H=>t(l).originName=H),"is-agent-city":m.cityOrAirport,"prefix-title":m.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1},8,["class"]),o("div",{class:"mt-[6px] ml-[2px] mr-[2px] w-[14px] h-[14px] cursor-pointer",onClick:C[2]||(C[2]=(...H)=>t(d)&&t(d)(...H))},[f(_,{class:"sort-text"},{default:x(()=>[f(t(Da))]),_:1})]),f(A,{class:ye(["agent-airport-item",m.avQueryFromFastQuery?"fast-query-airport-item":"airport-item"]),prop:"destination"},{default:x(()=>[f(Yt,{ref_key:"agentAirportDestinationRef",ref:b,modelValue:t(l).destination,"onUpdate:modelValue":C[3]||(C[3]=H=>t(l).destination=H),name:t(l).destName,"onUpdate:name":C[4]||(C[4]=H=>t(l).destName=H),"is-agent-city":m.cityOrAirport,"prefix-title":m.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1},8,["class"]),f(A,{class:ye(["departure-date",t(h)?"round-trip-date":""]),prop:"departureDate"},{default:x(()=>[f(Fa,{modelValue:t(l).departureDate,"onUpdate:modelValue":C[5]||(C[5]=H=>t(l).departureDate=H),roundTripSwitch:t(h),"onUpdate:roundTripSwitch":C[6]||(C[6]=H=>rt(h)?h.value=H:null),type:t(h)?"daterange":"date","show-switch":!1,clearable:!1,"prefix-icon":t(y),placeholder:m.$t("app.avSearch.date")},null,8,["modelValue","roundTripSwitch","type","prefix-icon","placeholder"])]),_:1},8,["class"]),f(A,{prop:"airlines",class:"airlines-input-item"},{default:x(()=>[o("div",rl,[o("div",{class:ye(["airlines-item-box",t(l).seamlessOrDa==="DA"?"radio-selected":"radio-cancel"]),onClick:C[7]||(C[7]=H=>t(S)("DA"))},ll,2),f(Ha,{modelValue:t(l).airlines,"onUpdate:modelValue":C[8]||(C[8]=H=>t(l).airlines=H),modelModifiers:{trim:!0},class:"airlines-input","tip-text":"CA",placeholder:m.$t("app.avSearch.airline")},null,8,["modelValue","placeholder"])])]),_:1}),f(A,{prop:"flightNumber"},{default:x(()=>[f(Ha,{modelValue:t(l).flightNumber,"onUpdate:modelValue":C[9]||(C[9]=H=>t(l).flightNumber=H),modelModifiers:{trim:!0},class:"flightNumber-input","tip-text":"CA1234",placeholder:m.$t("app.avSearch.flightNumber")},null,8,["modelValue","placeholder"])]),_:1}),f(A,{prop:"departureDateTime"},{default:x(()=>[f(P,{modelValue:t(l).departureDateTime,"onUpdate:modelValue":C[10]||(C[10]=H=>t(l).departureDateTime=H),class:"small-width",placeholder:m.$t("app.avSearch.departureTime"),onInput:C[11]||(C[11]=H=>t(l).departureDateTime=t(l).departureDateTime.toUpperCase().trim())},null,8,["modelValue","placeholder"])]),_:1}),f(A,{class:"transit-terminal input-focus-tip",prop:"transitTerminal"},{default:x(()=>[f(Ir,{modelValue:t(l).transitTerminal,"onUpdate:modelValue":C[12]||(C[12]=H=>t(l).transitTerminal=H),modelModifiers:{trim:!0},"is-agent-city":m.cityOrAirport},null,8,["modelValue","is-agent-city"])]),_:1}),f(A,null,{default:x(()=>[o("div",cl,[o("div",ul,[f(U,{modelValue:t(l).carrierFlight,"onUpdate:modelValue":C[13]||(C[13]=H=>t(l).carrierFlight=H)},{default:x(()=>[me(g(m.$t("app.avSearch.nonSharedFlights")),1)]),_:1},8,["modelValue"]),f(U,{modelValue:t(l).onlyDirectFlight,"onUpdate:modelValue":C[14]||(C[14]=H=>t(l).onlyDirectFlight=H),disabled:t(T)},{default:x(()=>[me(g(m.$t("app.avSearch.onlyFight")),1)]),_:1},8,["modelValue","disabled"])])])]),_:1}),f(A,null,{default:x(()=>[o("div",dl,[o("div",pl,[f(U,{modelValue:t(l).lowestPrice,"onUpdate:modelValue":C[15]||(C[15]=H=>t(l).lowestPrice=H)},{default:x(()=>[me(g(m.$t("app.avSearch.lowestPrice")),1)]),_:1},8,["modelValue"])]),o("div",gl,[f(U,{modelValue:t(l).timeSequence,"onUpdate:modelValue":C[16]||(C[16]=H=>t(l).timeSequence=H)},{default:x(()=>[me(g(m.$t("app.avSearch.timeSequence")),1)]),_:1},8,["modelValue"])])])]),_:1}),f(A,null,{default:x(()=>[o("div",fl,[o("div",ml,[f(U,{modelValue:t(D).S,"onUpdate:modelValue":C[17]||(C[17]=H=>t(D).S=H),label:"S",size:"large",onChange:C[18]||(C[18]=H=>t(I)("S"))},{default:x(()=>[me(g(m.$t("app.avSearch.onlyStar")),1)]),_:1},8,["modelValue"])]),o("div",vl,[f(U,{modelValue:t(D).T,"onUpdate:modelValue":C[19]||(C[19]=H=>t(D).T=H),label:"T",size:"large",onChange:C[20]||(C[20]=H=>t(I)("T"))},{default:x(()=>[me(g(m.$t("app.avSearch.onlySkyteam")),1)]),_:1},8,["modelValue"])])])]),_:1}),f(A,null,{default:x(()=>[o("div",hl,[o("div",yl,[f(U,{modelValue:t(D).W,"onUpdate:modelValue":C[21]||(C[21]=H=>t(D).W=H),label:"W",size:"large",onChange:C[22]||(C[22]=H=>t(I)("W"))},{default:x(()=>[me(g(m.$t("app.avSearch.onlyOneworld")),1)]),_:1},8,["modelValue"])])])]),_:1}),f(A,null,{default:x(()=>[f(K,{type:"primary",onClick:t(k)},{default:x(()=>[me(g(m.$t("app.avSearch.searchText")),1)]),_:1},8,["onClick"])]),_:1}),f(A,null,{default:x(()=>[f(K,{class:"ml-[8px]",onClick:t(p)},{default:x(()=>[me(g(m.$t("app.fastQuery.headerQuery.reset")),1)]),_:1},8,["onClick"])]),_:1}),f(A,null,{default:x(()=>[o("div",bl,[o("div",_l,[f(oe,{"popper-class":"av-history-popper",onCommand:t(E)},{dropdown:x(()=>[f(ne,null,{default:x(()=>[(u(!0),$(he,null,Ie(t(G),(H,O)=>(u(),Se(Y,{key:O,command:O},{default:x(()=>[t(w)===O?(u(),$("span",Dl,[f(_,{class:"bg-inherit !text-brand-2 !text-[14px]"},{default:x(()=>[f(t(nn))]),_:1}),o("span",$l,g(H),1)])):(u(),$("span",xl,[Sl,o("span",Tl,g(H),1)]))]),_:2},1032,["command"]))),128))]),_:1})]),default:x(()=>[f(K,{link:"",type:"primary",class:"service-book-btn"},{default:x(()=>[Cl,o("span",Al,g(m.$t("app.fastQuery.headerQuery.historyQuery")),1)]),_:1})]),_:1},8,["onCommand"])])])]),_:1})]),_:1},8,["model","rules"])])}}});const Fl=et(kl,[["__scopeId","data-v-850c0a23"]]),Ll=(e,a)=>{const i=ta(),{flightByDate:r}=Ft(i),c=B(),n=ct([]),{activeTag:l}=kt(),v=e.avQueryFromFastQuery?wt:l,d=Ee(()=>{var w;return((w=r.value[v])==null?void 0:w.activeFlightIndex)??0}),y=Ee(()=>{const w=r.value[v].flightDataList;return n.value=S(w),w}),p=Ee(()=>e.roundTripFlag??""),h=(w,E)=>{let D=w;if(D.includes(E[1])){const I=D.filter(m=>m!==E[1]);D=[E[1],...I]}if(D.includes(E[0])){const I=D.filter(m=>m!==E[0]);D=[E[0],...I]}return D},S=w=>{var m,C;const E=[],D=p.value?(C=((m=p.value)==null?void 0:m.split("/"))??[])==null?void 0:C.filter(A=>A.trim()!==""):[];return(p.value?h([...w.keys()],D):[...w.keys()]).forEach(A=>{var P;const _={};_.departureAirportCode=A.substring(0,3),_.arrivalAirportCode=A.substring(3,6),_.departureDate=A.substring(6),_.firstQueryDate=(P=w.get(A))==null?void 0:P.firstQueryDate,E.push(_)}),E},k=w=>`${w.departureAirportCode??""}${w.arrivalAirportCode??""}${w.departureDate??""}`,T=ba((w,E)=>{d.value!==E&&(i.setActiveFlightIndex(v,E),c.value=k(w),a("queryData",k(w)))},100,{trailing:!1}),V=w=>{const E=n.value[w],D=k(E),I=[...y.value.keys()][d.value];i.delFlightDataList(v,D);const m=[...y.value.keys()];if(m.length>=1)if(d.value===w)i.setActiveFlightIndex(v,0),a("queryData",m[0]);else{const C=m.findIndex(A=>A===I);i.setActiveFlightIndex(v,C)}else a("queryData","");a("closeAVHistory",`${E.departureAirportCode}${E.arrivalAirportCode}${(E==null?void 0:E.firstQueryDate)??""}`)},b=(w,E)=>{const D=_=>se(_).isValid()?se(_).format("YYYY-MM-DD"):"",I=(_,P,U)=>{const K=D(U);return`${_??""}${P??""}${K}`},m=I(w.departureAirportCode,w.arrivalAirportCode,E),C=n.value.findIndex(_=>m===I(_.departureAirportCode,_.arrivalAirportCode,_.departureDate));if(C>-1){T(n.value[C],C);return}const A=k(w);a("callQueryApi",A,Object.assign({},w,{departureDate:D(E)}))},G=()=>{var E;const w=(E=r.value[v])==null?void 0:E.flightDataList;w&&(n.value=S(w),n.value.length&&a("queryData",k(n.value[d.value])))};return Xe(()=>e.searchCompleteKey,()=>{if(!e.searchCompleteKey)return;const w=e.searchCompleteKey.split("+")[0];if(w===c.value)return;const E=[...y.value.keys()].indexOf(w);i.setActiveFlightIndex(v,E),E<0&&i.setActiveFlightIndex(v,n.value.length)},{immediate:!0}),bt(()=>{G()}),Rt(()=>{n.value.length=0}),{flightInfos:n,activeFlightIndex:d,flightClick:T,changeDate:b,clickClose:V}},Nl=(e,a)=>{const{activeTag:i}=kt(),r=ta(),{flightByDate:c}=Ft(r),n=B(a.segFlight.departureDate??""),l=()=>se(n.value).format("ddd").toUpperCase(),v=B(l()),d=ct({render(){return ht("em",{class:"iconfont icon-calendar"})}}),y=a.isFastQuery?wt:i,p=Ee(()=>{var b;return((b=c.value[y])==null?void 0:b.activeFlightIndex)??0}),h=Ee(()=>p.value===a.segIndex?a.segFlight.firstQueryDate===a.segFlight.departureDate?"active-blue-box":"active-red-box":""),S=()=>{e("changeDate",n.value),n.value=a.segFlight.departureDate},k=b=>se(b).isBefore(se(new Date),"date"),T=b=>se(b).isValid()?se(b).format("MM-DD"):"",V=()=>{e("clickClose",a.segIndex)};return Xe(()=>a.segFlight.departureDate,()=>{n.value=a.segFlight.departureDate,v.value=l()}),{clickClose:V,datePrefix:d,changeDate:S,dayOfWeek:v,activeStyle:h,formatDate:T,departureDate:n,disabledDate:k,activeFlightIndex:p}},wl=e=>(It("data-v-75a19b61"),e=e(),Et(),e),Il={class:"flex"},El={class:"flex font-bold text-[14px] text-[#676767] code"},Pl=wl(()=>o("span",null,"-",-1)),Rl={class:"flex items-center text-[12px] text-[#8C8C8C] font-normal"},Ol={class:"date-text mx-1.5 whitespace-nowrap"},Ql={class:"date-text mx-1.5 whitespace-nowrap"},Ml=Ye({__name:"FlightInfo",props:{segIndex:{},segFlight:{},isFastQuery:{type:Boolean}},emits:["clickClose","changeDate"],setup(e,{emit:a}){const i=e,r=a,{clickClose:c,changeDate:n,dayOfWeek:l,activeStyle:v,departureDate:d,datePrefix:y,formatDate:p,disabledDate:h,activeFlightIndex:S}=Nl(r,i);return(k,T)=>{const V=fo,b=je;return u(),$("div",null,[o("div",{class:ye(["flex justify-between items-center mr-[10px] pl-[10px] pr-[10px] h-[32px] border border-[#D9D9D9] rounded cursor-pointer",t(v)])},[o("div",Il,[o("div",El,[o("div",null,g(k.segFlight.departureAirportCode??""),1),Pl,o("div",null,g(k.segFlight.arrivalAirportCode??""),1)]),o("div",Rl,[o("div",Ol,g(t(p)(k.segFlight.departureDate)),1),o("div",Ql,g(t(l)),1),o("div",{class:"mr-[2px] h-[15px] date-icon flex items-center justify-center",onClick:T[1]||(T[1]=Vt(()=>{},["stop"]))},[f(V,{modelValue:t(d),"onUpdate:modelValue":T[0]||(T[0]=G=>rt(d)?d.value=G:null),"prefix-icon":t(y),type:"date",clearable:!1,disabled:t(S)!==k.segIndex,editable:!1,"disabled-date":t(h),format:"YYYY-MM-DD",onChange:t(n)},null,8,["modelValue","prefix-icon","disabled","disabled-date","onChange"])])])]),o("div",{class:"flex items-center",onClick:T[2]||(T[2]=Vt((...G)=>t(c)&&t(c)(...G),["stop"]))},[f(b,null,{default:x(()=>[f(t(ao),{class:"text-[#676767]"})]),_:1})])],2)])}}});const Vl=et(Ml,[["__scopeId","data-v-75a19b61"]]),Bl={key:0},Yl={class:"flex"},ql=Ye({__name:"AvHistory",props:{roundTripFlag:{},searchCompleteKey:{},queryForm:{},avQueryFromFastQuery:{type:Boolean}},emits:["queryData","callQueryApi","closeAVHistory"],setup(e,{emit:a}){const i=e,r=a,{flightInfos:c,flightClick:n,clickClose:l,changeDate:v}=Ll(i,r);return(d,y)=>{const p=mo;return t(c).length>0?(u(),$("div",Bl,[f(p,null,{default:x(()=>[o("div",Yl,[(u(!0),$(he,null,Ie(t(c),(h,S)=>(u(),Se(Vl,{key:S,"seg-flight":h,"seg-index":S,"is-fast-query":d.avQueryFromFastQuery,onClick:k=>t(n)(h,S),onChangeDate:k=>t(v)(h,k),onClickClose:t(l)},null,8,["seg-flight","seg-index","is-fast-query","onClick","onChangeDate","onClickClose"]))),128))])]),_:1})])):ge("",!0)}}}),Co="confirm",Ca=[{startTime:"00:00",endTime:"06:00",label:"time_0"},{startTime:"06:00",endTime:"12:00",label:"time_6"},{startTime:"12:00",endTime:"18:00",label:"time_12"},{startTime:"18:00",endTime:"24:00",label:"time_18"}],Ga=(e,a)=>{e.sort((i,r)=>{let c=0,n=0;return i.segments.forEach(l=>{c+=Number(l.flightTime.replaceAll(":",""))}),r.segments.forEach(l=>{n+=Number(l.flightTime.replaceAll(":",""))}),a?c-n:n-c})},Ao=(e,a,i)=>{const r=se(new Date).format("YYYY-MM-DD"),c=`${r} ${e}`,n=`${r} ${a}`,l=`${r} ${i}`;return(se(c).isSame(se(l))||se(l).isAfter(se(c)))&&se(l).isBefore(se(n))},Do=(e,a,i)=>{var r;if((r=e.segments)!=null&&r.length){const c=e.segments[0].departureTime;return Ao(a,i,c)}return!1},$o=(e,a,i)=>{var r;if((r=e.segments)!=null&&r.length){const c=e.segments[e.segments.length-1].arrivalTime;return Ao(a,i,c)}return!1},Ul=(e,a)=>{let i=[];if(e.filterDepartureTime.length){const r=Ca.filter(c=>e.filterDepartureTime.includes(c.label));i=a.filter(c=>r.some(n=>Do(c,n.startTime,n.endTime)))}else i=a;if(e.filterArrivalTime.length){const r=Ca.filter(c=>e.filterArrivalTime.includes(c.label));i=i.filter(c=>r.some(n=>$o(c,n.startTime,n.endTime)))}return i},Hl=(e,a)=>{const{t:i}=gt(),r=B(!1),c=ct(ma),n=B(ma),l=B([]),v=B([]),d=B({filterAirlines:""}),y={filterAirlines:[{pattern:Va,message:new Error(i("app.avHistoryFlightCondition.enterAirlineCode")),trigger:"change"}]},p=B([]),h=B(!1),S=B([]),k=B({label:i("app.avHistoryFlightCondition.airport"),screenPopoverData:[]}),T=B({label:i("app.avHistoryFlightCondition.timeDepartureAndArrival"),screenPopoverData:[]}),V=B(!1),b=0,G=1,w=i("app.avHistoryFlightCondition.departureAirport"),E=i("app.avHistoryFlightCondition.arrivalAirport"),D=i("app.avHistoryFlightCondition.departureTime"),I=i("app.avHistoryFlightCondition.arrivelTime"),m=ha({sortType:-1,ascNum:-1});let C={};const A=()=>{var j,W;const z=[];if(((j=e.moreDeparture)==null?void 0:j.length)>1){const ue=e.moreDeparture.map(Le=>({label:Le,value:Le}));z.push({label:w,codes:ue,checked:[]})}if(((W=e.moreArrival)==null?void 0:W.length)>1){const ue=e.moreArrival.map(Le=>({label:Le,value:Le}));z.push({label:E,codes:ue,checked:[]})}return z},_=z=>{const j=[],W=[];return Ca.forEach(ue=>{const Le=z==null?void 0:z.find(Oe=>Do(Oe,ue.startTime,ue.endTime)),$e=z==null?void 0:z.find(Oe=>$o(Oe,ue.startTime,ue.endTime));Le&&j.push(ue.label),$e&&W.push(ue.label)}),{departureFlightTimeCodes:j,arrivalFlightTimeCodes:W}},P=()=>{const z=[],{departureFlightTimeCodes:j,arrivalFlightTimeCodes:W}=_(e.avSearchData),ue=Le=>Le.map($e=>({label:i(`app.avHistoryFlightCondition.${$e}`),value:$e}));return j.length>1&&z.push({label:D,codes:ue(j),checked:[]}),W.length>1&&z.push({label:I,codes:ue(W),checked:[]}),z},U=()=>se().format("YYYY-MM-DD"),K=()=>{l.value=C.filterCondition},Y=()=>{p.value=C.transferTimes},ne=()=>{var z,j,W,ue,Le,$e,Oe,Ae;C={filterCondition:l.value,sortTypeNum:m.ascNum,filterDeparture:((j=(z=k.value.screenPopoverData)==null?void 0:z.find(Pe=>Pe.label===w))==null?void 0:j.checked)??[],filterArrival:((ue=(W=k.value.screenPopoverData)==null?void 0:W.find(Pe=>Pe.label===E))==null?void 0:ue.checked)??[],transferTimes:p.value,filterDepartureTime:(($e=(Le=T.value.screenPopoverData)==null?void 0:Le.find(Pe=>Pe.label===D))==null?void 0:$e.checked)??[],filterArrivalTime:((Ae=(Oe=T.value.screenPopoverData)==null?void 0:Oe.find(Pe=>Pe.label===I))==null?void 0:Ae.checked)??[]},fe(),a("sortFlight",C)},oe=()=>{r.value=!1,h.value=!1,ne()},Z=()=>{l.value=[],v.value=[],d.value.filterAirlines="",oe()},H=()=>{p.value=[],oe()},O=z=>{k.value=z,oe()},L=z=>{T.value=z,oe()},R=z=>ma.filter(j=>z.includes(j.code)),N=()=>{const z=[];(e.avSearchData??[]).forEach(j=>{var W,ue;(W=j.segments)!=null&&W.length&&((ue=j.flyType)==null||ue.forEach(Le=>z.includes(Le)||z.push(Le)))}),S.value=[...z].sort((j,W)=>j===Qt.STOP?1:W===Qt.STOP?-1:j-W).map(j=>({label:j.toString(),value:j.toString()}))},F=z=>z===Qt.STOP.toString()?i("app.avHistoryFlightCondition.transferTimes_stop"):i(`app.avHistoryFlightCondition.transferTimes_${z}`),ae=(z,j)=>{var W;(W=z.flyType)==null||W.forEach(ue=>j.includes(ue)||j.push(ue))},le=()=>{const z=[],j=[],W=[],ue=[];e.currentFlightList.map(Ae=>{Ae.segments.map(Ge=>{var ot,st,Be;z.includes((ot=Ge.airlines)==null?void 0:ot.airCode)||(st=Ge.airlines)!=null&&st.airCode&&z.push((Be=Ge.airlines)==null?void 0:Be.airCode)});const Pe=Ae.segments;if(Pe.length){ae(Ae,j);const Ge=Pe[0],ot=Pe[Pe.length-1];W.includes(Ge.departureAirportCode)||W.push(Ge.departureAirportCode??""),ue.includes(ot.arrivalAirportCode)||ue.push(ot.arrivalAirportCode??"")}});const{departureFlightTimeCodes:Le,arrivalFlightTimeCodes:$e}=_(e.currentFlightList);n.value.forEach(Ae=>Ae.disabled=!l.value.includes(Ae.code)&&!z.includes(Ae.code)),S.value.forEach(Ae=>Ae.disabled=!p.value.includes(Ae.value)&&!j.includes(Number(Ae.value)));const Oe=(Ae,Pe)=>{Ae.codes.forEach(Ge=>Ge.disabled=!Ae.checked.includes(Ge.value)&&!Pe.includes(Ge.value))};T.value.screenPopoverData.forEach(Ae=>{Ae.label===D&&Oe(Ae,Le),Ae.label===I&&Oe(Ae,$e)}),k.value.screenPopoverData.forEach(Ae=>{Ae.label===w&&Oe(Ae,W),Ae.label===E&&Oe(Ae,ue)})},de=()=>{p.value=[],e.queryFormParams.onlyDirectFlight&&p.value.push("0"),N(),c.value=e.airlines&&e.airlines.length?R(e.airlines):[],n.value=e.airlines&&e.airlines.length?R(e.airlines):[],l.value=[],v.value=[],m.sortType=-1,m.ascNum=-1,k.value.screenPopoverData=A(),T.value.screenPopoverData=P()},fe=()=>{v.value=R(l.value)},te=z=>{const j=e.queryResDateGroupBySessionId.get(`${e.queryFormParams.origin}${e.queryFormParams.destination}${e.queryFormParams.departureDate}`)||e.queryFormParams.departureDate;return!(z==="pre"&&j===U())},Te=z=>{a("changeDate",z)},be=z=>{if(V.value=!0,m.sortType=z,z)switch(m.ascNum){case 2:m.ascNum=3;break;case 3:m.ascNum=-1;break;default:m.ascNum=2;break}else switch(m.ascNum){case 0:m.ascNum=1;break;case 1:m.ascNum=-1;break;default:m.ascNum=0;break}ne()},Re=()=>{var z,j,W,ue,Le,$e,Oe,Ae;C={filterCondition:l.value,sortTypeNum:m.ascNum,filterDeparture:((j=(z=k.value.screenPopoverData)==null?void 0:z.find(Pe=>Pe.label===w))==null?void 0:j.checked)??[],filterArrival:((ue=(W=k.value.screenPopoverData)==null?void 0:W.find(Pe=>Pe.label===E))==null?void 0:ue.checked)??[],transferTimes:p.value,filterDepartureTime:(($e=(Le=T.value.screenPopoverData)==null?void 0:Le.find(Pe=>Pe.label===D))==null?void 0:$e.checked)??[],filterArrivalTime:((Ae=(Oe=T.value.screenPopoverData)==null?void 0:Oe.find(Pe=>Pe.label===I))==null?void 0:Ae.checked)??[]}},X=z=>{d.value.filterAirlines=z==null?void 0:z.toUpperCase(),Va.test(z)&&(n.value=c.value.filter(j=>j.code.includes(z==null?void 0:z.toUpperCase())),l.value=[])},re=()=>{d.value.filterAirlines="",n.value=c.value};return bt(()=>{de(),Re()}),Xe(()=>e.airlines,()=>{de(),Re()}),Xe(()=>e.updateFlightListFlag,()=>{le()}),Rt(()=>{c.value.length=0,n.value.length=0,l.value.length=0,v.value.length=0}),{isAllowChangeDate:te,changeDate:Te,popoverVisible:r,listSort:m,canChooseAirlines:c,checkedAirlines:l,filterForm:d,FORM_RULES:y,tipAirlinesObj:v,FLY_TIME_SORT:G,DEPARTURE_TIME_SORT:b,openPopover:K,sortClick:be,checkAirlines:fe,reset:Z,screeningAirlines:oe,isPermitSortByTime:V,confirmAirportDepartureAndArrival:O,confirmTimeDepartureAndArrival:L,airportDepartureAndArrival:k,timeDepartureAndArrival:T,popoverTransferTimes:h,openPopoverTransferTimes:Y,resetPopoverTransferTimes:H,canChooseTransferTimes:S,checkedTransferTimes:p,changeFilterAirlines:X,filterCanChooseAirlines:n,getTransferTimes:F,showFilterPopver:re}},jl={class:"text-[12px] text-brand-2 cursor-pointer ml-4 whitespace-nowrap"},Gl={class:"popover-airlines"},Kl={class:"airport-title text-[12px] text-gray-1 font-normal mb-[5px]"},zl=["textContent"],Wl={class:"button-operator"},Jl=Ye({__name:"ScreenPopover",props:{screenPopover:{},width:{}},emits:["confirm"],setup(e,{emit:a}){const i=e,r=a,c=B(!1),n=B(i.screenPopover),l=()=>{n.value=it(i.screenPopover)},v=()=>{c.value=!1},d=()=>{v(),r("confirm",n.value)},y=()=>{n.value.screenPopoverData.forEach(p=>p.checked=[]),v(),r("confirm",n.value)};return(p,h)=>{const S=je,k=ia,T=go,V=At;return u(),Se(V,{visible:c.value,"onUpdate:visible":h[0]||(h[0]=b=>c.value=b),trigger:"click",placement:"bottom-start","show-arrow":!1,width:p.width,"popper-class":"airport-popper"},{reference:x(()=>[o("div",jl,[o("span",{onClick:l},g(n.value.label),1),c.value?(u(),Se(S,{key:1,class:"ml-[4px]"},{default:x(()=>[f(t(Bt))]),_:1})):(u(),Se(S,{key:0,class:"ml-[4px]",onClick:l},{default:x(()=>[f(t(Wt))]),_:1}))])]),default:x(()=>[o("div",Gl,[(u(!0),$(he,null,Ie(n.value.screenPopoverData,(b,G)=>(u(),$("div",{key:b.label,class:ye(["popover-airport py-[8px]",G?"border-t border-gray-6":""])},[o("div",Kl,g(b.label),1),f(T,{modelValue:b.checked,"onUpdate:modelValue":w=>b.checked=w},{default:x(()=>[(u(!0),$(he,null,Ie(b.codes,(w,E)=>(u(),Se(k,{key:E,label:w.value,disabled:w.disabled},{default:x(()=>[o("span",{class:"code-label inline-block w-[22px] text-xs text-gray-2",textContent:g(w.label)},null,8,zl)]),_:2},1032,["label","disabled"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])],2))),128)),o("div",Wl,[o("div",null,[o("span",{onClick:d},g(p.$t("app.avHistoryFlightCondition.sure")),1),o("span",{onClick:y},g(p.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible","width"])}}});const Ka=et(Jl,[["__scopeId","data-v-283a3918"]]),Zl={class:"mr-[6px] h-[40px] flex items-end"},Xl={class:"flex justify-between items-center h-[32px] px-2.5 w-full border-[1px] border-[#EBF2FF] rounded"},ec={class:"flex items-center h-[24px]"},tc={class:"mr-[14px] text-[12px] font-normal text-[#595959]"},ac={class:"flex mr-[14px]"},oc={class:"text-[12px] text-brand-2 cursor-pointer mr-4 whitespace-nowrap"},nc={class:"popover-airport"},ic={class:"code-label inline-block text-xs font-normal text-gray-2"},rc={class:"button-operator"},sc={class:"text-[12px] text-brand-2 cursor-pointer whitespace-nowrap"},lc={key:0},cc={class:"flex justify-center items-center flex-wrap w-[300px] text-[13px]"},uc={class:"popover-airlines"},dc={class:"w-[13px] h-[14px]","aria-hidden":"true"},pc=["xlink:href"],gc=["textContent"],fc=["textContent"],mc={class:"button-operator"},vc={class:"flex items-center h-[24px] whitespace-nowrap"},hc={class:"ml-[4px] flex flex-col sort-box"},yc={class:"ml-[4px] flex flex-col sort-box"},bc=Ye({__name:"FlightCondition",props:{avSearchData:{},airlines:{},queryFormParams:{},queryResDateGroupBySessionId:{},moreDeparture:{},moreArrival:{},currentFlightList:{},updateFlightListFlag:{type:Boolean}},emits:["screeningDirect","sortFlight","changeDate","isAllowChangeDate"],setup(e,{emit:a}){const i=e,r=a,{popoverVisible:c,listSort:n,filterCanChooseAirlines:l,checkedAirlines:v,filterForm:d,FORM_RULES:y,tipAirlinesObj:p,FLY_TIME_SORT:h,DEPARTURE_TIME_SORT:S,isPermitSortByTime:k,isAllowChangeDate:T,changeDate:V,sortClick:b,openPopover:G,reset:w,screeningAirlines:E,airportDepartureAndArrival:D,timeDepartureAndArrival:I,confirmAirportDepartureAndArrival:m,confirmTimeDepartureAndArrival:C,popoverTransferTimes:A,openPopoverTransferTimes:_,resetPopoverTransferTimes:P,canChooseTransferTimes:U,checkedTransferTimes:K,getTransferTimes:Y,changeFilterAirlines:ne,showFilterPopver:oe}=Hl(i,r);return(Z,H)=>{var le,de,fe;const O=go,L=_t,R=Pt,N=Ut,F=Ht,ae=Pn;return u(),$("div",Zl,[o("div",Xl,[o("div",ec,[o("span",tc,g(Z.$t("app.avHistoryFlightCondition.total",{total:((le=Z.avSearchData)==null?void 0:le.length)??0})),1),o("div",ac,[o("span",{class:ye(["mr-[10px] operate",{"disable-to-operate":!t(T)("pre")}]),"data-gid":"01010209",onClick:H[0]||(H[0]=te=>t(V)("pre"))},g(Z.$t("app.avHistoryFlightCondition.previousDay")),3),o("span",{class:ye(["operate",{"disable-to-operate":!t(T)("next")}]),"data-gid":"01010210",onClick:H[1]||(H[1]=te=>t(V)("next"))},g(Z.$t("app.avHistoryFlightCondition.nextDay")),3)]),f(t(At),{visible:t(A),"onUpdate:visible":H[6]||(H[6]=te=>rt(A)?A.value=te:null),trigger:"click",placement:"bottom-start","show-arrow":!1,"popper-class":"airport-popper-av transfer-proper"},{reference:x(()=>[o("div",oc,[o("span",{onClick:H[2]||(H[2]=(...te)=>t(_)&&t(_)(...te))},g(Z.$t("app.avHistoryFlightCondition.directFlight")),1),t(A)?(u(),Se(t(je),{key:1,class:"ml-[4px]"},{default:x(()=>[f(t(Bt))]),_:1})):(u(),Se(t(je),{key:0,class:"ml-[4px]",onClick:t(_)},{default:x(()=>[f(t(Wt))]),_:1},8,["onClick"]))])]),default:x(()=>[o("div",nc,[f(O,{modelValue:t(K),"onUpdate:modelValue":H[3]||(H[3]=te=>rt(K)?K.value=te:null),class:"flex flex-col"},{default:x(()=>[(u(!0),$(he,null,Ie(t(U),te=>(u(),Se(t(ia),{key:te.value,label:te.value,disabled:te.disabled},{default:x(()=>[o("span",ic,g(t(Y)(te.value)),1)]),_:2},1032,["label","disabled"]))),128))]),_:1},8,["modelValue"]),o("div",rc,[o("div",null,[o("span",{onClick:H[4]||(H[4]=(...te)=>t(E)&&t(E)(...te))},g(Z.$t("app.avHistoryFlightCondition.sure")),1),o("span",{onClick:H[5]||(H[5]=(...te)=>t(P)&&t(P)(...te))},g(Z.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible"]),f(t(At),{visible:t(c),"onUpdate:visible":H[13]||(H[13]=te=>rt(c)?c.value=te:null),trigger:"click",placement:"bottom-start","show-arrow":!1,width:500,onShow:t(oe)},{reference:x(()=>[o("div",sc,[t(p).length>0?(u(),$("span",lc,[f(L,{effect:"dark","popper-class":"airlineTip",placement:"bottom"},{content:x(()=>[o("div",cc,[(u(!0),$(he,null,Ie(t(p),te=>(u(),$("span",{key:te.code,class:"flex items-center w-[40%] p-[7px]"},[o("span",{class:ye([["iconfont",te.icon],"inline-block mr-[10px]"])},null,2),me(" "+g(te.code)+"-"+g(te.name),1)]))),128))])]),default:x(()=>[o("span",{onClick:H[7]||(H[7]=(...te)=>t(G)&&t(G)(...te))},g(Z.$t("app.avHistoryFlightCondition.airlineScreening"))+" "+g(t(p).length??""),1)]),_:1})])):(u(),$("span",{key:1,onClick:H[8]||(H[8]=(...te)=>t(G)&&t(G)(...te))},g(Z.$t("app.avHistoryFlightCondition.airlineScreening")),1)),t(c)?(u(),Se(t(je),{key:3,class:"ml-[4px]"},{default:x(()=>[f(t(Bt))]),_:1})):(u(),Se(t(je),{key:2,class:"ml-[4px]",onClick:t(G)},{default:x(()=>[f(t(Wt))]),_:1},8,["onClick"]))])]),default:x(()=>[o("div",uc,[o("div",null,[f(F,{ref:"filterFormRef",class:"header-av-query-form","label-position":"top",model:t(d),rules:t(y)},{default:x(()=>[f(N,{prop:"filterAirlines"},{default:x(()=>[f(R,{modelValue:t(d).filterAirlines,"onUpdate:modelValue":H[9]||(H[9]=te=>t(d).filterAirlines=te),placeholder:Z.$t("app.avHistoryFlightCondition.supportInput"),onInput:t(ne)},null,8,["modelValue","placeholder","onInput"])]),_:1})]),_:1},8,["model","rules"])]),f(O,{modelValue:t(v),"onUpdate:modelValue":H[10]||(H[10]=te=>rt(v)?v.value=te:null)},{default:x(()=>[(u(!0),$(he,null,Ie(t(l),te=>(u(),Se(t(ia),{key:te.code,label:te.code,disabled:te.disabled},{default:x(()=>[(u(),$("svg",dc,[o("use",{"xlink:href":te.logo},null,8,pc)])),te.name.length>4?(u(),Se(L,{key:0,effect:"dark",content:te.name,placement:"top"},{default:x(()=>[o("span",{class:"inline-block ml-[4px] w-[160px] truncate",textContent:g(`${te.code}-${te.name}`)},null,8,gc)]),_:2},1032,["content"])):(u(),$("span",{key:1,class:"inline-block ml-[4px] w-[160px]",textContent:g(`${te.code}-${te.name}`)},null,8,fc))]),_:2},1032,["label","disabled"]))),128))]),_:1},8,["modelValue"]),o("div",mc,[o("div",null,[o("span",{onClick:H[11]||(H[11]=(...te)=>t(E)&&t(E)(...te))},g(Z.$t("app.avHistoryFlightCondition.sure")),1),o("span",{onClick:H[12]||(H[12]=(...te)=>t(w)&&t(w)(...te))},g(Z.$t("app.avHistoryFlightCondition.reset")),1)])])])]),_:1},8,["visible","onShow"]),(de=t(I).screenPopoverData)!=null&&de.length?(u(),Se(Ka,{key:0,"screen-popover":t(I),width:120,onConfirm:t(C)},null,8,["screen-popover","onConfirm"])):ge("",!0),(fe=t(D).screenPopoverData)!=null&&fe.length?(u(),Se(Ka,{key:1,"screen-popover":t(D),width:80,onConfirm:t(m)},null,8,["screen-popover","onConfirm"])):ge("",!0)]),o("div",vc,[f(ae,{underline:!1,class:"mr-[14px]",onClick:H[14]||(H[14]=te=>t(b)(t(S)))},{default:x(()=>[o("span",{class:ye(["text-[12px]",t(n).sortType===0&&t(k)?"sort-active":""])},g(Z.$t("app.avHistoryFlightCondition.departureTime")),3),o("div",hc,[f(t(je),null,{default:x(()=>[f(t(Oa),{class:ye(["h-[6px] leading-[6px]",t(n).ascNum===0?"text-brand-2":"text-gray-5"])},null,8,["class"])]),_:1}),f(t(je),null,{default:x(()=>[f(t(Qa),{class:ye(["h-[6px] leading-[6px]",t(n).ascNum===1?"text-brand-2":"text-gray-5"])},null,8,["class"])]),_:1})])]),_:1}),f(ae,{underline:!1,onClick:H[15]||(H[15]=te=>t(b)(t(h)))},{default:x(()=>[o("span",{class:ye(["text-[12px]",t(n).sortType===1&&t(k)?"sort-active":""])},g(Z.$t("app.avHistoryFlightCondition.flyTime")),3),o("div",yc,[f(t(je),null,{default:x(()=>[f(t(Oa),{class:ye(t(n).ascNum===2?"text-brand-2":"text-gray-5")},null,8,["class"])]),_:1}),f(t(je),null,{default:x(()=>[f(t(Qa),{class:ye(t(n).ascNum===3?"text-brand-2":"text-gray-5")},null,8,["class"])]),_:1})])]),_:1})])])])}}});const _c=et(bc,[["__scopeId","data-v-768517f4"]]),Cc=(e,a)=>{const{t:i}=gt(),r=B(!1),c=B(i("app.avSearch.quantityBooked")),n=ct([]);return{bookedNumStr:c,bookedTypeList:n,queryBookedType:async()=>{const v={flightNo:e.flightNo,option:"INFT,AVIH,WCHC,UMNR",departureDate:e!=null&&e.departureDate?se(e.departureDate).format("YYYY-MM-DD"):"",departureTime:e.departureDate?se(e.departureDate).format("HHmm"):""};try{r.value=!1,a("changeLoading",!0);const{data:d}=await vi(v,"01020108"),y=(d==null?void 0:d.value)??[];n.value=y.map(p=>{var h;return{name:p.option,counts:((h=p==null?void 0:p.quotaNumber)==null?void 0:h.split(" "))??["-"]}}),r.value=!0}catch{n.value=[]}finally{a("changeLoading",!1)}},popoverVisible:r}},Ac={class:"text-gray-2 text-xs font-normal leading-tight"},Dc={class:"text-center text-gray-3 text-xs font-normal leading-none"},$c={class:"text-brand-2 text-xs font-normal leading-tight"},xc=Ye({__name:"BookedPopover",props:{flightNo:{},departureDate:{}},emits:["changeLoading"],setup(e,{emit:a}){const i=e,r=a,{bookedNumStr:c,bookedTypeList:n,popoverVisible:l,queryBookedType:v}=Cc(i,r);return(d,y)=>{const p=je,h=At;return u(),Se(h,{visible:t(l),"onUpdate:visible":y[1]||(y[1]=S=>rt(l)?l.value=S:null),trigger:"hover","show-arrow":!1,disabled:t(n).length<=0,"popper-class":"booked-proper"},{reference:x(()=>[o("div",{class:ye(["w-[84px] h-5 px-0.5 rounded-sm justify-end items-center gap-1 inline-flex cursor-pointer",t(n).length>0?"bg-yellow-2":""]),onClick:y[0]||(y[0]=(...S)=>t(v)&&t(v)(...S))},[o("span",$c,g(t(c)),1),t(l)?(u(),Se(p,{key:1,class:"icon-drop"},{default:x(()=>[f(t(Bt))]),_:1})):(u(),Se(p,{key:0,class:"icon-drop"},{default:x(()=>[f(t(Wt))]),_:1}))],2)]),default:x(()=>[(u(!0),$(he,null,Ie(t(n),(S,k)=>(u(),$("div",{key:k,class:"self-stretch px-1.5 py-1 justify-start items-center gap-1.5 inline-flex"},[o("span",Ac,g(d.$t(`app.avSearch.${S.name}`)),1),(u(!0),$(he,null,Ie(S.counts,(T,V)=>(u(),$("span",{key:V,class:"h-4 px-1 bg-gray-7 rounded-sm justify-center items-center inline-flex"},[o("span",Dc,g(T),1)]))),128))]))),128))]),_:1},8,["visible","disabled"])}}});const Sc=et(xc,[["__scopeId","data-v-8a886d53"]]),xo=Ye({__name:"CopyButton",props:{segInfo:{},stopData:{}},setup(e){const a=e,{copy:i,isSupported:r}=rn({legacy:!0}),{t:c}=gt(),n=y=>y?Number(y):0,l=y=>y?` ${y.replace(":","")}`:"",v=(y=[])=>{let p=`
`;return y.forEach((h,S)=>{let k="";const T=`${c("app.fightSell.stopPoint")}${S+1}`,V=se(h.departureDate).isValid()?` ${se(h.departureDate).format("MM-DD")}`:"",b=` ${h.arrivalAirportCN}`,G=se(h.arrivalTime,"HH:mm").isValid()?` ${se(h.arrivalTime,"HH:mm").format("HHmm")}`:"",w=se(h.departureTime,"HH:mm").isValid()?` ${se(h.departureTime,"HH:mm").format("HHmm")}`:"",E=` ${c("app.avSearch.stop")}${ea((h==null?void 0:h.groundTime)??"")}`;k=`${T}${V}${b}${G}${w}${E}${S===y.length-1?"":`
`}`,p+=k}),p},d=()=>{if(!r||!a.segInfo)return;const{airlines:y}=a.segInfo,p=`${y.airCN}${y.airCode}${y.flightNo}`,h=y.isShared?` ${c("app.querySearch.carrierCompany")}${y.isShared}`:"",S=se(a.segInfo.departureDate).isValid()?` ${se(a.segInfo.departureDate).format("MM-DD")}`:"",k=` ${a.segInfo.departureAirportCN}${a.segInfo.departureTerminal??""}`,T=`${a.segInfo.arrivalAirportCN}${a.segInfo.arrivalTerminal??""}`,V=l(a.segInfo.departureTime),b=l(a.segInfo.arrivalTime),G=n(a.segInfo.arrivalArrdays),w=n(a.segInfo.deptArrdays),E=v(a.stopData);let D="";const I=G-w;I&&(D=I>0?`+${I}`:`-${I}`);const m=`${p}${h}${S}${k}—${T}${V}${b}${D}${E}`;i(m),$a({message:c("app.batchRefund.copySuccess"),type:"success"})};return(y,p)=>(u(),$("div",{class:"copy-btn bg-gray-0 rounded border border-brand-2 text-brand-2 p-[4px] cursor-pointer hover:bg-brand-4",onClick:d},g(y.$t("app.original.copy")),1))}}),Tc=e=>(It("data-v-fc328ba4"),e=e(),Et(),e),kc={class:"min-w-[343px] bg-gray-3 text-gray-0 p-2"},Fc={class:"flex items-center justify-between"},Lc={class:"mb-2 text-sm font-bold"},Nc={class:"flex items-center my-2 airlines-info"},wc={class:"flex items-center px-2 py-0"},Ic={class:"!w-6 !h-6 mr-1 text-xl icon svg-icon airline-icon","aria-hidden":"true"},Ec=["xlink:href"],Pc={class:"h-6 justify-start items-center gap-1 inline-flex"},Rc={class:"text-center text-gray-0 text-xs font-normal leading-tight"},Oc={class:"justify-start items-center gap-0.5 flex"},Qc={class:"text-center text-gray-0 text-xs font-normal leading-tight"},Mc={key:0,class:"px-1 h-5 bg-brand-3 rounded-sm justify-center items-center flex"},Vc={class:"text-center text-brand-1 text-xs font-normal leading-tight"},Bc=Tc(()=>o("div",{class:"split-label w-[1px] h-[14px] bg-gray-6"},null,-1)),Yc={class:"flex items-center px-2 py-0 mx-2 my-0 h-5 rounded-sm bg-brand-3 actual-carrier-box whitespace-nowrap"},qc={class:"mr-2 text-gray-2"},Uc={class:"text-brand-2"},Hc={key:1},jc={class:"mb-2 mr-2"},Gc={class:"mb-2 mr-2"},Kc=Ye({__name:"FlightInformationPopover",props:{segInfo:{},airportsDbData:{}},setup(e){const a=e,i=B(Zt()),r=Ee(()=>a.airportsDbData),c=B(""),n=B(""),l=(p,h,S,k)=>{const T=a.segInfo;T&&(c.value=`${p} ${S} ${T.departureAirportCode??"--"} ${T.departureAirportCN??"--"} ${T.departureTerminal??"--"} `,n.value=`${h} ${k} ${T.arrivalAirportCode??"--"} ${T.arrivalAirportCN??"--"} ${T.arrivalTerminal??"--"} `)},v=()=>{var T,V,b,G;let p="--",h="--";const S=((T=a.segInfo)==null?void 0:T.departureTime)||"--",k=((V=a.segInfo)==null?void 0:V.arrivalTime)||"--";a.segInfo&&(p=(b=a.segInfo)!=null&&b.departureDate?se(a.segInfo.departureDate).format("MM-DD"):"--",h=(G=a.segInfo)!=null&&G.arrivalDate?se(a.segInfo.arrivalDate).format("MM-DD"):"--"),l(p,h,S,k)},d=p=>{var h,S,k,T,V,b;return i.value==="en"?(k=(S=(h=r.value??[])==null?void 0:h.filter(G=>G.code===p))==null?void 0:S[0])==null?void 0:k.enName:(b=(V=(T=r.value??[])==null?void 0:T.filter(G=>G.code===p))==null?void 0:V[0])==null?void 0:b.cnName},y=p=>ea(p);return Xe(()=>a.segInfo,()=>{v()},{deep:!0}),bt(()=>{v()}),(p,h)=>{const S=At;return u(),Se(S,{width:"auto",persistent:!1,"show-after":400,class:"flight-common-info-detail","popper-class":"flight-common-info-detail-popover",placement:"top"},{reference:x(()=>[yt(p.$slots,"default",{},void 0,!0)]),default:x(()=>{var k,T,V,b,G,w,E,D,I,m,C,A,_,P,U,K,Y,ne,oe;return[o("div",kc,[o("div",Fc,[o("div",Lc,g(`${d(((k=p.segInfo)==null?void 0:k.departureAirportCode)??"")}-${d(((T=p.segInfo)==null?void 0:T.arrivalAirportCode)??"")}`),1),f(xo,{"seg-info":p.segInfo},null,8,["seg-info"])]),o("div",Nc,[o("div",wc,[o("span",null,[(u(),$("svg",Ic,[o("use",{"xlink:href":"#icon-"+((G=(b=(V=p.segInfo)==null?void 0:V.airlines)==null?void 0:b.airCode)==null?void 0:G.toLowerCase())+"-c"},null,8,Ec)]))]),o("div",Pc,[o("div",Rc,g(((E=(w=p.segInfo)==null?void 0:w.airlines)==null?void 0:E.airCN)??""),1),o("div",Oc,[o("div",Qc,g(`${((I=(D=p.segInfo)==null?void 0:D.airlines)==null?void 0:I.airCode)??""}${((C=(m=p.segInfo)==null?void 0:m.airlines)==null?void 0:C.flightNo)??""}`),1),(_=(A=p.segInfo)==null?void 0:A.airlines)!=null&&_.isShared?(u(),$("div",Mc,[o("div",Vc,g(p.$t("app.querySearch.shareFlight")),1)])):ge("",!0)])])]),(U=(P=p.segInfo)==null?void 0:P.airlines)!=null&&U.isShared?(u(),$(he,{key:0},[Bc,o("div",Yc,[o("span",qc,g(p.$t("app.querySearch.carrierCompany")),1),o("span",Uc,g(`${((Y=(K=p.segInfo)==null?void 0:K.airlines)==null?void 0:Y.isShared)??""}`),1)])],64)):ge("",!0),(ne=p.segInfo)!=null&&ne.flightTime?(u(),$("div",Hc,[o("span",null,g(p.$t("app.querySearch.flightDuration")),1),o("span",null,g(y(((oe=p.segInfo)==null?void 0:oe.flightTime)??"")??""),1)])):ge("",!0)]),o("div",null,[o("div",jc,g(c.value),1),o("div",Gc,g(n.value),1)])])]}),_:3})}}});const za=et(Kc,[["__scopeId","data-v-fc328ba4"]]),zc={class:"flex items-center"},Wc={class:"flex items-center justify-center w-5 h-5"},Jc={class:"grow shrink basis-0 self-stretch pr-[1.43px] justify-center items-center inline-flex"},Zc={class:"icon svg-icon airline-icon text-[20px]","aria-hidden":"true"},Xc=["xlink:href"],eu={class:"h-[22px] justify-start items-center gap-0.5 inline-flex"},tu={class:"h-[22px] text-sm font-bold leading-[22px] text-gray-1"},au={key:0,class:"min-w-[28px] max-w-[37px] h-4 px-0.5 bg-brand-3 rounded-sm justify-center items-center flex"},ou={class:"text-center text-brand-1 text-xs font-normal leading-none"},nu={key:0,class:"h-4 px-0.5 bg-brand-3 rounded-sm justify-center items-center inline-flex relative top-[-6px]"},iu={class:"text-brand-1 text-xs font-normal leading-none"},Wa=Ye({__name:"AirlineInfo",props:{segInfo:{}},setup(e){return(a,i)=>{var r,c,n,l,v,d,y,p,h,S,k,T,V,b,G;return u(),$("div",zc,[o("div",{class:ye(["w-[114px] gap-0.5 justify-start inline-flex relative",(c=(r=a.segInfo)==null?void 0:r.airlines)!=null&&c.isShared?"h-[38px] items-start":"h-[22px] items-center"])},[o("div",Wc,[o("div",Jc,[(u(),$("svg",Zc,[o("use",{"xlink:href":"#icon-"+((n=a.segInfo)==null?void 0:n.airlines.airCode.toLowerCase())+"-c"},null,8,Xc)]))])]),o("div",{class:ye([(v=(l=a.segInfo)==null?void 0:l.airlines)!=null&&v.isShared?"h-[38px]":"h-[22px]"])},[o("div",eu,[o("div",tu,g(((y=(d=a.segInfo)==null?void 0:d.airlines)==null?void 0:y.airCode)??"")+g(((h=(p=a.segInfo)==null?void 0:p.airlines)==null?void 0:h.flightNo)??""),1),((k=(S=a.segInfo)==null?void 0:S.airlines)==null?void 0:k.isShared)??""?(u(),$("div",au,[o("div",ou,g(a.$t("app.querySearch.shareFlight")),1)])):ge("",!0)]),(V=(T=a.segInfo)==null?void 0:T.airlines)!=null&&V.isShared?(u(),$("div",nu,[o("div",iu,g(`${((G=(b=a.segInfo)==null?void 0:b.airlines)==null?void 0:G.isShared)??""}`),1)])):ge("",!0)],2)],2)])}}}),ru={sellInternationalCreatePNR:"sell-international-createPNR",sellDomesticCreatePNR:"sell-domestic-createPNR",zhPassengerInfoQuery:"zh-passenger-info-query",sellInternationalMileageCreatePNR:"sell-mileage-international-createPNR",sellAgentAvUniversalStudios:"sell-agent-av-universal-studios",sellAgentCreatePNRSaveOrderToAIG:"sell-agent-create-pnr-save-order-to-aig"},su="/sgui/assets/service-food-2cad3905.svg",lu="/sgui/assets/service-wifi-b5879185.svg",cu=(e,a)=>{const i=B(!1),{t:r}=gt(),c=B(),n=ct({}),l=ct({}),v=ct([]),d=B([]),y=ct({render(){return ht("em",{class:"iconfont icon-calendar"})}}),p=Y=>ea(Y),{personalizationRules:h}=Rn(ru.sellAgentAvUniversalStudios),S=Y=>Y.length===0?"":se(Y).format("MM-DD"),k={FOOD:su,NETWORK:lu},T=Ee(()=>{var Y;return((Y=e.flightStops)==null?void 0:Y.map(ne=>ne.cityName))??[]}),V=()=>{var Y,ne,oe;return((Y=T.value)==null?void 0:Y.length)<=0?"":(oe=(ne=T.value)==null?void 0:ne.map(Z=>Z))==null?void 0:oe.join("/")},b=Y=>{const ne=Tn();if(sn(v.value))return ne.isAfter(Y);const oe=v.value[0].getTime(),Z=9*24*3600*1e3;return Y.getTime()>oe+Z||Y.getTime()<oe},G=()=>{const Y=se(d.value.length?d.value[0]:"").format("YYYY-MM-DD"),ne=se(d.value.length?d.value[1]:"").format("YYYY-MM-DD");a("multiDayQuery",e.segIndex,Y,ne)},w=()=>{a("retractMultiDayQuery",e.segIndex)},E=(Y,ne,oe,Z,H)=>({date:S(Y)??"",time:ne??"",code:oe??"",airportCn:Z??"",terminal:H??""}),D=()=>{if(!e.segInfo)return;const{departureDate:Y,departureTime:ne,departureAirportCode:oe,departureAirportCN:Z,departureTerminal:H,arrivalDate:O,arrivalTime:L,arrivalAirportCode:R,arrivalAirportCN:N,arrivalTerminal:F}=e.segInfo;n.value=E(Y,ne,oe,Z,H),l.value=E(O,L,R,N,F)},I=async()=>{var ne,oe,Z,H;const Y={airCode:e.segInfo.airlines.airCode,departureDate:se(e.segInfo.departureDate).format("YYYY-MM-DD"),flightNo:`${e.segInfo.airlines.airCode}${e.segInfo.airlines.flightNo}`};if((c.value??[]).length===0)try{i.value=!0;const{data:O}=await ho(Y,"01010201"),L=((Z=(oe=((ne=O.value)==null?void 0:ne.flightInfoList)??[])==null?void 0:oe[0])==null?void 0:Z.segments)??[],R=L==null?void 0:L.findLastIndex(ae=>{var le;return ae.arrivalAirportCode===((le=e.segInfo)==null?void 0:le.arrivalAirportCode)}),N=R>0?L==null?void 0:L.slice(0,R+1):L,F=[];for(let ae=0;ae<N.length-1;ae++){const{arrivalDate:le,arrivalTime:de,arrivalAirportCode:fe,arrivalAirportCN:te,arrivalTerminal:Te,airlines:be,groundTime:Re}=N[ae],X={arrivalDate:le,arrivalTime:de,arrivalAirportCode:fe,arrivalAirportCN:te,arrivalTerminal:Te,airlines:be,groundTime:Re};if(ae+1!==N.length){const{departureDate:re,departureTime:z}=N[ae+1]??[];X.departureDate=re,X.departureTime=z}X.airlines=(H=N[ae])==null?void 0:H.airlines,F.push(X)}c.value=F}finally{i.value=!1}},m=Y=>Y==="MEAL"?"FOOD":Y==="ADHOC"?"NETWORK":"",C=Y=>{v.value=Y},A=Y=>{i.value=Y},_=Y=>{var ne,oe;return(ne=Y==null?void 0:Y.airlines)!=null&&ne.isShared?(oe=Y==null?void 0:Y.airlines)==null?void 0:oe.isShared:`${Y==null?void 0:Y.airlines.airCode}${Y==null?void 0:Y.airlines.flightNo}`},P=Y=>Y&&Y!=="--"?`-${Y}`:"",U=Y=>{switch(Y){case"B":return`${Y}${r("app.avSearch.breakfast")}`;case"L":case"D":return`${Y}${r("app.avSearch.prepareDinner")}`;case"S":return`${Y}${r("app.avSearch.snacks")}`;case"M":return`${Y}${r("app.avSearch.prepareMeals")}`;default:return`${Y}${r("app.avSearch.meal")}`}},K=Y=>{switch(Y){case"SKYTEAM":return`${r("app.avSearch.allianceZT")}`;case"STAR ALLIANCE":return`${r("app.avSearch.allianceZS")}`;case"ONEWORLD":return`${r("app.avSearch.allianceZ7")}`;default:return`${Y}`}};return bt(()=>{D()}),Rt(()=>{(c.value??[]).length=0,v.value.length=0,d.value.length=0}),{getStopInfo:I,flightTimeFormat:p,stopData:c,departureTitle:n,arrivalTitle:l,combineFlight:E,formatMMDD:S,personalizationRules:h,deptReturnDate:d,disabledDate:b,queryMultiDay:G,changeMultiDate:w,serviceIcon:k,checkServiceType:m,calendarChange:C,loading:i,datePrefix:y,buildTerminalText:P,changeLoading:A,getFlightNo:_,getMealDescription:U,getAlliance:K,getStopCity:V,currentTransitCities:T}},uu=e=>{const a=B(),i=B(""),r=B(""),c=B(Zt()),n=Ee(()=>e.airportsDbData),l=p=>p.length===0?"":se(p).format("MM-DD");return{stopData:a,departureTitle:i,arrivalTitle:r,combineFlight:(p,h,S,k,T)=>`${l(p)??""} ${h??""} ${S??""} ${k??""} ${T??""}`,formatMMDD:l,getAirCity:p=>{var h,S,k,T,V,b;return c.value==="en"?(k=(S=(h=n.value??[])==null?void 0:h.filter(G=>G.code===p))==null?void 0:S[0])==null?void 0:k.enName:(b=(V=(T=n.value??[])==null?void 0:T.filter(G=>G.code===p))==null?void 0:V[0])==null?void 0:b.cnName},flightTimeFormat:p=>ea(p)}},du=uu,pu={class:"flex flex-col p-2 text-xs bg-gray-3 text-gray-0 min-w-[343px]"},gu={class:"flex items-center justify-between"},fu={class:"text-sm font-bold"},mu={class:"mx-0 mt-2 flex items-center h-6"},vu={class:"flex items-center stop-content-company-data"},hu={class:"!w-6 !h-6 inline-block air-icon icon svg-icon airline-icon","aria-hidden":"true"},yu=["xlink:href"],bu={class:"h-6 justify-start items-center gap-1 inline-flex"},_u={class:"text-center text-gray-0 text-xs font-normal leading-tight"},Cu={class:"justify-start items-center gap-0.5 flex"},Au={class:"text-center text-gray-0 text-xs font-normal leading-tight"},Du={key:0,class:"px-1 h-5 bg-brand-3 rounded-sm justify-center items-center flex"},$u={class:"text-center text-brand-1 text-xs font-normal leading-tight"},xu=o("span",{class:"w-[1px] h-[14px] split-label mx-[5px] bg-gray-6"},null,-1),Su={class:"px-2 py-0.5 my-0 rounded-sm bg-brand-3 actual-carrier-box whitespace-nowrap inline-block"},Tu={class:"mr-2 text-gray-2"},ku={class:"text-brand-2"},Fu={key:1,class:"ml-2"},Lu={class:"mt-[10px] mb-2"},Nu={class:"justify-start items-center gap-5 inline-flex"},wu=o("div",{class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},null,-1),Iu={class:"flex-col justify-start items-start gap-2 inline-flex"},Eu={class:"justify-start items-center gap-2.5 inline-flex"},Pu={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},Ru={class:"text-gray-0 text-xs font-normal leading-5"},Ou={class:"text-gray-0 text-xs font-normal leading-tight"},Qu={class:"h-10 justify-start items-center gap-5 inline-flex"},Mu={class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},Vu={class:"flex-col justify-start items-start gap-2 inline-flex"},Bu={class:"justify-start items-center gap-2.5 inline-flex"},Yu={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},qu={class:"text-gray-0 text-xs font-normal leading-5"},Uu={class:"text-gray-0 text-xs font-normal leading-5"},Hu={class:"text-gray-0 text-xs font-normal leading-tight"},ju={class:"text-gray-0 text-xs font-normal leading-tight"},Gu={class:"mt-[8px]"},Ku={class:"justify-start items-center gap-5 inline-flex"},zu=o("div",{class:"text-center text-gray-0 text-xs font-normal leading-tight w-11 flex-grow-0 flex-shrink-0"},null,-1),Wu={class:"flex-col justify-start items-start gap-2 inline-flex"},Ju={class:"justify-start items-center gap-2.5 inline-flex"},Zu={class:"w-[92px] flex-col justify-center items-start inline-flex flex-shrink-0"},Xu={class:"text-gray-0 text-xs font-normal leading-5"},ed={class:"text-gray-0 text-xs font-normal leading-tight"},td=Ye({__name:"AirStopMessagebox",props:{segInfo:{},stopData:{},departureTitle:{},arrivalTitle:{},airportsDbData:{}},setup(e){const a=e,{formatMMDD:i,getAirCity:r,flightTimeFormat:c}=du(a);return(n,l)=>{var d;const v=At;return Number(((d=n.segInfo)==null?void 0:d.stopCity)??0)>0?(u(),Se(v,{key:0,width:"auto",trigger:"click",teleported:!0,"popper-class":"flight-item-agent-stop-over-popover"},{reference:x(()=>[yt(n.$slots,"default")]),default:x(()=>{var y,p,h,S,k,T,V,b,G,w,E,D,I,m,C,A,_;return[o("div",pu,[o("div",gu,[o("div",fu,g(`${t(r)(((y=n.segInfo)==null?void 0:y.departureAirportCode)??"")}-${t(r)(((p=n.segInfo)==null?void 0:p.arrivalAirportCode)??"")}`),1),f(xo,{"seg-info":n.segInfo,"stop-data":n.stopData},null,8,["seg-info","stop-data"])]),o("div",mu,[o("div",vu,[o("span",null,[(u(),$("svg",hu,[o("use",{"xlink:href":"#icon-"+((h=n.segInfo)==null?void 0:h.airlines.airCode.toLowerCase())+"-c"},null,8,yu)]))]),o("div",bu,[o("div",_u,g(((k=(S=n.segInfo)==null?void 0:S.airlines)==null?void 0:k.airCN)??""),1),o("div",Cu,[o("div",Au,g(`${((V=(T=n.segInfo)==null?void 0:T.airlines)==null?void 0:V.airCode)??""}${((G=(b=n.segInfo)==null?void 0:b.airlines)==null?void 0:G.flightNo)??""}`),1),(E=(w=n.segInfo)==null?void 0:w.airlines)!=null&&E.isShared?(u(),$("div",Du,[o("div",$u,g(n.$t("app.querySearch.shareFlight")),1)])):ge("",!0)])])]),(I=(D=n.segInfo)==null?void 0:D.airlines)!=null&&I.isShared?(u(),$(he,{key:0},[xu,o("div",Su,[o("span",Tu,g(n.$t("app.querySearch.carrierCompany")),1),o("span",ku,g(`${((C=(m=n.segInfo)==null?void 0:m.airlines)==null?void 0:C.isShared)??""}`),1)])],64)):ge("",!0),(A=n.segInfo)!=null&&A.flightTime?(u(),$("div",Fu,[o("span",null,g(n.$t("app.querySearch.flightDuration")),1),o("span",null,g(t(c)(((_=n.segInfo)==null?void 0:_.flightTime)??"")??""),1)])):ge("",!0)]),o("div",null,[o("div",Lu,[o("div",Nu,[wu,o("div",Iu,[o("div",Eu,[o("div",Pu,[o("div",Ru,g(`${n.departureTitle.date} ${n.departureTitle.time}`),1)]),o("div",Ou,g(`${n.departureTitle.code} ${n.departureTitle.airportCn} ${n.departureTitle.terminal}`),1)])])])]),(u(!0),$(he,null,Ie(n.stopData,(P,U)=>(u(),$("div",{key:P.airlines.flightNo},[o("div",Qu,[o("div",Mu,g(n.$t("app.fightSell.stopPoint"))+g(U+1),1),o("div",Vu,[o("div",Bu,[o("div",Yu,[o("div",qu,g(`${t(i)(P.arrivalDate??"")} ${P.arrivalTime??""}`),1),o("div",Uu,g(`${t(i)(P.departureDate??"")} ${P.departureTime??""}`),1)]),o("div",Hu,g(`${P.arrivalAirportCode??""} ${P.arrivalAirportCN??""} ${P.arrivalTerminal??""}`),1),o("div",ju,g(`${n.$t("app.avSearch.stop")}${t(ea)((P==null?void 0:P.groundTime)??"")}`),1)])])])]))),128)),o("div",Gu,[o("div",Ku,[zu,o("div",Wu,[o("div",Ju,[o("div",Zu,[o("div",Xu,g(`${n.arrivalTitle.date} ${n.arrivalTitle.time}`),1)]),o("div",ed,g(`${n.arrivalTitle.code} ${n.arrivalTitle.airportCn} ${n.arrivalTitle.terminal}`),1)])])])])])])]}),_:3})):ge("",!0)}}});const Lt=e=>(It("data-v-d257d7de"),e=e(),Et(),e),ad={key:0,class:"w-[150px] h-[18px] pl-[34px] justify-start items-center gap-1 inline-flex"},od=Lt(()=>o("div",{class:"px-1.5 py-[3px] bg-yellow-3 rounded justify-start items-start gap-2.5 flex"},[o("div",{class:"text-xs font-normal leading-3 text-yellow-1"},"TCHB")],-1)),nd={class:"text-xs font-normal leading-3 text-yellow-1"},id={class:"flex items-center justify-center w-full flight-item text-gray-1"},rd={key:0,class:"w-[24px] h-[24px] mr-2.5 px-2 py-0.5 bg-gray-7 rounded justify-center items-center inline-flex"},sd={class:"text-xs font-bold leading-tight text-center text-gray-2"},ld={key:1,class:"w-[24px] h-[24px] mr-2.5 px-2 py-0.5 inline-flex"},cd={class:"flex-grow-0 flex-shrink-0 header-left-box basis-30 md-hidden"},ud=Lt(()=>o("div",{class:"border-r border-solid border-gray-7 min-h-[42px] mx-[16px] md-hidden"},null,-1)),dd={class:"flex items-center header-right"},pd={class:"flight-base-info"},gd={class:"md-box justify-between w-[170px]"},fd={class:"flex text-xs"},md={class:"min-w-[20px] min-h-box-20"},vd={class:"ml-[5px] min-w-[20px] min-h-box-20"},hd={class:"flex items-end justify-center"},yd={class:"date-box w-12"},bd={class:"flex flex-col justify-around"},_d={class:"relative text-sm font-bold min-h-box-22"},Cd={class:"absolute top-0 text-xs font-normal stopping text-red-1 right-[-8px]"},Ad={class:"text-xs font-normal min-h-box-20 whitespace-nowrap"},Dd=Lt(()=>o("div",{class:"space-divider"}," ",-1)),$d={class:"stop-info-box"},xd={key:0,class:"justify-center text-gray-1 text-xs font-normal uppercase leading-tight cursor-pointer"},Sd={key:1},Td={class:"self-stretch inline-flex justify-center items-center gap-0.5 cursor-pointer"},kd={class:"justify-center text-gray-1 text-xs font-normal uppercase leading-tight"},Fd={class:"h-4 px-0.5 bg-gray-7 rounded-sm flex justify-center items-center"},Ld={class:"text-center justify-start text-gray-3 text-xs font-normal leading-none"},Nd={class:"flex justify-center p-2 text-xs bg-gray-3 font-normal leading-tight text-gray-0"},wd={key:1,class:"flex items-center justify-center mb-1 font-bold text-brand-2 text-xs cursor-pointer"},Id={class:"stop-divider"},Ed=Lt(()=>o("div",{class:"stop-point-circle-white"},null,-1)),Pd=Lt(()=>o("div",{class:"stop-point-circle-triangle"},null,-1)),Rd={class:"flex h-[20px] leading-[20px] text-gray-4 text-xs font-normal"},Od=Lt(()=>o("div",{class:"space-divider"}," ",-1)),Qd={class:"w-12"},Md={class:"flex flex-col justify-around"},Vd={class:"relative text-sm font-bold min-h-box-22 text-end"},Bd={class:"stopping text-xs font-normal text-red-1 absolute top-0 right-[-16px]"},Yd={class:"text-xs font-normal text-right min-h-box-20 whitespace-nowrap"},qd={class:"md-item-right flex items-center"},Ud={class:"flex"},Hd={class:"flex text-xs text-right flex-col justify-around ml-[16px] md-hidden"},jd={class:"min-w-[20px] min-h-box-20"},Gd={class:"min-w-[20px] min-h-box-20"},Kd=Lt(()=>o("div",{class:"border-r border-solid border-gray-200 h-[42px] mx-[16px] md-hidden"},null,-1)),zd={class:"header-right-asr text-xs min-w-[36px] md-header-right-asr"},Wd={key:0,class:"min-h-box-20 md:mr-2.5"},Jd={key:0,class:"whitespace-nowrap"},Zd={key:1,class:"whitespace-nowrap"},Xd={class:"min-h-box-20"},ep={class:"header-right-wifi-box"},tp={key:0,class:"service-box"},ap={key:0,class:"icon","aria-hidden":"true"},op=Lt(()=>o("use",{"xlink:href":"#icon-wifi"},null,-1)),np=[op],ip={key:0,class:"header-right-personal-box"},rp={class:"item-right"},sp={class:"text-xs"},lp={key:1,class:"flex justify-end multi-day-box relative cursor-pointer"},cp={class:"text-xs text-brand-2 leading-5 absolute right-5 mr-0.5"},up={class:"flex-grow-[18]"},dp={class:"flex flex-row items-center mt-[6px] gap-2.5 md:pl-12 lg:pl-[180px] xl:pl-[180px]"},pp={key:0,class:"px-0.5 bg-gray-7 rounded-sm justify-center items-center inline-flex"},gp={class:"text-center text-gray-3 text-xs leading-4"},fp={key:1,class:"px-0.5 bg-gray-7 rounded-sm justify-center items-center inline-flex"},mp={class:"text-center text-gray-3 text-xs leading-4"},vp={class:"ml-1"},hp=Ye({__name:"FlightCommonInfo",props:{segIndex:{},segInfo:{},flightStops:{},multiDayCabin:{},sortIndex:{},segOrder:{},isTransfer:{type:Boolean},ignoreResolution:{type:Boolean},queryLowestFare:{type:Boolean},airportsDbData:{}},emits:["multiDayQuery","retractMultiDayQuery"],setup(e,{emit:a}){const i=e,r=a,{getStopInfo:c,personalizationRules:n,flightTimeFormat:l,departureTitle:v,stopData:d,arrivalTitle:y,deptReturnDate:p,disabledDate:h,queryMultiDay:S,changeMultiDate:k,calendarChange:T,loading:V,datePrefix:b,buildTerminalText:G,changeLoading:w,getFlightNo:E,getMealDescription:D,getAlliance:I,getStopCity:m,currentTransitCities:C}=cu(i,r);return(A,_)=>{var oe,Z,H,O,L,R,N,F,ae,le,de,fe,te,Te,be,Re,X,re,z,j,W,ue,Le,$e,Oe,Ae,Pe,Ge,ot,st,Be,ut;const P=At,U=ka,K=_t,Y=fo,ne=la;return Ct((u(),$("div",{class:ye(["inline-flex flex-col items-start justify-center w-full",A.ignoreResolution?"":"need-resolution"])},[(oe=A.segInfo)!=null&&oe.tcFlight?(u(),$("div",ad,[od,o("div",nd,g((Z=A.segInfo)==null?void 0:Z.tcFlight),1)])):ge("",!0),o("div",id,[A.segOrder===0?(u(),$("div",rd,[o("div",sd,g(isNaN(A.sortIndex)?"":A.sortIndex+1),1)])):(u(),$("div",ld)),o("div",cd,[f(za,{"seg-info":A.segInfo,"airports-db-data":A.airportsDbData},{default:x(()=>[f(Wa,{"seg-info":A.segInfo},null,8,["seg-info"])]),_:1},8,["seg-info","airports-db-data"])]),ud,o("div",dd,[o("div",pd,[o("div",gd,[f(za,{"seg-info":A.segInfo,"airports-db-data":A.airportsDbData},{default:x(()=>[f(Wa,{"seg-info":A.segInfo},null,8,["seg-info"])]),_:1},8,["seg-info","airports-db-data"]),o("div",fd,[o("div",md,g(((H=A.segInfo)==null?void 0:H.connectLevel)??""),1),o("div",vd,g(((L=(O=A.segInfo)==null?void 0:O.airlines)==null?void 0:L.planeType)??""),1)])]),o("div",hd,[o("div",yd,[o("span",bd,[o("div",_d,[me(g(((R=A.segInfo)==null?void 0:R.departureTime)??"")+" ",1),o("span",Cd,g(((N=A.segInfo)==null?void 0:N.deptArrdays)??""),1)]),o("div",Ad,g(((F=A.segInfo)==null?void 0:F.departureAirportCode)??"")+" "+g(t(G)(((ae=A.segInfo)==null?void 0:ae.departureTerminal)??"")),1)])]),Dd,o("div",$d,[o("div",{class:ye(Number(((le=A.segInfo)==null?void 0:le.stopCity)??0)>0?"stop-info":"no-stop-info")},[o("span",null,[f(td,{"seg-info":A.segInfo,"stop-data":t(d),"departure-title":t(v),"arrival-title":t(y),"airports-db-data":A.airportsDbData},{default:x(()=>{var Ue,ee;return[o("div",{class:"text-xs justify-center items-center text-gray-1 text-center font-normal font-mono uppercase leading-none cursor-pointer",onClick:_[0]||(_[0]=(...ce)=>t(c)&&t(c)(...ce))},[((Ue=t(C))==null?void 0:Ue.length)>0?(u(),$(he,{key:0},[((ee=t(C))==null?void 0:ee.length)<3?(u(),$("div",xd,g(t(m)()),1)):(u(),$("div",Sd,[f(P,{width:"auto",teleported:!0,"popper-class":"flight-stop-city-popover",placement:"top","popper-options":{modifiers:[{name:"offset",options:{offset:[2,8]}}]}},{reference:x(()=>{var ce;return[o("div",Td,[o("div",kd,g(t(C)[0]),1),o("div",Fd,[o("div",Ld,"+"+g(((ce=t(C))==null?void 0:ce.length)-1),1)])])]}),default:x(()=>[o("div",Nd,g(t(m)()),1)]),_:1})]))],64)):(u(),$("div",wd,g(A.$t("app.pnrManagement.flight.stopOver")),1))])]}),_:1},8,["seg-info","stop-data","departure-title","arrival-title","airports-db-data"]),o("div",Id,[Ed,f(U,{class:"divider"},{default:x(()=>{var Ue;return[o("div",{class:ye(Number(((Ue=A.segInfo)==null?void 0:Ue.stopCity)??0)>0?"stop-point-circle":"")},null,2)]}),_:1}),Pd])]),o("div",Rd,[o("span",null,g(t(l)(((de=A.segInfo)==null?void 0:de.flightTime)??"")??""),1)])],2)]),Od,o("div",Qd,[o("span",Md,[o("div",Vd,[me(g(((fe=A.segInfo)==null?void 0:fe.arrivalTime)??"")+" ",1),o("span",Bd,g(((te=A.segInfo)==null?void 0:te.arrivalArrdays)??""),1)]),o("div",Yd,g(((Te=A.segInfo)==null?void 0:Te.arrivalAirportCode)??"")+" "+g(t(G)(((be=A.segInfo)==null?void 0:be.arrivalTerminal)??"")),1)])])])]),o("div",qd,[o("div",Ud,[o("div",Hd,[o("div",jd,g(((Re=A.segInfo)==null?void 0:Re.connectLevel)??""),1),o("div",Gd,g(((re=(X=A.segInfo)==null?void 0:X.airlines)==null?void 0:re.planeType)??""),1)]),Kd,o("div",zd,[(z=A.segInfo)!=null&&z.commonMeal?(u(),$("div",Wd,[((j=A.segInfo)==null?void 0:j.commonMeal)==="S"?(u(),$("div",Jd,[f(K,{effect:"dark",content:`${(W=A.segInfo)==null?void 0:W.commonMeal}${A.$t("app.avSearch.snacksDimOrSum")}`,placement:"top"},{default:x(()=>{var Ue;return[me(g(t(D)(((Ue=A.segInfo)==null?void 0:Ue.commonMeal)??"")),1)]}),_:1},8,["content"])])):(u(),$("div",Zd,g(t(D)(((ue=A.segInfo)==null?void 0:ue.commonMeal)??"")),1))])):ge("",!0),o("div",Xd,"ASR"+g(((Le=A.segInfo)==null?void 0:Le.asr)??""),1)]),o("div",ep,[(Oe=($e=A.segInfo)==null?void 0:$e.airlines)!=null&&Oe.airService?(u(),$("div",tp,[A.segInfo.airlines.airService.find(Ue=>Ue.code==="ADHOC")?(u(),$("svg",ap,np)):ge("",!0)])):ge("",!0)]),t(n)?(u(),$("div",ip,[f(t(On),{"rule-info":t(n),origin:((Ae=A.segInfo)==null?void 0:Ae.arrivalAirportCode)??"",destination:(Pe=A.segInfo)==null?void 0:Pe.departureAirportCode},null,8,["rule-info","origin","destination"])])):ge("",!0)]),o("div",rp,[A.queryLowestFare?ge("",!0):(u(),$(he,{key:0},[A.multiDayCabin.day!==""?(u(),$("div",{key:0,class:"flex w-full h-5 text-brand-2 justify-end items-center px-0.5 gap-1 cursor-pointer",onClick:_[1]||(_[1]=(...Ue)=>t(k)&&t(k)(...Ue))},[o("span",sp,g(A.$t("app.avSearch.fold")),1),f(t(je),{size:16,class:"mt-[-4px]"},{default:x(()=>[f(t(Bt))]),_:1})])):ge("",!0),A.multiDayCabin.day===""?(u(),$("div",lp,[o("div",cp,g(A.$t("app.avSearch.multiDayQuery")),1),A.multiDayCabin.day===""?(u(),Se(Y,{key:0,modelValue:t(p),"onUpdate:modelValue":_[2]||(_[2]=Ue=>rt(p)?p.value=Ue:null),class:"av-range-picker",format:"YYYY-MM-DD",type:"daterange","prefix-icon":t(b),editable:!1,"disabled-date":t(h),"popper-class":"query-flight-date-picker-style",onChange:t(S),onCalendarChange:t(T)},null,8,["modelValue","prefix-icon","disabled-date","onChange","onCalendarChange"])):ge("",!0)])):ge("",!0)],64)),f(Sc,{"flight-no":t(E)(A.segInfo),"departure-date":((Ge=A.segInfo)==null?void 0:Ge.departureDate)??"",onChangeLoading:t(w)},null,8,["flight-no","departure-date","onChangeLoading"])])])]),o("div",up,[yt(A.$slots,"default",{},void 0,!0)])]),o("div",dp,[(ot=A.segInfo)!=null&&ot.alliance?(u(),$("div",pp,[o("div",gp,[o("span",null,g(t(I)(((st=A.segInfo)==null?void 0:st.alliance)??"")),1)])])):ge("",!0),(Be=A.segInfo)!=null&&Be.flightDistance?(u(),$("div",fp,[o("div",mp,[o("span",null,g(A.$t("app.avSearch.flightDistance")),1),o("span",vp,g(((ut=A.segInfo)==null?void 0:ut.flightDistance)??""),1)])])):ge("",!0)])],2)),[[ne,t(V)]])}}});const yp=et(hp,[["__scopeId","data-v-d257d7de"]]),bp=(e,a)=>{const{t:i}=gt(),r=B({tktNum:e.tktNum??1}),c=B([]),n=b=>b==="A"||/^[1-9]$/.test(b),l=b=>["L","Q","S"].includes(b),v=b=>n(b)||l(b),d=async(b,G)=>{const{segIndex:w,day:E,cabins:D,segInfo:I}=e;let m="",C="";b>-1&&D&&D[b]&&(m=D[b].cabinName,C=D[b].state);const A={segIndex:w??"",day:E??"",cabinName:m,cabinStatus:C,cancel:!1,tktNum:G?r.value.tktNum:0,segInfo:I};if(e.activeCabin===m||b===-1){A.cancel=!0,a("selectCabin",A);return}a("selectCabin",A)},y=Mt(async(b,G)=>{const{notSelectable:w,activeTag:E,avQueryFromFastQuery:D}=e;e.autoPlaceholder&&!D&&await ln(E,i,ht)||w||d(b,G)}),p=(b,G,w)=>{if(b)return"cabin-item cabin-item-sk";const E="cabin-item cabin-item-av",D=`${E} cabin-item-gray`,I=`${E} cabin-item-yellow`;let m=E;return v(G)?l(G)&&(m=I):m=D,e.activeCabin===w&&(!e.avQueryFromFastQuery||e.autoPlaceholder)&&(m=`${m} cabin-item-active`),m};return{getCabinStyle:p,showChosenCabin:y,cabinClass:(b,G)=>{const{state:w,cabinName:E}=G,D=e.notSelectable?"not-selectable":"";return`${p(b,w,E)} ${D}`},form:r,confirm:()=>{a("closeCabinPopover",r.value.tktNum)},cancel:()=>{a("closeCabinPopover")},showPopover:()=>{r.value={tktNum:e.tktNum??1}},checkCharacter:b=>{const G=b.key,w=/^[1-9]$/;b.returnValue=w.test(G)?G:0},cabinRef:c}},_p={class:"grow basis-32 cabin-info"},Cp=["onClick"],Ap={class:"flex items-center justify-center"},Dp={key:1},$p=Ye({__name:"FlightCabin",props:{segIndex:{},cabins:{},skQuery:{type:Boolean},day:{},segInfo:{},notSelectable:{type:Boolean},activeCabin:{},activeTag:{},confirmText:{},avQueryFromFastQuery:{type:Boolean},autoPlaceholder:{type:Boolean},visibleCabinPopover:{type:Boolean},tktNum:{}},emits:["selectCabin","closeCabinPopover"],setup(e,{emit:a}){const i=e,r=a,{cabinClass:c,showChosenCabin:n,form:l,cancel:v,confirm:d,showPopover:y,checkCharacter:p,cabinRef:h}=bp(i,r);return(S,k)=>{const T=Qn,V=Ut,b=Ht,G=sa,w=At;return u(),$("div",_p,[(u(!0),$(he,null,Ie(S.cabins??[],(E,D)=>(u(),$("div",{key:`${D}${new Date}`,class:ye(t(c)(S.skQuery,E)),onClick:Vt(I=>t(n)(D),["stop"])},[S.activeCabin===E.cabinName&&S.visibleCabinPopover&&!S.avQueryFromFastQuery?(u(),Se(w,{key:0,ref_for:!0,ref:I=>{I&&t(h).push(I)},teleported:!1,visible:S.activeCabin===E.cabinName&&S.visibleCabinPopover,trigger:"manual",width:t(cn)()==="zh-cn"?"190":"300",onShow:t(y)},{reference:x(()=>[o("div",null,g(S.skQuery?E.cabinName:E.cabinName+E.state),1)]),default:x(()=>[S.activeCabin===E.cabinName?(u(),$("div",{key:0,class:"seize-seat-popover-content",onClick:k[1]||(k[1]=Vt(()=>{},["stop"]))},[S.tktNum?ge("",!0):(u(),Se(b,{key:0,model:t(l)},{default:x(()=>[f(V,{label:S.$t("app.pnrManagement.flight.numberOfOccupiedPassengers"),class:"num-input"},{default:x(()=>[f(T,{modelValue:t(l).tktNum,"onUpdate:modelValue":k[0]||(k[0]=I=>t(l).tktNum=I),min:1,max:9,size:"small",onKeydown:t(p)},null,8,["modelValue","onKeydown"])]),_:1},8,["label"])]),_:1},8,["model"])),o("div",Ap,[f(G,{type:"primary",size:"mini",onClick:t(d)},{default:x(()=>[me(g(S.confirmText||S.$t("app.pnrManagement.flight.confirm")),1)]),_:1},8,["onClick"]),S.avQueryFromFastQuery?(u(),Se(G,{key:0,size:"mini",onClick:t(v)},{default:x(()=>[me(g(S.$t("app.pnrManagement.flight.cancel")),1)]),_:1},8,["onClick"])):ge("",!0)])])):ge("",!0)]),_:2},1032,["visible","width","onShow"])):(u(),$("div",Dp,g(S.skQuery?E.cabinName:E.cabinName+E.state),1))],10,Cp))),128))])}}});const Ja=et($p,[["__scopeId","data-v-235747be"]]),xp={class:"relative mt-[10px] mb-[10px] border-b-[1px] border-dashed border-gray-7"},Sp={class:"absolute -top-[8px] left-[200px] flex justify-center items-center pl-[9px] pr-[9px] h-[18px] text-[12px] text-gray-2 rounded bg-brand-7"},Tp=Ye({__name:"TransferDivider",props:{transferText:{}},setup(e){return(a,i)=>(u(),$("div",xp,[o("div",Sp,[o("span",null,g(a.transferText),1)])]))}}),kp=(e,a)=>{const{t:i}=gt(),r=B(!1),c=B(),n=B(""),l=ct(!1),v=B([]),d=ct([]),y=[],p=[],h=io(e.segmentData),S=O=>`${h}${O}`,k=O=>se(O||e.departureDate).format("YYYY-MM-DD"),T=O=>{const L=O.split("/"),R=L[L.length-1],N=v.value.find(F=>{var ae;return(ae=F.multDayCabins)==null?void 0:ae.find(le=>{const de=le.key===O;return de&&(le.activeCabin=""),de})});N&&p.forEach(F=>{`${N.segIndex}>${k(R)}`==`${F.segIndex}>${k(F.day)}`&&(F.cabinName="",F.cabinStatus="")})},V=O=>{v.value.find(L=>{var R;if(O.includes(L.segIndex))return(R=L.multDayCabins)==null?void 0:R.find(N=>{const F=O===`${L.segIndex}>${k(N.day)}`;return F&&(N.activeCabin=""),F})}),p.forEach(L=>{O===`${L.segIndex}>${k(L.day)}`&&(L.cabinName="",L.cabinStatus="")})},b=O=>{const L=[];return(O??[]).forEach(R=>{const N={cabinName:R.cabinName,state:R.state,day:R.day};L.push(N)}),L},G=O=>O.multDayCabins.length===1&&O.multDayCabins[0].day==="",w=()=>{v.value.forEach(O=>{O.multDayCabins.forEach(L=>{if(G(O))return;const R=(y??[]).find(N=>N.key===O.segIndex)??{};if(R){const N=(R.cabins??[]).find(F=>L.day===(F==null?void 0:F.dateTime))??{};if(N){const F=b(N.cabinNos);L.cabins=F,L.activeCabin=""}}})})},E=()=>{if(e.flightNoQueryFlag||l.value)return!0;const O=p.filter(L=>L.cabinName!=="");return(O==null?void 0:O.length)===e.segmentData.segments.length},D=(O,L,R,N)=>p.filter(F=>F.segIndex===O).some(F=>F.cabinName===L&&F.cabinStatus===R&&F.day===N),I=(O,L)=>{const{cabinName:R,cancel:N,day:F}=O;return!e.autoSelectedCabin||e.flightNoQueryFlag||L||v.value.length<2||N?!1:(v.value.forEach(ae=>{ae.multDayCabins.forEach(de=>{if(de.activeCabin="",k(de.day)===k(F)&&de.cabins.find(te=>te.cabinName===R)){const te=de.cabins.find(Te=>Te.cabinName===R);if(te){const Te=p.find(be=>be.segIndex===ae.segIndex);Te&&(Te.cabinName=R,Te.cabinStatus=te.state,Te.day=F,de.activeCabin=R)}}});const le={transferCabinCache:p,day:F,travelKey:ae.segIndex,segmentData:e.segmentData,tktType:e.tktType};a("selectCabin",le,n.value)}),!0)},m=()=>{var ae;if(!n.value)return;const O=n.value.split("-"),L=Number(O[0]),R=Number(O[1]),N=(ae=v.value[L])==null?void 0:ae.multDayCabins[R];N.activeCabin="";const F=p.find(le=>`${le.segIndex}>${k(le.day)}`==`${S(L)}>${k(N.day)}`);F&&(F.cabinName="",F.cabinStatus=""),n.value=""},C=(O,L,R)=>{n.value=`${L}-${R}`;const{segIndex:N,day:F,cabinName:ae,cabinStatus:le,cancel:de,segInfo:fe,tktNum:te}=O;if(I(O,L))return;v.value.forEach(be=>{be.segIndex===N?be.multDayCabins.forEach(Re=>{k(Re.day)===k(F)&&(Re.activeCabin=de?"":ae)}):e.flightNoQueryFlag&&be.multDayCabins.forEach(Re=>{k(Re.day)===k(F)&&(Re.activeCabin="")})});const Te={transferCabinCache:p,day:F,travelKey:N,segmentData:e.segmentData,tktType:e.tktType,tktNum:te};if(de||D(N,ae,le,F)){p.filter(be=>be.segIndex===N).forEach(be=>{be.cabinName="",be.cabinStatus="",be.day=F}),a("cancelSelectCabin",Te);return}p.forEach(be=>{be.segIndex===N?(be.cabinName=ae,be.cabinStatus=le,be.day=F):e.flightNoQueryFlag&&(be.cabinName="",be.cabinStatus="",be.day="")}),Te.transferCabinCache=p.filter(be=>be.cabinName),fe&&(E()||e.autoPlaceholder)&&a("selectCabin",Te,n.value)},A=(O,L,R,N)=>{const F=N,ae=[];if(F&&F.airlines){const de={airCode:F.airlines.airCode,flightNo:`${F.airlines.airCode}${F.airlines.flightNo}`,departureCity:F.departureAirportCode,arrivalCity:F.arrivalAirportCode};ae.push(de)}return{airlines:ae,departureStart:L,departureEnd:R,index:e.segmentData.pkId}},_=(O,L)=>{l.value=!1,v.value.filter(R=>R.segIndex===O).forEach(R=>{const N=[],F=L;if(F){const ae=b(F.cabins),le=k(F.departureDate),de={day:"",isShowMultDayQuery:!0,cabins:ae,activeCabin:"",key:pa(F,le)};N.push(de),R.multDayCabins=N,y.forEach((fe,te)=>{O===fe.key&&y.splice(te,1)})}})},P=async(O,L,R,N)=>{var F,ae,le,de,fe;try{r.value=!0,l.value=!0;const{data:te}=await gi(A(O,L,R,N),"01010209"),Te=((F=te.value)==null?void 0:F.flightInfoList)??[];if(Te.length>0&&((le=(ae=Te[0])==null?void 0:ae.segments)==null?void 0:le.length)>0){const be=Te[0],Re=(fe=(de=be==null?void 0:be.segments)==null?void 0:de[0])==null?void 0:fe.cabins;if(Re){const X={key:O,cabins:Re};y.push(X),v.value.filter(re=>re.segIndex===O).forEach(re=>{var j;const z=[];Re.forEach((W,ue)=>{const Le=N,$e=k(W.dateTime),Oe={day:W.dateTime,isShowMultDayQuery:ue===0,cabins:W.cabinNos,activeCabin:"",key:pa(Le,$e)};z.push(Oe)}),d.value=b((j=re==null?void 0:re.multDayCabins)==null?void 0:j[0].cabins),re.multDayCabins=z}),w()}}}catch{_(O,N)}finally{r.value=!1}},U=(O,L)=>{var F;const R=[],N={day:"",isShowMultDayQuery:((F=e.segmentData)==null?void 0:F.segments.length)===1,cabins:O,activeCabin:"",key:pa(L)};return R.push(N),R},K=()=>{var O;v.value=[],p.length=0,n.value="",e.segmentData&&(((O=e.segmentData)==null?void 0:O.segments)??[]).forEach((L,R)=>{const N=b(L.cabins),F={segIndex:S(R),multDayCabins:U(N,L)};v.value.push(F);const{arrivalAirportCode:ae,departureAirportCode:le,departureDate:de,arrivalDate:fe,airlines:te}=L,Te={segIndex:S(R),day:"",cabinName:"",cabinStatus:"",flightNo:`${(te==null?void 0:te.airCode)??""}${(te==null?void 0:te.flightNo)??""}`,arrivalAirportCode:ae,departureAirportCode:le,departureDate:de,arrivalDate:fe,segInfo:L};p.push(Te)});for(let L=0;L<e.segmentData.segments.length;L++)if(e.segmentData.segments[L].transferDate="",L<e.segmentData.segments.length-1){const R=e.segmentData.segments[L],N=e.segmentData.segments[L+1],F=se(`${R.arrivalDate}`).format("YYYY-MM-DD HHmm"),ae=se(`${N.departureDate}`).format("YYYY-MM-DD HHmm");e.segmentData.segments[L].transferDate=kn(F,ae)}},Y=O=>{if(!O)return"";const L=O.transferDate??"";return O.tcFlight?`${i("app.fastQuery.assistedSearch.travelDistance",{time:L})}`:e.flightNoQueryFlag?`${i("app.fightSell.afterStopTime",{time:L})}`:`${i("app.fightSell.transfer",{time:L})}`},ne=async()=>{const O=await pt("searchLocalData");c.value=(JSON.parse(O.localData??"")??[]).map(L=>({code:L.airportCode,cnName:L.cityArray[1],enName:L.cityArray[0]}))},oe=async O=>{var L;(((L=e.segmentData)==null?void 0:L.segments)??[]).length&&e.avQueryFromFastQuery&&K(),a("closeCabinPopover",O)},Z=O=>{const L=O.split("-"),R=n.value.split("-");return(L==null?void 0:L[0])===e.sortIndex.toString()&&(L==null?void 0:L[1])===R[0]&&e.showPlaceholderPopoverIndex.includes(Co)?(n.value="",!0):!1},H=O=>{const L=O.split("-"),R=n.value.split("-");((L==null?void 0:L[0])!==e.sortIndex.toString()||(L==null?void 0:L[1])===R[0]&&(L!=null&&L[2])&&(L==null?void 0:L[2])!==R[1])&&m()};return Xe(()=>e.showPlaceholderPopoverIndex,O=>{if(e.autoPlaceholder&&n.value){if(Z(O))return;H(O)}}),bt(()=>{var O;ne(),K(),(O=v.value)!=null&&O.length&&w(),Jt.on(`${qe.DELETE_LEFT_FLIGHT}${e.activeTag}`,L=>{if(L.includes(qe.DELETE_LEFT_PLACEHOLDER_FLIGHT)){const R=L.replace(qe.DELETE_LEFT_PLACEHOLDER_FLIGHT,"");T(R);return}V(L)})}),Rt(()=>{v.value.length=0,d.value.length=0,y.length=0,p.length=0,Jt.off(`${qe.DELETE_LEFT_FLIGHT}${e.activeTag}`)}),{selectCabin:C,multDayQuery:P,retractMultDayQuery:_,segCabins:v,multDayCabinQ:l,loading:r,transferText:Y,visibleCabinPopoverIndex:n,closeCabinPopover:m,confirmTktNum:oe,airportsDbData:c}},Fp={class:"w-full box-border border border-brand-3 mb-[10px] last:mb-0 rounded flex-none flex flex-col items-start px-[10px] py-[6px] gap-2.5 order-3 grow-0 self-stretch"},Lp={key:0},Np={class:"flex"},wp=o("div",{class:"self-center bg-gray-7 h-px flex-1 ml-1 text-xs font-bold"},null,-1),Ip={key:0},Ep={key:1,class:"text-center leading-6"},Pp={key:1},Rp={class:"flex"},Op={key:0,class:"px-0.5 bg-brand-3 rounded-sm justify-center items-center inline-flex"},Qp={class:"text-center text-brand-1 text-xs leading-4"},Mp={key:0,class:"ml-1"},Vp={key:1,class:"ml-1"},Bp=Ye({__name:"FlightItemInfo",props:{segmentData:{},tktType:{},sortIndex:{},departureDate:{},notSelectable:{type:Boolean},autoSelectedCabin:{type:Boolean},flightNoQueryFlag:{type:Boolean},queryLowestFare:{type:Boolean},activeTag:{},visibleCabinPopover:{type:Boolean},showPlaceholderPopoverIndex:{},autoPlaceholder:{type:Boolean},avQueryFromFastQuery:{type:Boolean},confirmText:{},tktNum:{}},emits:["selectCabin","closeCabinPopover"],setup(e,{expose:a,emit:i}){const r=e,c=i,{segCabins:n,multDayQuery:l,retractMultDayQuery:v,selectCabin:d,multDayCabinQ:y,loading:p,transferText:h,airportsDbData:S,visibleCabinPopoverIndex:k,closeCabinPopover:T,confirmTktNum:V}=kp(r,c);return a({closeCabinPopover:T}),(b,G)=>{const w=la;return Ct((u(),$("div",Fp,[(u(!0),$(he,null,Ie(t(n)??[],(E,D)=>{var I,m,C,A,_,P;return u(),$("div",{key:`${D}${new Date}`,class:"w-full"},[f(yp,{"seg-info":(I=b.segmentData)==null?void 0:I.segments[D],"flight-stops":(C=(m=b.segmentData)==null?void 0:m.segments[D])==null?void 0:C.transitCities,"seg-index":E.segIndex,"seg-order":D,"is-transfer":t(n).length>1,"sort-index":b.sortIndex,"ignore-resolution":b.notSelectable,"multi-day-cabin":E.multDayCabins[0],"query-lowest-fare":b.queryLowestFare,"airports-db-data":t(S),onMultiDayQuery:(U,K,Y)=>{var ne;return t(l)(U,K,Y,(ne=b.segmentData)==null?void 0:ne.segments[D])},onRetractMultiDayQuery:U=>{var K;return t(v)(U,(K=b.segmentData)==null?void 0:K.segments[D])}},{default:x(()=>[t(y)?(u(),$("div",Lp,[(u(!0),$(he,null,Ie((E==null?void 0:E.multDayCabins)??[],(U,K)=>{var Y;return u(),$("div",{key:`${K}${new Date}`},[o("div",Np,[o("div",null,g((U==null?void 0:U.day)??""),1),wp]),U.cabins.length>0?(u(),$("div",Ip,[f(Ja,{"visible-cabin-popover":b.visibleCabinPopover&&t(k)===`${D}-${K}`,cabins:U.cabins,"active-cabin":U.activeCabin,"sk-query":!1,"seg-index":E.segIndex,day:U.day,"not-selectable":b.notSelectable,"seg-info":(Y=b.segmentData)==null?void 0:Y.segments[D],"active-tag":b.activeTag,"auto-placeholder":b.autoPlaceholder,"tkt-num":b.tktNum,"confirm-text":b.confirmText,"av-query-from-fast-query":b.avQueryFromFastQuery,onSelectCabin:ne=>t(d)(ne,D,K),onCloseCabinPopover:t(V)},null,8,["visible-cabin-popover","cabins","active-cabin","seg-index","day","not-selectable","seg-info","active-tag","auto-placeholder","tkt-num","confirm-text","av-query-from-fast-query","onSelectCabin","onCloseCabinPopover"])])):(u(),$("div",Ep,"NO-OP"))])}),128))])):(u(),$("div",Pp,[(u(!0),$(he,null,Ie((E==null?void 0:E.multDayCabins)??[],(U,K)=>{var Y,ne,oe,Z,H,O,L,R,N;return u(),$("div",{key:`${K}${new Date}`},[o("div",Rp,[o("div",null,g((U==null?void 0:U.day)??""),1)]),b.queryLowestFare?(u(),$("div",Op,[o("div",Qp,[o("span",null,g(b.$t("app.avSearch.lowestFare")),1),(ne=(Y=b.segmentData)==null?void 0:Y.segments[D])!=null&&ne.fare||(Z=(oe=b.segmentData)==null?void 0:oe.segments[D])!=null&&Z.fareClass?(u(),$("span",Mp,g(((O=(H=b.segmentData)==null?void 0:H.segments[D])==null?void 0:O.fareClass)??"")+g(`: ${((R=(L=b.segmentData)==null?void 0:L.segments[D])==null?void 0:R.fare)??""}`),1)):(u(),$("span",Vp,"NO PRICE"))])])):ge("",!0),f(Ja,{"visible-cabin-popover":b.visibleCabinPopover&&t(k)===`${D}-${K}`,cabins:U.cabins,"active-cabin":U.activeCabin,"sk-query":!1,"seg-index":E.segIndex,day:U.day,"not-selectable":b.notSelectable,"seg-info":(N=b.segmentData)==null?void 0:N.segments[D],"active-tag":b.activeTag,"auto-placeholder":b.autoPlaceholder,"tkt-num":b.tktNum,"confirm-text":b.confirmText,"av-query-from-fast-query":b.avQueryFromFastQuery,onSelectCabin:F=>t(d)(F,D,K),onCloseCabinPopover:t(V)},null,8,["visible-cabin-popover","cabins","active-cabin","seg-index","day","not-selectable","seg-info","active-tag","auto-placeholder","tkt-num","confirm-text","av-query-from-fast-query","onSelectCabin","onCloseCabinPopover"])])}),128))]))]),_:2},1032,["seg-info","flight-stops","seg-index","seg-order","is-transfer","sort-index","ignore-resolution","multi-day-cabin","query-lowest-fare","airports-db-data","onMultiDayQuery","onRetractMultiDayQuery"]),((_=(A=b.segmentData)==null?void 0:A.segments[D])==null?void 0:_.transferDate)!==""?(u(),Se(Tp,{key:0,"transfer-text":t(h)((P=b.segmentData)==null?void 0:P.segments[D]),class:"my-3.5"},null,8,["transfer-text"])):ge("",!0)])}),128))])),[[w,t(p)]])}}}),Yp=(e,a)=>{const{t:i}=gt(),r=un(),c=dn(),n=ca(),l="",v=B([]),d=B(l),y=B(e.avQueryFromFastQuery?i("app.pnrManagement.flight.bookTicket"):""),p=66,h=B(!1),S=B(!1),k=Xt(),T=kt(),{activeTag:V,orderInfo:b}=Ft(T),{saveCrsTemporaryOrder:G}=Mn(),w=ta(),E=Ee(()=>{var ce;const ee=e.avQueryFromFastQuery?wt:T.activeTag;return(ce=w.flightByDate[ee])==null?void 0:ce.flightDataList.size}),D=B([]),I=B(!1),m=B(!1),C=B({filterCondition:[],sortTypeNum:0,filterDeparture:[],filterArrival:[],transferTimes:[],filterDepartureTime:[],filterArrivalTime:[]}),A=B(),_=B(),P=B(),U=B(!1),K=Ee(()=>{const ee=[];return[...e.avSearchData??[]].forEach(ce=>{((ce==null?void 0:ce.segments)??[]).forEach(pe=>{var ke,De;ee.includes(((ke=pe==null?void 0:pe.airlines)==null?void 0:ke.airCode)??"")||ee.push(((De=pe==null?void 0:pe.airlines)==null?void 0:De.airCode)??"")})}),ee}),Y=Ee(()=>Array.from(new Set([...e.avSearchData??[]].map(ee=>{var ce;return((ce=ee.segments??[])==null?void 0:ce[0].departureAirportCode)??""})))),ne=Ee(()=>Array.from(new Set([...e.avSearchData??[]].map(ee=>{var ce,pe;return((pe=(ce=ee.segments??[])==null?void 0:ce[ee.segments.length-1])==null?void 0:pe.arrivalAirportCode)??""})))),oe=Ee(()=>{var ee;return(ee=k.getters.userPreferences)==null?void 0:ee.autoSelectCabinClass}),Z=B(0),H=Ee(()=>{var ce;return!(I.value&&m.value)&&((ce=k.getters.userPreferences)==null?void 0:ce.autoOccupy)&&(e.avQueryFromFastQuery||!Number(b.value.get(V.value).psgType)&&b.value.get(V.value).type==="2")}),O=Ee(()=>{var ee;return((ee=k.getters.userPreferences)==null?void 0:ee.autoSearch)==="1"}),L=B(0);let R=!1;const N=Ee(()=>e.currentPage??1),F=B(0),ae=ee=>ee||se(e.queryForm.departureDate).format("YYYY-MM-DD"),le=()=>{var ce;const ee=(ce=k.state.user)==null?void 0:ce.agentManageStatus;return ee===3||ee===4},de=async ee=>{var pe;switch(h.value=(e.avSearchData??[]).length,await Tt(),D.value=[...e.avSearchData??[]],ee.filterCondition.length>0&&(D.value=D.value.filter(ke=>{let De=!1;return ee.filterCondition.forEach(M=>ke.segments.forEach(q=>{M===q.airlines.airCode&&(De=!0)})),De})),ee.filterDeparture.length>0&&(D.value=D.value.filter(ke=>{let De=!1;return ee.filterDeparture.forEach(M=>{var q,J;M===((J=(q=ke.segments??[])==null?void 0:q[0])==null?void 0:J.departureAirportCode)&&(De=!0)}),De})),ee.filterArrival.length>0&&(D.value=D.value.filter(ke=>{let De=!1;return ee.filterArrival.forEach(M=>{var q,J;M===((J=(q=ke.segments??[])==null?void 0:q[ke.segments.length-1])==null?void 0:J.arrivalAirportCode)&&(De=!0)}),De})),ee.transferTimes.length>0&&(D.value=D.value.filter(ke=>{const De=ke.segments.length-1,M=[];return ke.segments.some(J=>Number((J==null?void 0:J.stopCity)??0))?(M.push(-1),De&&M.push(De)):M.push(De),M.some(J=>ee.transferTimes.includes(J.toString()))})),D.value=Ul(ee,D.value),ee.sortTypeNum){case 0:D.value.sort((ke,De)=>{var M,q;return se((((M=ke.segments)==null?void 0:M[0])??[]).departureDate).unix()-se((((q=De.segments)==null?void 0:q[0])??[]).departureDate).unix()});break;case 1:D.value.sort((ke,De)=>{var M,q;return se((((M=De.segments)==null?void 0:M[0])??[]).departureDate).unix()-se((((q=ke.segments)==null?void 0:q[0])??[]).departureDate).unix()});break;case 2:Ga(D.value,!0);break;case 3:Ga(D.value,!1);break}L.value+=1;const ce=((pe=_.value)==null?void 0:pe.getBoundingClientRect().height)/p+3;Z.value=(D.value??[]).length<ce?(D.value??[]).length:Number(ce.toFixed(0)),U.value=!U.value,D.value.length||Ae(!1)},fe=()=>{const ee=Z.value+3;ee>(D.value??[]).length?Z.value=(D.value??[]).length:Z.value=ee},te=ee=>{const{transferCabinCache:ce,day:pe,travelKey:ke}=ee;if(e.queryForm.flightNumber){const De=`${ke}>${ae(pe)}`;T.delSeizeSeatInfoFlight(V.value,De)}else ce.forEach(De=>ae(pe)===ae(De.day)&&T.delSeizeSeatInfoFlight(V.value,`${De.segIndex}>${ae(De.day)}`));vt(qe.UPDATE_FLIGHT_LIST,"")},Te=(ee,ce)=>(ee??[]).map(pe=>{var De,M;const ke=pe.cabins;return{airline:"",airNo:`${pe.airlines.airCode??""}${pe.airlines.flightNo??""}`,fltClass:((M=(De=ke==null?void 0:ke[0])==null?void 0:De.cabinName)==null?void 0:M.substring(0,1))??"",orgCity:pe.departureAirportCode??"",actionCode:pe.actionCode??"",desCity:pe.arrivalAirportCode??"",tktNum:ce.length,departureTime:se(pe.departureDate).format("YYYY-MM-DD"),type:0}}),be=async(ee,ce)=>{S.value=!0;try{const{data:pe}=await yo(ce);pe.value==="OK"&&($a({message:i("app.avSearch.addSegmentSuccess"),type:"success"}),G(pn(gn.UPDATE,b.value.get(V.value))),vt(`PnrManagement.vue${ee}`,ee))}finally{S.value=!1}},Re=(ee,ce)=>{var pe,ke;if(((pe=ee.segments)==null?void 0:pe[0].segmentType)==="1"){const De=ee.segments.map(J=>{var Me;return(Me=J.cabins[0])==null?void 0:Me.cabinName}).join(""),M=ce.map(J=>J==null?void 0:J.cabinName).join("");return(De!==M||!["RR","HK"].includes(((ke=ee.segments)==null?void 0:ke[0].actionCode)??""))&&(ee.disabled=!0),!0}return!1},X=ee=>{const{transferCabinCache:ce}=ee,ke=(b.value.get(`${V.value}`).flight??[]).find(De=>De.disabled?!1:ce.find(M=>{const q=M.segInfo,J=se(q.departureDate).format("YYYY-MM-DD"),Me=`${q.airlines.airCode}${q.airlines.flightNo}${J}`,Ce=De.segments[0],Ke=se(Ce.departureDate).format("YYYY-MM-DD"),tt=`${Ce.airlines.airCode}${Ce.airlines.flightNo}${Ke}`;return Ce.segmentType!=="2"&&Me===tt}));if(ke){if(Re(ke,ce))return;T.delSeizeSeatInfoFlight(V.value,ke.key)}},re=(ee,ce,pe)=>{if((ce==null?void 0:ce.segments)??[])for(let De=0;De<ce.segments.length;De++){const M=ce.segments[De];if(ee===pe)return M}return null},z=ee=>{const{transferCabinCache:ce,day:pe,travelKey:ke,segmentData:De,tktType:M}=it(ee),q=[];return ce.forEach((J,Me)=>{var Ke;const Ce=J.segInfo;if(Ce&&`${ae(J.day)}`==`${ae(pe)}`){const tt=[],nt={cabinName:J.cabinName,state:J.cabinStatus,day:J.day};tt.push(nt);const Ze={airlines:Ce.airlines,arrDays:Ce.arrDays,deptArrdays:Ce.deptArrdays,arrivalArrdays:Ce.arrivalArrdays,arrivalAirportCN:Ce.arrivalAirportCN,arrivalAirportCode:Ce.arrivalAirportCode,arrivalDate:(pe==null?void 0:pe.length)>0?`${pe}${Ce.arrivalDate.substring(10)}`:Ce.arrivalDate,arrivalTerminal:Ce.arrivalTerminal,arrivalTime:Ce.arrivalTime,asr:Ce.asr,cabins:tt,connectLevel:Ce.connectLevel,departureAirportCN:Ce.departureAirportCN,departureAirportCode:Ce.departureAirportCode,departureDate:(pe==null?void 0:pe.length)>0?`${pe}${Ce.departureDate.substring(10)}`:Ce.departureDate,departureTerminal:Ce.departureTerminal,departureTime:Ce.departureTime,flightDistance:Ce.flightDistance,flightTime:Ce.flightTime,stopCity:Ce.stopCity,transitCities:Ce.transitCities??[],marriedSegmentNumber:Ce.marriedSegmentNumber,transferDate:Me>0?(Ke=re(ce[Me-1].segIndex,De,ke))==null?void 0:Ke.transferDate:"",tcFlight:Ce.tcFlight,commonMeal:Ce.commonMeal,etInd:Ce.etInd??!1,segmentType:"0"},lt=se(Ze.departureDate).format("YYYY-MM-DD"),jt={arriveCity:{code:J.arrivalAirportCode,name:""},departureCity:{code:J.departureAirportCode,name:""},date:{departureDate:lt},segments:[Ze],tktType:M??"",key:`${J.segIndex}>${ae(J.day)}`,openFlag:!1};q.push(jt)}}),q},j=(ee,ce)=>{const pe=ee.split("-");return ce?`${pe[0]}-${pe[1]}-${ce}`:`${pe[0]}-${pe[1]}`},W=async ee=>{if(!ee){d.value=l;return}let ce=ee??1,pe=qe.PLACEHOLDER_FLIGHT;e.avQueryFromFastQuery&&(n.closeFastQuery(),e.placeholderFlagTag||(pe=qe.FAST_QUERY_AV_PLACEHOLDER_FLIGHT),c.path.includes("crs/pnrManagement")||(k.dispatch("setFullLoading",!0),await r.push({path:"v2/crs/pnrManagement"}),await Tt()),e.placeholderFlagTag&&(await T.setActiveTag(e.placeholderFlagTag),ce=F.value)),d.value=j(d.value,Co),T.setPlaceholderFlightsParams({flightKeys:[],params:{bookAirSegs:[]},seizeSeatInfoFlight:[]},ce),vt(qe.UPDATE_FLIGHT_LIST,`${pe}-${V.value}`),k.dispatch("setFullLoading",!1)},ue=async(ee,ce,pe)=>{var nt;const{transferCabinCache:ke,day:De,segmentData:M,tktNum:q}=ee,J=b.value.get(V.value),Me=(nt=J==null?void 0:J.occupySpecialContent)==null?void 0:nt.includes(i("app.basic.occupy"));if(!e.queryForm.flightNumber&&M.segments.length>1&&!De){const Ze=ke.filter(lt=>`${ae(lt.day)}`==`${ae(De)}`&&lt.cabinName).length===M.segments.length;if(d.value=j(ce),!Ze)return}d.value=Me?j(ce):ce;const Ce=pe.map(Ze=>Ze.key??"");let Ke=q??1;if(Me){Ke=J.flight.find(jt=>jt.segments.some(Gt=>Gt.segmentType==="2")).segments[0].tktNum??1;const lt=ga(Ke??1,pe);T.setPlaceholderFlightsParams({flightKeys:Ce,params:lt,seizeSeatInfoFlight:pe}),vt(qe.UPDATE_FLIGHT_LIST,`${qe.PLACEHOLDER_FLIGHT}-${V.value}`);return}const tt=ga(Ke??1,pe);T.setPlaceholderFlightsParams({flightKeys:Ce,params:tt,seizeSeatInfoFlight:pe})},Le=async(ee,ce,pe)=>{var Ce,Ke,tt;const{transferCabinCache:ke,day:De,segmentData:M,tktNum:q}=ee;if(!e.queryForm.flightNumber&&M.segments.length>1&&!De){const nt=ke.filter(Ze=>`${ae(Ze.day)}`==`${ae(De)}`&&Ze.cabinName).length===M.segments.length;if(d.value=j(ce),!nt)return}const J=pe.map(nt=>nt.key??"");d.value=ce,F.value=e.placeholderFlagTag?((tt=(Ke=(Ce=b.value.get(e.placeholderFlagTag))==null?void 0:Ce.flight)==null?void 0:Ke[0])==null?void 0:tt.segments[0].tktNum)??0:0;const Me=ga(q??1,pe);T.setPlaceholderFlightsParams({flightKeys:J,params:Me,seizeSeatInfoFlight:pe}),T.setPlaceholderFlightsQueryForm(e.queryForm)},$e=(ee,ce)=>{const{transferCabinCache:pe,day:ke,segmentData:De}=it(ee);!e.queryForm.flightNumber&&De.segments.length>1&&!ke&&!(pe.filter(q=>`${ae(q.day)}`==`${ae(ke)}`&&q.cabinName).length===De.segments.length)||(ce.forEach(M=>T.setFlight(V.value,M)),vt(qe.UPDATE_FLIGHT_LIST,""))},Oe=async(ee,ce)=>{const pe=ee,ke=z(ee);if(H.value){if(e.avQueryFromFastQuery){Le(ee,ce,ke);return}ue(ee,ce,ke);return}d.value=j(ce),X(pe),$e(pe,ke)},Ae=ee=>a("update:scrollHide",ee),Pe=()=>{N.value!==1&&a("turnPage",N.value,"P")},Ge=ee=>{a("screeningDirect",ee)},ot=ee=>{a("changeDate",ee)},st=()=>{a("turnPage",N.value,"N")},Be=ee=>!((ee==="pre"||!ee)&&N.value===1),ut=Mt(ee=>{if(!P.value||e.avQueryFromFastQuery)return;const ce=ee.deltaY<0,pe=ee.deltaY>0;if(ce&&O.value){R=!1,Ae(R);return}if(pe&&O.value){R=!0,Ae(R);return}},300),Ue=()=>{var ee;(ee=P.value)==null||ee.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"}),R=!1,Ae(!1)};return Xe(()=>e.searchCompleteKey,async()=>{D.value=[],await Tt(),de(C.value)}),bt(async()=>{I.value=await k.getters.userSeatDisabled,m.value=le(),C.value.sortTypeNum=-1,de(C.value),document.addEventListener("click",()=>{W()})}),Rt(()=>{D.value.length=0}),{loadFlightData:fe,cancelSelectCabin:te,selectCabin:Oe,flightDataListSize:E,updateKey:L,count:Z,airlineList:K,SortFlightData:de,flightData:D,turnPre:Pe,currentPage:N,turnNext:st,screeningDirect:Ge,isAllowTurn:Be,changeDate:ot,dynamicScrollerRef:A,flightsContainerRef:_,flightsBoxRef:P,scrollToTop:Ue,scrollFlightsContent:ut,loading:S,moreDeparture:Y,moreArrival:ne,activeTag:V,buildSegmentInfo:Te,doAddSegment:be,updateFlightListFlag:U,autoSelectedCabin:oe,showPlaceholderPopoverIndex:d,flightItemInfoRefs:v,autoPlaceholder:H,closeCabinPopover:W,confirmText:y,placeholderNum:F,showContainer:h}},qp={key:0,class:"w-full h-8 mt-2.5 px-2.5 bg-yellow-3 rounded border border-solid border-yellow-2 flex-col justify-center items-start gap-2.5 inline-flex"},Up={class:"justify-start items-center gap-1 inline-flex text-yellow-1"},Hp={class:"text-xs leading-5"},jp={class:"pagination"},Gp={class:"page-panel"},Kp={key:1,class:"dynamic-no-data"},zp=Ye({__name:"FlightContainer",props:{queryForm:{},avSearchData:{},currentPage:{},searchCompleteKey:{default:""},queryResDateGroupBySessionId:{},flightOpRightTop:{},avQueryFromFastQuery:{type:Boolean},isQueryCurrentDay:{type:Boolean},placeholderFlagTag:{}},emits:["screeningDirect","turnPage","changeDate","update:scrollHide"],setup(e,{expose:a,emit:i}){const r=e,c=i,{updateKey:n,airlineList:l,SortFlightData:v,screeningDirect:d,flightData:y,selectCabin:p,scrollToTop:h,dynamicScrollerRef:S,updateFlightListFlag:k,autoPlaceholder:T,placeholderNum:V,changeDate:b,flightDataListSize:G,cancelSelectCabin:w,scrollFlightsContent:E,flightsContainerRef:D,flightsBoxRef:I,showContainer:m,activeTag:C,closeCabinPopover:A,confirmText:_,flightItemInfoRefs:P,isAllowTurn:U,turnPre:K,turnNext:Y,loadFlightData:ne,count:oe,loading:Z,moreDeparture:H,moreArrival:O,autoSelectedCabin:L,showPlaceholderPopoverIndex:R}=Yp(r,c);return a({closeCabinPopover:A}),(N,F)=>{const ae=je,le=la,de=di;return u(),$("div",{ref_key:"dynamicScrollerRef",ref:S,class:"dynamic-scroller relative"},[f(_c,{airlines:t(l),"av-search-data":N.avSearchData,"current-flight-list":t(y),"update-flight-list-flag":t(k),"query-form-params":N.queryForm,"query-res-date-group-by-session-id":N.queryResDateGroupBySessionId,"more-departure":t(H),"more-arrival":t(O),onSortFlight:t(v),onScreeningDirect:t(d),onChangeDate:t(b)},null,8,["airlines","av-search-data","current-flight-list","update-flight-list-flag","query-form-params","query-res-date-group-by-session-id","more-departure","more-arrival","onSortFlight","onScreeningDirect","onChangeDate"]),t(m)?(u(),$(he,{key:0},[N.isQueryCurrentDay?ge("",!0):(u(),$("div",qp,[o("div",Up,[f(ae,{size:16},{default:x(()=>[f(t(fn))]),_:1}),o("div",Hp,g(N.$t("app.avSearch.noFlightTip")),1)])])),Ct((u(),$("div",{ref_key:"flightsContainerRef",ref:D,"infinite-scroll-distance":"3",class:"infinite-list infinite-list-height flight-list-scroller overflow-hidden flex-1",onWheel:F[0]||(F[0]=(...fe)=>t(E)&&t(E)(...fe))},[t(G)>0&&t(y).length>0?(u(),$("div",{key:0,ref_key:"flightsBoxRef",ref:I,class:"py-[10px]"},[(u(!0),$(he,null,Ie(t(oe),fe=>{var te;return u(),Se(Bp,{key:`${fe}${t(n)}`,ref_for:!0,ref:Te=>{Te&&t(P).push(Te)},"not-selectable":!!N.avQueryFromFastQuery&&!t(T),"flight-no-query-flag":!!N.queryForm.flightNumber,"sort-index":fe-1,"tkt-type":t(y)[fe-1].tktType,"segment-data":t(y)[fe-1],"departure-date":N.queryForm.departureDate,"active-tag":t(C),"show-placeholder-popover-index":t(R),"more-departure":t(H),"more-arrival":t(O),"query-lowest-fare":N.queryForm.lowestPrice,"auto-selected-cabin":t(L),"visible-cabin-popover":t(T)&&Number((te=t(R).split("-"))==null?void 0:te[0])===fe-1&&t(R).split("-").length===3,"tkt-num":t(V),"av-query-from-fast-query":N.avQueryFromFastQuery,"confirm-text":t(_),"auto-placeholder":t(T),onCloseCabinPopover:t(A),onSelectCabin:(Te,be)=>t(p)(Te,`${fe-1}-${be}`),onCancelSelectCabin:t(w)},null,8,["not-selectable","flight-no-query-flag","sort-index","tkt-type","segment-data","departure-date","active-tag","show-placeholder-popover-index","more-departure","more-arrival","query-lowest-fare","auto-selected-cabin","visible-cabin-popover","tkt-num","av-query-from-fast-query","confirm-text","auto-placeholder","onCloseCabinPopover","onSelectCabin","onCancelSelectCabin"])}),128))],512)):ge("",!0)],32)),[[le,t(Z)],[de,t(ne)]]),o("div",jp,[o("div",{class:ye(["operate",t(U)("pre")?"able-to-operate":"disable-to-operate"]),"data-gid":"01010211",onClick:F[1]||(F[1]=(...fe)=>t(K)&&t(K)(...fe))},g(N.$t("app.avSearch.previousPage")),3),o("span",Gp,g(N.$t("app.avSearch.noPage",{currentPage:N.currentPage})),1),o("div",{class:ye(["operate",t(U)("next")?"able-to-operate":"disable-to-operate"]),"data-gid":"01010212",onClick:F[2]||(F[2]=(...fe)=>t(Y)&&t(Y)(...fe))},g(N.$t("app.avSearch.nextPage")),3)]),N.avQueryFromFastQuery?ge("",!0):(u(),$("div",{key:1,class:"absolute right-[1px] bottom-[60px] cursor-pointer w-[28px] h-[28px] p-[6px] bg-white rounded shadow border border-brand-2 animate-bounce flex items-center justify-center",onClick:F[3]||(F[3]=(...fe)=>t(h)&&t(h)(...fe))},[f(ae,{class:"w-[16px] h-[16px]",size:"16px"},{default:x(()=>[f(t(mn),{class:"text-brand-2"})]),_:1})]))],64)):(u(),$("div",Kp,g(N.$t("app.avSearch.noData")),1))],512)}}});const Wp=et(zp,[["__scopeId","data-v-f1d7d1e5"]]),Jp=(e,a)=>{const{t:i}=gt(),r=Xt(),c=ro("--bkc-el-color-primary",null),n=B(),l=B(),v=B(),d=kt(),{orderInfo:y,activeTag:p}=Ft(d),h=B(""),S=B({...e.ssQueryForm,tktNum:e.tktNum||e.ssQueryForm.tktNum}),k=ct({render(){return ht("em",{class:"iconfont icon-calendar"})}});let T="";const V=(R,N,F)=>{if(N.length>3||!St.test(N)){F(new Error(i("app.fastQuery.headerQuery.formatError")));return}if(S.value.arrivalAirportCode&&N===S.value.arrivalAirportCode){F(new Error(i("app.fastQuery.headerQuery.identical")));return}F()},b=(R,N,F)=>{if(N.length>3||!St.test(N)){F(new Error(i("app.fastQuery.headerQuery.formatError")));return}if(S.value.departureAirportCode&&N===S.value.departureAirportCode){F(new Error(i("app.fastQuery.headerQuery.identical")));return}F()},G=(R,N,F)=>{if(N){const ae=/^\d+$/.test(N.slice(0,2))?"":N.slice(0,2);bn.test(N)?ae?!S.value.airlines||ae===S.value.airlines?F():F(new Error(i("app.avSearch.validateFltAirline"))):F():F(new Error(i("app.avSearch.validateFltNo")));return}},w=(R,N,F)=>{if(N&&T&&Number(N)!==Number(T)){F(new Error(i("app.fastQuery.headerQuery.inconsistentNum")));return}F()},E=Ee(()=>({departureAirportCode:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:V,trigger:"blur"}],arrivalAirportCode:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:b,trigger:"blur"}],departureDate:[{required:!S.value.isOpen,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"}],cabinCode:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:vn,message:new Error(i("app.fastQuery.headerQuery.inputCorrectCabin")),trigger:"blur"}],airlines:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:no,message:new Error(i("app.fastQuery.headerQuery.correctAirlineNum")),trigger:"blur"}],flightNumber:[{required:!S.value.isOpen,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{validator:G,trigger:"blur"}],actionCode:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:hn,message:new Error(i("app.fastQuery.headerQuery.inputCorrectActionCode")),trigger:"blur"}],tktNum:[{required:!0,message:i("app.fastQuery.headerQuery.must"),trigger:"blur"},{pattern:yn,message:new Error(i("app.fastQuery.headerQuery.inputCorrectTktNum")),trigger:"blur"},{validator:w,trigger:"blur"}]})),D=()=>{const R=S.value.departureAirportCode;S.value.departureAirportCode=S.value.arrivalAirportCode,S.value.arrivalAirportCode=R;const N=S.value.originName;S.value.originName=S.value.destName,S.value.destName=N},I=()=>{a("inputTktNumBlur")},m=R=>{const N=Ta();return R.getTime()<N.getTime()},C=R=>{S.value.seamlessOrDa===R?S.value.seamlessOrDa="":S.value.seamlessOrDa=R},A=R=>{a("deleteSsFlight",R)},_=async(R,N)=>{a("update:addSegLoading",!0);try{const{data:F}=await yo(N);F.value==="OK"&&($a({message:i("app.avSearch.addSegmentSuccess"),type:"success"}),vt(`PnrManagement.vue${R}`,R))}finally{a("update:addSegLoading",!1)}},P=(R,N)=>{let F=-1;return S.value.arrivalAirportCode===R.segments[0].departureAirportCode?S.value.departureAirportCode===R.segments[R.segments.length-1].arrivalAirportCode?(F=N+1,F):(F=N,F):((S.value.departureAirportCode===R.segments[R.segments.length-1].arrivalAirportCode||!R.openFlag)&&(F=N+1),F)},U=(R,N)=>{const F=y.value.get(R);Ma(F)?F.rebook.flight=it(N):F.flight=it(N),F.flightNotNeedSort=!1,y.value.set(R,F),vt(qe.UPDATE_FLIGHT_LIST,"")},K=(R,N)=>{N.forEach(F=>{A(F);const{departureAirportCode:ae,arrivalAirportCode:le,departureDate:de,departureTime:fe,arrivalDate:te,arrivalTime:Te,departureTerminal:be,arrivalTerminal:Re,departureAirportCN:X,arrivalAirportCN:re,actionCode:z}=F,j={},W={};W.airCode=F.airlines,W.airCN=F.airCN??"",W.isShared=F.isShared??"",W.flightNo=F.isOpen?"OPEN":/^\d+[a_zA_Z]?$/.test(F.flightNumber)?F.flightNumber:F.flightNumber.slice(2),j.departureAirportCode=ae,j.departureAirportCN=X??"",j.arrivalAirportCode=le,j.arrivalAirportCN=re??"",j.departureDate=de?`${se(de).format("YYYY-MM-DD HH:mm")}`:"",j.cabins=[{cabinName:F.cabinCode,state:""}],j.airlines=W,z&&(j.actionCode=z),j.segmentType="0",Object.assign(j,{departureTime:fe,arrivalDate:te,arrivalTime:Te,departureTerminal:be,arrivalTerminal:Re});const ue={code:F.arrivalAirportCode,name:""},Le={code:F.departureAirportCode,name:""},$e={};if($e.openFlag=F.isOpen,$e.segments=[j],$e.arriveCity=ue,$e.departureCity=Le,$e.key=io($e),F.departureDate&&F.isOpen){let Oe=0;R.some((Ae,Pe)=>{if(se(F.departureDate).isBefore(se(Ae.segments[0].departureDate),"day"))return Oe=Pe,!0;if(se(F.departureDate).isSame(se(Ae.segments[0].departureDate),"day")){if(R.length>1&&R.length!==Pe+1&&Ae.segments[Ae.segments.length-1].arrivalAirportCode===R[Pe+1].segments[0].departureAirportCode)return!1;if(Oe=P(Ae,Pe),Oe>-1)return!0}return Oe=Pe+1,!1}),R.splice(Oe,0,$e),U(p.value,R);return}R.push($e),d.setFlight(p.value,$e),vt(qe.UPDATE_FLIGHT_LIST,"")})},Y=async R=>{T=R;let N=!1;return await n.value.validate(F=>{N=F}),N},ne=R=>se(R).isValid()?se(R).format("YYYY-MM-DDTHH:mm"):"",oe=async(R,N)=>{T=N,S.value[R]&&await n.value.validateField(R)},Z=R=>{const N={bookAirSegs:[]};return R.forEach(F=>{const{tktNum:ae,actionCode:le,cabinCode:de,departureAirportCode:fe,arrivalAirportCode:te,departureDate:Te,airlines:be,flightNumber:Re,departureTerminal:X,arrivalTerminal:re,isShared:z}=F,j=/^\d+[a_zA_Z]?$/.test(Re)?Re:Re.slice(2),W={fltClass:de??"",orgCity:fe,desCity:te,departureTime:ne(Te),arrivalTime:ne(Te),airCode:be+j,carrierAirline:z??"",tktNum:(ae==null?void 0:ae.toString())??"",equipmentCode:"",etInd:!0,departureTerminal:X??"",arrivalTerminal:re??"",actionCode:le};N.bookAirSegs.push(W)}),N},H=R=>{var Te;const N=p.value,F=Ma(y.value.get(N));let ae=[],le=[];const de=y.value.get(N).flight??[],fe=((Te=y.value.get(N).rebook)==null?void 0:Te.flight)??[];if(F?(ae=[...fe],le=[...de,...fe]):(ae=[...de],le=[...de]),le.length>0?le.length&&le.some(be=>!be.openFlag):R.some(be=>!be.isOpen)){const be=[],Re=[];if(R.forEach(re=>re.isOpen||e.isGroup?be.push(re):Re.push(re)),K(ae,be),!Re.length){vt(qe.UPDATE_FLIGHT_LIST,qe.SS_ADD_FLIGHT_SUCCESS);return}const X=Z(Re);d.setPlaceholderFlightsParams({flightKeys:[],params:X,seizeSeatInfoFlight:[]}),r.dispatch("setFullLoading",!0),vt(qe.UPDATE_FLIGHT_LIST,`${qe.EDIT_FLIGHT_PLACEHOLDER}${qe.SS_ADD_FLIGHT_PLACEHOLDER}`)}else so.confirm(i("app.avSearch.addOpenFlightTips"),{icon:ht(je,{color:c.value,size:32},()=>ht(lo)),customClass:"warning-p-msg crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:i("app.avSearch.confirm")})},O=()=>{S.value.flightNumber="",S.value.actionCode="",S.value.tktNum=e.tktNum??"",n.value.clearValidate()},L=R=>{h.value=R};return Xe(()=>e.tktNum,()=>{e.tktNum&&(S.value.tktNum=e.tktNum)}),{queryFormRef:n,queryForm:S,FORM_RULES:E,revertFromTo:D,disabledDate:m,datePrefix:k,seamlessOrDaClick:C,agentAirportOriginRef:l,agentAirportDestinationRef:v,openChange:O,ssValidate:Y,ssValidateField:oe,doAddSegment:_,addSsFlightsToCache:H,focusErrorTip:L,inputTktNumBlur:I,deleteSsFlight:A,currentError:h}},Zp={class:"add-ss-flight flex justify-between"},Xp={class:"header-av-query-form flex flex-1"},eg=Ye({__name:"AddSSFlight",props:{cityOrAirport:{},ssQueryForm:{},index:{},showDelete:{type:Boolean},addSegLoading:{type:Boolean},isGroup:{type:Boolean},tktNum:{}},emits:["deleteSsFlight","update:addSegLoading","inputTktNumBlur"],setup(e,{expose:a,emit:i}){const r=e,c=i,{FORM_RULES:n,queryFormRef:l,revertFromTo:v,disabledDate:d,datePrefix:y,openChange:p,queryForm:h,deleteSsFlight:S,inputTktNumBlur:k,agentAirportOriginRef:T,agentAirportDestinationRef:V,ssValidate:b,ssValidateField:G,addSsFlightsToCache:w,focusErrorTip:E,currentError:D}=Jp(r,c);return a({ssValidate:b,ssValidateField:G,queryForm:h,addSsFlightsToCache:w}),(I,m)=>{const C=Bn,A=Ut,_=je,P=Pt,U=Ht;return u(),$("div",Zp,[f(U,{ref_key:"queryFormRef",ref:l,model:t(h),rules:t(n),"validate-on-rule-change":!1,class:"flight-from"},{default:x(()=>[o("div",null,[f(A,{class:"open-item",label:I.$t("app.avSearch.openFlight")},{default:x(()=>[f(C,{modelValue:t(h).isOpen,"onUpdate:modelValue":m[0]||(m[0]=K=>t(h).isOpen=K),"inline-prompt":"","active-text":I.$t("app.avSearch.openYes"),"inactive-text":I.$t("app.avSearch.openNo"),onChange:t(p)},null,8,["modelValue","active-text","inactive-text","onChange"])]),_:1},8,["label"])]),o("div",Xp,[f(A,{label:" ",class:"agent-airport-item",prop:"departureAirportCode"},{default:x(()=>[f(Yt,{ref_key:"agentAirportOriginRef",ref:T,modelValue:t(h).departureAirportCode,"onUpdate:modelValue":m[1]||(m[1]=K=>t(h).departureAirportCode=K),name:t(h).originName,"onUpdate:name":m[2]||(m[2]=K=>t(h).originName=K),"is-agent-city":I.cityOrAirport,"prefix-title":I.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),o("div",{class:"mt-[6px] ml-[2px] mr-[2px] w-[14px] h-[14px] cursor-pointer",onClick:m[3]||(m[3]=(...K)=>t(v)&&t(v)(...K))},[f(_,{class:"sort-text"},{default:x(()=>[f(t(Da))]),_:1})]),f(A,{class:"agent-airport-item",prop:"arrivalAirportCode"},{default:x(()=>[f(Yt,{ref_key:"agentAirportDestinationRef",ref:V,modelValue:t(h).arrivalAirportCode,"onUpdate:modelValue":m[4]||(m[4]=K=>t(h).arrivalAirportCode=K),name:t(h).destName,"onUpdate:name":m[5]||(m[5]=K=>t(h).destName=K),"is-agent-city":I.cityOrAirport,"prefix-title":I.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),f(A,{label:" ",class:"departure-date",prop:"departureDate"},{default:x(()=>[f(Fa,{modelValue:t(h).departureDate,"onUpdate:modelValue":m[6]||(m[6]=K=>t(h).departureDate=K),type:"date","value-format":"YYYY-MM-DD","auto-compute-date":"","disabled-date":t(d),"disable-end-date":new Date().toString(),"prefix-icon":t(y),placeholder:I.$t("app.avSearch.date")},null,8,["modelValue","disabled-date","disable-end-date","prefix-icon","placeholder"])]),_:1}),f(A,{label:" ",class:ye(["airline-item",t(D)==="cabin"?"currentError":""]),prop:"cabinCode"},{default:x(()=>[f(P,{modelValue:t(h).cabinCode,"onUpdate:modelValue":m[7]||(m[7]=K=>t(h).cabinCode=K),modelModifiers:{trim:!0},class:"airlines-input",placeholder:I.$t("app.avSearch.cabinCode"),onInput:m[8]||(m[8]=K=>t(h).cabinCode=t(h).cabinCode.toUpperCase()),onFocus:m[9]||(m[9]=K=>t(E)("cabin"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"]),f(A,{label:" ",class:ye(["airline-item",t(D)==="airline"?"currentError":""]),prop:"airlines"},{default:x(()=>[f(P,{modelValue:t(h).airlines,"onUpdate:modelValue":m[10]||(m[10]=K=>t(h).airlines=K),modelModifiers:{trim:!0},class:"airlines-input",placeholder:I.$t("app.avSearch.airline"),onInput:m[11]||(m[11]=K=>t(h).airlines=t(h).airlines.toUpperCase()),onFocus:m[12]||(m[12]=K=>t(E)("airline"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"]),t(h).isOpen?ge("",!0):(u(),Se(A,{key:0,label:" ",class:ye([t(D)==="flightNo"?"currentError":""]),prop:"flightNumber"},{default:x(()=>[f(P,{modelValue:t(h).flightNumber,"onUpdate:modelValue":m[13]||(m[13]=K=>t(h).flightNumber=K),modelModifiers:{trim:!0},class:"small-width",placeholder:I.$t("app.avSearch.flightNumber"),onInput:m[14]||(m[14]=K=>t(h).flightNumber=t(h).flightNumber.toUpperCase()),onFocus:m[15]||(m[15]=K=>t(E)("flightNo"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"])),!t(h).isOpen&&!I.isGroup?(u(),Se(A,{key:1,label:" ",class:ye([t(D)==="actionCode"?"currentError":""]),prop:"actionCode"},{default:x(()=>[f(P,{modelValue:t(h).actionCode,"onUpdate:modelValue":m[16]||(m[16]=K=>t(h).actionCode=K),modelModifiers:{trim:!0},class:"small-width",placeholder:I.$t("app.avSearch.applicationStatus"),onInput:m[17]||(m[17]=K=>{var Y;return t(h).actionCode=(Y=t(h).actionCode)==null?void 0:Y.toUpperCase()}),onFocus:m[18]||(m[18]=K=>t(E)("actionCode"))},null,8,["modelValue","placeholder"])]),_:1},8,["class"])):ge("",!0),!t(h).isOpen&&!I.isGroup?(u(),Se(A,{key:2,label:" ",class:ye([t(D)==="tktNum"?"currentError":""]),prop:"tktNum"},{default:x(()=>[f(P,{modelValue:t(h).tktNum,"onUpdate:modelValue":m[19]||(m[19]=K=>t(h).tktNum=K),modelModifiers:{trim:!0},class:"airlines-input",disabled:!!I.tktNum,placeholder:I.$t("app.avSearch.numberOfPeople"),onFocus:m[20]||(m[20]=K=>t(E)("tktNum")),onBlur:t(k)},null,8,["modelValue","disabled","placeholder","onBlur"])]),_:1},8,["class"])):ge("",!0)])]),_:1},8,["model","rules"]),I.showDelete?(u(),$("div",{key:0,class:"text-center mb-[18px] ml-[5px]",onClick:m[21]||(m[21]=K=>t(S)(I.ssQueryForm))},[f(_,{class:"cursor-pointer iconfont icon-delete",size:"20",color:"var(--bkc-el-color-primary)"})])):ge("",!0)])}}});const tg=et(eg,[["__scopeId","data-v-92e010ae"]]),ag={class:"add-group-flight"},og={class:"flex justify-end"},ng=Ye({__name:"AddGroupFlight",props:{cityOrAirport:{}},emits:["addGroupFlight"],setup(e,{emit:a}){const i=a,{t:r}=gt(),c=B(),n=B({departureAirportCode:"",originName:"",arrivalAirportCode:"",destName:"",departureDate:""}),l=ct({render(){return ht("em",{class:"iconfont icon-calendar"})}}),v=(T,V,b)=>{if(V.length>3||!St.test(V)){b(new Error(r("app.fastQuery.headerQuery.formatError")));return}if(n.value.arrivalAirportCode&&V===n.value.arrivalAirportCode){b(new Error(r("app.fastQuery.headerQuery.identical")));return}b()},d=(T,V,b)=>{if(V.length>3||!St.test(V)){b(new Error(r("app.fastQuery.headerQuery.formatError")));return}if(n.value.departureAirportCode&&V===n.value.departureAirportCode){b(new Error(r("app.fastQuery.headerQuery.identical")));return}b()},y=Ee(()=>({departureAirportCode:[{required:!0,message:r("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:v,trigger:"blur"}],arrivalAirportCode:[{required:!0,message:r("app.fastQuery.headerQuery.must"),trigger:"change"},{validator:d,trigger:"blur"}]})),p=()=>{const T=n.value.departureAirportCode;n.value.departureAirportCode=n.value.arrivalAirportCode,n.value.arrivalAirportCode=T;const V=n.value.originName;n.value.originName=n.value.destName,n.value.destName=V},h=T=>{const V=Ta();return T.getTime()<V.getTime()},S=()=>{n.value={departureAirportCode:"",originName:"",arrivalAirportCode:"",destName:"",departureDate:""},c.value.clearValidate(),c.value.resetFields()},k=()=>{c.value.validate(T=>{if(T){const{departureAirportCode:V,arrivalAirportCode:b,departureDate:G}=n.value,w={date:{departureDate:G},segments:[{airlines:{flightNo:"ARNK"},departureAirportCode:V,arrivalAirportCode:b,departureDate:se(G).isValid()?`${se(G).format("YYYY-MM-DD")} 00:00`:"",segmentType:"0"}]};S(),i("addGroupFlight",w)}})};return(T,V)=>{const b=Ut,G=je,w=Ht,E=sa;return u(),$("div",ag,[f(w,{ref_key:"groupSegmentFormRef",ref:c,model:n.value,rules:y.value,"validate-on-rule-change":!1,class:"group-flight-from",onKeyup:oa(k,["enter"])},{default:x(()=>[f(b,{label:" ",class:"agent-airport-item",prop:"departureAirportCode"},{default:x(()=>[f(Yt,{ref:"agentAirportOriginRef",modelValue:n.value.departureAirportCode,"onUpdate:modelValue":V[0]||(V[0]=D=>n.value.departureAirportCode=D),name:n.value.originName,"onUpdate:name":V[1]||(V[1]=D=>n.value.originName=D),"is-agent-city":T.cityOrAirport,"prefix-title":T.$t("app.avSearch.depAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),o("div",{class:"revert-icon",onClick:p},[f(G,{class:"sort-text"},{default:x(()=>[f(t(Da))]),_:1})]),f(b,{class:"agent-airport-item",prop:"arrivalAirportCode"},{default:x(()=>[f(Yt,{ref:"agentAirportDestinationRef",modelValue:n.value.arrivalAirportCode,"onUpdate:modelValue":V[2]||(V[2]=D=>n.value.arrivalAirportCode=D),name:n.value.destName,"onUpdate:name":V[3]||(V[3]=D=>n.value.destName=D),"is-agent-city":T.cityOrAirport,"prefix-title":T.$t("app.avSearch.arrAirport")},null,8,["modelValue","name","is-agent-city","prefix-title"])]),_:1}),f(b,{label:" ",class:"departure-date",prop:"departureDate"},{default:x(()=>[f(Fa,{modelValue:n.value.departureDate,"onUpdate:modelValue":V[4]||(V[4]=D=>n.value.departureDate=D),type:"date","value-format":"YYYY-MM-DD","auto-compute-date":"","disabled-date":h,"disable-end-date":new Date().toString(),"prefix-icon":l.value,placeholder:T.$t("app.avSearch.date")},null,8,["modelValue","disable-end-date","prefix-icon","placeholder"])]),_:1})]),_:1},8,["model","rules"]),o("div",og,[f(E,{type:"primary",onClick:k},{default:x(()=>[me(g(T.$t("app.avSearch.addEnter")),1)]),_:1})])])}}});const ig=et(ng,[["__scopeId","data-v-384be854"]]),rg=e=>{const a={[_e.FLIGHT_SS]:{value:"",required:!0},[_e.COMPANY_CODE]:{value:"",required:!0},[_e.FLIGHT_NUMBER]:{value:"",required:!0},[_e.CABIN_CODE]:{value:"",required:!0},[_e.DEPARTURE_DATE]:{value:"",required:!0},[_e.DEPARTURE_AIRPORT]:{value:"",required:!0},[_e.ARRIVAL_AIRPORT]:{value:"",required:!0}};return e||Object.assign(a,{[_e.ACTION_CODE]:{value:"",required:!0},[_e.TKT_NUM]:{value:"",required:!0}}),a},Aa=(e,a)=>{const i=a?_e.ARRIVAL_AIRPORT:_e.TKT_NUM,r=rg(a);let c=e.replaceAll(/ */gi,"");try{if(Object.keys(r).forEach(n=>{const l=zt.get(n);if(l){const v=l.exec(c),d=r[n];if(!v||v.index){if(d!=null&&d.required)throw new Error;return}d.value=v[0],c=c.slice(v[0].length),n===i&&(c=""),n===_e.FLIGHT_NUMBER&&/^[a-zA-Z]+$/.test(c.slice(0,2))&&(d.value=`${d.value}${c.slice(0,1)}`,c=c.slice(1))}}),c)throw new Error;if(r[_e.DEPARTURE_DATE].value.length>5){const n=So(r[_e.DEPARTURE_DATE].value);if(se(`${n}`).isBefore(se(new Date).format("YYYY-MM-DD")))return{valid:!0,dateValid:!1,flightInfoForm:r}}return{valid:!0,dateValid:!0,flightInfoForm:r}}catch{return{valid:!1,dateValid:!0,flightInfoForm:r}}},sg=e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase(),So=e=>{if(co.test(e)){const a=se(new Date).format("YYYY").slice(0,2),[i,r,c]=e.match(/(\d{2})([A-Za-z]{3})(\d{2})/).slice(1),n=`${a}${c}`,l=se(`${n}-${sg(r)}-${i}`,"YYYY-MMM-DD");if(!l.isValid())throw new Error;return l.format("YYYY-MM-DD")}else{const a=Sa(e,se(new Date).format("YYYY-MM-DD").substring(0,4)),i=se(`${a}`);return i.isBefore(se(new Date).format("YYYY-MM-DD"))?i.add(1,"year").format("YYYY-MM-DD"):i.format("YYYY-MM-DD")}},lg=(e,a)=>{var r,c;const i={id:xa(),airlines:e[_e.COMPANY_CODE].value,isOpen:!1,destName:"",originName:"",seamlessOrDa:"",departureAirportCode:e[_e.DEPARTURE_AIRPORT].value,arrivalAirportCode:e[_e.ARRIVAL_AIRPORT].value,departureDate:So(e[_e.DEPARTURE_DATE].value),cabinCode:e[_e.CABIN_CODE].value,flightNumber:e[_e.FLIGHT_NUMBER].value};return a||(i.actionCode=((r=e[_e.ACTION_CODE])==null?void 0:r.value)??"",i.tktNum=((c=e[_e.TKT_NUM])==null?void 0:c.value)??""),i},cg=(e,a,i)=>{const r=[];return e.some(l=>{const{valid:v,flightInfoForm:d}=Aa(l,i);return a&&!i&&(d[_e.TKT_NUM].value=a),r.push(d),!v})?[]:r.map(l=>lg(l,i))??[]},Za=e=>({isOpen:!1,destName:"",originName:"",departureAirportCode:"",arrivalAirportCode:"",departureDate:"",airlines:"",cabinCode:"",seamlessOrDa:"",flightNumber:"",actionCode:"",tktNum:e,id:xa()}),ug=e=>{const{t:a}=gt(),i=B(!1),r=B(),c=B(!1),n=B(),l=kt(),v=Xt(),d=Ee(()=>v.getters.userPreferences),{activeTag:y,orderInfo:p,originPnrData:h}=Ft(l),S=e.avQueryFromFastQuery?wt:y.value,k=Ee(()=>{var Q,ie;return p.value.get(y.value).type==="1"?(Q=p.value.get(y.value))==null?void 0:Q.group:((ie=p.value.get(y.value))==null?void 0:ie.psgType)==="1"}),T=Ee(()=>{var Q,ie;return(ie=(Q=p.value.get(y.value))==null?void 0:Q.newChange)==null?void 0:ie.isNewVoluntaryRescheduling}),V=Ee(()=>{var Q;return(((Q=p.value.get(y.value))==null?void 0:Q.type)??"")==="2"}),b=Ee(()=>{var Q;return((Q=Array.from(p.value.keys()))==null?void 0:Q.find(ie=>{var Fe,xe;return(xe=(Fe=p.value.get(ie))==null?void 0:Fe.occupySpecialContent)==null?void 0:xe.includes(a("app.basic.occupy"))}))??""}),G=Ee(()=>{var ie,Fe,xe;const Q=(Fe=(ie=p.value.get(y.value))==null?void 0:ie.flight)==null?void 0:Fe.find(we=>we.segments.some(ze=>Number(ze.segmentType)));return((xe=Q==null?void 0:Q.segments[0])==null?void 0:xe.tktNum)??""}),w=ta(),E=B(),D=B(0),I=B(!1),m=Ee(()=>{var Q;return((Q=d.value)==null?void 0:Q.autoSearch)==="1"}),C=B({}),A=B(""),_=B(),P=B("AV"),U=Ee(()=>{const Q=w.flightByDate[S].flightDataList;return Q.size>0||(ne.value=""),Q}),K=B(!0),Y=B(""),ne=B(""),oe=new Map,Z=new Map,H=new Map,O=ct(),L=B(1),R=B({departureCity:"",arriveCity:""});let N=[];const F=B(),ae=B(),le=B([Za(G.value)]),de=B([]),fe=B(),te=B({segmentList:""}),Te=B(!1),be=B(!1),Re=()=>{var ie;const Q=(ie=v.state.user)==null?void 0:ie.agentManageStatus;return Q===3||Q===4},X=(Q,ie,Fe)=>{if(ie){const ze=ie.replace(/\n+/gi,`
`).replace(/ +/gi," ").trim().split(`
`),He=ze.some(Ve=>!Aa(Ve,k.value).valid),We=ze.some(Ve=>!Aa(Ve,k.value).dateValid);!He&&We?Fe(new Error(a("app.fastQuery.useInternationalPriceQuery.flightDateError"))):He&&!We&&Fe(new Error(a("app.fastQuery.useInternationalPriceQuery.flightErrorError")))}Fe()},re={segmentList:[{required:!0,message:a("app.fastQuery.useInternationalPriceQuery.required"),trigger:"blur"},{validator:X,trigger:"blur"}]},z=Q=>{const ie=[];return Q.onlyCompany&&ie.push(Q.onlyCompany),Q.carrierFlight&&ie.push("O"),Q.lowestPrice&&ie.push("P"),Q.timeSequence&&ie.push("E"),Q.unsharedFlight&&ie.push("NO"),ie.length>0?ie.join("|"):""},j=(Q,ie,Fe,xe)=>{var Dt,Qe,dt,$t,xt;const we=_o(y.value,p.value),ze=((Qe=(Dt=h.value)==null?void 0:Dt.get(y.value))==null?void 0:Qe.flight)??[],He=xe?W(Q.origin,Q.destination,xe):W(Q.origin,Q.destination,Q.departureDate),We=(($t=(dt=w.flightByDate[S].flightDataList.get(He))==null?void 0:dt.queryForm)==null?void 0:$t.departureDate)??"";A.value=((xt=w.flightByDate[S].flightDataList.get(He))==null?void 0:xt.sessionId)??"";const Ve=ie&&Fe?We:Q.departureDate,ve={departureCity:Q.origin??"",arriveCity:Q.destination??"",departureTime:Q.departureDateTime??"",departureDate:Ve??"",flightNo:Q.flightNumber??"",onlyDirect:Q.onlyDirectFlight?"D":"",airCode:Q.airlines??"",seamlessOrDa:Q.seamlessOrDa==="DA"?Q.seamlessOrDa:"seamless",queryType:"REAL_TIME_AV",pagingType:"R",sessionId:"",flightType:"",transitCity:(Q.transitTerminal??"").toUpperCase()};return V.value&&we.length&&!e.avQueryFromFastQuery&&(ve.preOccupySegmentInfoList=ra(!1,we)),!V.value&&ze.length&&!e.avQueryFromFastQuery&&(ve.preOccupySegmentInfoList=ra(!0,ze)),ve.flightType=z(Q),A.value&&(ve.sessionId=A.value??""),Fe&&(ve.pagingType=Fe),ve.pagingType==="R"&&(L.value=1,ve.sessionId=""),ve},W=(Q,ie,Fe)=>`${Q}${ie}${Fe}`,ue=async Q=>{C.value=Q,$e()},Le=async()=>{let Q=!1;const ie=Te.value&&be.value;return await _n(ie,a,ht)&&(Q=!0),Q},$e=async(Q,ie,Fe)=>{var xe,we,ze,He,We,Ve;i.value=!0,I.value=!1;try{const ve=Q&&ie&&Fe&&JSON.stringify(Fe)!=="{}"?it(Fe):it(C.value),Qe=(await ho(j(ve,Q,ie,oe.get(`${ve.origin}${ve.destination}${ve.departureDate}`)),"01010207")).data.value;if(((Qe==null?void 0:Qe.flightInfoList)??[]).length<=0){O.value=[];return}if(_.value=ve,Q&&ie&&(ie==="N"?L.value=Q+1:ie==="P"&&(L.value=Q-1)),Qe.sessionId){if(A.value=Qe.sessionId,Qe.firstQueryDate=ve.departureDate,Z.set(`${ve.origin}${ve.destination}${ve.departureDate}`,ve.departureDate),(Qe.flightInfoList??[]).length>0&&Qe.flightInfoList[0].segments&&Qe.flightInfoList[0].segments[0].departureDate){const ft=se(Qe.flightInfoList[0].segments[0].departureDate).format("YYYY-MM-DD");ve.departureDate!==ft&&oe.set(`${ve.origin}${ve.destination}${ve.departureDate}`,ft)}}else Qe.sessionId=A.value,Qe.firstQueryDate=Z.get(`${ve.origin}${ve.destination}${ve.departureDate}`)||ve.departureDate;K.value=Qe.flightInfoList[0].segments[0].departureDate?se(Qe.flightInfoList[0].segments[0].departureDate).format("YYYY-MM-DD")===ve.departureDate:!0,O.value=al(Qe.flightInfoList),Qe.flightInfoList=O.value;let dt="",$t="",xt="";if((Qe.flightInfoList??[]).length>0&&Qe.flightInfoList[0].segments){let ft="",Kt="";const Nt=Qe.flightInfoList[0].segments,Ia=Nt[0].departureDate?se(Nt[0].departureDate).format("YYYY-MM-DD"):ve.departureDate;if(H.set(`${ve.origin}${ve.destination}${ve.departureDate}`,Ia),ve.flightNumber){if(!N.length){const ua=await pt("searchLocalData");N=JSON.parse((ua==null?void 0:ua.localData)??"")}ft=((xe=Nt==null?void 0:Nt[0])==null?void 0:xe.departureAirportCode)??"",Kt=((we=Nt[Nt.length-1])==null?void 0:we.arrivalAirportCode)??""}else ft=ve.origin,Kt=ve.destination;const Ea=oe.get(`${ve.origin}${ve.destination}${ve.departureDate}`);xt=Ea?W(ft,Kt,Ea):W(ft,Kt,ve.departureDate),$t=W(ft,Kt,Ia),dt=$t}else dt=W(ve.origin,ve.destination,ve.departureDate);if(Qe.sessionId!==null){const ft={...ve};R.value.departureCity=ft.origin,R.value.arriveCity=ft.destination,ne.value=`${dt}+${new Date().getTime().toString()}`}Qe||(L.value=((He=(ze=U==null?void 0:U.value)==null?void 0:ze.get(dt))==null?void 0:He.currentPage)??1),(ie==="P"||ie==="N")&&($t!==xt&&w.delFlightDataList(S,xt),(Qe.flightInfoList??[]).length>0&&Qe.flightInfoList[0].segments&&Qe.flightInfoList[0].segments[0].departureDate?oe.set(`${ve.origin}${ve.destination}${ve.departureDate}`,se((Ve=(We=Qe.flightInfoList[0])==null?void 0:We.segments[0])==null?void 0:Ve.departureDate).format("YYYY-MM-DD")):oe.set(`${ve.origin}${ve.destination}${ve.departureDate}`,ve.departureDate)),Y.value=dt.substring(dt.length-10),w.setFlightDataList(S,it(ve),Qe,dt,L.value)}finally{i.value=!1}},Oe=async Q=>{C.value=Q,await $e(),Mt(()=>{!e.avQueryFromFastQuery&&(O.value??[]).length>0&&(I.value=m.value)},300)()},Ae=Q=>{if(Q.size<2)return new Map(Q);const ie=Array.from(Q.entries()),[Fe,xe]=ie[0],[we,ze]=ie[1],He=new Map;He.set(we,ze),He.set(Fe,xe);for(let We=2;We<ie.length;We++){const[Ve,ve]=ie[We];He.set(Ve,ve)}return He},Pe=()=>{const Q=Ae(U.value);w.setFlightSort(S,Q)},Ge=async(Q,ie)=>{var Fe;C.value=Q,await $e(),C.value=ie,await $e(),F.value&&Pe(),ut((Fe=Array.from(U.value.keys()))==null?void 0:Fe[0]),Mt(()=>{!e.avQueryFromFastQuery&&(O.value??[]).length>0&&(I.value=m.value)},300)()},ot=Q=>{F.value=Q},st=Q=>{const ie=Y.value||C.value.departureDate;Q==="pre"&&ie!==Ks()&&(C.value.departureDate=Ws(ie),$e()),Q==="next"&&(C.value.departureDate=zs(ie),$e())},Be=Q=>{R.value.departureCity=Q.substring(0,3),R.value.arriveCity=Q.substring(3,6)},ut=Q=>{var ie,Fe,xe,we,ze,He,We,Ve;Y.value=(Q==null?void 0:Q.substring(Q.length-10))??"",U.value.size>=1?(O.value=((Fe=(ie=U==null?void 0:U.value)==null?void 0:ie.get(Q))==null?void 0:Fe.flightInfoList)??[],L.value=((we=(xe=U==null?void 0:U.value)==null?void 0:xe.get(Q))==null?void 0:we.currentPage)??1,A.value=((He=(ze=U==null?void 0:U.value)==null?void 0:ze.get(Q))==null?void 0:He.sessionId)??"",C.value=((Ve=(We=U==null?void 0:U.value)==null?void 0:We.get(Q))==null?void 0:Ve.queryForm)??{},C.value.departureDate=Z.get(`${C.value.origin}${C.value.destination}${C.value.departureDate}`)||C.value.departureDate,ne.value=Q,Be(Q),_.value=C.value,K.value=(Q==null?void 0:Q.substring(6))===C.value.departureDate):(!Q&&(I.value=!1),O.value=[],K.value=!0)},Ue=(Q,ie)=>{Object.assign(C.value,{departureDate:ie.departureDate,destination:ie.arrivalAirportCode,origin:ie.departureAirportCode});const Fe=W(C.value.origin,C.value.destination,C.value.departureDate);w.updateFlightDataListKey(S,Fe,Q,{...C.value}),$e()},ee=(Q,ie)=>{var He,We,Ve,ve,Dt,Qe,dt,$t,xt;const Fe=((He=w.flightByDate[S])==null?void 0:He.activeFlightIndex)??0,xe=Array.from(w.flightByDate[S].flightDataList.entries())[Fe][1],we=(We=ne.value)==null?void 0:We.slice(6,16);!oe.get(`${(Ve=xe==null?void 0:xe.queryForm)==null?void 0:Ve.origin}${(ve=xe==null?void 0:xe.queryForm)==null?void 0:ve.destination}${(Dt=xe==null?void 0:xe.queryForm)==null?void 0:Dt.departureDate}`)&&we!==((Qe=xe==null?void 0:xe.queryForm)==null?void 0:Qe.departureDate)&&oe.set(`${(dt=xe==null?void 0:xe.queryForm)==null?void 0:dt.origin}${($t=xe==null?void 0:xe.queryForm)==null?void 0:$t.destination}${(xt=xe==null?void 0:xe.queryForm)==null?void 0:xt.departureDate}`,we),$e(L.value,ie,(xe==null?void 0:xe.queryForm)??{})},ce=async()=>{const Q=oo("08100117"),{data:ie}=await fi(Q);ae.value=ie.value},pe=Q=>{l.setAvTabCheck(y.value,Q==="AV"),Q==="AV"&&Gt(),P.value=Q,I.value=!1},ke=()=>{var Q;D.value=((Q=E.value)==null?void 0:Q.getBoundingClientRect().top)??0},De=()=>{l.setDefaultJumpHistory(y.value)},M=()=>{le.value.push({isOpen:!1,destName:"",originName:"",departureAirportCode:"",arrivalAirportCode:"",departureDate:"",airlines:"",cabinCode:"",seamlessOrDa:"",flightNumber:"",id:xa()})},q=(Q,ie,Fe)=>{Q&&(de.value[Fe]=Q)},J=()=>{var ie,Fe;const Q=((Fe=(ie=de.value.find(xe=>{var we,ze;return(ze=(we=xe.queryForm)==null?void 0:we.tktNum)==null?void 0:ze.toString()}))==null?void 0:ie.queryForm)==null?void 0:Fe.tktNum)??"";de.value.forEach(xe=>xe.ssValidateField("tktNum",Q))},Me=async()=>{var He,We;const Q=((We=(He=de.value.find(Ve=>{var ve,Dt;return(Dt=(ve=Ve.queryForm)==null?void 0:ve.tktNum)==null?void 0:Dt.toString()}))==null?void 0:He.queryForm)==null?void 0:We.tktNum)??"";le.value=[];const ie=[];for(let Ve=0;Ve<de.value.length;Ve++)ie.push(de.value[Ve].ssValidate(Q)),le.value.push(de.value[Ve].queryForm);const Fe=le.value.some(Ve=>!Ve.isOpen);if(V.value&&!k.value&&Fe&&await Le()||(await Promise.all(ie)).some(Ve=>!Ve))return;const we=p.value.get(y.value).flight??[];if(!(we.length>0?we.length&&we.some(Ve=>!Ve.openFlag):le.value.some(Ve=>!Ve.isOpen))){so.confirm(a("app.avSearch.addOpenFlightTips"),{icon:ht(je,{color:ro("--bkc-el-color-primary",null).value,size:32},()=>ht(lo)),customClass:"warning-p-msg crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:a("app.avSearch.confirm")});return}k.value&&(c.value=!0,await tl(le.value,N,y.value,h.value,p.value),c.value=!1),le.value.length&&de.value[0].addSsFlightsToCache(le.value)},Ce=()=>{de.value=[],Gt(),le.value=[Za(G.value)],P.value="AV"},Ke=Q=>{le.value=le.value.filter(ie=>ie.id!==Q.id),de.value=de.value.filter(ie=>ie.queryForm.id!==Q.id)},tt=Q=>{var ie;w.setClickFlightSearchFlag(S,!((ie=w.flightByDate[S])!=null&&ie.clickFlightSearchFlag)),_.value=it(Q),C.value=Q},nt=Q=>{oe.delete(Q)},Ze=Q=>Q.replace(/\n+/gi,`
`).replace(/ +/gi," ").trim().split(`
`),lt=()=>{var Q;(Q=fe.value)==null||Q.validate(async ie=>{if(ie){const Fe=cg(Ze(te.value.segmentList),G.value,k.value);le.value.push(...Fe);const xe=le.value.filter(we=>!(we.departureAirportCode&&we.arrivalAirportCode&&we.departureDate&&we.cabinCode&&we.flightNumber&&we.airlines)).map(we=>we.id);de.value=de.value.filter(we=>!xe.includes(we.queryForm.id)),le.value=le.value.filter(we=>we.departureAirportCode||we.arrivalAirportCode||we.departureDate||we.cabinCode||we.flightNumber||we.airlines)}})},jt=()=>{var Q;e.avQueryFromFastQuery&&((Q=n.value)==null||Q.closeCabinPopover())},Gt=()=>{var Q;(Q=fe.value)==null||Q.resetFields(),te.value.segmentList=""},wa=Q=>{var ie;Q!=null&&Q.includes(qe.PLACEHOLDER_FLIGHT)&&l.placeholderFlightsQueryForm.origin&&(_.value=it(l.placeholderFlightsQueryForm),l.setPlaceholderFlightsQueryForm({}),(ie=r.value)==null||ie.search())},ko=Q=>{const ie=y.value,Fe=`ARNK/${ie}/${new Date().getTime().toString()}`;l.setFlight(ie,{...Q,key:Fe}),vt(qe.UPDATE_FLIGHT_LIST,""),P.value="AV"};return bt(async()=>{Te.value=await v.getters.userSeatDisabled,be.value=Re(),v.dispatch("setFullLoading",!1);const Q=y.value,ie=await pt("searchLocalData");N=JSON.parse((ie==null?void 0:ie.localData)??""),ke(),w.flightByDate[S]||w.initAgentSell(S),await ce(),Jt.on(qe.UPDATE_FLIGHT_LIST,Fe=>{!e.avQueryFromFastQuery&&Q&&Q===y.value&&wa(Fe),Fe!=null&&Fe.includes(qe.SS_ADD_FLIGHT_SUCCESS)&&Q===y.value&&Ce()}),!e.avQueryFromFastQuery&&Q&&wa(qe.PLACEHOLDER_FLIGHT)}),Rt(()=>{l.setPlaceholderFlightsParams({flightKeys:[],params:{bookAirSegs:[]},seizeSeatInfoFlight:[]}),N.length=0,O.value=[]}),{searchClick:Oe,searchRoundClick:Ge,placeholderFlagTag:b,title:R,checkTab:pe,avSearchData:O,screeningDirect:ue,loadingInstance:i,queryData:ut,searchCompleteKey:ne,turnPage:ee,currentPage:L,queryResDateGroupBySessionId:H,changeDate:st,queryForm:C,tabType:P,callQueryApi:Ue,scrollHide:I,flightOpRightRef:E,flightOpRightTop:D,cityOrAirport:ae,newChangeFlag:T,toBack:De,addMultSs:M,ssQueryFormList:le,addSsFlight:Me,deleteSsFlight:Ke,handleSetFromRefList:q,historyQueryForm:_,addSegLoading:c,echoAndQuery:tt,closeAVHistory:nt,isQueryCurrentDay:K,importFormRef:fe,importForm:te,IMPORT_FORM_RULES:re,importSsFlight:lt,flightContainerRef:n,closeCabinPopover:jt,addGroupFlight:ko,headerAvQueryRef:r,resetSsImportClick:Gt,tktNum:G,inputTktNumBlur:J,isGroup:k,fromRefList:de,flightSearchFlag:S,getRoundTripFlag:ot,roundTripFlag:F}},To=e=>(It("data-v-d622a442"),e=e(),Et(),e),dg={class:"overflow-hidden"},pg={class:"ml-[4px] h-[20px] leading-[20px]"},gg={class:"mr-[20px] font-bold leading-normal text-gray-1"},fg={key:0,class:"h-full flex flex-col gap-[14px]"},mg=To(()=>o("div",{class:"mb-0 border-b border-dashed border-gray-5"},null,-1)),vg={class:"flex-1 overflow-hidden"},hg={class:"flex justify-center items-center flex-col mt-[86px]"},yg=["alt"],bg={class:"mt-[19px] text-lg leading-[24px] font-bold text-gray-2"},_g={class:"text-base text-gray-4"},Cg={key:1,class:"w-full ss-box"},Ag={class:"justify-end flex items-end"},Dg={key:0,class:"warning-tip mb-[16px] p-[4px] flex items-center text-yellow-1 bg-yellow-3 border border-solid border-yellow-2 rounded-sm text-[12px]"},$g={class:"flex items-center"},xg=To(()=>o("em",{class:"iconfont icon-warning-circle-fill mr-[6px]"},null,-1)),Sg={class:"flex justify-end"},Tg={key:2},kg=Ye({__name:"FlightOpRight",props:{editFlag:{type:Boolean},avQueryFromFastQuery:{type:Boolean}},setup(e,{expose:a}){const i=e,{avSearchData:r,screeningDirect:c,queryData:n,tabType:l,searchCompleteKey:v,turnPage:d,currentPage:y,callQueryApi:p,scrollHide:h,flightOpRightTop:S,flightContainerRef:k,closeCabinPopover:T,isGroup:V,queryResDateGroupBySessionId:b,changeDate:G,queryForm:w,searchClick:E,searchRoundClick:D,cityOrAirport:I,loadingInstance:m,checkTab:C,newChangeFlag:A,toBack:_,flightOpRightRef:P,historyQueryForm:U,addGroupFlight:K,inputTktNumBlur:Y,addMultSs:ne,ssQueryFormList:oe,addSsFlight:Z,deleteSsFlight:H,handleSetFromRefList:O,addSegLoading:L,echoAndQuery:R,closeAVHistory:N,isQueryCurrentDay:F,importFormRef:ae,importForm:le,tktNum:de,fromRefList:fe,IMPORT_FORM_RULES:te,importSsFlight:Te,resetSsImportClick:be,placeholderFlagTag:Re,headerAvQueryRef:X,flightSearchFlag:re,getRoundTripFlag:z,roundTripFlag:j}=ug(i);return a({echoAndQuery:R}),(W,ue)=>{const Le=je,$e=sa,Oe=Pt,Ae=Ut,Pe=Ht,Ge=ka,ot=mo,st=la;return u(),$("div",{ref_key:"flightOpRightRef",ref:P,class:ye([W.avQueryFromFastQuery?"fast-query-flight-op-right":"flight-op-right"]),onClick:ue[10]||(ue[10]=Be=>t(T)())},[o("div",dg,[W.editFlag?(u(),$("div",{key:0,class:ye(["transition-height duration-700 flex items-center relative",t(h)?"h-0":"h-[20px] my-1"])},[yt(W.$slots,"default",{},void 0,!0)],2)):W.avQueryFromFastQuery?ge("",!0):(u(),$("div",{key:1,class:ye(["transition-height duration-700 flex items-center relative",t(h)?"h-0":"h-[20px] my-1"])},[t(A)?(u(),$("div",{key:0,class:"back-box text-brand-2 cursor-pointer flex items-center mr-[10px]",onClick:ue[0]||(ue[0]=(...Be)=>t(_)&&t(_)(...Be))},[f(Le,{class:"icon iconfont icon-left"}),o("div",pg,g(W.$t("app.pnrManagement.btnGroups.Back")),1)])):ge("",!0),o("div",gg,g(W.$t("app.pnrManagement.flight.addSegmentTitle")),1),o("div",null,[o("div",{class:ye(["tab-container rounded-tl-sm rounded-bl-sm border-y border-l",{"active-container border-r":t(l)==="AV"}]),onClick:ue[1]||(ue[1]=Be=>t(C)("AV"))},[o("div",{class:ye(["tab-container-box",{"active-box":t(l)==="AV"}])},g(`AV${W.$t("app.avSearch.search")}[AV]`),3)],2),o("div",{class:ye(["tab-container border-y border-x",{"active-container border-l":t(l)==="SS"}]),onClick:ue[2]||(ue[2]=Be=>t(C)("SS"))},[o("div",{class:ye(["tab-container-box",{"active-box":t(l)==="SS"}])},g(`${W.$t("app.avSearch.addSeg")}[SS]`),3)],2),o("div",{class:ye(["tab-container rounded-tr-sm rounded-br-sm border-y border-r",{"active-container border-l":t(l)==="SA"}]),onClick:ue[3]||(ue[3]=Be=>t(C)("SA"))},[o("div",{class:ye(["tab-container-box",{"active-box":t(l)==="SA"}])},g(`${W.$t("app.pnrManagement.flight.addGroundSection")}[SA]`),3)],2)]),t(l)==="SS"?(u(),Se($e,{key:1,link:"",type:"primary",size:"small",class:"ml-auto mr-3.5",onClick:t(ne)},{default:x(()=>[me(g(W.$t("app.avSearch.addMult")),1)]),_:1},8,["onClick"])):ge("",!0)],2))]),o("div",{class:ye(["mt-[14px] flex-1 flex-col overflow-hidden",t(h)||W.avQueryFromFastQuery?"!mt-0":""])},[t(l)==="AV"?(u(),$("div",fg,[o("div",{class:ye(["transition-height duration-75 overflow-hidden",t(h)?"max-h-0":"max-h-[200px]"])},[f(Fl,{ref_key:"headerAvQueryRef",ref:X,"av-query-from-fast-query":W.avQueryFromFastQuery,"city-or-airport":t(I),"history-query-form":t(U),"flight-search-flag":t(re),onSearchClick:t(E),onSearchRoundClick:t(D),onGetRoundTripFlag:t(z)},null,8,["av-query-from-fast-query","city-or-airport","history-query-form","flight-search-flag","onSearchClick","onSearchRoundClick","onGetRoundTripFlag"]),mg],2),f(ql,{class:"mr-[6px]","av-query-from-fast-query":W.avQueryFromFastQuery,"search-complete-key":t(v),"query-form":t(w),"round-trip-flag":t(j),onQueryData:t(n),onCallQueryApi:t(p),onCloseAVHistory:t(N)},null,8,["av-query-from-fast-query","search-complete-key","query-form","round-trip-flag","onQueryData","onCallQueryApi","onCloseAVHistory"]),Ct((u(),$("div",vg,[(t(r)??[]).length>0?(u(),Se(Wp,{key:0,ref_key:"flightContainerRef",ref:k,scrollHide:t(h),"onUpdate:scrollHide":ue[4]||(ue[4]=Be=>rt(h)?h.value=Be:null),"search-complete-key":t(v),"query-form":t(w),"av-search-data":t(r),"current-page":t(y),"query-res-date-group-by-session-id":t(b),"flight-op-right-top":t(S),"placeholder-flag-tag":t(Re),"av-query-from-fast-query":W.avQueryFromFastQuery,"is-query-current-day":t(F),onScreeningDirect:t(c),onChangeDate:t(G),onTurnPage:t(d)},null,8,["scrollHide","search-complete-key","query-form","av-search-data","current-page","query-res-date-group-by-session-id","flight-op-right-top","placeholder-flag-tag","av-query-from-fast-query","is-query-current-day","onScreeningDirect","onChangeDate","onTurnPage"])):ge("",!0),o("div",hg,[o("img",{src:Sn,alt:W.$t("app.fastQuery.skQuerys.nodata")},null,8,yg),o("div",bg,g(W.$t("app.fastQuery.skQuerys.noOrderInfo")),1),o("div",_g,g(W.$t("app.fastQuery.skQuerys.plChangeConditionsSearchAgain")),1)])])),[[st,t(m)]])])):t(l)==="SS"?Ct((u(),$("div",Cg,[f(ot,null,{default:x(()=>[f(Pe,{ref_key:"importFormRef",ref:ae,model:t(le),rules:t(te),inline:!0,class:"import-flight-from"},{default:x(()=>[f(Ae,{label:W.$t("app.avSearch.fastImport"),class:"import-flight-input",prop:"segmentList"},{default:x(()=>[f(Oe,{modelValue:t(le).segmentList,"onUpdate:modelValue":ue[5]||(ue[5]=Be=>t(le).segmentList=Be),type:"textarea",autosize:"",placeholder:W.$t(`app.avSearch.${t(V)?"ssImportTipGroup":"ssImportTip"}`),class:"seg-item",onBlur:ue[6]||(ue[6]=Be=>t(le).segmentList=t(le).segmentList.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),o("div",Ag,[f($e,{type:"primary",onClick:ue[7]||(ue[7]=Be=>t(Te)())},{default:x(()=>[me(g(W.$t("app.fastQuery.useInternationalPriceQuery.import")),1)]),_:1}),f($e,{onClick:t(be)},{default:x(()=>[me(g(W.$t("app.fastQuery.headerQuery.reset")),1)]),_:1},8,["onClick"])])]),_:1},8,["model","rules"]),f(Ge,{class:"el-bkc-divider"}),o("div",{class:"mt-[14px]",onKeyup:ue[9]||(ue[9]=oa((...Be)=>t(Z)&&t(Z)(...Be),["enter"]))},[(u(!0),$(he,null,Ie(t(oe),(Be,ut)=>{var Ue,ee;return u(),Se(tg,{key:Be.id,ref_for:!0,ref:ce=>t(O)(ce,Be.id,ut),addSegLoading:t(L),"onUpdate:addSegLoading":ue[8]||(ue[8]=ce=>rt(L)?L.value=ce:null),"city-or-airport":t(I),index:ut,"ss-query-form":((ee=(Ue=t(fe))==null?void 0:Ue[ut])==null?void 0:ee.queryForm)||Be,"show-delete":t(oe).length>1,"tkt-num":t(de),"is-group":t(V),onInputTktNumBlur:t(Y),onDeleteSsFlight:t(H)},null,8,["addSegLoading","city-or-airport","index","ss-query-form","show-delete","tkt-num","is-group","onInputTktNumBlur","onDeleteSsFlight"])}),128)),t(V)?ge("",!0):(u(),$("div",Dg,[o("div",$g,[xg,me(g(W.$t("app.fastQuery.headerQuery.addSegmentTip")),1)])])),o("div",Sg,[f($e,{type:"primary",onClick:t(Z)},{default:x(()=>[me(g(W.$t("app.avSearch.addEnter")),1)]),_:1},8,["onClick"])])],32)]),_:1})])),[[st,t(L)]]):(u(),$("div",Tg,[f(ig,{"city-or-airport":t(I),onAddGroupFlight:t(K)},null,8,["city-or-airport","onAddGroupFlight"])]))],2)],2)}}});const Hf=et(kg,[["__scopeId","data-v-d622a442"]]),jf=(e,a,i)=>Ne(`${qt}/v2/queue/queryQueueList`,{headers:{gid:a}},{ignoreError:i,originalValue:!0}).post(e).json(),Gf=(e,a,i)=>Ne(`${qt}/v2/query/pnr/queuePnr`,{headers:{gid:a}},{ignoreError:i,originalValue:!0}).post(e).json(),Kf=(e,a)=>Ne(`${qt}/v2/queue/subQueueCrsPnr`,{headers:{gid:a}},{originalValue:!0}).post(e).json(),zf=(e,a)=>Ne(`${qt}/v2/queue/unsubscribeCrsQueue`,{headers:{gid:a}}).post(e).json(),Wf=(e,a)=>Ne(`${qt}/v2/queue/updateCrsQueueReadStatus`,{headers:{gid:a}},{ignoreError:!0,originalValue:!0}).post(e).json(),Jf=(e,a)=>Ne(`${qt}/v2/queue/subQueueCrsPnrDetail`,{headers:{gid:a}},{originalValue:!0}).post(e).json();export{Nf as $,Yt as A,lf as B,pf as C,uf as D,di as E,wt as F,df as G,mf as H,If as I,Rf as J,Af as K,Cf as L,Jf as M,ni as N,el as O,Yf as P,hf as Q,gf as R,ff as S,Of as T,kf as U,Ff as V,$f as W,wf as X,Tf as Y,_f as Z,Ir as _,cf as a,vf as a0,sf as a1,Df as a2,Lf as a3,Pf as a4,Ef as a5,Wf as a6,Yn as b,ma as c,ho as d,yf as e,bf as f,Qf as g,Mf as h,Bf as i,Vf as j,qf as k,Uf as l,Sf as m,xf as n,Hf as o,jf as p,fi as q,Gf as r,Kf as s,zf as t,ta as u,qr as v,za as w,td as x,nf as y,rf as z};
