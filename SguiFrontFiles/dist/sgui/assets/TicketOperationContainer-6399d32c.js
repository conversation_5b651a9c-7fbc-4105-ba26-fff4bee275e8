import{L as Ha,M as Xn,f2 as Ka,eQ as kn,ec as Pn,f3 as An,eR as ca,e5 as Ya,q as He,f4 as Wa,f5 as Xa,f6 as Ja,v as Za,r as O,w as Le,dr as es,f7 as ts,o as ct,x as s,y as re,z as i,P as t,D as Ve,A as e,C as Jn,G as o,B as m,H as at,e8 as ns,ai as xe,aj as Ee,F as nn,ak as z,Q as n,ah as xt,X as as,b7 as cn,E as yn,e0 as ss,_ as os,U as Dn,m as is,K as ls,ab as et,eY as Bn,a9 as Dt,b6 as yt,bB as pn,au as nt,aJ as Pe,eX as an,bA as Ft,av as De,bQ as rs,a$ as fn,J as W,ae as ht,b9 as Ue,a5 as $e,al as st,az as bn,bn as dn,f8 as Zn,e3 as cs,ac as bt,f9 as ea,bb as Xt,am as Ut,an as jt,s as Be,aY as rt,eW as ua,b2 as ut,bc as sn,aI as ln,fa as Qn,ad as mn,aH as da,b3 as wt,fb as Qt,fc as En,a6 as hn,R as it,as as us,fd as pa,fe as ds,cg as ps,ff as St,ce as Un,fg as Wt,fh as fs,bC as vn,fi as fa,b1 as jn,d3 as ms,aE as gs,aF as ks,aX as ys,fj as hs,fk as ta,fl as On,a8 as vs,fm as _s,cd as Ts,fn as bs,bX as xs,fo as Ns,ax as $s,fp as Rs,aK as Cs,bf as ws}from"./index-18f146fc.js";import{P as It}from"./PrintNoSelect-c8861788.js";import{D as Vt,R as ma,P as ga,b as $t,c as Rt,d as ka,e as ya,f as ha,g as na,h as Fn,i as va,j as _a,k as Ss,l as Ps,m as Ds,n as Ta,o as As,p as Es,q as Os,S as Fs,r as Vs,E as Ms}from"./regular-crs-4d4d60ea.js";import{d as Ls,a as ba,b as Bs,c as Qs,e as Us,g as js,h as Is,i as aa,j as zs,k as on,l as qs,r as Gs,m as Hs,n as xa,o as Na,p as Vn,s as Mn,t as Ks,u as Ys,v as Ws,w as Xs,x as Js,y as Zs,z as eo,A as sa,B as to,C as no,D as ao,E as so,q as oo,F as io}from"./ticketOperationApi-fe1536da.js";import{E as Jt,a as Zt}from"./index-d7d71e18.js";import{E as dt,a as pt}from"./index-c5503643.js";import{u as lo,E as Nt}from"./index-6ea30548.js";import{E as gn}from"./index-2494e7da.js";import{E as zt,a as qt}from"./index-20269f93.js";import{E as ft}from"./index-385c3d86.js";import{C as ro,a as Yt,S as un,P as oa,m as co,E as $a,r as uo,b as po}from"./jspdf.es.min-ef66de55.js";import{E as Ht,a as Ra}from"./index-9b639e2a.js";import{E as Ca}from"./index-e3a5adf8.js";import{E as fo,a as mo,b as go}from"./index-a197ff1b.js";import{_ as vt}from"./_plugin-vue_export-helper-c27b6911.js";import{u as wa,_ as rn}from"./TicketOriginalPopover.vue_vue_type_script_setup_true_lang-98b79173.js";import{s as In}from"./index-6aec4f36.js";import{o as ia,a as ko,b as yo,p as _n,q as Sa,c as ho,d as vo,e as _o,_ as To,R as bo,m as xo}from"./RtktWindoing.vue_vue_type_script_setup_true_lang-ca8bd03a.js";import{D as No,a as $o,p as Pa,h as Ro}from"./refundUtil-51abb28e.js";import{D as Ze,b as kt}from"./common-d870312b.js";import{v as Da,T as zn,R as Co,D as wo,a as So,p as Aa,t as Po,P as Do}from"./TicketRefundForm-28604069.js";import{E as Ea,a as Oa}from"./index-b0de6559.js";import{g as Ct,a as la}from"./pnrUtils-1bb76fce.js";import{E as qn}from"./index-5035a026.js";import{E as Ao}from"./index-1c4b8a79.js";import{g as Eo}from"./index-21b1c834.js";import{_ as Gn}from"./theme-light_empty-0081a108.js";import{_ as Oo}from"./Page.vue_vue_type_script_setup_true_lang-ca55cadb.js";import{E as Fa,a as Va}from"./index-3d51361b.js";import{E as Fo}from"./index-cb25ab55.js";import{E as Vo}from"./index-93952dc4.js";import{E as Mo}from"./index-28cc82cf.js";import{u as Lo}from"./usePersonalization-956f86ae.js";import{E as Bo}from"./exceljs.min-08e2fc65.js";import{E as Qo}from"./index-3e6ab209.js";import{_ as Uo}from"./TicketOriginalItem-ed3e7677.js";import"./index-2610c77e.js";import"./strings-8b86a061.js";import"./isEqual-9c56e106.js";import"./index-c5921abf.js";import"./castArray-f685dae0.js";import"./isUndefined-aa0326a0.js";import"./refs-649593ac.js";import"./_createMathOperation-2069a064.js";import"./browser-6cfa1fde.js";import"./flatten-1142070a.js";import"./dropdown-bbbdd88c.js";import"./throttle-39cac876.js";import"./index-0fa663e2.js";import"./passengerSpecialType-f6d133eb.js";import"./dictApi-38f10700.js";import"./index-3ecf128d.js";import"./index-037d7e25.js";import"./index-3a8869fd.js";import"./empty-8ebd02ca.js";import"./index-24b42935.js";const jo=Ha({valueKey:{type:String,default:"value"},modelValue:{type:[String,Number],default:""},debounce:{type:Number,default:300},placement:{type:Xn(String),values:["top","top-start","top-end","bottom","bottom-start","bottom-end"],default:"bottom-start"},fetchSuggestions:{type:Xn([Function,Array]),default:Ka},popperClass:{type:String,default:""},triggerOnFocus:{type:Boolean,default:!0},selectWhenUnmatched:{type:Boolean,default:!1},hideLoading:{type:Boolean,default:!1},label:{type:String},teleported:lo.teleported,highlightFirstItem:{type:Boolean,default:!1},fitInputWidth:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},name:String}),Io={[kn]:c=>Pn(c),[An]:c=>Pn(c),[ca]:c=>Pn(c),focus:c=>c instanceof FocusEvent,blur:c=>c instanceof FocusEvent,clear:()=>!0,select:c=>Ya(c)},zo=["aria-expanded","aria-owns"],qo={key:0},Go=["id","aria-selected","onClick"],Ma="ElAutocomplete",Ho=He({name:Ma,inheritAttrs:!1}),Ko=He({...Ho,props:jo,emits:Io,setup(c,{expose:p,emit:l}){const f=c,T=Wa(),k=Xa(),a=Ja(),v=Za("autocomplete"),r=O(),g=O(),b=O(),h=O();let E=!1,y=!1;const D=O([]),$=O(-1),A=O(""),V=O(!1),u=O(!1),te=O(!1),X=Le(()=>v.b(String(Eo()))),ge=Le(()=>k.style),q=Le(()=>(D.value.length>0||te.value)&&V.value),Y=Le(()=>!f.hideLoading&&te.value),L=Le(()=>r.value?Array.from(r.value.$el.querySelectorAll("input")):[]),le=()=>{q.value&&(A.value=`${r.value.$el.offsetWidth}px`)},C=()=>{$.value=-1},j=es(async B=>{if(u.value)return;const P=N=>{te.value=!1,!u.value&&(Dn(N)?(D.value=N,$.value=f.highlightFirstItem?0:-1):is(Ma,"autocomplete suggestions must be an array"))};if(te.value=!0,Dn(f.fetchSuggestions))P(f.fetchSuggestions);else{const N=await f.fetchSuggestions(B,P);Dn(N)&&P(N)}},f.debounce),Te=B=>{const P=!!B;if(l(An,B),l(kn,B),u.value=!1,V.value||(V.value=P),!f.triggerOnFocus&&!B){u.value=!0,D.value=[];return}j(B)},se=B=>{var P;a.value||(((P=B.target)==null?void 0:P.tagName)!=="INPUT"||L.value.includes(document.activeElement))&&(V.value=!0)},x=B=>{l(ca,B)},R=B=>{y?y=!1:(V.value=!0,l("focus",B),f.triggerOnFocus&&!E&&j(String(f.modelValue)))},pe=B=>{setTimeout(()=>{var P;if((P=b.value)!=null&&P.isFocusInsideContent()){y=!0;return}V.value&&he(),l("blur",B)})},Re=()=>{V.value=!1,l(kn,""),l("clear")},we=async()=>{q.value&&$.value>=0&&$.value<D.value.length?J(D.value[$.value]):f.selectWhenUnmatched&&(l("select",{value:f.modelValue}),D.value=[],$.value=-1)},me=B=>{q.value&&(B.preventDefault(),B.stopPropagation(),he())},he=()=>{V.value=!1},ue=()=>{var B;(B=r.value)==null||B.focus()},U=()=>{var B;(B=r.value)==null||B.blur()},J=async B=>{l(An,B[f.valueKey]),l(kn,B[f.valueKey]),l("select",B),D.value=[],$.value=-1},ne=B=>{if(!q.value||te.value)return;if(B<0){$.value=-1;return}B>=D.value.length&&(B=D.value.length-1);const P=g.value.querySelector(`.${v.be("suggestion","wrap")}`),_=P.querySelectorAll(`.${v.be("suggestion","list")} li`)[B],w=P.scrollTop,{offsetTop:ce,scrollHeight:ie}=_;ce+ie>w+P.clientHeight&&(P.scrollTop+=ie),ce<w&&(P.scrollTop-=ie),$.value=B,r.value.ref.setAttribute("aria-activedescendant",`${X.value}-item-${$.value}`)};return ts(h,()=>{q.value&&he()}),ct(()=>{r.value.ref.setAttribute("role","textbox"),r.value.ref.setAttribute("aria-autocomplete","list"),r.value.ref.setAttribute("aria-controls","id"),r.value.ref.setAttribute("aria-activedescendant",`${X.value}-item-${$.value}`),E=r.value.ref.hasAttribute("readonly")}),p({highlightedIndex:$,activated:V,loading:te,inputRef:r,popperRef:b,suggestions:D,handleSelect:J,handleKeyEnter:we,focus:ue,blur:U,close:he,highlight:ne}),(B,P)=>(s(),re(e(Nt),{ref_key:"popperRef",ref:b,visible:e(q),placement:B.placement,"fallback-placements":["bottom-start","top-start"],"popper-class":[e(v).e("popper"),B.popperClass],teleported:B.teleported,"gpu-acceleration":!1,pure:"","manual-mode":"",effect:"light",trigger:"click",transition:`${e(v).namespace.value}-zoom-in-top`,persistent:"",role:"listbox",onBeforeShow:le,onHide:C},{content:i(()=>[t("div",{ref_key:"regionRef",ref:g,class:Ve([e(v).b("suggestion"),e(v).is("loading",e(Y))]),style:Jn({[B.fitInputWidth?"width":"minWidth"]:A.value,outline:"none"}),role:"region"},[o(e(Ao),{id:e(X),tag:"ul","wrap-class":e(v).be("suggestion","wrap"),"view-class":e(v).be("suggestion","list"),role:"listbox"},{default:i(()=>[e(Y)?(s(),m("li",qo,[o(e(at),{class:Ve(e(v).is("loading"))},{default:i(()=>[o(e(ns))]),_:1},8,["class"])])):(s(!0),m(xe,{key:1},Ee(D.value,(N,_)=>(s(),m("li",{id:`${e(X)}-item-${_}`,key:_,class:Ve({highlighted:$.value===_}),role:"option","aria-selected":$.value===_,onClick:w=>J(N)},[nn(B.$slots,"default",{item:N},()=>[z(n(N[B.valueKey]),1)])],10,Go))),128))]),_:3},8,["id","wrap-class","view-class"])],6)]),default:i(()=>[t("div",{ref_key:"listboxRef",ref:h,class:Ve([e(v).b(),B.$attrs.class]),style:Jn(e(ge)),role:"combobox","aria-haspopup":"listbox","aria-expanded":e(q),"aria-owns":e(X)},[o(e(xt),as({ref_key:"inputRef",ref:r},e(T),{clearable:B.clearable,disabled:e(a),name:B.name,"model-value":B.modelValue,onInput:Te,onChange:x,onFocus:R,onBlur:pe,onClear:Re,onKeydown:[P[0]||(P[0]=cn(yn(N=>ne($.value-1),["prevent"]),["up"])),P[1]||(P[1]=cn(yn(N=>ne($.value+1),["prevent"]),["down"])),cn(we,["enter"]),cn(he,["tab"]),cn(me,["esc"])],onMousedown:se}),ss({_:2},[B.$slots.prepend?{name:"prepend",fn:i(()=>[nn(B.$slots,"prepend")])}:void 0,B.$slots.append?{name:"append",fn:i(()=>[nn(B.$slots,"append")])}:void 0,B.$slots.prefix?{name:"prefix",fn:i(()=>[nn(B.$slots,"prefix")])}:void 0,B.$slots.suffix?{name:"suffix",fn:i(()=>[nn(B.$slots,"suffix")])}:void 0]),1040,["clearable","disabled","name","model-value","onKeydown"])],14,zo)]),_:3},8,["visible","placement","popper-class","teleported","transition"]))}});var Yo=os(Ko,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/autocomplete/src/autocomplete.vue"]]);const La=ls(Yo),Wo=c=>{const{t:p}=et(),l=Bn("--bkc-el-color-primary",null),f=Dt(),T=Le(()=>{var q;return(q=f.state.user)==null?void 0:q.entityType}),k=O(),a=O(),v=O(!0),r=O("1"),g=yt({printerNo:"",ticketNo:"",refundNo:"",domestic:!0,ticketOrganization:""}),b={ticketOrganization:[{required:!0,message:p("app.ticketStatus.deviceNumNull"),trigger:["change","blur"]}],printerNo:[{required:!0,message:p("app.ticketStatus.deviceNumNull"),trigger:["change","blur"]},{pattern:Vt,trigger:["change","blur"],message:p("app.ticketStatus.deviceError")}],ticketNo:[{required:!0,message:p("app.agentTicketQuery.validate.tktNoNull"),trigger:["change","blur"]},{pattern:pn,message:p("app.agentTicketQuery.validate.tktNoError"),trigger:["change","blur"]}],domestic:[{required:!0,message:p("app.agentTicketQuery.validate.required"),trigger:["change","blur"]}],refundNo:[{required:!0,message:p("app.agentTicketQuery.validate.required"),trigger:["change","blur"]},{pattern:ma,message:p("app.agentTicketQuery.validate.refundNoError"),trigger:["change","blur"]}]},h=O([]),E={BSP:{label:p("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:p("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:p("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:p("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:p("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:p("app.agentTicketQuery.OWNTicket"),value:"ARL"}},y=Le(()=>!["CDS","GPCDS"].includes(g.ticketOrganization)),D=q=>{g.domestic=q==="D"||!q},$=async()=>{var Y,L,le,C,d,j,Te,se;let q;try{q=Ft.service({fullscreen:!0});const x={refundNo:(L=(Y=a.value)==null?void 0:Y.deleteRefundFrom)==null?void 0:L.refundNo,ticketType:(C=(le=a.value)==null?void 0:le.deleteRefundFrom)!=null&&C.domestic?"D":"I",ticketManagementOrganizationCode:(j=(d=a.value)==null?void 0:d.deleteRefundFrom)==null?void 0:j.ticketOrganization,printerNo:(se=(Te=a.value)==null?void 0:Te.deleteRefundFrom)==null?void 0:se.printerNo},R=De("091T0105");await Ls(x,R),await rs(p("app.refundForm.successMsg")),c("update:modelValue",!1)}finally{q&&q.close()}},A=async()=>{!a.value||!await a.value.validateForm()||nt.confirm(p("app.agentTicketQuery.wainMsg"),{icon:Pe(at,{color:l.value,size:32},()=>Pe(an)),customClass:"warning-p-msg crs-btn-ui crs-btn-message-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,confirmButtonText:p("app.pnrManagement.flight.confirm"),cancelButtonText:p("app.pnrManagement.flight.cancel")}).then(async()=>{$()})},V=()=>{v.value?k.value.validate(q=>{q&&c("openRefundDialog",g.ticketNo,g.refundNo,g.domestic,g.printerNo,g.ticketOrganization)}):A()},u=()=>{c("update:modelValue",!1)},te=()=>{r.value==="1"&&(g.refundNo=""),r.value==="2"&&(g.ticketNo="")},X=()=>{var q,Y,L,le,C,d,j,Te,se,x,R,pe;((q=T.value)!=null&&q.includes("$$$")||(Y=T.value)!=null&&Y.includes("BSP"))&&(h.value.push(E.BSP),h.value.push(E.GPBSP)),!((L=T.value)!=null&&L.includes("BSP"))&&((le=T.value)!=null&&le.includes("GP"))&&h.value.push(E.GPBSP),((C=T.value)!=null&&C.includes("$$$")||(d=T.value)!=null&&d.includes("BOP"))&&h.value.push(E.BOPBSP),((j=T.value)!=null&&j.includes("$$$")||(Te=T.value)!=null&&Te.includes("CDS"))&&(h.value.push(E.CDS),h.value.push(E.GPCDS)),((se=T.value)!=null&&se.includes("$$$")||(x=T.value)!=null&&x.includes("本票"))&&h.value.push(E.ARL),g.ticketOrganization=((pe=(R=h.value)==null?void 0:R[0])==null?void 0:pe.value)??""},ge=q=>{v.value=q==="queryForm"};return ct(()=>{X()}),{refundType:r,formDate:k,printNoFrom:g,PRINTER_NO_RULES:b,ticketOrganizationList:h,isShowPrintNo:y,confirmPrinterNo:V,closeDialog:u,deliverPrintType:D,changeRefundType:te,handleClickTab:ge,showQueryRefundForm:v,deleteFormDateRef:a}},Xo=Wo,Jo=()=>{const{t:c}=et(),p=Dt(),l=Le(()=>{var y;return(y=p.state.user)==null?void 0:y.entityType}),f=O(),T=O(!0),k=O("1"),a=yt({printerNo:"",refundNo:"",domestic:!0,ticketOrganization:""}),v={ticketOrganization:[{required:!0,message:c("app.ticketStatus.deviceNumNull"),trigger:"blur"}],printerNo:[{required:!0,message:c("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:Vt,trigger:"blur",message:c("app.ticketStatus.deviceError")}],ticketNo:[{required:!0,message:c("app.agentTicketQuery.validate.tktNoNull"),trigger:"blur"},{pattern:pn,message:c("app.agentTicketQuery.validate.tktNoError"),trigger:"blur"}],domestic:[{required:!0,message:c("app.agentTicketQuery.validate.required"),trigger:"blur"}],refundNo:[{required:!0,message:c("app.agentTicketQuery.validate.required"),trigger:"blur"},{pattern:ma,message:c("app.agentTicketQuery.validate.refundNoError"),trigger:"blur"}]},r=O([]),g={BSP:{label:c("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:c("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:c("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:c("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:c("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:c("app.agentTicketQuery.OWNTicket"),value:"ARL"}},b=Le(()=>!["CDS","GPCDS"].includes(a.ticketOrganization)),h=y=>{a.domestic=y==="D"||!y},E=()=>{var y,D,$,A,V,u,te,X,ge,q,Y,L;((y=l.value)!=null&&y.includes("$$$")||(D=l.value)!=null&&D.includes("BSP"))&&(r.value.push(g.BSP),r.value.push(g.GPBSP)),!(($=l.value)!=null&&$.includes("BSP"))&&((A=l.value)!=null&&A.includes("GP"))&&r.value.push(g.GPBSP),((V=l.value)!=null&&V.includes("$$$")||(u=l.value)!=null&&u.includes("BOP"))&&r.value.push(g.BOPBSP),((te=l.value)!=null&&te.includes("$$$")||(X=l.value)!=null&&X.includes("CDS"))&&(r.value.push(g.CDS),r.value.push(g.GPCDS)),((ge=l.value)!=null&&ge.includes("$$$")||(q=l.value)!=null&&q.includes("本票"))&&r.value.push(g.ARL),a.ticketOrganization=((L=(Y=r.value)==null?void 0:Y[0])==null?void 0:L.value)??""};return ct(()=>{E()}),{refundType:k,deleteRefundFrom:a,PRINTER_NO_RULES:v,ticketOrganizationList:r,isShowPrintNo:b,deliverPrintType:h,showQueryRefundForm:T,deleteFormDateRef:f}},Zo=Jo,ei={class:"carType-option-panel"},ti={class:"flex"},ni=t("br",null,null,-1),ai=t("i",{class:"iconfont icon-info-circle-line ticket-icon ml-[5px] mt-1"},null,-1),si=He({__name:"DeleteRefundForm",setup(c,{expose:p}){const{deleteRefundFrom:l,PRINTER_NO_RULES:f,ticketOrganizationList:T,isShowPrintNo:k,deliverPrintType:a,deleteFormDateRef:v}=Zo();return p({deleteRefundFrom:l,validateForm:()=>new Promise(r=>{var g;(g=v.value)==null||g.validate(b=>{r(b)})})}),(r,g)=>{const b=at,h=Jt,E=Zt,y=dt,D=xt,$=Nt,A=gn,V=pt;return s(),re(V,{ref_key:"deleteFormDateRef",ref:v,model:e(l),rules:e(f),"label-position":"left","require-asterisk-position":"right",class:"mt-[20px]"},{default:i(()=>[o(y,{prop:"ticketOrganization",label:r.$t("app.agentTicketQuery.ticketOrganization")},{default:i(()=>[o(E,{modelValue:e(l).ticketOrganization,"onUpdate:modelValue":g[0]||(g[0]=u=>e(l).ticketOrganization=u),class:"ticket-management-organization",disabled:e(l).ticketOrganization==="",placeholder:e(l).ticketOrganization===""?r.$t("app.agentTicketQuery.noData"):""},{default:i(()=>[(s(!0),m(xe,null,Ee(e(T),u=>(s(),re(h,{key:u.value,label:u.label,value:u.value},{default:i(()=>[t("div",ei,[t("div",{class:Ve(e(l).ticketOrganization===u.value?"show-select":"hidden-select")},[o(b,null,{default:i(()=>[o(e(fn))]),_:1})],2),t("span",null,n(u.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label"]),t("div",ti,[o(y,{prop:"refundNo",label:r.$t("app.agentTicketQuery.rtNum")},{default:i(()=>[o(D,{modelValue:e(l).refundNo,"onUpdate:modelValue":g[1]||(g[1]=u=>e(l).refundNo=u),modelModifiers:{trim:!0},clearable:""},null,8,["modelValue"])]),_:1},8,["label"]),o($,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:i(()=>[z(n(r.$t("app.agentTicketQuery.refundNoError")),1),ni,z(" "+n(r.$t("app.agentTicketQuery.forExample"))+"：999-123456789"+n(r.$t("app.agentTicketQuery.or"))+"999123456789"+n(r.$t("app.agentTicketQuery.or"))+"123456789 ",1)]),default:i(()=>[ai]),_:1})]),e(k)?(s(),re(y,{key:0,prop:"printerNo",label:r.$t("app.ticketStatus.deviceNum")},{default:i(()=>[o(It,{modelValue:e(l).printerNo,"onUpdate:modelValue":[g[2]||(g[2]=u=>e(l).printerNo=u),g[3]||(g[3]=u=>e(v).validateField("printerNo"))],modelModifiers:{trim:!0},"select-class":"w-[340px]",onDeliverPrintType:e(a)},null,8,["modelValue","onDeliverPrintType"])]),_:1},8,["label"])):W("",!0),o(y,{class:"inline-flex mr-[40px] mb-[10px] w-[60px]",prop:"domestic",label:r.$t("app.agentTicketQuery.printType")},{default:i(()=>[o(A,{modelValue:e(l).domestic,"onUpdate:modelValue":g[4]||(g[4]=u=>e(l).domestic=u),"inline-prompt":"","active-text":r.$t("app.issue.dom"),"inactive-text":r.$t("app.issue.intr")},null,8,["modelValue","active-text","inactive-text"])]),_:1},8,["label"])]),_:1},8,["model","rules"])}}}),oi=t("i",{class:"iconfont icon-close"},null,-1),ii=[oi],li={class:"text-xs h-6 justify-start items-start flex cursor-pointer mb-[10px]"},ri={key:0},ci={class:"carType-option-panel"},ui={key:0,class:"flex"},di=t("br",null,null,-1),pi=t("i",{class:"iconfont icon-info-circle-line ticket-icon ml-[5px] mt-1"},null,-1),fi={key:1,class:"flex"},mi=t("br",null,null,-1),gi=t("i",{class:"iconfont icon-info-circle-line ticket-icon ml-[5px] mt-1"},null,-1),ki={key:1},yi=He({__name:"RefundParameterDialog",emits:["update:modelValue","openRefundDialog"],setup(c,{emit:p}){const l=p,{refundType:f,formDate:T,printNoFrom:k,PRINTER_NO_RULES:a,ticketOrganizationList:v,isShowPrintNo:r,confirmPrinterNo:g,closeDialog:b,deliverPrintType:h,changeRefundType:E,handleClickTab:y,showQueryRefundForm:D,deleteFormDateRef:$}=Xo(l);return(A,V)=>{const u=zt,te=qt,X=at,ge=Jt,q=Zt,Y=dt,L=xt,le=Nt,C=gn,d=pt,j=st,Te=ft,se=ht("trimUpper");return s(),re(Te,{title:A.$t("app.agentTicketQuery.refundBtnOperation"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(b)},{footer:i(()=>[t("div",null,[o(j,{type:"primary","data-gid":"091T0107",onClick:V[10]||(V[10]=x=>e(g)())},{default:i(()=>[z(n(A.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),o(j,{onClick:e(b)},{default:i(()=>[z(n(A.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:V[0]||(V[0]=(...x)=>e(b)&&e(b)(...x))},ii),o(d,{ref_key:"formDate",ref:T,model:e(k),rules:e(a),"label-position":"left","require-asterisk-position":"right",class:"mt-[20px]"},{default:i(()=>[t("div",li,[t("div",{class:Ve(["w-[80px] px-2 py-0.5 rounded-tl-sm border",[e(D)?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:V[1]||(V[1]=x=>e(y)("queryForm"))},n(A.$t("app.agentTicketQuery.queryRefundBtn")),3),t("div",{class:Ve(["w-[80px] px-2 py-0.5 border",[e(D)?"bg-gray-0 border-gray-6":"bg-brand-7 border-brand-2 text-brand-2"]]),onClick:V[2]||(V[2]=x=>e(y)("deleteFrom"))},n(A.$t("app.agentTicketQuery.deleteRefundBtn")),3)]),e(D)?(s(),m("div",ri,[o(te,{modelValue:e(f),"onUpdate:modelValue":V[3]||(V[3]=x=>Ue(f)?f.value=x:null),onChange:e(E)},{default:i(()=>[o(u,{label:"1"},{default:i(()=>[z(n(A.$t("app.agentTicketQuery.useTicketNo")),1)]),_:1}),o(u,{label:"2"},{default:i(()=>[z(n(A.$t("app.agentTicketQuery.useRefundNo")),1)]),_:1})]),_:1},8,["modelValue","onChange"]),o(Y,{prop:"ticketOrganization",label:A.$t("app.agentTicketQuery.ticketOrganization")},{default:i(()=>[o(q,{modelValue:e(k).ticketOrganization,"onUpdate:modelValue":V[4]||(V[4]=x=>e(k).ticketOrganization=x),class:"ticket-management-organization",disabled:e(k).ticketOrganization==="",placeholder:e(k).ticketOrganization===""?A.$t("app.agentTicketQuery.noData"):""},{default:i(()=>[(s(!0),m(xe,null,Ee(e(v),x=>(s(),re(ge,{key:x.value,label:x.label,value:x.value},{default:i(()=>[t("div",ci,[t("div",{class:Ve(e(k).ticketOrganization===x.value?"show-select":"hidden-select")},[o(X,null,{default:i(()=>[o(e(fn))]),_:1})],2),t("span",null,n(x.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label"]),e(f)==="1"?(s(),m("div",ui,[o(Y,{prop:"ticketNo",label:A.$t("app.agentTicketQuery.ticketNo")},{default:i(()=>[$e(o(L,{modelValue:e(k).ticketNo,"onUpdate:modelValue":V[5]||(V[5]=x=>e(k).ticketNo=x),clearable:""},null,8,["modelValue"]),[[se]])]),_:1},8,["label"]),o(le,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:i(()=>[z(n(A.$t("app.agentTicketQuery.ticketNumberTips")),1),di,z(" "+n(A.$t("app.agentTicketQuery.forExample"))+"：999-1234567890"+n(A.$t("app.agentTicketQuery.or"))+"9991234567890 ",1)]),default:i(()=>[pi]),_:1})])):(s(),m("div",fi,[o(Y,{prop:"refundNo",label:A.$t("app.agentTicketQuery.rtNum")},{default:i(()=>[$e(o(L,{modelValue:e(k).refundNo,"onUpdate:modelValue":V[6]||(V[6]=x=>e(k).refundNo=x),clearable:""},null,8,["modelValue"]),[[se]])]),_:1},8,["label"]),o(le,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:i(()=>[z(n(A.$t("app.agentTicketQuery.refundNoError")),1),mi,z(" "+n(A.$t("app.agentTicketQuery.forExample"))+"：999-123456789"+n(A.$t("app.agentTicketQuery.or"))+"999123456789"+n(A.$t("app.agentTicketQuery.or"))+"123456789 ",1)]),default:i(()=>[gi]),_:1})])),e(r)?(s(),re(Y,{key:2,prop:"printerNo",label:A.$t("app.ticketStatus.deviceNum")},{default:i(()=>[o(It,{modelValue:e(k).printerNo,"onUpdate:modelValue":[V[7]||(V[7]=x=>e(k).printerNo=x),V[8]||(V[8]=x=>e(T).validateField("printerNo"))],modelModifiers:{trim:!0},"select-class":"w-[340px]",onDeliverPrintType:e(h)},null,8,["modelValue","onDeliverPrintType"])]),_:1},8,["label"])):W("",!0),o(Y,{class:"inline-flex mr-[40px] mb-[10px] w-[60px]",prop:"domestic",label:A.$t("app.agentTicketQuery.printType")},{default:i(()=>[o(C,{modelValue:e(k).domestic,"onUpdate:modelValue":V[9]||(V[9]=x=>e(k).domestic=x),"inline-prompt":"","active-text":A.$t("app.issue.dom"),"inactive-text":A.$t("app.issue.intr")},null,8,["modelValue","active-text","inactive-text"])]),_:1},8,["label"])])):(s(),m("div",ki,[o(si,{ref_key:"deleteFormDateRef",ref:$},null,512)]))]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const hi=(c,p)=>{const{t:l}=et(),f=Dt(),T=O();let k=null;const a=O(!1),v=bn(),r=yt({optionType:"1",selectType:"NI",tktNo:"",pnrNo:"",ticketHistory:!1,passengerName:"",certificateNo:"",secondFactorType:"certificate",secondFactorCode:"NI",secondFactorValue:"",briefInfo:!1}),g=Le(()=>r.briefInfo),b=O([`${l("app.fastQuery.headerQuery.notHistory")}`]),h=O([`${l("app.fastQuery.headerQuery.notHistory")}`]),E=O(),y=O(),D={3:[{label:`NI ${l("app.agentTicketQuery.certs.by_IDAndResidence")}`,value:"NI"},{label:`PP ${l("app.agentTicketQuery.certs.by_PassportAndOther")}`,value:"PP"},{label:`UU ${l("app.agentTicketQuery.certs.by_Unable")}`,value:"UU"}]},$=(_,w,ce)=>{if(!(r.optionType==="3"&&r.selectType==="NI")){ce();return}ea.test(w)?ce():ce(l("app.agentTicketQuery.validate.enterCorrectIDTips"))},A=(_,w,ce)=>{!w&&!p.isOneFactor&&!g.value?r.secondFactorType==="PNR"?ce(l("app.agentTicketQuery.validate.inputPNRNo")):r.secondFactorType==="name"?ce(l("app.agentTicketQuery.validate.inputPassengerName")):ce(l("app.agentTicketQuery.validate.inputCertificateNo")):w&&r.secondFactorType==="PNR"&&!dn.test(w)?ce(l("app.agentTicketQuery.validate.enterCorrectPNRnumber")):w&&r.secondFactorType==="name"&&!Zn.test(w)?ce(l("app.agentTicketQuery.validate.passengerNameError")):w&&r.secondFactorType==="certificate"&&r.secondFactorCode==="NI"&&!ea.test(w)&&ce(l("app.agentTicketQuery.validate.enterCorrectIDTips")),ce()},V={optionType:[{required:!0,message:l("app.agentTicketQuery.validate.required"),trigger:"change"}],selectType:[{required:!0,message:l("app.agentTicketQuery.validate.required"),trigger:"change"}],tktNo:[{required:!0,message:l("app.agentTicketQuery.validate.tktNoNull"),trigger:["change","blur"]},{pattern:pn,message:l("app.agentTicketQuery.validate.tktNoError"),trigger:["change","blur"]}],pnrNo:[{required:!0,message:l("app.agentTicketQuery.validate.inputPNRNo"),trigger:["change","blur"]},{pattern:dn,message:l("app.agentTicketQuery.validate.enterCorrectPNRnumber"),trigger:["change","blur"]}],certificateNo:[{required:!0,message:l("app.agentTicketQuery.validate.inputCertificateNo"),trigger:["change","blur"]},{validator:$,trigger:["change","blur"]}],passengerName:[{required:!0,message:l("app.agentTicketQuery.validate.inputPassengerName"),trigger:["change","blur"]},{pattern:Zn,message:l("app.agentTicketQuery.validate.passengerNameError"),trigger:["change","blur"]}],secondFactorValue:[{validator:A,trigger:["change","blur"]}]},u=_=>{r.secondFactorType=_.secondFactorType,r.secondFactorCode=_.secondFactorCode,r.secondFactorValue=_.secondFactorValue},te=()=>{const{tktNo:_,secondFactorCode:w,secondFactorValue:ce}=r;let ie=null;return w&&ce&&(ie={secondFactorCode:w,secondFactorValue:ce}),{ticketNo:_,detrType:"CONJUNCTIVE_TICKET",secondFactor:ie}},X=_=>{var w;(w=b.value)!=null&&w.includes(_)||(y.value="")},ge=_=>{var w;(w=h.value)!=null&&w.includes(_)||(y.value="")},q=async()=>{const _=r.optionType,w=r.secondFactorType,ce=r.secondFactorCode,ie=await f.getters.user,oe=localStorage.getItem("twoFactorHistory"),Se={agent:ie.agent,optionType:_,secondFactorType:w,secondFactorCode:ce};if(!oe)localStorage.setItem("twoFactorHistory",JSON.stringify([Se]));else{const de=JSON.parse(oe),I=de.findIndex(ve=>ve.agent===ie.agent);de[I]=Se,localStorage.setItem("twoFactorHistory",JSON.stringify(de))}},Y=async()=>{try{const _=De("091M0101"),w=(await ba(te(),_)).data.value;c("handleQueryTicket",w,r.optionType);const{tktNo:ce,secondFactorCode:ie,secondFactorValue:oe,secondFactorType:Se}=r;w&&J("query_ticket_history",{ticketNo:ce,secondFactorType:Se,secondFactorCode:Se==="certificate"?ie:"",secondFactorValue:Se==="certificate"?"":oe,timestamp:new Date().toISOString()}),q()}catch{R()}},L=async()=>{try{const _=De("091M0108"),w=(await Qs({certNo:r.certificateNo,certCode:r.selectType},_)).data.value;c("handleQueryTicket",w,r.optionType)}catch{R()}},le=async()=>{try{const _=De("091M0110"),w=(await Bs(r.pnrNo,_)).data.value;c("handleQueryTicket",w,r.optionType)}catch{R()}},C=async()=>{try{const _=De("091M0109"),w=(await Us({certCode:"NM",certNo:Xt.encode(r.passengerName.trim())},_)).data.value;c("handleQueryTicket",w,r.optionType);const{passengerName:ce}=r;w&&J("query_name_history",{passengerName:ce,timestamp:new Date().toISOString()})}catch{R()}},d=async()=>{try{const _=De("091M0111"),{data:w}=await js(Xt.encode(r.passengerName.trim()),_);c("handleQueryTicket",w.value,"5")}catch{R()}},j=async()=>{try{const _=De("091M0112"),{data:w}=await Is(r.tktNo,_);c("handleQueryTicket",w.value,"6")}catch{R()}},Te=async()=>{var _;(_=T.value)==null||_.validate(async w=>{if(w){switch(k=Ft.service({fullscreen:!0}),r.optionType){case"1":r.briefInfo?j():Y();break;case"2":le();break;case"3":L();break;case"4":r.briefInfo?d():C();break}v.replace("/v2/crs/ticketOperation")}})},se=()=>{var _,w;r.selectType=((w=(_=D[r.optionType])==null?void 0:_[0])==null?void 0:w.value)??"TICKET",r.pnrNo="",r.tktNo="",r.ticketHistory=!1,r.certificateNo="",r.passengerName="",r.briefInfo=!1,r.secondFactorValue="",y.value=""},x=()=>{var _;(_=T.value)==null||_.clearValidate("certificateNo")},R=()=>{k&&k.close()},pe=(_,w)=>{w==="1"?(r.optionType=w,r.selectType="TICKET",r.tktNo=_):w==="2"?(r.optionType=w,r.pnrNo=_):w==="3"&&(r.optionType=w,r.selectType="NI",r.certificateNo=_),Te(),v.replace("/v2/crs/ticketOperation")},Re=_=>{_.get("secondFactorCode")&&_.get("secondFactorValue")?(r.secondFactorType="PNR",r.secondFactorCode=_.get("secondFactorCode")??"CN",r.secondFactorValue=_.get("secondFactorValue")??""):(r.secondFactorType="certificate",r.secondFactorCode="NI",r.secondFactorValue="")},we=()=>{a.value=!0},me=()=>{a.value=!1},he=(_,w,ce,ie,oe)=>{c("openRefundDialog",_,w,ce,ie,oe)},ue=async _=>{var w,ce;if(r.optionType==="1"){const ie=(w=E.value)==null?void 0:w.find(oe=>oe.ticketNo===_);y.value=_,r.tktNo=ie==null?void 0:ie.ticketNo,r.secondFactorType=ie==null?void 0:ie.secondFactorType,r.secondFactorType==="name"?r.secondFactorCode="NM":r.secondFactorCode=((ie==null?void 0:ie.secondFactorCode)??"")===""?"CN":ie==null?void 0:ie.secondFactorCode,r.secondFactorValue=(ie==null?void 0:ie.secondFactorValue)??""}else if(r.optionType==="4"){const ie=(ce=E.value)==null?void 0:ce.find(oe=>oe.passengerName===_);y.value=_,r.passengerName=ie==null?void 0:ie.passengerName}},U=_=>{try{const w=localStorage.getItem(_);return w?JSON.parse(w):[]}catch{return[]}},J=(_,w)=>{var ie,oe;if(r.optionType==="1"){const Se=ne(U(_));E.value=Se.filter(de=>de.ticketNo!==w.ticketNo)}else if(r.optionType==="4"){const Se=B(U(_));E.value=Se.filter(de=>de.passengerName!==w.passengerName)}if(E.value.unshift(w),r.optionType==="1"){const Se=(ie=E.value)==null?void 0:ie.map(de=>de==null?void 0:de.ticketNo);b.value=Se==null?void 0:Se.slice(0,5)}else if(r.optionType==="4"){const Se=(oe=E.value)==null?void 0:oe.map(de=>de==null?void 0:de.passengerName);h.value=Se==null?void 0:Se.slice(0,5)}const ce=[w,...E.value];localStorage.setItem(_,JSON.stringify(ce))},ne=_=>{const w=new Map;return _.forEach(ce=>{if(r.optionType==="1"){const ie=w.get(ce.ticketNo);(!ie||new Date(ce.timestamp)>new Date(ie.timestamp))&&w.set(ce.ticketNo,ce)}else if(r.optionType==="4"){const ie=w.get(ce.passengerName);(!ie||new Date(ce.timestamp)>new Date(ie.timestamp))&&w.set(ce.passengerName,ce)}}),Array.from(w.values())},B=_=>_.filter((w,ce,ie)=>{const oe=w.passengerName.trim(),Se=ie.findLast(de=>de.passengerName.trim()===oe);return w===Se}),P=()=>{var _,w;if(r.optionType==="1"){E.value=ne(U("query_ticket_history"));const ce=(_=E.value)==null?void 0:_.map(ie=>ie==null?void 0:ie.ticketNo);b.value=ce==null?void 0:ce.slice(0,5)}else if(r.optionType==="4"){E.value=B(U("query_name_history"));const ce=(w=E.value)==null?void 0:w.map(ie=>ie==null?void 0:ie.passengerName);h.value=ce==null?void 0:ce.slice(0,5)}},N=async()=>{const _=localStorage.getItem("twoFactorHistory");if(_){const w=await f.getters.user,ie=JSON.parse(_).find(oe=>oe.agent===w.agent);ie&&(r.optionType=ie.optionType,r.secondFactorType=ie.secondFactorType,r.secondFactorCode=ie.secondFactorCode)}};return ct(()=>{P(),N()}),cs(()=>{N()}),bt(()=>r.optionType,()=>{P()}),bt(()=>v.currentRoute.value.fullPath,_=>{const w=new URLSearchParams(window.location.search);_.includes("ticketOperation")&&(w.get("pnrNo")&&(r.pnrNo=w.get("pnrNo")??"",r.optionType="2",k=Ft.service({fullscreen:!0}),le()),w.get("ticketNumber")&&(r.tktNo=w.get("ticketNumber")??"",r.optionType="1",Re(w),r.secondFactorCode&&r.secondFactorValue&&(k=Ft.service({fullscreen:!0}),Y())),v.replace("/v2/crs/ticketOperation"))},{immediate:!0,deep:!0}),{formRef:T,queryForm:r,queryTkt:Te,SELECT_OPTION:D,FORM_RULES:V,updateSecondFactorFormData:u,clearOtherInput:se,clearCertValid:x,queryTktByRoute:pe,closeLoading:R,refundView:we,showPrintNoDialog:a,queryRefundMessage:he,closeRefundView:me,handleSelectItem:ue,chooseTicketItems:b,chooseNameItems:h,selectedItem:y,changeSelectedItem:X,changeNameSelectedItem:ge,isRequired:g}},vi=hi,Kt=c=>(Ut("data-v-3b15cd26"),c=c(),jt(),c),_i={class:"query-condition-container bg-gray-0 p-[10px] rounded-t-lg"},Ti={class:"inline-block mr-[10px] mb-[10px]"},bi={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[36px]"},xi=Kt(()=>t("span",{class:"text-red-1 text-xs font-normal"},"*",-1)),Ni=Kt(()=>t("br",null,null,-1)),$i=Kt(()=>t("i",{class:"iconfont icon-info-circle-line ticket-icon mr-[10px]"},null,-1)),Ri={key:0,class:"text-red-1 text-xs font-normal leading-tight mr-[3px]"},Ci={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[47px]"},wi=Kt(()=>t("span",{class:"text-red-1 text-xs font-normal"},"*",-1)),Si=Kt(()=>t("span",{class:"text-red-1 text-xs font-normal leading-tight"},"*",-1)),Pi={key:0,class:"iconfont icon-right-line text-[12px]"},Di={key:1,class:"inline-block w-[12px] mr-[4px]"},Ai={class:"inline-block text-gray-3 text-xs font-normal leading-tight min-w-[35px] -mt-[2px]"},Ei=Kt(()=>t("span",{class:"text-red-1 text-xs font-normal leading-tight"},"*",-1)),Oi={class:"flex-col justify-start items-start gap-5 inline-flex ml-[10px]"},Fi={class:"inline-flex items-center justify-center gap-1"},Vi=Kt(()=>t("span",{class:"iconfont icon-time-circle2"},null,-1)),Mi={class:"text-xs"},Li={key:0,class:"flex items-center"},Bi={class:"text-brand-2 text-xs font-normal leading-tight"},Qi={key:1,class:"flex items-center"},Ui=Kt(()=>t("div",{class:"w-[19px]"},null,-1)),ji={class:"text-gray-2 text-xs font-normal leading-tight"},Ii=He({__name:"TicketQueryCondition",props:{isOneFactor:{type:Boolean}},emits:["handleQueryTicket","addNewTab","openAuthOffice","openBatchRefund","openBopRefund","openManualRefund","openRefundDialog","openRtkt","openCccf"],setup(c,{expose:p,emit:l}){const f=l,T=c,{formRef:k,queryForm:a,queryTkt:v,SELECT_OPTION:r,FORM_RULES:g,updateSecondFactorFormData:b,clearOtherInput:h,clearCertValid:E,queryTktByRoute:y,closeLoading:D,refundView:$,showPrintNoDialog:A,queryRefundMessage:V,closeRefundView:u,handleSelectItem:te,chooseTicketItems:X,chooseNameItems:ge,selectedItem:q,changeSelectedItem:Y,changeNameSelectedItem:L,isRequired:le}=vi(f,T);return p({queryTkt:v,queryTktByRoute:y,closeLoading:D,closeRefundView:u}),(C,d)=>{const j=zt,Te=qt,se=dt,x=xt,R=Nt,pe=Ht,Re=Jt,we=Zt,me=st,he=Ca,ue=at,U=fo,J=mo,ne=go,B=pt,P=ht("trimUpper"),N=ht("permission");return s(),m(xe,null,[t("div",_i,[o(B,{ref_key:"formRef",ref:k,inline:!0,model:e(a),rules:e(g),class:"query-condition-form"},{default:i(()=>[o(se,{prop:"optionType",class:"query-left mb-[10px]"},{default:i(()=>[o(Te,{modelValue:e(a).optionType,"onUpdate:modelValue":d[0]||(d[0]=_=>e(a).optionType=_),class:"ml-4",onChange:e(h)},{default:i(()=>[o(j,{label:"1",size:"large"},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketNumber")),1)]),_:1}),o(j,{label:"3",size:"large"},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.idNumber")),1)]),_:1}),o(j,{label:"4",size:"large"},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.passengerName")),1)]),_:1})]),_:1},8,["modelValue","onChange"])]),_:1}),t("div",Ti,[e(a).optionType==="1"?(s(),m(xe,{key:0},[t("div",bi,[z(n(C.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+" ",1),xi]),o(se,{prop:"tktNo"},{default:i(()=>[$e(o(x,{modelValue:e(a).tktNo,"onUpdate:modelValue":d[1]||(d[1]=_=>e(a).tktNo=_),placeholder:C.$t("app.agentTicketQuery.inputEmdNo"),class:"ticket-input",clearable:"",onInput:d[2]||(d[2]=_=>{var w;return e(a).tktNo=((w=e(a).tktNo)==null?void 0:w.toUpperCase())??""}),onBlur:d[3]||(d[3]=_=>e(Y)(e(a).tktNo))},null,8,["modelValue","placeholder"]),[[P]])]),_:1}),o(R,{placement:"top",effect:"dark","popper-class":"ticket-conditon-popper"},{content:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketNumberTips")),1),Ni,z(" "+n(C.$t("app.agentTicketQuery.suchAs"))+"：999-1234567890 ",1)]),default:i(()=>[$i]),_:1}),!C.isOneFactor&&!e(le)?(s(),m("span",Ri,"*")):W("",!0),o(ro,{"item-props":"secondFactorValue","second-factor-type":e(a).secondFactorType,"second-factor-code":e(a).secondFactorCode,"second-factor-value":e(a).secondFactorValue,onUpdateFormData:e(b),onValidateProp:d[4]||(d[4]=_=>{var w;return(w=e(k))==null?void 0:w.validateField("secondFactorValue")}),onClearPropValidate:d[5]||(d[5]=_=>{var w;return(w=e(k))==null?void 0:w.clearValidate("secondFactorValue")})},null,8,["second-factor-type","second-factor-code","second-factor-value","onUpdateFormData"]),o(se,{prop:"briefInfo",class:"ml-[10px]"},{default:i(()=>[o(pe,{modelValue:e(a).briefInfo,"onUpdate:modelValue":d[6]||(d[6]=_=>e(a).briefInfo=_),label:C.$t("app.agentTicketQuery.briefInfo")},null,8,["modelValue","label"])]),_:1})],64)):W("",!0),e(a).optionType==="2"?(s(),m(xe,{key:1},[t("div",Ci,[z(n(C.$t("app.agentTicketQuery.pnrNo"))+" ",1),wi]),o(se,{prop:"pnrNo"},{default:i(()=>[$e(o(x,{modelValue:e(a).pnrNo,"onUpdate:modelValue":d[7]||(d[7]=_=>e(a).pnrNo=_),placeholder:C.$t("app.agentTicketQuery.inputPNRNo"),class:"pnr-input",clearable:"",onInput:d[8]||(d[8]=_=>{var w;return e(a).pnrNo=((w=e(a).pnrNo)==null?void 0:w.toUpperCase())??""})},null,8,["modelValue","placeholder"]),[[P]])]),_:1})],64)):W("",!0),e(a).optionType==="3"?(s(),m(xe,{key:2},[o(se,{prop:"selectType"},{default:i(()=>[Si,o(we,{modelValue:e(a).selectType,"onUpdate:modelValue":d[9]||(d[9]=_=>e(a).selectType=_),"popper-class":"doctype-selector-popper",class:"doctype-select",teleported:!1,onChange:e(E)},{default:i(()=>[(s(!0),m(xe,null,Ee(e(r)[e(a).optionType],_=>(s(),re(Re,{key:_.value,label:_.label,value:_.value},{default:i(()=>[t("span",null,[e(a).selectType===_.value?(s(),m("i",Pi)):(s(),m("span",Di)),t("span",null,n(_.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])]),_:1}),o(se,{prop:"certificateNo"},{default:i(()=>[$e(o(x,{modelValue:e(a).certificateNo,"onUpdate:modelValue":d[10]||(d[10]=_=>e(a).certificateNo=_),placeholder:C.$t("app.agentTicketQuery.inputCertificateNo"),class:"cert-input",clearable:"",onInput:d[11]||(d[11]=_=>{var w;return e(a).certificateNo=((w=e(a).certificateNo)==null?void 0:w.toUpperCase())??""})},null,8,["modelValue","placeholder"]),[[P]])]),_:1})],64)):W("",!0),e(a).optionType==="4"?(s(),m(xe,{key:3},[t("div",Ai,[z(n(C.$t("app.agentTicketQuery.name"))+" ",1),Ei]),o(se,{prop:"passengerName"},{default:i(()=>[$e(o(x,{modelValue:e(a).passengerName,"onUpdate:modelValue":d[12]||(d[12]=_=>e(a).passengerName=_),class:"name-input",clearable:"",onInput:d[13]||(d[13]=_=>{var w;return e(a).passengerName=((w=e(a).passengerName)==null?void 0:w.toUpperCase())??""}),onBlur:d[14]||(d[14]=_=>e(L)(e(a).passengerName))},null,8,["modelValue"]),[[P]])]),_:1}),o(se,{prop:"briefInfo",class:"ml-[10px]"},{default:i(()=>[o(pe,{modelValue:e(a).briefInfo,"onUpdate:modelValue":d[15]||(d[15]=_=>e(a).briefInfo=_),label:C.$t("app.agentTicketQuery.briefInfo")},null,8,["modelValue","label"])]),_:1})],64)):W("",!0)]),o(se,null,{default:i(()=>[o(me,{type:"primary","data-gid":"091M0101",onClick:e(v)},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.queryBtn")),1)]),_:1},8,["onClick"]),$e((s(),re(me,{onClick:d[16]||(d[16]=_=>f("openAuthOffice"))},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketAuthBtn")),1)]),_:1})),[[N,"crs-tc-ticketOperation-ticketQuery-ticketAuth-button"]]),$e((s(),re(me,{onClick:d[17]||(d[17]=_=>f("openBatchRefund"))},{default:i(()=>[z(n(C.$t("app.batchRefund.batchRefund")),1)]),_:1})),[[N,"crs-tc-ticketOperation-ticketQuery-refundTicket-button"]]),$e((s(),re(me,{onClick:d[18]||(d[18]=_=>f("openBopRefund"))},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.bopRefund")),1)]),_:1})),[[N,"crs-ticket-manage-ticket-query-page-bop-refund-button"]]),$e((s(),re(me,{onClick:d[19]||(d[19]=_=>f("openManualRefund"))},{default:i(()=>[z(n(C.$t("app.agentTicketRefund.manualRefundBtn")),1)]),_:1})),[[N,"crs-ticket-manage-ticket-query-page-manual-refund-button"]]),o(me,{onClick:e($)},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.refundBtnOperation")),1)]),_:1},8,["onClick"]),o(me,{onClick:d[20]||(d[20]=_=>f("openRtkt"))},{default:i(()=>[z("RTKT")]),_:1}),o(he,{"popper-class":"credit-card-receipt-print-popover",placement:"top",effect:"dark",width:"auto",content:C.$t("app.cccf.creditCardReceiptPrint")},{reference:i(()=>[o(me,{onClick:d[21]||(d[21]=_=>f("openCccf"))},{default:i(()=>[z("CCCF")]),_:1})]),_:1},8,["content"])]),_:1}),e(a).optionType==="1"||e(a).optionType==="4"?(s(),re(se,{key:0},{default:i(()=>[t("div",Oi,[t("div",Fi,[o(ne,{"popper-class":"history-popper",onCommand:e(te)},{dropdown:i(()=>[o(J,null,{default:i(()=>[(s(!0),m(xe,null,Ee(e(a).optionType==="1"?e(X):e(ge),(_,w)=>(s(),re(U,{key:w,command:_},{default:i(()=>[e(q)===_?(s(),m("span",Li,[o(ue,{class:"bg-inherit !text-brand-2 !text-[14px]"},{default:i(()=>[o(e(fn))]),_:1}),t("span",Bi,n(_),1)])):(s(),m("span",Qi,[Ui,t("span",ji,n(_),1)]))]),_:2},1032,["command"]))),128))]),_:1})]),default:i(()=>[o(me,{link:"",type:"primary",class:"service-book-btn"},{default:i(()=>[Vi,t("span",Mi,n(C.$t("app.fastQuery.headerQuery.historyQuery")),1)]),_:1})]),_:1},8,["onCommand"])])])]),_:1})):W("",!0)]),_:1},8,["model","rules"])]),e(A)?(s(),re(yi,{key:0,modelValue:e(A),"onUpdate:modelValue":d[22]||(d[22]=_=>Ue(A)?A.value=_:null),onOpenRefundDialog:e(V)},null,8,["modelValue","onOpenRefundDialog"])):W("",!0)],64)}}});const zi=vt(Ii,[["__scopeId","data-v-3b15cd26"]]),qi=(c,p)=>{var Wn;const l=O(),f=Dt(),T=wa(),{t:k}=et(),a=O(!1),v=O(!0),r=O(!1),g=O([]),b=O([]),h=Be(!1),E=O({}),y=O([]),D=O(!1),$=O(!1),A=O(!1),V=yt({isAlreadyClick:!1,isAlreadySuccessSearch:!1}),u=Le(()=>{const M=g.value.filter(K=>K.infants).length;return Yt(g.value.length,M)===b.value.length}),te=Be(""),X=Be(""),ge=Be(""),q=O(""),Y=Be(c.tktNo),L=O(!1),le=O([]),C=O(!1),d=O("auto"),j=Be(!0),Te=Be(""),se=O(),x=O([]),R=yt({pnrNo:""}),pe=O(!1),Re={pnrNo:[{required:!0,message:k("app.agentTicketQuery.validate.inputPNRNo"),trigger:"blur"},{pattern:dn,message:k("app.agentTicketQuery.validate.enterCorrectPNRnumber"),trigger:["change","blur"]}]},we=Le(()=>b.value.length>0?b.value.every(M=>{var K;return(K=M.ticket.segment)==null?void 0:K.some(ke=>["OPEN FOR USE","AIRPORT CNTL"].includes(ke.ticketStatus??""))}):!1),me=M=>({passengerNameSuffix:M.passengerNameSuffix??"",passengerName:M.name,specialPassengerType:M.specialPassengerType,passengerType:M.specialPassengerType??"",index:"0",secondFactor:M.secondFactor,ticketNos:M.ticketNo.includes("-")?[M.ticketNo.split("-")[0],M.ticketNo.split("-")[1]]:[M.ticketNo],isChecked:!0}),he=O({}),ue=O({}),U=O(""),{defaultOffice:J,office:ne,defaultRoleWithPid:B}=f.state.user,N=(B?J:((Wn=ne==null?void 0:ne.split(";"))==null?void 0:Wn[0])??"")===No,_=(M,K)=>{const ke=new Map;return M.forEach(_e=>{var Ge;const Me=(Ge=_e[K])==null?void 0:Ge.replace(/-/,"");ke.set(Me,ke.get(Me)||[]),ke.get(Me).push(_e)}),ke},w=M=>!M||!M.secondFactorCode||!M.secondFactorValue?null:M,ce=async(M,K,ke,_e)=>{const Me=b.value.some(Ge=>Ge.ticket.ticketNo.includes(M));if(ke&&Me)return"";if(ke)try{a.value=!0;const Ge={tktNo:M,secondFactor:w(K),refundQuery:!0},ze=(await ia(Ge,_e)).data.value;return ze!=null&&ze.data?(ze.data.ticket.ticketSegment=_(ze.data.ticket.segment??[],"tktTag"),ue.value=ze==null?void 0:ze.data,b.value.push(ze.data),v.value&&g.value.push(me(ze.data.ticket)),ze.data.ticket.crsPnrNo??""):""}finally{a.value=!1}else return b.value=b.value.filter(Ge=>!Ge.ticket.ticketNo.includes(M)),b.value.length===1&&(ue.value=b.value[0]),""},ie=M=>M==null?void 0:M.replace("-",""),oe=M=>{M.ticketNos=M.ticketNos.map(ie),M.infants&&(M.infants.ticketNos=M.infants.ticketNos.map(ie))},Se=M=>{M.forEach(oe)},de=M=>{var ke,_e;const K=(ke=c.tktNo)==null?void 0:ke.replace("-","");return(_e=M.ticketNos)==null?void 0:_e.includes(K)},I=(M,K)=>(j.value=M.some(de),j.value?ge.value=K:Te.value=(M==null?void 0:M.length)===0?k("app.agentTicketRefund.queryPnrFailed"):k("app.agentTicketRefund.currentTicketNotMatchPnr"),j.value),ve=M=>{var ke,_e,Me;g.value.splice(0,1);const K=g.value.findIndex(Ge=>Ge.index===(M==null?void 0:M.index));g.value.unshift(g.value.splice(K,1)[0]),(Me=(_e=(ke=g.value[0])==null?void 0:ke.infants)==null?void 0:_e.ticketNos)!=null&&Me.length&&(g.value[0].infants.isChecked=!0)},F=async(M,K)=>{var ke,_e,Me;if(g.value[0].infants=M==null?void 0:M.infants,g.value[0].specialPassengerType=M==null?void 0:M.specialPassengerType,g.value[0].index=(M==null?void 0:M.index)??"",g.value[0].secondFactor=M==null?void 0:M.secondFactor,(Me=(_e=(ke=g.value[0])==null?void 0:ke.infants)==null?void 0:_e.ticketNos)!=null&&Me.length){g.value[0].infants.isChecked=!0;const{ticketNos:Ge,secondFactor:ze={}}=g.value[0].infants;await ce(Ge[0],ze,!0,K)}},Z=async(M,K)=>{const ke=M.find(de)??{};M.forEach(_e=>{!de(_e)&&(_e.specialPassengerType!=="INF"||_e.passengerType!=="INF")&&g.value.push(_e)}),(ke==null?void 0:ke.specialPassengerType)==="INF"?ve(ke):await F(ke,K)},Q=async(M,K,ke)=>{var _e,Me;try{ke&&await(ke==null?void 0:ke.validate()),a.value=!0;const Ge=((Me=(_e=await ko({pnrNo:M},K))==null?void 0:_e.data)==null?void 0:Me.value)??{},{passengers:ze=[]}=Ge;if(Se(ze),!I(ze,M))return;await Z(ze,K)}finally{a.value=!1}},ee=M=>{const K={pnrHandleType:M,pnrNo:u.value?"":ge.value,xePnr:Ce()?"":!u.value&&!Ce()?q.value:ge.value,passengerInfoList:[]};return gt().length?gt().forEach(ke=>{var _e,Me;K.passengerInfoList.push({name:Xt.encode(((_e=ke==null?void 0:ke.infants)==null?void 0:_e.passengerNameSuffix)??""),psgType:"INF",ticketNo:((Me=ke==null?void 0:ke.infants)==null?void 0:Me.ticketNos[0])??""})}):b.value.forEach(ke=>{!u.value&&ke.ticket.psgType==="INF"&&K.passengerInfoList.push({name:Xt.encode(ke.ticket.name),psgType:ke.ticket.psgType,ticketNo:ke.ticket.ticketNo})}),K},fe=()=>b.value.map(M=>{var K,ke,_e;return{ticketNo:((ke=(K=M.ticket.segment)==null?void 0:K[0])==null?void 0:ke.tktTag)??"",domestic:(_e=M.ticket.tktType)==null?void 0:_e.includes("D"),printNo:M.printerNo}}),Ce=()=>b.value.every(M=>M.ticket.psgType==="INF"),Oe=async M=>{await un(k("app.agentTicketRefund.refundSuccess")),A.value=!0,r.value=!0;const K=rt(b.value);b.value=[],K.forEach(async ke=>{var _e,Me;await ce(((Me=(_e=ke.ticket.segment)==null?void 0:_e[0])==null?void 0:Me.tktTag)??"",ke.ticket.secondFactor,!0,M)})},ye=M=>(M??[]).reduce((K,ke)=>{let _e="";K||(_e=`<p class="text-gray-1 text-lg font-normal pb-2.5">${k("app.agentTicketRefund.refundPartFail")}</p>`);const Me=ke.success?'<i class="iconfont icon-ticket text-green-2 mr-2.5"></i>':'<i class="iconfont icon-close text-red-1 mr-2.5"></i>',Ge=`${_e}<p class="text-sm font-bold leading-normal mt-4">${Me}${ke.ticketNo}</p>`;return K+Ge},""),Ye=async(M,K)=>{nt.confirm(ye(M),{icon:Pe(at,{color:"#FF3636",size:32},()=>Pe(ua)),customClass:"invalidated-warning-msg crs-btn-ui",closeOnClickModal:!1,showClose:!0,showCancelButton:!1,confirmButtonText:k("app.agentTicketRefund.confirm"),dangerouslyUseHTMLString:!0,draggable:!0}),r.value=!0;const ke=rt(b.value);b.value=[];for(let _e=0;_e<ke.length;_e++)try{a.value=!0;const Me=(await ia({tktNo:ke[_e].ticket.ticketNo,secondFactor:ke[_e].ticket.secondFactor,refundQuery:!0},K)).data.value;if(!(Me!=null&&Me.data))return;Me.data.ticket.ticketSegment=_(Me.data.ticket.segment??[],"tktTag"),b.value.push(Me.data)}finally{a.value=!1}b.value.forEach(_e=>{_e.ticket.isRefundFail=M.some(Me=>Me.ticketNo.replace("-","").includes(_e.ticket.ticketNo)&&!Me.success)})},qe=M=>{C.value=!0,d.value="auto",le.value=(M??[]).map(K=>({ticketNo:K.ticketNo,trfdNo:K.trfdno??""})),(M??[]).forEach(K=>{K.amount&&x.value.push(K.amount)})},Qe=M=>{d.value="manual",le.value=M??[]},ae=M=>{const K=M.indexOf("("),ke=M.indexOf("*");if(K>-1&&ke>-1){const _e=Math.min(K,ke);return(M==null?void 0:M.substring(0,_e))??""}else{if(K===-1&&ke>-1)return(M==null?void 0:M.substring(0,ke))??"";if(K>-1&&ke===-1)return(M==null?void 0:M.substring(0,K))??""}return M.trim()},ot=(M,K)=>{const ke=rt(b.value);b.value=[],ke.forEach(async _e=>{var Ge,ze;const Me={secondFactorCode:"NM",secondFactorValue:ae(_e.ticket.name)};M&&q.value&&(ge.value=q.value),await ce(((ze=(Ge=_e.ticket.segment)==null?void 0:Ge[0])==null?void 0:ze.tktTag)??"",Me,!0,K)})},S=(M,K)=>{ot(!0,K);const ke=`/v2/crs/pnrManagement?pnrNo=${M}`;mn.setLink(ke)},G=M=>M&&(u.value||Ce()||gt().length===0),Ne=M=>{A.value=!0,r.value=!0;const K=b.value[0].ticket.tktType==="I";if(K||(un(k("app.agentTicketRefund.refundSuccess")),ot(!1,M)),G(K)){let ke="C",_e=q.value?q.value:ge.value,Me=k("app.agentTicketRefund.cancelPnr"),Ge=k("app.agentTicketRefund.refundSuccessCanclePnrTips",{pnrNo:q.value?q.value:ge.value});Ce()&&(_e=ge.value,ke="D",Me=k("app.agentTicketRefund.xePassenger"),Ge=k("app.agentTicketRefund.refundSuccessDeleteTips",{pnrNo:ge.value}));const ze=nt;ze.confirm(Pe("div",{},[Pe("span",{className:"text-[18px] text-gray-1"},Ge),Pe("span",{className:"text-[18px] underline text-brand-2 cursor-pointer",onClick:()=>{S(_e,M),ze.close()}},k("app.agentTicketRefund.toOrder")),Pe("span",{className:"text-[18px] text-gray-1"},k("app.agentTicketRefund.handlePnr"))]),{icon:Pe(at,{color:Bn("--bkc-tw-green-2",null).value,size:32},()=>Pe(Qn)),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:Me,cancelButtonText:k("app.agentTicketRefund.cancel"),showClose:!1}).then(async()=>{await At(ke,ke==="C"?_e:"",M),ot(ke==="C",M)}).catch(()=>{ot(ke==="C",M)})}else K&&!u.value&&(pe.value=!0)},Ae=async M=>{let K=k("app.agentTicketRefund.cancelPnr"),ke="C";const _e=b.value[0].ticket.tktType==="I";let Me=gt().length&&!_e?k("app.agentTicketRefund.refundSeatTipsNonCarryInft",{pnrNo:q.value?q.value:ge.value,ticketNo:Et(gt())}):k("app.agentTicketRefund.refundSeatTips",{pnrNo:q.value?q.value:ge.value});Ce()&&(ke="D",K=k("app.agentTicketRefund.xePassenger"),Me=k("app.agentTicketRefund.refundSeatDeleteTips",{pnrNo:ge.value})),nt.confirm(Pe("div",{className:"whitespace-pre-line"},Me),{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:K,cancelButtonText:k("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1}).then(async()=>{await At(ke,ke==="C"?q.value?q.value:ge.value:"",M),(te.value==="S"||X.value==="S")&&We(M)}).catch(async Ge=>{var ze,Pt,Lt;if(Ge==="cancel"&&q.value){const Ot=(Lt=(Pt=(ze=b.value.filter(Sn=>Sn.ticket.psgType!=="INF"))==null?void 0:ze[0])==null?void 0:Pt.ticket)==null?void 0:Lt.ticketNo,Bt={secondFactorCode:"CN",secondFactorValue:q.value},wn={title:`${k("app.agentTicketRefund.refund")}${Ot}`,name:`refund?type=ticketNo&sign=${Ot}`,content:sn(Tn)};p("addNewTab",wn,"",Bt),await ln(),p("removeTab",-1,`newRefund?type=ticketNo&sign=${Y.value}`)}})},Fe=async M=>{a.value=!0;const K=(await aa({ticketList:fe(),save:"NO"},M)).data.value;K!=null&&K.allSuccess&&Oe(M),!(K!=null&&K.allSuccess)&&!(K!=null&&K.allFail)&&Ye((K==null?void 0:K.refundResult)??[],M),qe((K==null?void 0:K.refundResult)??[])},We=async M=>{a.value=!0;let K;try{K=(await aa({ticketList:fe(),save:"NO"},M)).data.value}finally{a.value=!1}if(K!=null&&K.needSeatVacated){Ae(M);return}K!=null&&K.allSuccess&&Ne(M),!(K!=null&&K.allSuccess)&&!(K!=null&&K.allFail)&&Ye((K==null?void 0:K.refundResult)??[],M),qe((K==null?void 0:K.refundResult)??[])},Je=async M=>{if(M&&te.value==="S"&&b.value[0].ticket.tktType==="I"){await un(k("app.agentTicketRefund.pnrCancelSuccess",{pnrNo:M}));return}if(Ce()&&X.value==="S"&&b.value[0].ticket.tktType==="I"){await un(k("app.agentTicketRefund.cancelPassengerSuccess"));return}const K=X.value==="S"||te.value==="S";await ut({message:k(K?"app.agentTicketRefund.xeSuccess":"app.agentTicketRefund.xeFaild"),type:K?"success":"error",customClass:"z-3000"})},tt=M=>({fullName:M.passengerName,paxId:Number(M.index.replace("P","")),unMinor:!1,unMinorAge:0}),Xe=()=>{const M=[];return g.value.forEach(K=>{K.isChecked&&M.push(tt(K))}),M},Ke=async M=>{var _e;const K={orderId:"",count:0,passengerRecordLocator:ge.value,travellers:Xe(),isGrp:!1},ke=(await In(K,M)).data.value;q.value=((_e=ke==null?void 0:ke.splitedOrder)==null?void 0:_e.passengerRecordLocator)??"",ut({message:k("app.agentTicketRefund.splitSuccess"),type:"success"})},At=async(M,K,ke)=>{a.value=!0;try{const _e=(await _n(ee(M),ke)).data.value;te.value=(_e==null?void 0:_e.xePnrExecutionStatus)??"N",X.value=(_e==null?void 0:_e.deleteInfantExecutionStatus)??"N"}finally{a.value=!1}await Je(K)},Et=M=>{let K=[];return M.forEach(ke=>{var _e;K=K.concat(((_e=ke.infants)==null?void 0:_e.ticketNos)??[])}),K.join("、")},en=async()=>{let M=!1;if(!u.value&&!Ce()){const K=b.value[0].ticket.tktType==="I",ke=gt().length&&!K?k("app.agentTicketRefund.splitTipNonCarryInf",{ticketNo:Et(gt())}):k("app.agentTicketRefund.splitTip");await nt.confirm(Pe("div",{className:"whitespace-pre-line"},ke),{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:k("app.agentTicketRefund.sure"),cancelButtonText:k("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1}).catch(async _e=>{var Me,Ge,ze;if(M=!0,_e==="cancel"&&gt().length&&b.value[0].ticket.tktType!=="I"){const Pt=(ze=(Ge=(Me=b.value.filter(Bt=>Bt.ticket.psgType!=="INF"))==null?void 0:Me[0])==null?void 0:Ge.ticket)==null?void 0:ze.ticketNo,Lt={secondFactorCode:"CN",secondFactorValue:ge.value},Ot={title:`${k("app.agentTicketRefund.refund")}${Pt}`,name:`refund?type=ticketNo&sign=${Pt}`,content:sn(Tn)};p("addNewTab",Ot,"",Lt),await ln(),p("removeTab",-1,`newRefund?type=ticketNo&sign=${Y.value}`)}})}return M},gt=()=>g.value.filter(K=>!K.isChecked&&K.infants&&K.infants.isChecked),xn=M=>{var ke;return((ke=b.value.filter(_e=>_e.ticket.ticketNo===M)[0])==null?void 0:ke.printerNo)??""},Nn=()=>gt().map(K=>{var ke,_e;return{ticketNo:(ke=K.infants)==null?void 0:ke.ticketNos[0],domestic:b.value[0].ticket.tktType==="D",printNo:xn(((_e=K.infants)==null?void 0:_e.ticketNos[0])??"")}}),$n=async M=>{if(M){const K=k("app.agentTicketRefund.refundSeatDeleteTips",{pnrNo:ge.value});await nt.confirm(Pe("div",{className:"whitespace-pre-line"},K),{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:k("app.agentTicketRefund.xePassenger"),cancelButtonText:k("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})}},Rn=async M=>{if(!u.value&&!Ce()&&gt().length&&b.value[0].ticket.tktType!=="I"){let ke;try{a.value=!0,ke=(await zs({ticketList:Nn(),save:"NO"},M)).data.value}finally{a.value=!1}await $n((ke==null?void 0:ke.needSeatVacated)??!1),ke!=null&&ke.needSeatVacated&&await At("D","",M)}if(!await en())try{a.value=!0,!u.value&&!Ce()&&await Ke(M),await We(M)}finally{a.value=!1}},Cn=()=>{const M=g.value.filter(_e=>_e.isChecked&&_e.passengerType==="ADT"),K=g.value.filter(_e=>!_e.isChecked&&_e.passengerType==="INF");return(M??[]).some(_e=>K.some(Me=>{var Ge,ze;return Me.ticketNos.includes(((ze=(Ge=_e.infants)==null?void 0:Ge.ticketNos)==null?void 0:ze[0])??"")}))},H=async M=>{if(ge.value)Rn(M);else try{a.value=!0,await Fe(M)}finally{a.value=!1}},be=async M=>{if(Cn()){const K=k("app.agentTicketRefund.refundWithBabyTip");await nt.confirm(K,{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:k("app.agentTicketRefund.sure"),cancelButtonText:k("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})}M()},Ie=()=>b.value.some(K=>!((K==null?void 0:K.ticketManagementOrganizationCode)??"").includes("CDS")&&!K.printerNo?(K.printError=k("app.ticketStatus.deviceNumNull"),!0):!1),je=async()=>{if(Ie())return;const M=k("app.agentTicketRefund.autoRefundTip");await nt.confirm(M,{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:k("app.agentTicketRefund.sure"),cancelButtonText:k("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1});const K=De("091Q0105");be(()=>{H(K)})},mt=async()=>{h.value=!0},lt=async()=>{Ie()||be(mt)},Mt=M=>{V.isAlreadyClick=M,Tt()},_t=()=>{var M,K,ke,_e,Me,Ge,ze,Pt;return{tktNos:b.value.map(Lt=>{var Ot,Bt;return((Bt=(Ot=Lt.ticket.segment)==null?void 0:Ot[0])==null?void 0:Bt.tktTag)??""}),tktType:((ke=(K=(M=b.value)==null?void 0:M[0])==null?void 0:K.ticket)==null?void 0:ke.tktType)??"D",cityCode:((Pt=(ze=(Ge=(Me=(_e=b.value)==null?void 0:_e[0])==null?void 0:Me.ticket)==null?void 0:Ge.segment)==null?void 0:ze[0])==null?void 0:Pt.departureCode)??""}},Tt=async()=>{var K,ke;const M=De("091Q0103");if(b.value.length!==0){E.value={};try{a.value=!0,E.value=(ke=(K=await yo(_t(),M))==null?void 0:K.data)==null?void 0:ke.value}catch{E.value.status="FAILURE"}finally{V.isAlreadySuccessSearch=E.value.status==="SUCCESS",a.value=!1}}},Ia=M=>{var _e;const K=g.value[M].infants&&((_e=g.value[M].infants)==null?void 0:_e.isChecked)&&g.value[M].isChecked,ke=g.value[M].infants&&!g.value[M].isChecked;return K||ke},za=async M=>{var ke,_e,Me,Ge,ze,Pt,Lt,Ot,Bt;const K=De("091Q0101");if(Ia(M)){const wn=(((_e=(ke=g.value[M].infants)==null?void 0:ke.ticketNos)==null?void 0:_e[0])??"").replace("-",""),Sn=(((Me=g.value[M].ticketNos)==null?void 0:Me[0])??"").replace("-","");await Promise.all([ce(Sn,(Ge=g.value[M])==null?void 0:Ge.secondFactor,g.value[M].isChecked??!1,K),ce(wn??"",(Lt=(Pt=(ze=g.value)==null?void 0:ze[M])==null?void 0:Pt.infants)==null?void 0:Lt.secondFactor,((Ot=g.value[M].infants)==null?void 0:Ot.isChecked)??!1,K)])}else await ce(g.value[M].ticketNos[0].replace("-",""),(Bt=g.value[M])==null?void 0:Bt.secondFactor,g.value[M].isChecked??!1,K);V.isAlreadySuccessSearch&&Tt()},qa=(M,K)=>{D.value=K,$.value=!K,A.value=K,y.value=M.map(ke=>{const _e=ke.resultpre;return{..._e.amount,..._e.ticket}})},Ga=async()=>{const M=rt(b.value);b.value=[],le.value=[],C.value=!1;const K=De("091Q0101");M.forEach(async ke=>{var _e,Me;await ce(((Me=(_e=ke.ticket.segment)==null?void 0:_e[0])==null?void 0:Me.tktTag)??"",ke.ticket.secondFactor,!0,K)})};return bt(()=>h.value,M=>{T.setShowManualRefund(M)}),ct(async()=>{const M=De("091Q0101");ge.value=await ce(c.tktNo,c.factor,!0,M),[R.pnrNo,v.value]=[ge.value,!1],ge.value&&await Q(ge.value,M)}),{splitPnrNo:q,isDragonBoatOffice:N,pnrNo:ge,amountRef:l,fullscreenLoading:a,allPassengerList:g,ticketDetailList:b,manualDisabled:L,manualDialogVisible:h,packageData:he,clacAmountInfosRes:E,clacAmountInfo:V,partSuccess:$,isAutoRefundFinished:r,isXePnr:u,currentTktNo:Y,deviceNum:U,isFinishManualRefund:D,manualRefundAmountInfo:y,isCanRefund:we,queryTicketDetail:ce,manualRefund:lt,handleAutoRefund:je,getCalcInfo:Mt,queryCalcAmount:Tt,queryTicketAndCalcAmount:za,getManualRefundAmountDetail:qa,refresh:Ga,ticketTrfdNoDetails:le,isShowTrfdNo:C,getTicketTrfdNoDetailsFromManualRefund:Qe,refundType:d,isRelatedCorrectPnr:j,updatePnrForm:se,updatePnrFormData:R,updatePnrFormRules:Re,queryAllPassenger:Q,queryPnrTip:Te,showInterRefundSuccess:pe,refreshTicketDetail:ot,refundTicketSuccess:A,batchAutoAmount:x}},Gi=(c,p)=>{const{t:l}=et(),f=Dt(),T=O(),k=O(),a=I=>T.value=(I==null?void 0:I.$el)||I,v=O(!1),r=()=>{v.value=!0},g=()=>{v.value=!1},b=Be(!1),h=O(),E=27,y=Le(()=>c.reuseTicketInfo),D=Le(()=>u.currency==="CNY"),$=O(""),A=O([]),V=I=>{var F,Z;return(A.value??[]).some(Q=>I===Q.value)&&I?I:((Z=(F=A.value)==null?void 0:F[0])==null?void 0:Z.value)??""},u=yt({...c.refundData,ticketStatus:c.refundData.etTag==="1"}),te={BSP:{label:l("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:l("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:l("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:l("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:l("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:l("app.agentTicketQuery.OWNTicket"),value:"ARL"}},X=Le(()=>{var I;return(I=f.state.user)==null?void 0:I.entityType}),ge=Le(()=>!["CDS","GPCDS"].includes(u.ticketManagementOrganizationCode)),q=Le(()=>{var I;return((I=c.refundData)==null?void 0:I.tktType)==="I"}),Y=(I,ve,F)=>{var Q;const Z=Number(I.field.split(".")[1]);u.taxs[Z].name&&!ve?F(l("app.agentTicketRefund.taxAmount")):!u.taxs[Z].name&&!ve&&((Q=h.value)==null||Q.clearValidate(`taxs.${Z}.name`),F()),F()},L=(I,ve,F)=>{var Q;const Z=Number(I.field.split(".")[1]);u.taxs[Z].value&&!ve?F(l("app.agentTicketRefund.taxes")):!u.taxs[Z].value&&!ve&&((Q=h.value)==null||Q.clearValidate(`taxs.${Z}.value`),F()),F()},le=(I,ve,F)=>{u.payType==="TC"&&(ve?!u.isDragonBoatOffice&&!va.test(ve)?F(l("app.agentTicketRefund.creditCardInput")):u.isDragonBoatOffice&&!_a.test(ve)&&F(l("app.agentTicketRefund.dragonBoatOfficeInput")):F(l("app.agentTicketRefund.creditCardNotEmpty"))),F()},C=(I,ve,F)=>{u.remark&&!na.test(ve)&&F(l("app.agentTicketRefund.formatError")),F()},d=(I,ve,F)=>{var Z;ve?((Z=h.value)==null||Z.validateField("remarkCode"),u.remarkCode==="IC"?(Ss.test(`${u.remarkCode}${ve}`)||F(l("app.agentTicketRefund.remarkIC")),F()):u.remarkCode&&!Fn.test(`${u.remarkCode}${ve}`)&&F(l("app.agentTicketRefund.remarkHint")),F()):u.remarkCode&&F(l("app.agentTicketRefund.remarkHint")),F()},j=Le(()=>({currency:[{required:!0,message:l("app.agentTicketRefund.currencyNotEmpty"),trigger:"change"}],payType:[{required:!0,message:l("app.agentTicketRefund.paymentSel"),trigger:"change"},{pattern:ga,message:l("app.agentTicketRefund.paymentInput"),trigger:"change"}],totalAmount:[{pattern:D.value?$t:Rt,message:D.value?l("app.agentTicketRefund.cnyTip"):l("app.agentTicketRefund.correctPrice"),trigger:"change"}],taxValue:[{pattern:D.value?ka:ya,message:D.value?l("app.agentTicketRefund.cnyTip"):l("app.agentTicketRefund.correctPrice"),trigger:"change"},{validator:Y,trigger:"change"}],taxName:[{pattern:ha,message:l("app.agentTicketRefund.taxes"),trigger:"change"},{validator:L,trigger:"change"}],commisionRate:[{pattern:D.value?$t:Rt,message:D.value?l("app.agentTicketRefund.cnyTip"):l("app.agentTicketRefund.correctRate"),trigger:"change"}],otherDeductionRate:[{pattern:D.value?$t:Rt,message:D.value?l("app.agentTicketRefund.cnyTip"):l("app.agentTicketRefund.correctRate"),trigger:"change"}],commision:[{pattern:D.value?$t:Rt,message:D.value?l("app.agentTicketRefund.cnyTip"):l("app.agentTicketRefund.correctPrice"),trigger:"change"}],psdName:[{validator:Da,trigger:"change"}],creditCard:[{validator:le,trigger:"change"}],remarkCode:[{pattern:na,message:l("app.agentTicketRefund.formatError"),trigger:["change","blur"]},{validator:C,trigger:["change","blur"]}],remark:[{validator:d,trigger:["change","blur"]}],remarkInfo:[{pattern:Fn,message:l("app.agentTicketRefund.remarkHint"),trigger:["change","blur"]}],netRefund:[{required:!0,message:l("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:D.value?$t:Rt,message:D.value?l("app.agentTicketRefund.cnyTip"):l("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]}],ticketManagementOrganizationCode:[{required:!0,message:l("app.agentTicketRefund.prntNoNotEmpty"),trigger:["change","blur"]}]})),Te=()=>{if(u.taxs.length!==E)if(u.taxs.length>22&&u.taxs.length<E){const I=new Array(E-u.taxs.length).fill({name:"",value:""});u.taxs=u.taxs.concat(I).map(ve=>({...ve}))}else{const I=new Array(5).fill({name:"",value:""});u.taxs=u.taxs.concat(I).map(ve=>({...ve}))}},se=async()=>{const I=await B();p("validateChange",c.ticketIndex,I)},x=()=>{const{totalAmount:I,totalTaxs:ve,otherDeduction:F,commision:Z,commisionRate:Q}=u;if(me()){se();return}if(Z){const ee=new Ze(kt(Number(I),Number(ve),"+")).minus(new Ze(kt(Number(F),Number(Z),"+"))).toString();u.netRefund=ee??""}else{const ee=new Ze(new Ze(Number(I)).times(Number(Q))).dividedBy(100).toString(),fe=Number(ee).toString(),Ce=new Ze(kt(Number(I),Number(ve),"+")).minus(new Ze(kt(Number(F),Number(fe),"+"))).toString();u.netRefund=Ce??""}u.commision!==""&&(u.commision=u.commision??"")},R=()=>{u.commisionRate||(u.commision="")},pe=()=>{let I=new Ze(0);u.taxs.forEach((ve,F)=>{var Z;(Z=h.value)==null||Z.validateField(`taxs.${F}.value`).then(Q=>{Q&&(I=I.add(new Ze(ve.value?ve.value:0)),u.totalTaxs=I.toString(),x())})})},Re=async()=>{const I=[];u.taxs.forEach((ve,F)=>{var Z,Q;I.push((Z=h.value)==null?void 0:Z.validateField(`taxs.${F}.name`)),I.push((Q=h.value)==null?void 0:Q.validateField(`taxs.${F}.value`))}),await Promise.all(I),u.taxs.forEach((ve,F)=>{u.taxs[F].value&&(u.taxs[F].value=u.taxs[F].value??0)}),pe(),se()},we=I=>I&&(D.value?!$t.test(I):!Rt.test(I)),me=()=>{const{totalAmount:I,otherDeductionRate:ve,otherDeduction:F,commision:Z,commisionRate:Q}=u;return we(I??"")||we(ve??"")||we(F??"")||we(Z??"")||we(Q??"")},he=async I=>{if(me()){se();return}if(I==="otherDeductionRate"&&u.otherDeductionRate){u.otherDeductionRate=u.otherDeductionRate??"";const ve=new Ze(new Ze(Number(u.totalAmount)).times(Number(u.otherDeductionRate))).dividedBy(100).toString();u.otherDeduction=ve??""}I==="commisionRate"&&u.commisionRate&&(u.commision=new Ze(new Ze(Number(u.totalAmount)).times(Number(u.commisionRate))).dividedBy(100).toString()),x(),se()},ue=()=>{h.value.clearValidate("creditCard"),u.payType.toUpperCase()==="TC"&&(u.creditCard=u.isDragonBoatOffice?$o:"")},U=I=>{I.target.value!==""&&!Pa.some(ve=>ve.label===I.target.value)&&(u.payType=I.target.value),se()},J=()=>([...u.segment.values()].flatMap(F=>[...F]).filter(F=>F.isAllowCheck)??[]).some(F=>F.isChecked),ne=()=>{const ve=([...u.segment.values()].flatMap(F=>[...F]).filter(F=>F.isAllowCheck)??[]).every(F=>F.isChecked);p("watchCheckedSegment",c.ticketIndex,ve),se()},B=async()=>{var I;try{return await((I=h.value)==null?void 0:I.validate())&&J()}catch{return!1}},P=()=>u,N=I=>I.length<10?I.concat(new Array(10-I.length).fill({name:"",value:""})).map(ve=>({...ve})):I,_=async()=>{var I,ve;try{const F=De("091Q0203");b.value=!0;const Z=u.ticketNo.indexOf("-"),Q=Z!==-1?u.ticketNo.substring(0,Z):u.ticketNo,ee=(ve=(I=await Sa({ticketNo:Q},F))==null?void 0:I.data)==null?void 0:ve.value;if(!(ee!=null&&ee.rtKTTaxes)||(ee==null?void 0:ee.rtKTTaxes.length)===0)return;u.taxs=[],((ee==null?void 0:ee.rtKTTaxes)??[]).forEach(fe=>{u.taxs.push({name:fe.taxType,value:fe.taxAmount})}),u.taxs=N(u.taxs),pe()}finally{b.value=!1}},w=I=>{p("reuseTicket",I)},ce=I=>{const ve=I.indexOf("("),F=I.indexOf("*");if(ve>-1&&F>-1){const Z=Math.min(ve,F);return(I==null?void 0:I.substring(0,Z))??""}else{if(ve===-1&&F>-1)return(I==null?void 0:I.substring(0,F))??"";if(ve>-1&&F===-1)return(I==null?void 0:I.substring(0,ve))??""}return I.trim()};da(()=>{[...u.segment.values()].forEach(I=>{let ve="";const F=[];I.forEach(Z=>{ve=Z.tktTag,F.push({...Z,isAllowCheck:Z.isAble==="1",isChecked:Z.isAble==="1"})}),u.segment.set(ve,F)})});const ie=I=>I.map(ve=>({name:ve.name,value:ve.value}));bt([()=>c.reuseTimes,()=>y.value],async()=>{if(!y.value||c.currentTicket!==u.ticketNo)return;const I=rt(y.value);u.commision=(I==null?void 0:I.commision)??"",u.taxs=ie(I==null?void 0:I.taxs),u.otherDeduction=(I==null?void 0:I.otherDeduction)??"",u.totalAmount=(I==null?void 0:I.totalAmount)??"",u.totalTaxs=(I==null?void 0:I.totalTaxs)??"",u.currency=(I==null?void 0:I.currency)??"",u.payType=(I==null?void 0:I.payType)??"",u.etTag=(I==null?void 0:I.etTag)??"",u.remarkInfo=(I==null?void 0:I.remarkInfo)??"",u.creditCard=(I==null?void 0:I.creditCard)??"",u.otherDeductionRate=(I==null?void 0:I.otherDeductionRate)??"",u.commisionRate=(I==null?void 0:I.commisionRate)??"",pe();const ve=await B();p("validateChange",c.ticketIndex,ve)});const oe=()=>{u.commision=u.commision??"",u.taxs=ie(u.taxs),u.otherDeduction=(u==null?void 0:u.otherDeduction)??"",u.totalAmount=(u==null?void 0:u.totalAmount)??"",u.totalTaxs=(u==null?void 0:u.totalTaxs)??"",u.otherDeductionRate=(u==null?void 0:u.otherDeductionRate)??"",u.commisionRate=(u==null?void 0:u.commisionRate)??""},Se=()=>{var I,ve,F,Z,Q,ee,fe,Ce,Oe,ye;((I=X.value)!=null&&I.includes("$$$")||(ve=X.value)!=null&&ve.includes("BSP"))&&(A.value.push(te.BSP),A.value.push(te.GPBSP)),!((F=X.value)!=null&&F.includes("BSP"))&&((Z=X.value)!=null&&Z.includes("GP"))&&A.value.push(te.GPBSP),((Q=X.value)!=null&&Q.includes("$$$")||(ee=X.value)!=null&&ee.includes("BOP"))&&A.value.push(te.BOPBSP),((fe=X.value)!=null&&fe.includes("$$$")||(Ce=X.value)!=null&&Ce.includes("CDS"))&&(A.value.push(te.CDS),A.value.push(te.GPCDS)),((Oe=X.value)!=null&&Oe.includes("$$$")||(ye=X.value)!=null&&ye.includes("本票"))&&A.value.push(te.ARL),u.ticketManagementOrganizationCode=V(u.ticketManagementOrganizationCode)},de=()=>{q.value&&(u.ticketStatus=u.etTag==="1")};return ct(()=>{oe(),x(),u.name=ce(u.name),Se(),se(),ne()}),{refundFormRef:h,refundFormData:u,fullscreenLoading:b,FORM_RULES:j,MAX_TAX_NUM:E,reuseValue:$,addTax:Te,checkTax:Re,calcAmount:he,changePayType:ue,bindPaymentValue:U,deliverValid:se,getEditRefundData:P,getTaxAll:_,changeReuseTicket:w,commisionRateChange:R,isInternational:q,ticketOrganizationList:A,isShowPrintNo:ge,realRef:T,setRealRef:a,popoverRef:k,showTicketOriginalContainer:v,show:r,hide:g,changeET:de,checkSegment:ne}},Gt=c=>(Ut("data-v-f42a3975"),c=c(),jt(),c),Hi={class:"refund-form"},Ki={class:"relative"},Yi={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},Wi={key:0,class:"text-gray-3 text-xs absolute top-0 right-[0]"},Xi={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},Ji={class:"self-stretch justify-start items-start gap-5 inline-flex"},Zi={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},el={class:"w-[84px] text-gray-3 text-xs shrink-0"},tl=Gt(()=>t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1)),nl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},al={class:"w-[84px] text-gray-3 text-xs shrink-0"},sl=Gt(()=>t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1)),ol={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] ticketManagementOrganizationCode"},il={key:1,class:"inline-block w-[12px]"},ll={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},rl={class:"w-[84px] text-gray-3 text-xs shrink-0"},cl={key:0,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},ul={key:1,class:"justify-start items-start flex text-gray-2 text-xs font-bold"},dl={class:"self-stretch justify-start items-start gap-5 inline-flex"},pl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},fl={class:"w-[84px] text-gray-3 text-xs shrink-0"},ml={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},gl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},kl={class:"w-[84px] text-gray-3 text-xs shrink-0"},yl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},hl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},vl={class:"w-[84px] text-gray-3 text-xs shrink-0"},_l={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Tl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},bl={class:"w-[84px] text-gray-3 text-xs shrink-0"},xl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Nl={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},$l={class:"self-stretch justify-start items-start gap-5 inline-flex"},Rl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Cl={class:"w-[84px] text-gray-3 text-xs shrink-0"},wl={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Sl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Pl={class:"w-[84px] text-gray-3 text-xs shrink-0"},Dl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Al={class:"text-gray-3 text-xs shrink-0 w-[84px]"},El={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},Ol=Gt(()=>t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),Fl={class:"self-stretch justify-start items-start gap-5 inline-flex"},Vl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] passenger-name"},Ml={class:"grow shrink basis-0 min-h-[32px] justify-start gap-1 flex mb-[10px]"},Ll={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0"},Bl=Gt(()=>t("span",{class:"text-red-1 ml-1"},"*",-1)),Ql={class:"justify-start items-start text-gray-2 text-xs font-bold"},Ul={class:"justify-start gap-4 flex h-[20px]"},jl={class:"text-gray-2 text-xs leading-tight"},Il={class:"flex items-center h-[20px] text-gray-3 font-normal text-xs leading-tight"},zl={class:"justify-start gap-4 flex h-[20px]"},ql={class:"text-gray-2 text-xs leading-tight"},Gl={class:"justify-start items-start gap-5 inline-flex"},Hl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require amount"},Kl={key:0,class:"not-required-tip"},Yl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require pay-type"},Wl={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require refund-type"},Xl={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},Jl={class:"self-stretch justify-start items-start gap-5 inline-flex"},Zl={class:"shrink h-[32px] w-[120px] justify-start items-center gap-1 flex mb-[10px]"},er={key:0,class:"ml-[122px] shrink h-[32px] justify-start items-center gap-1 flex mb-[10px]"},tr={class:"text-gray-2 text-xs font-bold leading-tight"},nr={class:"w-full mb-[10px]"},ar={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},sr={class:"ml-[20px]"},or={class:"text-gray-2 font-[700]"},ir={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},lr={class:"w-[20px] text-gray-3 text-xs shrink-0 leading-8"},rr={class:"w-[40px] mr-[6px] shrink-0"},cr={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},ur={class:"self-stretch justify-start items-start gap-5 inline-flex"},dr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},pr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},fr=Gt(()=>t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),mr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},gr=Gt(()=>t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1)),kr={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},yr={class:"self-stretch justify-start items-start gap-5 inline-flex"},hr={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},vr={class:"w-[72px] text-gray-3 text-xs shrink-0"},_r=Gt(()=>t("span",{class:"iconfont icon-info-circle-line absolute ml-[4px]"},null,-1)),Tr={class:"justify-start items-center flex text-gray-2 text-xs relative"},br={class:"grow shrink basis-0 min-h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},xr={class:"text-gray-2 font-[700]"},Nr=Gt(()=>t("span",null,[t("i",{class:"iconfont icon-info-circle-line text-[20px] font-normal ml-[10px] text-gray-4"})],-1)),$r={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Rr=Gt(()=>t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1)),Cr=He({__name:"ManualRefundForm",props:{refundData:{},ticketIndex:{},ticketList:{},reuseTicketInfo:{},reuseTimes:{},currentTicket:{}},emits:["validateChange","watchCheckedSegment","reuseTicket"],setup(c,{expose:p,emit:l}){const f=c,T=l,{refundFormRef:k,refundFormData:a,fullscreenLoading:v,FORM_RULES:r,MAX_TAX_NUM:g,reuseValue:b,addTax:h,checkTax:E,calcAmount:y,changePayType:D,bindPaymentValue:$,deliverValid:A,getEditRefundData:V,getTaxAll:u,changeReuseTicket:te,commisionRateChange:X,ticketOrganizationList:ge,isShowPrintNo:q,isInternational:Y,changeET:L,checkSegment:le}=Gi(f,T);return p({getEditRefundData:V}),(C,d)=>{const j=Jt,Te=Zt,se=at,x=dt,R=xt,pe=Ht,Re=gn,we=st,me=Nt,he=pt,ue=wt,U=ht("trimUpper");return s(),m("div",Hi,[t("div",Ki,[$e((s(),m("div",Yi,[z(n(C.$t("app.agentTicketRefund.refundInformationForm")),1)])),[[ue,e(v),void 0,{fullscreen:!0,lock:!0}]]),C.ticketList.length>1?(s(),m("div",Wi,[z(n(C.$t("app.agentTicketRefund.reuse"))+" ",1),o(Te,{modelValue:e(b),"onUpdate:modelValue":d[0]||(d[0]=J=>Ue(b)?b.value=J:null),class:"reuse w-[174px] mx-[6px]",onChange:e(te)},{default:i(()=>[(s(),re(j,{key:0,label:"",value:""})),(s(!0),m(xe,null,Ee(C.ticketList.filter(J=>{var ne;return J!==((ne=e(a))==null?void 0:ne.ticketNo)}),(J,ne)=>(s(),re(j,{key:ne+1,label:J,value:J},null,8,["label","value"]))),128))]),_:1},8,["modelValue","onChange"]),z(" "+n(C.$t("app.agentTicketRefund.refundInfo")),1)])):W("",!0)]),o(he,{ref_key:"refundFormRef",ref:k,model:e(a),"require-asterisk-position":"right"},{default:i(()=>{var J,ne,B,P,N,_,w,ce,ie,oe,Se;return[t("div",Xi,[t("div",Ji,[t("div",Zi,[t("div",el,n(C.$t("app.agentTicketRefund.refundTicketNumber")),1),tl]),t("div",nl,[t("div",al,n(C.$t("app.agentTicketRefund.rtType")),1),sl]),t("div",ol,[o(x,{label:C.$t("app.refundForm.ticketManagementOrganizationCode"),prop:"ticketManagementOrganizationCode",rules:e(r).ticketManagementOrganizationCode},{default:i(()=>[o(Te,{modelValue:e(a).ticketManagementOrganizationCode,"onUpdate:modelValue":d[1]||(d[1]=de=>e(a).ticketManagementOrganizationCode=de),disabled:!e(a).ticketManagementOrganizationCode,placeholder:e(a).ticketManagementOrganizationCode?"":C.$t("app.agentTicketQuery.noData")},{default:i(()=>[(s(!0),m(xe,null,Ee(e(ge),de=>(s(),re(j,{key:de.value,label:de.label,value:de.value},{default:i(()=>[t("span",null,[e(a).ticketManagementOrganizationCode===de.value?(s(),re(se,{key:0,size:12,class:"iconfont icon-right-line"})):(s(),m("span",il))]),z(" "+n(de.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder"])]),_:1},8,["label","rules"])]),t("div",ll,[t("div",rl,n(C.$t("app.agentTicketRefund.prntNo")),1),e(q)?(s(),m("div",cl,n(e(a).ticketManagementOrganizationCode==="CDS"?"-":((J=e(a))==null?void 0:J.printNo)??"-"),1)):(s(),m("div",ul,"-"))])]),t("div",dl,[t("div",pl,[t("div",fl,n(C.$t("app.agentTicketRefund.refundAgent")),1),t("div",ml,n(((ne=e(a))==null?void 0:ne.agent)??"-"),1)]),t("div",gl,[t("div",kl,n(C.$t("app.agentTicketRefund.refundIataNo")),1),t("div",yl,n(((B=e(a))==null?void 0:B.iata)??"-"),1)]),t("div",hl,[t("div",vl,n(C.$t("app.agentTicketRefund.refundOffice")),1),t("div",_l,n(((P=e(a))==null?void 0:P.office)??"-"),1)]),t("div",Tl,[t("div",bl,n(C.$t("app.agentTicketRefund.refundDate")),1),t("div",xl,n(((N=e(a))==null?void 0:N.refundDate)??"-"),1)])])]),t("div",Nl,[t("div",$l,[t("div",Rl,[t("div",Cl,n(C.$t("app.agentTicketRefund.refundAirlineSettlementCode")),1),t("div",wl,n(((_=e(a))==null?void 0:_.airline)??"-"),1)]),t("div",Sl,[t("div",Pl,n(C.$t("app.agentTicketRefund.refundTicketNo")),1),o(rn,{"tkt-index":((w=e(a))==null?void 0:w.ticketNo)??"-","ticket-number":((ce=e(a))==null?void 0:ce.ticketNo)??"-","second-factor":(ie=e(a))==null?void 0:ie.secondFactor},null,8,["tkt-index","ticket-number","second-factor"])]),t("div",Dl,[t("div",Al,n(C.$t("app.agentTicketRefund.numberOfCombinedTickets")),1),t("div",El,n(e(a).tktType==="I"?(oe=e(a))==null?void 0:oe.conjunction:1),1)]),Ol]),t("div",Fl,[t("div",Vl,[o(x,{label:C.$t("app.agentTicketRefund.passName"),prop:"name",rules:e(r).psdName},{default:i(()=>[$e(o(R,{modelValue:e(a).name,"onUpdate:modelValue":d[2]||(d[2]=de=>e(a).name=de),clearable:"",onInput:d[3]||(d[3]=de=>e(a).name=e(a).name.toUpperCase()),onBlur:e(A)},null,8,["modelValue","onBlur"]),[[U]])]),_:1},8,["label","rules"])]),t("div",Ml,[t("div",Ll,[z(n(C.$t("app.agentTicketRefund.refundSeg")),1),Bl]),t("div",Ql,[e(a).tktType==="D"?(s(!0),m(xe,{key:0},Ee([...e(a).segment.values()][0],(de,I)=>(s(),m("div",{key:I,class:"inline-block leading-[32px] dom-tikcet"},[o(x,{prop:`segment[${I}]`},{default:i(()=>[t("div",Ul,[o(pe,{modelValue:de.isChecked,"onUpdate:modelValue":ve=>de.isChecked=ve,disabled:!de.isAllowCheck,label:de,onChange:e(le)},{default:i(()=>[t("div",jl,n((de==null?void 0:de.departureCode)??"")+"-"+n((de==null?void 0:de.arriveCode)??""),1)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","label","onChange"]),t("div",{class:Ve([de.isChecked?"text-brand-2":"text-gray-5","text-[14px] justify-center items-center flex relative right-[12px] h-[16px] w-[16px] top-[5px]"])},n(C.$t(`app.queryRefunds.number_${I+1}`)),3)])]),_:2},1032,["prop"])]))),128)):(s(!0),m(xe,{key:1},Ee([...e(a).segment.values()],(de,I)=>(s(),m("div",{key:I,class:Ve({"mb-[10px]":I<[...e(a).segment.values()].length-1})},[t("div",Il,n(C.$t(`app.agentTicketRefund.couponNo_${I+1}`)),1),o(x,{prop:`segment[${I}]`},{default:i(()=>[t("div",zl,[(s(!0),m(xe,null,Ee(de,(ve,F)=>(s(),m("div",{key:F+"segmet",class:"justify-start items-center gap-[2px] flex h-[20px]"},[o(pe,{modelValue:ve.isChecked,"onUpdate:modelValue":Z=>ve.isChecked=Z,disabled:!ve.isAllowCheck,label:ve,onChange:e(le)},{default:i(()=>[t("div",ql,n(ve.departureCode)+"-"+n(ve.arriveCode),1)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","label","onChange"]),t("div",{class:Ve([ve.isChecked?"text-brand-2":"text-gray-5","text-[14px] justify-center items-center flex relative top-[-1px] h-[20px]"])},n(C.$t(`app.queryRefunds.number_${F+1}`)),3)]))),128))])]),_:2},1032,["prop"])],2))),128))])])]),t("div",Gl,[t("div",Hl,[o(x,{label:C.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",rules:e(r).totalAmount,class:Ve({"not-required-container":!e(a).totalAmount})},{default:i(()=>[$e(o(R,{modelValue:e(a).totalAmount,"onUpdate:modelValue":d[4]||(d[4]=de=>e(a).totalAmount=de),clearable:"",onBlur:d[5]||(d[5]=de=>e(y)("totalAmount"))},null,8,["modelValue"]),[[U]]),e(a).totalAmount?W("",!0):(s(),m("div",Kl,n(C.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),t("div",Yl,[o(x,{label:C.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:e(r).payType},{default:i(()=>[o(Te,{modelValue:e(a).payType,"onUpdate:modelValue":d[6]||(d[6]=de=>e(a).payType=de),modelModifiers:{trim:!0},filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:C.$t("app.agentTicketRefund.paymentSel"),clearable:"",onChange:e(D),onBlur:e($)},{default:i(()=>[(s(!0),m(xe,null,Ee(e(Pa),(de,I)=>(s(),re(j,{key:I,label:de.label,value:de.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onBlur"])]),_:1},8,["label","rules"])]),t("div",Wl,[o(x,{label:C.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:e(r).currency},{default:i(()=>[$e(o(R,{modelValue:e(a).currency,"onUpdate:modelValue":d[7]||(d[7]=de=>e(a).currency=de),clearable:"",onInput:d[8]||(d[8]=de=>e(a).currency=e(a).currency.toUpperCase()),onBlur:e(A)},null,8,["modelValue","onBlur"]),[[U]])]),_:1},8,["label","rules"])])])]),t("div",Xl,[t("div",Jl,[t("div",Zl,[o(x,{label:C.$t("app.agentTicketRefund.etTag")},{default:i(()=>[o(Re,{modelValue:e(a).etTag,"onUpdate:modelValue":d[9]||(d[9]=de=>e(a).etTag=de),"inline-prompt":"","active-text":"Y","inactive-text":"N","active-value":"1","inactive-value":"0",onChange:e(L)},null,8,["modelValue","onChange"])]),_:1},8,["label"])]),e(Y)&&e(a).airline!=="784"?(s(),m("div",er,[o(x,null,{default:i(()=>[o(pe,{modelValue:e(a).ticketStatus,"onUpdate:modelValue":d[10]||(d[10]=de=>e(a).ticketStatus=de)},{default:i(()=>[t("span",tr,n(C.$t("app.agentTicketRefund.modifyTicketStatus")),1)]),_:1},8,["modelValue"])]),_:1})])):W("",!0)]),t("div",nr,[t("div",ar,[t("div",null,[t("span",null,n(C.$t("app.agentTicketRefund.refundTax")),1),t("span",sr,n(C.$t("app.fare.singleFare.totalTax")),1),t("span",or," "+n(e(a).currency)+" "+n(e(a).totalTaxs),1)]),t("div",null,[o(we,{link:"",type:"primary","data-gid":"091Q0203",size:"small",onClick:e(u)},{default:i(()=>[z(n(C.$t("app.agentTicketRefund.rtktTax")),1)]),_:1},8,["onClick"]),o(we,{link:"",type:"primary",size:"small",disabled:((Se=e(a).taxs)==null?void 0:Se.length)===e(g),onClick:e(h)},{default:i(()=>[z(n(C.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"])])]),t("div",ir,[(s(!0),m(xe,null,Ee(e(a).taxs,(de,I)=>(s(),m("div",{key:I,class:"grow shrink-0 basis-0 h-[32px] justify-start flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[t("div",lr,n(I+1),1),t("div",rr,[o(x,{prop:"taxs."+I+".name",rules:e(r).taxName},{default:i(()=>[$e(o(R,{modelValue:de.name,"onUpdate:modelValue":ve=>de.name=ve,onInput:ve=>de.name=de.name.toUpperCase(),onBlur:e(E)},null,8,["modelValue","onUpdate:modelValue","onInput","onBlur"]),[[U]])]),_:2},1032,["prop","rules"])]),o(x,{prop:"taxs."+I+".value",rules:e(r).taxValue},{default:i(()=>[$e(o(R,{modelValue:de.value,"onUpdate:modelValue":ve=>de.value=ve,onBlur:e(E)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),[[U]])]),_:2},1032,["prop","rules"])]))),128))])])]),t("div",cr,[t("div",ur,[t("div",dr,[o(x,{label:C.$t("app.agentTicketRefund.commision"),prop:"commision",rules:e(r).commision},{default:i(()=>[$e(o(R,{modelValue:e(a).commision,"onUpdate:modelValue":d[11]||(d[11]=de=>e(a).commision=de),clearable:"",placeholder:"0.00",onBlur:d[12]||(d[12]=de=>e(y)("commision"))},null,8,["modelValue"]),[[U]])]),_:1},8,["label","rules"])]),t("div",pr,[o(x,{label:C.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:e(r).commisionRate},{default:i(()=>[$e(o(R,{modelValue:e(a).commisionRate,"onUpdate:modelValue":d[13]||(d[13]=de=>e(a).commisionRate=de),clearable:"",placeholder:"0.00",onBlur:d[14]||(d[14]=de=>e(y)("commisionRate")),onInput:e(X)},null,8,["modelValue","onInput"]),[[U]]),fr]),_:1},8,["label","rules"])]),t("div",mr,[o(x,{label:C.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",rules:e(r).otherDeductionRate},{default:i(()=>[$e(o(R,{modelValue:e(a).otherDeductionRate,"onUpdate:modelValue":d[15]||(d[15]=de=>e(a).otherDeductionRate=de),placeholder:"1-100",onBlur:d[16]||(d[16]=de=>e(y)("otherDeductionRate"))},null,8,["modelValue"]),[[U]]),gr]),_:1},8,["label","rules"])]),t("div",kr,[o(x,{label:C.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",rules:e(r).totalAmount},{default:i(()=>[$e(o(R,{modelValue:e(a).otherDeduction,"onUpdate:modelValue":d[17]||(d[17]=de=>e(a).otherDeduction=de),clearable:"",placeholder:"0.00",onBlur:d[18]||(d[18]=de=>e(y)("otherDeduction"))},null,8,["modelValue"]),[[U]])]),_:1},8,["label","rules"])])]),t("div",yr,[t("div",hr,[t("div",vr,[z(n(C.$t("app.agentTicketRefund.remark"))+" ",1),o(me,{placemant:"top",content:C.$t("app.agentTicketRefund.remarkTips")},{default:i(()=>[_r]),_:1},8,["content"])]),t("div",Tr,[o(x,{prop:"remarkInfo",rules:e(r).remarkInfo},{default:i(()=>[$e(o(R,{modelValue:e(a).remarkInfo,"onUpdate:modelValue":d[19]||(d[19]=de=>e(a).remarkInfo=de),clearable:"",placeholder:C.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:d[20]||(d[20]=de=>e(a).remarkInfo=e(a).remarkInfo.toUpperCase()),onBlur:e(A)},null,8,["modelValue","placeholder","onBlur"]),[[U]])]),_:1},8,["rules"])])]),t("div",br,[o(x,{label:C.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund"},{default:i(()=>[t("span",xr,n(e(a).currency)+" "+n(e(a).netRefund),1),o(me,{placement:"top",effect:"dark"},{content:i(()=>[z(n(C.$t("app.agentTicketRefund.netRefundTip")),1)]),default:i(()=>[Nr]),_:1})]),_:1},8,["label"])]),t("div",$r,[o(x,{label:C.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:e(r).creditCard},{default:i(()=>[$e(o(R,{modelValue:e(a).creditCard,"onUpdate:modelValue":d[21]||(d[21]=de=>e(a).creditCard=de),clearable:"",onInput:d[22]||(d[22]=de=>e(a).creditCard=e(a).creditCard.toUpperCase()),onBlur:e(A)},null,8,["modelValue","onBlur"]),[[U]])]),_:1},8,["label","rules"])]),Rr])])]}),_:1},8,["model"])])}}});const wr=vt(Cr,[["__scopeId","data-v-f42a3975"]]),Sr=(c,p)=>{const l=O(0),f=O([]);return{currentTicketIndex:l,formRefs:f,validateChange:(r,g)=>{p("validateChange",c.passengerIndex,r,g)},watchCheckedSegment:(r,g)=>{p("watchCheckedSegment",c.passengerIndex,r,g)},getEditRefundDatas:()=>(f.value??[]).map(g=>g.getEditRefundData()),reuseTicket:r=>{p("reuseTicket",r)}}},Pr={class:"border px-[8px] py-[4px] rounded-[1px] cursor-pointer ticket-number"},Dr=He({__name:"RefundItem",props:{ticketInfos:{},passengerIndex:{},ticketList:{},reuseTicketInfo:{},reuseTimes:{},activeName:{},currentTicket:{}},emits:["validateChange","watchCheckedSegment","reuseTicket"],setup(c,{expose:p,emit:l}){const f=c,T=l,{currentTicketIndex:k,formRefs:a,getEditRefundDatas:v,validateChange:r,watchCheckedSegment:g,reuseTicket:b}=Sr(f,T);return p({getEditRefundDatas:v,currentTicketIndex:k}),(h,E)=>{const y=at,D=Ea,$=Oa;return s(),re($,{modelValue:e(k),"onUpdate:modelValue":E[0]||(E[0]=A=>Ue(k)?k.value=A:null),class:"ticket-container"},{default:i(()=>[(s(!0),m(xe,null,Ee(h.ticketInfos,(A,V)=>(s(),re(D,{key:A.ticketNo,label:A.ticketNo,name:V},{label:i(()=>[t("div",Pr,[z(n(e(Qt)(A.ticketNo))+" ",1),$e(o(y,{class:"pass-icon"},{default:i(()=>[o(e(En))]),_:2},1536),[[hn,A.validate]])])]),default:i(()=>[t("div",null,[(s(),re(wr,{ref_for:!0,ref:u=>{u&&(e(a)[V]=u)},key:V,"refund-data":A.refundData,"ticket-index":V,"ticket-list":h.ticketList,"reuse-ticket-info":h.reuseTicketInfo,"reuse-times":h.reuseTimes,"current-ticket":h.currentTicket,onValidateChange:e(r),onWatchCheckedSegment:e(g),onReuseTicket:e(b)},null,8,["refund-data","ticket-index","ticket-list","reuse-ticket-info","reuse-times","current-ticket","onValidateChange","onWatchCheckedSegment","onReuseTicket"]))])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])}}});const Ar=vt(Dr,[["__scopeId","data-v-37870136"]]),Er=Le(()=>{const c=Dt().getters.userPreferences.backfieldEnName;return c===null||c}),Or=c=>Er.value&&!Ps.test(c)?c:"",Fr=c=>{let p=[];return p=c.map(l=>({name:l.name,value:l.value})),p.length<10?p.concat(new Array(10-p.length).fill({name:"",value:""})).map(l=>({...l})):p},ra=(c,p,l,f,T)=>{var b,h,E;const k=it().format("DDMMMYY/HHmm").toUpperCase();let a="";if((b=c.ticket)!=null&&b.payType){const y=c.ticket.payType.toUpperCase();y.startsWith("CASH")?a="CASH":(y.startsWith("CC")||y.startsWith("TC"))&&(a="TC")}const v=(y,D,$)=>y&&y!=="0.00"?y:y==="0.00"&&D!=="0.00"?new Ze(new Ze(Number($)).times(Number(D))).dividedBy(100).toString():y==="0.00"?"0":y??"",r=(y,D)=>D&&D!=="0.00"?D:"";return{iata:c.iata,agent:c.agent,office:c.office,volunteer:"VOLUNTEER_MANUAL",createUser:c.operator,printNo:c.printerNo,marketAirline:c.ticket.marketAirline,currency:c.ticket.currency,name:Or(c.ticket.passengerNameSuffix??""),psgType:c.ticket.psgType,etTag:(h=c.ticket)==null?void 0:h.etTag,remark:"",remarkCode:"",remarkInfo:"",creditCard:"",conjunction:c.conjunction,airline:c.ticket.airline,tktType:c.ticket.tktType,payType:a,secondFactor:c.ticket.secondFactor,ticketNo:f,totalAmount:Number(c.conjunction)>1&&c.ticket.tktType==="D"?"":c.ticket.totalAmount,commision:v(c.ticket.commission,c.ticket.commissionRate,c.ticket.totalAmount),commisionRate:r(c.ticket.commission,c.ticket.commissionRate),otherDeduction:"0",otherDeductionRate:"",netRefund:(E=kt(Number(c.ticket.totalAmount),Number(c.ticket.totalTaxs??"0"),"+"))==null?void 0:E.toString(),totalTaxs:c.ticket.totalTaxs??"0",taxs:Fr(c.ticket.taxs??[]),rate:"0",receiptPrinted:c.receiptPrinted,segment:l,crsPnrNo:c.ticket.crsPnrNo,pnr:c.ticket.pnr,isCoupon:c.ticket.isCoupon,isDragonBoatOffice:p,refundDate:k,conjunctionTicketNos:T,ticketManagementOrganizationCode:c.ticketManagementOrganizationCode??""}},Vr=c=>[...c.segment.values()].filter(l=>l.some(f=>f.isAllowCheck)).length.toString(),Mr=(c,p)=>c.map(f=>{var T;return{modificationType:"ONLY_REFUND",prntNo:f.ticketManagementOrganizationCode==="CDS"?"":f.printNo,ticketManagementOrganizationCode:f.ticketManagementOrganizationCode??"",resultpre:{amount:{commision:f.commision!==""&&Number(f.commision)>0?f.commision:"0",commisionRate:f.commisionRate??"",netRefund:f.netRefund,otherDeduction:f.otherDeduction||"0",taxs:f.taxs.filter(k=>k.value!==""),totalAmount:f.totalAmount||"",totalTaxs:f.totalTaxs},conjunction:f.tktType==="D"?f.conjunction:Vr(f),creditCard:f.creditCard,isCoupon:f.isCoupon,office:f.office,operator:f.createUser,remark:f.remarkInfo??"",segList:[],ticket:{airline:f.airline,crsPnrNo:p||f.crsPnrNo,currency:f.currency,etTag:f.etTag,marketAirline:f.marketAirline,name:(T=f.name)==null?void 0:T.trim(),payType:f.payType.toUpperCase(),pnr:f.pnr,psgType:f.psgType,segment:[...f.segment.values()].flatMap(k=>k.map(a=>({arriveCode:a.arriveCode,departureCode:a.departureCode,e8Rph:a.e8Rph,isAble:a.isAble,rph:a.rph,tktTag:a.tktTag,isCheck:a.isChecked?"1":"0"}))),ticketNo:f.tktType==="I"?[...f.segment.keys()].join("-"):f.ticketNo??"",secondFactor:f.secondFactor,tktType:f.tktType,ticketStatus:f.tktType==="I"&&f.airline!=="784"?f.ticketStatus:!1},volunteer:"NON_VOLUNTEER_MANUAL"}}}),Lr=(c,p)=>{const{t:l}=et(),f=O(0),T=O(0),k=O([]),a=O([]),v=O(""),r=O(!1),g=O([]),b=O([]),h=O(!1),E=Bn("--bkc-el-color-primary",null),y={ONLY_REFUND:{label:l("app.agentTicketRefund.onlyRefund",{pnrNo:c.pnrNo}),value:"ONLY_REFUND"},XE_PNR:{label:l("app.agentTicketRefund.xePnr",{pnrNo:c.pnrNo}),value:"XE_PNR"},DELETE_PASSENGER:{label:l("app.agentTicketRefund.deleteRefundPassenger",{pnrNo:c.pnrNo}),value:"DELETE_PASSENGER"},SPLIT_PASSENGER:{label:l("app.agentTicketRefund.splitPassenger",{pnrNo:c.pnrNo}),value:"SPLIT_PASSENGER"},DELETE_SPLIT_PASSENGER:{label:l("app.agentTicketRefund.deleteSplitPassenger",{pnrNo:c.pnrNo}),value:"DELETE_SPLIT_PASSENGER"}},D=Le(()=>{const Q=[];return k.value.forEach(ee=>(ee.ticketInfos??[]).forEach(fe=>Q.push(fe.ticketNo??""))),Q}),$=O(),A=O(0),V=O(!1),u=O([]),te=Be(c.pnrNo),X=O([]),ge=()=>{p("update:modelValue",!1)},q=(Q,ee,fe)=>{k.value[Q].ticketInfos[ee].validate=fe},Y=(Q,ee,fe)=>{k.value[Q].ticketInfos[ee].isAllChecked=fe,h.value=k.value.every(Ce=>Ce.ticketInfos.every(Oe=>Oe.isAllChecked??!1))},L=()=>{X.value=(g.value??[]).map(Q=>({ticketNo:Q.ticketNo,trfdNo:Q.trfdno??""})),p("getTicketTrfdNoDetails",X.value),p("update:isShowTrfdNo",!0)},le=()=>{const Q=[];return u.value.forEach(ee=>{Q.push(...ee.getEditRefundDatas())}),{refundList:Mr(Q,c.pnrNo)}},C=(Q,ee)=>{p("getManualRefundAmountDetail",Q,ee)},d=()=>{const Q=De("091Q0202"),ee=rt(c.ticketDetailList);p("update:ticketDetailList",[]),ee.forEach(async fe=>{var Oe;const Ce={secondFactorCode:"NM",secondFactorValue:fe.ticket.name};await p("queryTicketDetail",((Oe=fe.ticket.segment)==null?void 0:Oe[0].tktTag)??"",Ce,!0,Q)})},j=()=>{C(b.value,r.value),d(),L(),ge()},Te=async Q=>{const ee=`/v2/crs/pnrManagement?pnrNo=${Q}`;await mn.setLink(ee),nt.close(),ge(),j()},se=async(Q,ee,fe)=>{const Ce=Pe("p",{className:"flex flex-wrap"},[Pe("span",{className:"text-[18px]"},`${l("app.agentTicketRefund.handlePNR")}`),Pe("span",{className:"text-[18px] text-brand-2 cursor-pointer underline",onClick:()=>{Te(fe?te.value:c.pnrNo)}},`${l("app.agentTicketRefund.order")}`),Pe("span",{className:"text-[18px]"},`${l("app.agentTicketRefund.handle")}`)]),Oe=Pe("div",{className:"flex flex-wrap"},[Pe("p",{className:"text-[18px] w-full"},`${l("app.agentTicketRefund.cancelPNR",{pnrNo:fe?te.value:c.pnrNo})}`),Pe("p",{className:"text-[18px]"},[Pe("span",{className:"text-[18px]"},`${l("app.agentTicketRefund.orPlease")}`),Pe("span",{className:"text-[18px] text-brand-2 cursor-pointer underline",onClick:()=>{Te(fe?te.value:c.pnrNo)}},`${l("app.agentTicketRefund.order")}`),Pe("span",{className:"text-[18px]"},`${l("app.agentTicketRefund.handle")}`)])]),ye=Pe("div",{className:"flex flex-wrap"},[Pe("p",{className:"text-[18px] w-full"},`${l("app.agentTicketRefund.deletePNRPassenger",{pnrNo:fe?te.value:c.pnrNo})}`),Pe("p",{className:"text-[18px]"},[Pe("span",{className:"text-[18px]"},`${l("app.agentTicketRefund.orPlease")}`),Pe("span",{className:"text-[18px] text-brand-2 cursor-pointer underline",onClick:()=>{Te(fe?te.value:c.pnrNo)}},`${l("app.agentTicketRefund.order")}`),Pe("span",{className:"text-[18px]"},`${l("app.agentTicketRefund.handle")}`)])]),Ye=l("app.agentTicketRefund.refundSuccess"),qe=Q,Qe=ee?`${l("app.agentTicketRefund.cancelPnr")}`:`${l("app.agentTicketRefund.xePassenger")}`;return co(Ye,Qe,qe,Q?ee?Oe:ye:Ce)},x=async()=>r.value?(await ut({message:l("app.agentTicketRefund.refundSuccess"),type:"success"}),!1):(await oa(g.value??[],l("app.agentTicketRefund.partFaild"),"error"),!0),R=(Q,ee)=>({passengerName:Q,ticketNo:ee}),pe=Q=>{const ee=[];return(c.allPassengers??[]).forEach(fe=>{var Ce,Oe,ye;Q&&fe.isChecked&&ee.push(R(fe.passengerName,((Ce=fe.ticketNos)==null?void 0:Ce[0])??"")),Q&&fe.isChecked&&fe.infants&&fe.infants.isChecked&&ee.push(R(fe.infants.passengerName,((Oe=fe.infants.ticketNos)==null?void 0:Oe[0])??"")),!Q&&!fe.isChecked&&fe.infants&&fe.infants.isChecked&&ee.push(R(fe.infants.passengerName,((ye=fe.infants.ticketNos)==null?void 0:ye[0])??""))}),ee},Re=Q=>({pnr:Q?te.value:c.pnrNo,passengerInfos:pe(Q)}),we=async Q=>{var ee,fe;try{V.value=!0;const Ce=Re(Q),Oe=De("091Q0202");return(fe=(ee=await vo(Ce,Oe))==null?void 0:ee.data)==null?void 0:fe.value}finally{V.value=!1}},me=async()=>{if(["DELETE_PASSENGER","DELETE_SPLIT_PASSENGER"].includes(v.value)){const ee=await we(!1)??!1;await se(ee,!1,!1).then(async()=>{await ie(),j()}).catch(()=>{j()})}else j()},he=async Q=>{let ee=!0;Q&&(ee=await ce()??!1),ee&&v.value==="DELETE_SPLIT_PASSENGER"?me():j()},ue=async()=>{if(await x()){j();return}try{const ee=["XE_PNR","SPLIT_PASSENGER","DELETE_SPLIT_PASSENGER"].includes(v.value);if(ee){const fe=await we(!0)??!1;se(fe,ee,!0).then(()=>{he(ee)}).catch(()=>{me()})}else v.value==="DELETE_PASSENGER"?me():j()}catch{j()}},U=async()=>{var fe,Ce,Oe,ye;const Q=Date.now();let ee=null;try{V.value=!0;const Ye=De("091Q0202"),qe=le(),Qe=(Ce=(fe=await ho(qe,Ye))==null?void 0:fe.data)==null?void 0:Ce.value;r.value=((Oe=Qe==null?void 0:Qe.data)==null?void 0:Oe.status)==="ALL_SUCCESS",g.value=((ye=Qe==null?void 0:Qe.data)==null?void 0:ye.passengerStatuses)??[],b.value=(qe==null?void 0:qe.refundList)??[],ee=Date.now(),ds(Q,ee,"手工退票")}finally{V.value=!1}ue()},J=Q=>{if(!Q){$.value=null;return}const ee=[];u.value.forEach(fe=>{ee.push(...fe.getEditRefundDatas())}),$.value=rt(ee.find(fe=>fe.ticketNo===Q)),A.value+=1},ne=()=>{const Q=(k.value??[]).every(ee=>(ee.ticketInfos??[]).every(fe=>fe.validate));if(!Q){const ee=(k.value??[]).flatMap(fe=>(fe.ticketInfos??[]).map(Ce=>({ticketNo:Ce.ticketNo??"",passengerName:fe.name??"",success:Ce.validate})));oa(ee,l("app.agentTicketRefund.pleaseCompleteInfo"),"warn")}return Q},B=()=>{const Q=[];return(c.allPassengers??[]).map(ee=>{ee.isChecked&&Q.push({fullName:ee.passengerName,paxId:Number(ee.index.replace("P","")),unMinor:!1,unMinorAge:0})}),Q},P=()=>({orderId:"",count:0,passengerRecordLocator:c.pnrNo,travellers:B()}),N=async()=>{var Q,ee;try{V.value=!0;const fe=De("091Q0202"),Ce=P(),Oe=await In(Ce,fe);te.value=((ee=(Q=Oe.data.value)==null?void 0:Q.splitedOrder)==null?void 0:ee.passengerRecordLocator)??"",ut({message:l("app.agentTicketRefund.splitSuccess"),type:"success"})}finally{V.value=!1}},_=()=>{const Q=[];return(c.ticketDetailList??[]).forEach(ee=>{ee.ticket.psgType==="INF"&&Q.push({name:Xt.encode(ee.ticket.name),psgType:ee.ticket.psgType,ticketNo:ee.ticket.ticketNo})}),Q},w=(Q,ee)=>({pnrHandleType:ee?"C":"D",pnrNo:Q?c.pnrNo:"",xePnr:ee?te.value:"",passengerInfoList:_()}),ce=async()=>{var Q;try{V.value=!0;const ee=De("091Q0202"),fe=w(!1,!0),Oe=((Q=(await _n(fe,ee)).data.value)==null?void 0:Q.xePnrExecutionStatus)==="S";return ut({message:l(Oe?"app.agentTicketRefund.xeSuccess":"app.agentTicketRefund.xeFaild"),type:Oe?"success":"error"}),Oe}catch{j()}finally{V.value=!1}},ie=async()=>{var Q;try{V.value=!0;const ee=De("091Q0202"),fe=w(!0,!1),Oe=((Q=(await _n(fe,ee)).data.value)==null?void 0:Q.deleteInfantExecutionStatus)==="S";ut({message:l(Oe?"app.agentTicketRefund.xeSuccess":"app.agentTicketRefund.xeFaild"),type:Oe?"success":"error"})}catch{j()}finally{V.value=!1}},oe=async Q=>{let ee=c.isXepnr?l("app.agentTicketRefund.notDeletePnrTip"):l("app.agentTicketRefund.notDeletePassengerTip");Q&&(ee=l("app.agentTicketRefund.splitRefundPassenger")),await nt.confirm(Pe("div",{className:"whitespace-pre-line"},ee),{icon:Pe(at,{color:E.value,size:32},()=>Pe(an)),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.agentTicketRefund.sure"),cancelButtonText:l("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})},Se=async()=>{await nt.confirm(Pe("div",{className:"whitespace-pre-line"},l("app.agentTicketRefund.beforeRefundTip")),{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.agentTicketRefund.sure"),cancelButtonText:l("app.agentTicketRefund.cancel"),showClose:!1,closeOnClickModal:!1})},de=async()=>{ne()&&(v.value==="ONLY_REFUND"&&await oe(!1),["SPLIT_PASSENGER","DELETE_SPLIT_PASSENGER"].includes(v.value)&&(await oe(!0),await N()),["XE_PNR","DELETE_PASSENGER"].includes(v.value)&&await Se(),U())},I=Q=>[...new Set((Q??[]).map(ee=>ee.tktTag))],ve=()=>((c==null?void 0:c.ticketDetailList)??[]).map(Q=>{const ee=[],fe=I(Q.ticket.segment);if(Q.ticket.tktType==="I"){const Ce=new Map;fe.forEach(Ye=>{const qe=(Q.ticket.segment??[]).filter(Qe=>Qe.tktTag===Ye);Ce.set(Ye,qe)});const Oe=(fe==null?void 0:fe.length)>1?fe:[],ye=ra(Q,c.isDragonBoatOffice,Ce,Q.ticket.ticketNo,Oe);ee.push({validate:!0,ticketNo:Q.ticket.ticketNo,refundData:ye})}else fe.forEach(Ce=>{const Oe=(Q.ticket.segment??[]).filter(Qe=>Qe.tktTag===Ce);if(!(Oe??[]).some(Qe=>pa.includes(Qe.ticketStatus??"")))return;const Ye=new Map;Ye.set(Ce,Oe);const qe=ra(Q,c.isDragonBoatOffice,Ye,Ce,[]);ee.push({ticketNo:Ce,refundData:qe,validate:!0})});return{ticketInfos:ee,tktType:Q.ticket.tktType,psgType:Q.ticket.psgType,name:Q.ticket.passengerNameSuffix??""}}),F=()=>(c.allPassengers??[]).some(Q=>!Q.isChecked&&Q.infants&&Q.infants.isChecked),Z=()=>{const Q=k.value.every(fe=>fe.psgType==="INF");if(a.value.push(y.ONLY_REFUND),v.value=y.ONLY_REFUND.value,!c.pnrNo)return;c.isXepnr&&a.value.push(y.XE_PNR),!c.isXepnr&&Q&&a.value.push(y.DELETE_PASSENGER),!c.isXepnr&&!Q&&!F()&&a.value.push(y.SPLIT_PASSENGER),!c.isXepnr&&!Q&&F()&&a.value.push(y.DELETE_SPLIT_PASSENGER);const ee=a.value.shift()??{};a.value.push(ee)};return ct(()=>{k.value=ve(),Z()}),us(()=>{u.value=[]}),{isShowOtherRefundType:h,passengers:k,activeName:f,refundOperation:a,closeDialog:ge,refundType:v,refundItemRefs:u,validateChange:q,watchCheckedSegment:Y,submitRefund:de,fullscreenLoading:V,ticketList:D,reuseTicket:J,reuseTicketInfo:$,reuseTimes:A,tabIndex:T}},Ba=c=>(Ut("data-v-64d0d775"),c=c(),jt(),c),Br=Ba(()=>t("i",{class:"iconfont icon-close"},null,-1)),Qr=[Br],Ur={class:"title relative"},jr={class:"text-[16px] max-w-[150px] font-bold cursor-pointer inline-block whitespace-nowrap overflow-hidden text-ellipsis"},Ir=Ba(()=>t("span",{class:"line"},null,-1)),zr={class:"flex justify-center pt-[10px] w-full mt-[10px] footer items-center crs-btn-dialog-ui"},qr=He({__name:"ManualRefundDialog",props:{packageData:{},allPassengers:{},deviceNum:{},pnrNo:{},tktNo:{},ticketDetailList:{},isXepnr:{type:Boolean},isDragonBoatOffice:{type:Boolean}},emits:["update:ticketDetailList","update:modelValue","queryTicketDetail","getManualRefundAmountDetail","getTicketTrfdNoDetails","reuseTicket"],setup(c,{emit:p}){const l=c,f=p,{isShowOtherRefundType:T,passengers:k,activeName:a,refundOperation:v,closeDialog:r,refundType:g,refundItemRefs:b,validateChange:h,watchCheckedSegment:E,submitRefund:y,fullscreenLoading:D,ticketList:$,reuseTicket:A,reuseTicketInfo:V,reuseTimes:u}=Lr(l,f);return(te,X)=>{const ge=Nt,q=Ea,Y=Oa,L=zt,le=qt,C=st,d=ft,j=ht("debounce"),Te=wt;return s(),re(d,{width:"1040",title:te.$t("app.agentTicketRefund.manualRefundBtn"),"show-close":!1,"close-on-click-modal":!1,class:"manual-refund-dialog","align-center":"true",onClose:e(r)},{default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:X[0]||(X[0]=(...se)=>e(r)&&e(r)(...se))},Qr),$e((s(),m("div",null,[o(Y,{modelValue:e(a),"onUpdate:modelValue":X[1]||(X[1]=se=>Ue(a)?a.value=se:null),class:"tabs-container"},{default:i(()=>[(s(!0),m(xe,null,Ee(e(k),(se,x)=>(s(),re(q,{key:x,name:x,class:"test"},{label:i(()=>[t("span",Ur,[o(ge,{effect:"dark",content:se.name,placement:"top"},{default:i(()=>[t("span",jr,n(se.name),1)]),_:2},1032,["content"]),Ir])]),default:i(()=>{var R,pe,Re,we,me,he;return[(s(),re(Ar,{ref_for:!0,ref:ue=>{ue&&(e(b)[x]=ue)},key:x,"ticket-infos":se.ticketInfos,lazy:!0,"passenger-index":x,"ticket-list":e($),"reuse-ticket-info":e(V),"reuse-times":e(u),"active-name":e(a),"current-ticket":((he=(me=(pe=(R=e(k))==null?void 0:R[e(a)])==null?void 0:pe.ticketInfos)==null?void 0:me[((we=(Re=e(b))==null?void 0:Re[e(a)??0])==null?void 0:we.currentTicketIndex)??0])==null?void 0:he.ticketNo)??"",onValidateChange:e(h),onWatchCheckedSegment:e(E),onReuseTicket:e(A)},null,8,["ticket-infos","passenger-index","ticket-list","reuse-ticket-info","reuse-times","active-name","current-ticket","onValidateChange","onWatchCheckedSegment","onReuseTicket"]))]}),_:2},1032,["name"]))),128))]),_:1},8,["modelValue"]),t("div",zr,[o(le,{modelValue:e(g),"onUpdate:modelValue":X[2]||(X[2]=se=>Ue(g)?g.value=se:null)},{default:i(()=>[(s(!0),m(xe,null,Ee(e(v),se=>(s(),m(xe,{key:se.value},[se.value==="ONLY_REFUND"||e(T)?(s(),re(L,{key:0,label:se.value},{default:i(()=>[z(n(se.label),1)]),_:2},1032,["label"])):W("",!0)],64))),128))]),_:1},8,["modelValue"]),$e((s(),re(C,{type:"primary"},{default:i(()=>[z(n(te.$t("app.agentTicketRefund.refund")),1)]),_:1})),[[j,e(y)]])])])),[[Te,e(D)]])]),_:1},8,["title","onClose"])}}});const Gr=vt(qr,[["__scopeId","data-v-64d0d775"]]),Hr=(c,p)=>{const l=Le(()=>c.allPassengerList),f=a=>a.ticketNos.length===0||c.refundTicketSuccess,T=a=>{var v;return f(a)||a.isChecked&&((v=a==null?void 0:a.infants)==null?void 0:v.isChecked)},k=async(a,v,r)=>{!v&&(r!=null&&r.infants)&&(r.infants.isChecked=!0),!(r.isChecked&&v)&&await p("queryTicketAndCalcAmount",a)};return ct(async()=>{}),{allPnrPassengerList:l,judgeIsDisabled:f,infantIsDisabled:T,handleCheckPassenger:k}},Kr={class:"new-refund-passenger-card"},Yr={class:"text-gray-1 text-sm font-bold leading-normal pt-2.5 pb-[4px]"},Wr={class:"flex flex-row flex-wrap"},Xr={class:"text-center text-gray-3 text-xs font-normal leading-tight px-1 py-[2px] bg-gray-7 rounded-sm"},Jr={key:0,class:"iconfont icon-connect mr-[-5px] ml-[-5px] mt-[5px]"},Zr={class:"text-center text-gray-3 text-xs font-normal leading-tight px-1 py-[2px] bg-gray-7 rounded-sm"},ec=He({__name:"Passenger",props:{allPassengerList:{},clacAmountInfo:{},refundTicketSuccess:{type:Boolean}},emits:["queryTicketDetail","queryTicketAndCalcAmount"],setup(c,{emit:p}){const l=c,f=p,{allPnrPassengerList:T,judgeIsDisabled:k,handleCheckPassenger:a}=Hr(l,f);return(v,r)=>{const g=Nt,b=Ht;return s(),m("div",Kr,[t("div",Yr,n(v.$t("app.agentTicketRefund.rtPassenger")),1),t("div",Wr,[(s(!0),m(xe,null,Ee(e(T),(h,E)=>(s(),m("div",{key:E+h.index,class:Ve([[h.isChecked?"refund-passenger":"grey-connect"],"flex"])},[t("div",{class:Ve(["flex items-center rounded border mb-2.5",[h.isChecked?"border-brand-2 bg-brand-4":"border-3",h.infants?"mr-0":"mr-2.5"]])},[o(b,{modelValue:h.isChecked,"onUpdate:modelValue":y=>h.isChecked=y,disabled:e(k)(h),onChange:y=>e(a)(E,!1,h)},{default:i(()=>[o(g,{effect:"dark",placement:"top",content:h.passengerNameSuffix},{default:i(()=>[t("span",{class:Ve(["text-gray-1 text-xs font-bold leading-tight mr-1 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e(Ct)(h.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(h.passengerNameSuffix),3)]),_:2},1032,["content"]),t("div",Xr,n(e(Ct)(h.specialPassengerType)),1)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])],2),h.infants?(s(),m("div",Jr)):W("",!0),h.infants?(s(),m("div",{key:1,class:Ve(["flex items-center rounded border mr-2.5 mb-2.5",[h.infants.isChecked?"border-brand-2 bg-brand-4":"border-3"]])},[o(b,{modelValue:h.infants.isChecked,"onUpdate:modelValue":y=>h.infants.isChecked=y,disabled:e(k)(h.infants)||h.isChecked,onChange:y=>e(a)(E,!0,h)},{default:i(()=>[o(g,{effect:"dark",placement:"top",content:h.infants.passengerNameSuffix},{default:i(()=>[t("span",{class:Ve(["text-gray-1 text-xs font-bold leading-tight mr-1 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e(Ct)(h.infants.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(h.infants.passengerNameSuffix),3)]),_:2},1032,["content"]),t("div",Zr,n(e(Ct)(h.infants.specialPassengerType)),1)]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","onChange"])],2)):W("",!0)],2))),128))])])}}});const tc=(c,p)=>{const l=O(0),f=O(0),T=O(""),k=O(!1),a=()=>{k.value=!0,c("get-calc-info",k.value)},v=()=>{var g,b;if(l.value=0,f.value=0,T.value=((b=(g=p.amountData)==null?void 0:g[0])==null?void 0:b.currency)??"",p.isAutoRefundFinished){(p.batchAutoAmount??[]).forEach(h=>{l.value=Yt(Number((h==null?void 0:h.otherDeduction)??"0"),l.value),f.value=Yt(Number((h==null?void 0:h.netRefund)??"0"),f.value)});return}(p.amountData??[]).forEach(h=>{var E,y;l.value=Yt(Number(((E=h==null?void 0:h.amount)==null?void 0:E.otherDeduction)??"0"),l.value),f.value=Yt(Number(((y=h==null?void 0:h.amount)==null?void 0:y.netRefund)??"0"),f.value)})},r=()=>{var g,b;l.value=0,f.value=0,T.value=((b=(g=p.manualRefundAmountInfo)==null?void 0:g[0])==null?void 0:b.currency)??"",(p.manualRefundAmountInfo??[]).forEach(h=>{l.value=Yt(Number((h==null?void 0:h.otherDeduction)??"0"),l.value),f.value=Yt(Number((h==null?void 0:h.netRefund)??"0"),f.value)})};return bt([()=>p.amountData,()=>p.isFinishManualRefund],()=>{p.isFinishManualRefund?r():v()},{deep:!0}),ct(()=>{k.value=!1}),{otherDeduction:l,totalAmount:f,currency:T,isAlreadyClickCalcAmount:k,clacAmount:a}},nc={class:"w-[1820px] h-8 justify-start items-center gap-2.5 inline-flex mt-[14px]"},ac={class:"justify-start items-center gap-0.5 flex"},sc={class:"text-gray-1 text-sm font-bold leading-snug"},oc={key:0,class:"text-red-1 text-base font-bold leading-normal"},ic={key:0,class:"text-red-1 text-base font-bold leading-normal"},lc={key:1,class:"text-red-1 text-base font-bold leading-normal"},rc={key:2,class:"text-red-1 text-base font-bold leading-normal"},cc={class:"justify-start items-center gap-0.5 flex"},uc={class:"text-gray-1 text-sm font-bold leading-snug"},dc={key:0,class:"text-red-1 text-base font-bold leading-normal"},pc={key:0,class:"text-red-1 text-base font-bold leading-normal"},fc={key:1,class:"text-red-1 text-base font-bold leading-normal"},mc={key:2,class:"text-red-1 text-base font-bold leading-normal"},gc={key:0,class:"px-2.5 py-[5px] justify-center items-center gap-2.5 flex"},kc=He({__name:"RefundAmount",props:{amountData:{},status:{},isAutoRefundFinished:{type:Boolean},isFinishManualRefund:{type:Boolean},manualRefundAmountInfo:{},isCanRefund:{type:Boolean},batchAutoAmount:{}},emits:["get-calc-info"],setup(c,{emit:p}){const l=p,f=c,{otherDeduction:T,totalAmount:k,currency:a,isAlreadyClickCalcAmount:v,clacAmount:r}=tc(l,f);return(g,b)=>{const h=st;return s(),m("div",null,[t("div",nc,[t("div",ac,[t("div",sc,n(g.$t("app.agentTicketRefund.charge")),1),g.isFinishManualRefund?(s(),m("div",oc,n(e(a))+" "+n(e(T)),1)):(s(),m(xe,{key:1},[!e(v)||e(v)&&!g.status?(s(),m("div",ic,"--")):W("",!0),e(v)&&g.status&&g.status!=="SUCCESS"?(s(),m("div",lc,n(g.$t("app.agentTicketRefund.calcFail")),1)):W("",!0),e(v)&&g.status==="SUCCESS"?(s(),m("div",rc,n(e(a))+" "+n(e(T)),1)):W("",!0)],64))]),t("div",cc,[t("div",uc,n(g.$t("app.agentTicketRefund.TotalAmountToBeRefunded")),1),g.isFinishManualRefund?(s(),m("div",dc,n(e(a))+" "+n(e(k)),1)):(s(),m(xe,{key:1},[!e(v)||e(v)&&!g.status?(s(),m("div",pc,"--")):W("",!0),e(v)&&g.status&&g.status!=="SUCCESS"?(s(),m("div",fc,n(g.$t("app.agentTicketRefund.calcFail")),1)):W("",!0),e(v)&&g.status==="SUCCESS"?(s(),m("div",mc,n(e(a))+" "+n(e(k)),1)):W("",!0)],64))]),!g.isAutoRefundFinished&&!g.isFinishManualRefund?(s(),m("div",gc,[o(h,{disabled:!g.isCanRefund,"data-gid":"091Q0103",onClick:e(r)},{default:i(()=>[z(n(e(v)?g.$t("app.agentTicketRefund.recalc"):g.$t("app.agentTicketRefund.calcAmount")),1)]),_:1},8,["disabled","onClick"])])):W("",!0)])])}}});const yc=vt(kc,[["__scopeId","data-v-8e23591d"]]),tn=ps.global.t,hc={BSP:{label:tn("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:tn("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:tn("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:tn("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:tn("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:tn("app.agentTicketQuery.OWNTicket"),value:"ARL"}},vc={key:0},_c={class:"font-bold leading-5 text-gray-0"},Tc={class:"flex tx-xs text-gray-6"},bc={class:"w-[42px] leading-6"},xc={class:"leading-6"},Nc={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},$c={class:"w-[42px] leading-6"},Rc={class:"leading-6"},Cc={key:1},wc={class:"font-bold leading-5 text-gray-0"},Sc={class:"flex tx-xs text-gray-6"},Pc={class:"w-[42px] leading-6"},Dc={class:"leading-6"},Ac={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},Ec={class:"w-[42px] leading-6"},Oc={class:"leading-6"},Fc={key:1,class:"flex tx-xs"},Vc=t("div",{class:"w-[42px] leading-6"},"-",-1),Mc=t("div",{class:"leading-6"},"-",-1),Lc=[Vc,Mc],Bc={key:2},Qc={class:"font-bold leading-5 text-gray-0"},Uc={class:"flex tx-xs text-gray-6"},jc={class:"w-[42px] leading-6"},Ic={class:"leading-6"},zc={class:"max-h-[120px] overflow-auto tax-content text-gray-0"},qc={class:"w-[42px] leading-6"},Gc={class:"leading-6"},Hc={key:3},Kc={class:"text-gray-0"},Ln=He({__name:"RefundTaxDetailPopover",props:{ticket:{},taxs:{},isFinishManualRefund:{type:Boolean},partSuccess:{type:Boolean},isAutoRefundFinished:{type:Boolean},batchAutoTaxes:{}},setup(c){return(p,l)=>{const f=Ca;return s(),re(f,{placement:"top",width:150,trigger:"hover","popper-class":`refund-tax-detail-popper_${p.ticket.ticketNo} refund-tax-detail-popper`},{reference:i(()=>[nn(p.$slots,"tax-deatil")]),default:i(()=>{var T,k;return[((T=p.ticket.taxs)==null?void 0:T.length)>0&&!(p.isFinishManualRefund||p.partSuccess)&&!p.isAutoRefundFinished?(s(),m("div",vc,[t("div",_c,n(p.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",Tc,[t("div",bc,n(p.$t("app.agentTicketRefund.taxes")),1),t("div",xc,n(p.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",Nc,[(s(!0),m(xe,null,Ee(p.ticket.taxs,(a,v)=>(s(),m("div",{key:v,class:"flex tx-xs"},[t("div",$c,n(a.name),1),t("div",Rc,n(p.ticket.currency)+" "+n(a.value),1)]))),128))])])])):p.isFinishManualRefund||p.partSuccess?(s(),m("div",Cc,[t("div",wc,n(p.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",Sc,[t("div",Pc,n(p.$t("app.agentTicketRefund.taxes")),1),t("div",Dc,n(p.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",Ac,[(p.taxs??[]).length!==0?(s(!0),m(xe,{key:0},Ee(p.taxs,(a,v)=>(s(),m("div",{key:v,class:"flex tx-xs"},[t("div",Ec,n(a.name),1),t("div",Oc,n(p.ticket.currency)+" "+n(a.value),1)]))),128)):(s(),m("div",Fc,Lc))])])])):p.batchAutoTaxes&&((k=p.batchAutoTaxes)==null?void 0:k.length)>0&&p.isAutoRefundFinished?(s(),m("div",Bc,[t("div",Qc,n(p.$t("app.agentTicketRefund.taxDetails")),1),t("div",null,[t("div",Uc,[t("div",jc,n(p.$t("app.agentTicketRefund.taxes")),1),t("div",Ic,n(p.$t("app.agentTicketRefund.taxAmount")),1)]),t("div",zc,[(s(!0),m(xe,null,Ee(p.batchAutoTaxes,(a,v)=>(s(),m("div",{key:v,class:"flex tx-xs"},[t("div",qc,n(a.name),1),t("div",Gc,n(p.ticket.currency)+" "+n(a.value),1)]))),128))])])])):(s(),m("div",Hc,[t("span",Kc,n(p.$t("app.agentTicketRefund.noData")),1)]))]}),_:3},8,["popper-class"])}}});const Yc=(c,p)=>{const{t:l}=et(),f=Dt(),T=Le(()=>{var u;return(u=f.state.user)==null?void 0:u.entityType}),k=O(),a=O(""),v=O([]),r=yt({printerNo:"",ticketManagementOrganizationCode:""}),g={ticketManagementOrganizationCode:[{required:!0,message:l("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:"blur"}],printerNo:[{required:!0,message:l("app.ticketStatus.deviceNumNull"),trigger:"blur"},{pattern:Vt,trigger:"blur",message:l("app.ticketStatus.deviceError")}]},b=Le(()=>!["CDS","GPCDS"].includes(r.ticketManagementOrganizationCode)),h=u=>{a.value=u},E=u=>{var X,ge;return(v.value??[]).some(q=>u===q.value)&&u?u:((ge=(X=v.value)==null?void 0:X[0])==null?void 0:ge.value)??""},y=async()=>{k.value.validate(async u=>{if(!u)return;const te=De("091T0104");p("openDialog",te,r.printerNo,a.value,r.ticketManagementOrganizationCode),p("update:modelValue",!1)})},D=()=>{p("update:modelValue",!1)},$=()=>{b.value||(r.printerNo="")},A={BSP:{label:l("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:l("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:l("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:l("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:l("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:l("app.agentTicketQuery.OWNTicket"),value:"ARL"}},V=()=>{var u,te,X,ge,q,Y,L,le,C,d;((u=T.value)!=null&&u.includes("$$$")||(te=T.value)!=null&&te.includes("BSP"))&&(v.value.push(A.BSP),v.value.push(A.GPBSP)),!((X=T.value)!=null&&X.includes("BSP"))&&((ge=T.value)!=null&&ge.includes("GP"))&&v.value.push(A.GPBSP),((q=T.value)!=null&&q.includes("$$$")||(Y=T.value)!=null&&Y.includes("BOP"))&&v.value.push(A.BOPBSP),((L=T.value)!=null&&L.includes("$$$")||(le=T.value)!=null&&le.includes("CDS"))&&(v.value.push(A.CDS),v.value.push(A.GPCDS)),((C=T.value)!=null&&C.includes("$$$")||(d=T.value)!=null&&d.includes("本票"))&&v.value.push(A.ARL),r.ticketManagementOrganizationCode=E(c.ticketManagementOrganizationCode??"")};return ct(async()=>{V()}),{formDate:k,printNoFrom:r,PRINTER_NO_RULES:g,ticketOrganizationList:v,confirmPrinterNo:y,closeDialog:D,deliverPrintType:h,isShowPrintNo:b,changeTicketManagementOrganizationCode:$}},Wc=t("i",{class:"iconfont icon-close"},null,-1),Xc=[Wc],Jc={class:"carType-option-panel"},Qa=He({__name:"PrintNoDialog",props:{ticketManagementOrganizationCode:{}},emits:["openDialog","update:modelValue","update:showTicketRefundFormDialog"],setup(c,{emit:p}){const l=p,f=c,{formDate:T,printNoFrom:k,PRINTER_NO_RULES:a,ticketOrganizationList:v,confirmPrinterNo:r,closeDialog:g,deliverPrintType:b,isShowPrintNo:h,changeTicketManagementOrganizationCode:E}=Yc(f,l);return(y,D)=>{const $=at,A=Jt,V=Zt,u=dt,te=pt,X=st,ge=ft;return s(),re(ge,{title:y.$t("app.ticketStatus.selectTicket"),width:"680px",class:"print-no-dialog","show-close":!1,"close-on-click-modal":!1,onClose:e(g)},{footer:i(()=>[t("div",null,[o(X,{type:"primary",onClick:D[4]||(D[4]=q=>e(r)())},{default:i(()=>[z(n(y.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),o(X,{onClick:e(g)},{default:i(()=>[z(n(y.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:D[0]||(D[0]=(...q)=>e(g)&&e(g)(...q))},Xc),o(te,{ref_key:"formDate",ref:T,model:e(k),rules:e(a),"label-position":"left","require-asterisk-position":"right"},{default:i(()=>[o(u,{prop:"ticketManagementOrganizationCode",label:y.$t("app.agentTicketQuery.ticketOrganization")},{default:i(()=>[o(V,{modelValue:e(k).ticketManagementOrganizationCode,"onUpdate:modelValue":D[1]||(D[1]=q=>e(k).ticketManagementOrganizationCode=q),class:"ticket-management-organization",disabled:e(k).ticketManagementOrganizationCode==="",placeholder:e(k).ticketManagementOrganizationCode===""?y.$t("app.agentTicketQuery.noData"):"",onChange:e(E)},{default:i(()=>[(s(!0),m(xe,null,Ee(e(v),q=>(s(),re(A,{key:q.value,label:q.label,value:q.value},{default:i(()=>[t("div",Jc,[t("div",{class:Ve(e(k).ticketManagementOrganizationCode===q.value?"show-select":"hidden-select")},[o($,null,{default:i(()=>[o(e(fn))]),_:1})],2),t("span",null,n(q.label),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["label"]),e(h)?(s(),re(u,{key:0,prop:"printerNo",label:y.$t("app.ticketStatus.deviceNum")},{default:i(()=>[o(It,{modelValue:e(k).printerNo,"onUpdate:modelValue":[D[2]||(D[2]=q=>e(k).printerNo=q),D[3]||(D[3]=q=>e(T).validateField("printerNo"))],"select-class":"w-[340px]",onDeliverPrintType:e(b)},null,8,["modelValue","onDeliverPrintType"])]),_:1},8,["label"])):W("",!0)]),_:1},8,["model","rules"])]),_:1},8,["title","onClose"])}}});const Zc=c=>{const{t:p}=et(),l=Be(!1),f=O([]),T=O([]),k=U=>J=>{J&&(f.value[U]={el:J.$el||J})},a=U=>J=>{J&&(T.value[U]=J)},v=O([]),r=U=>{v.value[U]=!0},g=U=>{v.value[U]=!1},b=Be(!1),h=Be(!1),E=Be(""),y=Be(""),D=O({ticketType:"",ticketNo:""}),$=O({}),A=O(c.ticketDetailList??[]),V=(U,J)=>{const ne=new Map;return U.forEach(B=>{var N;const P=(N=B[J])==null?void 0:N.replace(/-/,"");ne.set(P,ne.get(P)||[]),ne.get(P).push(B)}),ne},u=Le(()=>{const U=c.isAutoRefundFinished?rt(c.batchAutoAmount??[]):rt(c.ticketAmountList??[]).map(J=>J.amount);return V(U,"ticketNo")}),te=Le(()=>{const U=new Map;return(rt(c.ticketTrfdNoDetails)??[]).forEach(ne=>{var B,P,N,_;if(((B=Qt(ne.ticketNo))==null?void 0:B.length)===14)U.set((P=ne.ticketNo)==null?void 0:P.replace(/-/,""),ne.trfdNo);else{const w=(_=(N=ne.ticketNo)==null?void 0:N.split("-"))==null?void 0:_[0];U.set(`${w}`,ne.trfdNo)}}),U}),X=U=>Number(U.conjunction)>1&&U.ticket.tktType==="D"&&c.refundType==="manual",ge=U=>{var J,ne,B;return(J=U==null?void 0:U.toUpperCase())!=null&&J.startsWith("CA")?p("app.agentTicketRefund.pay.cash"):(ne=U==null?void 0:U.toUpperCase())!=null&&ne.startsWith("CC")?p("app.agentTicketRefund.pay.tc"):(B=U==null?void 0:U.toUpperCase())!=null&&B.startsWith("CHECK")?p("app.agentTicketRefund.pay.check"):U},q=U=>{var J,ne,B;return(J=U==null?void 0:U.toUpperCase())!=null&&J.startsWith("CC")&&`${((ne=U==null?void 0:U.split("/"))==null?void 0:ne[1])??""}${((B=U==null?void 0:U.split("/"))==null?void 0:B[2])??""}`||"--"},Y=U=>{var ne,B;const J=(B=u.value.get(((ne=U.segment)==null?void 0:ne[0].tktTag)??""))==null?void 0:B[0];return c.ticketAmountList&&J?U.currency:""},L=(U,J)=>{var ne;if(c.ticketAmountList){const B=(ne=u.value.get(U))==null?void 0:ne[0];return B?B[J]:p("app.agentTicketRefund.calcFail")}return"--"},le=(U,J)=>{var ne;if(c.ticketAmountList){const B=(ne=u.value.get(U))==null?void 0:ne[0],P=B?B[J]:[];return typeof P=="string"?[]:P}return[]},C=U=>[...U.keys()].map(J=>c.manualRefundAmountInfo.find(ne=>{var B,P;return((P=(B=c.manualRefundAmountInfo)==null?void 0:B[0])==null?void 0:P.tktType)==="D"?ne.ticketNo===J:ne.ticketNo.split("-")[0]===J})),d=U=>c.manualRefundAmountInfo.find(J=>J.ticketNo===U),j=U=>{var J;return((J=d(U))==null?void 0:J.currency)??""},Te=(U,J)=>(U??[]).map(ne=>({...ne,currency:J})),se=U=>(U??[]).reduce((J,ne)=>J+Number(ne.value),0).toFixed(2),x=(U,J)=>{var ne;if(c.isFinishManualRefund||c.partSuccess){const B=C(U),P=((ne=B==null?void 0:B[0])==null?void 0:ne.currency)??"";let N=0;const _=J==="commision"&&B.every(w=>(w==null?void 0:w.commision)==="0")&&B.every(w=>(w==null?void 0:w.commisionRate)!=="");return(B??[]).forEach(w=>{J==="commision"&&!_?N=kt((w==null?void 0:w.commision)??"0",N,"+"):J==="commision"&&_&&(N=kt((w==null?void 0:w.commisionRate)??"0",N,"+")),J==="otherDeduction"&&(N=kt((w==null?void 0:w.otherDeduction)??"0",N,"+")),J==="netRefund"&&(N=kt((w==null?void 0:w.netRefund)??"0",N,"+")),J==="totalAmount"&&(N=kt((w==null?void 0:w.totalAmount)??"0",N,"+")),J==="tax"&&(N=kt((w==null?void 0:w.totalTaxs)??"0",N,"+"))}),_?`${N}%`:`${P} ${N}`}return"--"},R=U=>{if(c.isFinishManualRefund||c.partSuccess){const J=[...C(U).values()],ne=[];J.forEach(P=>{((P==null?void 0:P.taxs)??[]).forEach(N=>{const _={...N,currency:P.currency};ne.push(_)})});const B=(ne??[]).reduce((P,N)=>(P[N.name]?P[N.name]+=Number(N.value):P[N.name]=Number(N.value),P),{});return ne.forEach(P=>{P.value=B[P.name]}),[...new Set(ne.map(P=>JSON.stringify(P)))].map(P=>JSON.parse(P))}},pe=()=>({ticketNo:D.value.ticketNo,ticketType:y.value?y.value:D.value.ticketType,ticketManagementOrganizationCode:D.value.ticketManagementOrganizationCode,printerNo:E.value,refundNo:D.value.refundNo,secondFactor:D.value.secondFactor}),Re=async U=>{var ne;const J=pe();try{l.value=!0;const{data:B}=await on(J,U);$.value=(ne=B.value)==null?void 0:ne.data,$.value.ticketManagementOrganizationCode=D.value.ticketManagementOrganizationCode??"",b.value=!0}finally{l.value=!1}},we=async(U,J,ne,B)=>{E.value=J,y.value=ne,D.value.ticketManagementOrganizationCode=B,await Re(U)},me=async(U,J,ne,B,P,N)=>{if(D.value={ticketNo:U,ticketType:J,secondFactor:P,ticketManagementOrganizationCode:ne,refundNo:N},y.value=J,E.value=B??"",D.value.ticketManagementOrganizationCode&&B){const _=De("091T0104");await Re(_)}else h.value=!0},he=U=>{if(!A.value[U].printerNo){A.value[U].printError=p("app.ticketStatus.deviceNumNull");return}if(!Vt.test(A.value[U].printerNo)){A.value[U].printError=p("app.ticketStatus.deviceError");return}A.value[U].printError=""},ue=(U,J)=>J&&(J==null?void 0:J.length)>=9&&U!=="ARL"?J.slice(-9):J;return bt(()=>c.ticketDetailList,()=>{A.value=c.ticketDetailList??[]},{deep:!0}),{loading:l,ticketList:A,isManualDomesticConjunctionTicket:X,showRefundFormDialog:b,printNo:E,printType:y,refundOperationCondition:D,refundFormData:$,getPayType:ge,getCreditCard:q,getCurrencyByTicket:Y,getRefundFeeByTicketNo:L,getAmountAndCurrency:x,getManualTax:R,getDomManualCurrency:j,getDomManualTicket:d,getDomManualTax:Te,dealTaxTotal:se,ticketTrfdNo:te,openRefundFormDialog:me,checkPrinterNo:he,showPrintNoDialog:h,openDialog:we,getRefundFormNumber:ue,getBatchAutoTaxes:le,realRefs:f,setRealRefs:k,setPopoverRefs:a,showTicketOriginalContainerList:v,show:r,hide:g}},Hn=c=>(Ut("data-v-94d2cbfe"),c=c(),jt(),c),eu={class:"w-full h-[44px] p-[10px] bg-brand-4 border-b border-brand-3 justify-between items-center inline-flex"},tu={class:"justify-start items-center flex"},nu={class:"justify-start items-start gap-2 flex mr-2.5"},au={key:0,class:"text-brand-2 text-base font-bold leading-normal"},su={key:1},ou={class:"px-1 bg-gray-7 rounded-sm justify-start items-start gap-2.5 flex"},iu={class:"text-center text-gray-3 text-xs font-normal leading-tight"},lu={class:"justify-start items-center gap-2.5 flex ml-2.5"},ru={class:"justify-start items-center gap-1 flex"},cu={key:0,class:"iconfont icon-inf mr-[4px] text-gray-4"},uu={key:1,class:"iconfont icon-user-fill mr-[4px] text-gray-4"},du={class:"text-gray-1 text-base font-bold leading-normal"},pu={class:"px-1 bg-gray-7 rounded-sm justify-start items-start gap-2.5 flex"},fu={class:"text-center text-gray-3 text-xs font-normal leading-tight"},mu={class:"justify-start items-center gap-3.5 flex"},gu={key:0,class:"justify-start items-start flex"},ku={class:"text-gray-3 text-sm font-normal leading-snug"},yu=["onClick"],hu={class:"justify-start items-start flex"},vu={class:"text-gray-3 text-sm font-normal leading-snug"},_u={class:"text-gray-1 text-sm font-normal leading-snug"},Tu={class:"justify-start items-start flex"},bu={class:"text-gray-3 text-sm font-normal leading-snug"},xu={class:"text-gray-1 text-sm font-normal leading-snug"},Nu={class:"justify-start items-start flex"},$u={class:"text-gray-3 text-sm font-normal leading-snug"},Ru={class:"text-gray-1 text-sm font-normal leading-snug"},Cu={key:1,class:"justify-start items-center flex"},wu={class:"text-gray-3 text-sm font-normal leading-snug"},Su={class:"text-gray-1 text-sm font-normal leading-snug"},Pu={key:0,class:"w-full h-5 px-[10px] justify-start items-center gap-2.5 inline-flex align-center"},Du=Hn(()=>t("div",{class:"grow border-t-[1px] border-dashed border-gray-6"},null,-1)),Au={key:0,class:"text-xs w-[150px]"},Eu=["onClick"],Ou={key:0,class:"w-[135px] flex items-center"},Fu={key:0,class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},Vu={class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Mu={class:"text-gray-1 text-sm font-normal leading-snug"},Lu={key:1,class:"w-[135px] flex items-center"},Bu={class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},Qu={class:"text-gray-1 text-sm font-normal leading-snug"},Uu={key:2,class:"w-[200px]"},ju={class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Iu={key:3,class:"w-[200px] self-stretch justify-start items-center gap-5 flex"},zu={class:"w-[78px] text-gray-1 text-sm font-normal leading-snug"},qu={class:"text-gray-3 text-sm font-normal leading-snug"},Gu={class:"w-[220px] self-stretch justify-start items-center gap-2.5 flex"},Hu={class:"text-gray-1 text-sm font-normal leading-snug"},Ku={class:"text-gray-3 text-sm font-normal leading-snug"},Yu={key:0},Wu={class:"w-[400px] self-stretch px-1 justify-start items-center gap-3 flex"},Xu={key:1,class:"w-full h-[42px] p-[10px] justify-between items-center inline-flex border-gray-6"},Ju={class:"justify-start items-center gap-3.5 flex"},Zu={class:"justify-start items-start gap-0.5 flex"},ed={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},td={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},nd={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},ad={class:"justify-start items-start gap-0.5 flex"},sd={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},od={class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},id={class:"justify-start items-start gap-2.5 flex"},ld={class:"justify-start items-center gap-0.5 flex"},rd={class:"text-gray-1 text-sm font-normal leading-snug"},cd={class:"text-red-1 text-sm font-normal leading-snug"},ud={class:"justify-start items-center gap-0.5 flex"},dd={class:"text-gray-1 text-sm font-normal leading-snug"},pd={class:"text-red-1 text-sm font-normal leading-snug"},fd={class:"justify-start items-center gap-0.5 flex"},md={class:"text-gray-1 text-sm font-normal leading-snug"},gd={class:"text-red-1 text-sm font-normal leading-snug"},kd={key:0,class:"justify-start items-start gap-2.5 flex"},yd={class:"text-red-1 text-[14px] font-[700]"},hd=Hn(()=>t("div",{class:"w-full border-t-[1px] border-dashed border-gray-6"},null,-1)),vd={class:"w-full h-[52px] p-[10px] justify-between items-center inline-flex"},_d={class:"justify-start items-center gap-3.5 flex"},Td={key:0,class:"justify-start items-start gap-0.5 flex"},bd={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},xd={class:"text-gray-1 text-sm font-normal leading-snug"},Nd={key:1,class:"justify-start items-start gap-0.5 flex"},$d={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},Rd={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},Cd={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},wd={class:"justify-start items-start gap-0.5 flex"},Sd={class:"text-gray-3 text-sm font-normal leading-snug mr-[4px]"},Pd={key:0,class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},Dd={key:1,class:"text-sm font-normal leading-snug cursor-pointer border-b-[1.5px] tax-detail"},Ad={key:2},Ed={class:"text-gray-2"},Od=Hn(()=>t("span",{class:"text-red-1 mx-[3px]"},"*",-1)),Fd={key:0,class:"justify-start items-start gap-2.5 flex"},Vd={class:"justify-start items-center gap-0.5 flex"},Md={class:"text-gray-1 text-sm font-normal leading-snug"},Ld={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Bd={key:1,class:"text-red-1 text-sm font-normal leading-snug"},Qd={class:"justify-start items-center gap-0.5 flex"},Ud={class:"text-gray-1 text-sm font-normal leading-snug"},jd={class:"text-red-1 text-sm font-normal leading-snug"},Id={class:"justify-start items-center gap-0.5 flex"},zd={class:"text-gray-1 text-sm font-normal leading-snug"},qd={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Gd={key:1,class:"text-red-1 text-sm font-normal leading-snug"},Hd={class:"justify-start items-center gap-0.5 flex"},Kd={class:"text-gray-1 text-sm font-normal leading-snug"},Yd={key:0,class:"text-red-1 text-sm font-normal leading-snug"},Wd={key:1,class:"text-red-1 text-sm font-normal leading-snug"},Xd={key:1,class:"justify-start items-start gap-2.5 flex"},Jd={class:"text-red-1 text-[14px] font-[700]"},Zd=He({__name:"TicketDetailInfo",props:{ticketDetailList:{},ticketAmountList:{},isFinishManualRefund:{type:Boolean},partSuccess:{type:Boolean},manualRefundAmountInfo:{},isShowTrfdNo:{type:Boolean},ticketTrfdNoDetails:{},refundType:{},deviceNum:{},refundTicketSuccess:{type:Boolean},batchAutoAmount:{},isAutoRefundFinished:{type:Boolean}},setup(c){const p=c,{loading:l,ticketList:f,isManualDomesticConjunctionTicket:T,showRefundFormDialog:k,printNo:a,printType:v,refundOperationCondition:r,refundFormData:g,getPayType:b,getCreditCard:h,getCurrencyByTicket:E,getRefundFeeByTicketNo:y,getAmountAndCurrency:D,getManualTax:$,getDomManualCurrency:A,getDomManualTicket:V,getDomManualTax:u,dealTaxTotal:te,ticketTrfdNo:X,openRefundFormDialog:ge,checkPrinterNo:q,showPrintNoDialog:Y,openDialog:L,getRefundFormNumber:le,getBatchAutoTaxes:C}=Zc(p);return(d,j)=>{const Te=dt,se=pt,x=wt;return s(),m(xe,null,[(s(!0),m(xe,null,Ee(e(f),(R,pe)=>{var Re,we,me,he,ue,U,J,ne,B,P,N,_,w,ce,ie,oe,Se,de,I,ve,F,Z,Q,ee,fe,Ce,Oe;return $e((s(),m("div",{key:pe,class:Ve(["rounded border border-brand-3 overflow-hidden",pe!==((Re=e(f))==null?void 0:Re.length)-1?"mb-[10px]":"mb-[0px]"])},[t("div",eu,[t("div",tu,[t("div",nu,[Number(R.conjunction)>1?(s(),m("div",au,n(e(Qt)(R.ticket.ticketNo)),1)):(s(),m("div",su,[o(rn,{"tkt-index":pe,"ticket-number":R.ticket.ticketNo,"second-factor":R.ticket.secondFactor,"refund-class-type":"0"},null,8,["tkt-index","ticket-number","second-factor"])]))]),t("div",ou,[t("div",iu,n(R!=null&&R.ticketManagementOrganizationCode?e(hc)[R==null?void 0:R.ticketManagementOrganizationCode].label:""),1)]),t("div",lu,[t("div",ru,[((we=R==null?void 0:R.ticket)==null?void 0:we.specialPassengerType)==="INF"?(s(),m("em",cu)):(s(),m("em",uu)),t("div",du,n(R.ticket.passengerNameSuffix),1),t("div",pu,[t("div",fu,n(e(Ct)(((me=R==null?void 0:R.ticket)==null?void 0:me.specialPassengerType)??"ADT")),1)])])])]),t("div",mu,[!e(T)(R)&&d.isShowTrfdNo?(s(),m("div",gu,[t("div",ku,n(d.$t("app.agentTicketRefund.refundTicketNumber"))+"：",1),t("div",{class:"text-brand-2 text-sm font-bold leading-snug cursor-pointer",onClick:ye=>{var Ye,qe,Qe,ae;return e(ge)((qe=(Ye=R.ticket.ticketNo)==null?void 0:Ye.split("-"))==null?void 0:qe[0],R.ticket.tktType,R.ticketManagementOrganizationCode??"",R==null?void 0:R.refundPrintNumber,R.ticket.secondFactor,e(X).get((ae=(Qe=R.ticket.ticketNo)==null?void 0:Qe.split("-"))==null?void 0:ae[0])??"")}},n(e(le)((R==null?void 0:R.ticketManagementOrganizationCode)??"",e(X).get(((ue=(he=R.ticket.ticketNo)==null?void 0:he.split("-"))==null?void 0:ue[0])??"")??"")||"--"),9,yu)])):W("",!0),t("div",hu,[t("div",vu,n(d.$t("app.agentTicketRefund.oldTicketNo"))+"：",1),t("div",_u,n(e(Qt)(R.ticket.exchangeTktNo??"")||"--"),1)]),t("div",Tu,[t("div",bu,n(d.$t("app.agentTicketRefund.electronic"))+"：",1),t("div",xu,n(d.$t("app.agentTicketRefund.yes")),1)]),t("div",Nu,[t("div",$u,n(d.$t("app.agentTicketRefund.payment"))+"：",1),t("div",Ru,n(e(b)(R.ticket.payType)),1)]),(J=(U=R.ticket.payType)==null?void 0:U.toUpperCase())!=null&&J.startsWith("CC")?(s(),m("div",Cu,[t("div",wu,n(d.$t("app.agentTicketRefund.creditCard"))+"：",1),t("div",Su,n(e(h)(R.ticket.payType)),1)])):W("",!0)])]),(s(!0),m(xe,null,Ee(R.ticket.ticketSegment,(ye,Ye)=>{var qe,Qe,ae,ot,S,G,Ne,Ae,Fe,We,Je,tt,Xe;return s(),m(xe,{key:Ye},[Number(R.conjunction)>1?(s(),m("div",Pu,[o(rn,{"tkt-index":`${pe}_${Ye}`,"ticket-number":(ye==null?void 0:ye[0])??"","second-factor":R.ticket.secondFactor,"refund-class-type":"1"},null,8,["tkt-index","ticket-number","second-factor"]),Du,e(T)(R)&&d.isShowTrfdNo?(s(),m("div",Au,[z(n(d.$t("app.agentTicketRefund.refundTicketNumber"))+"：",1),t("span",{class:"text-brand-2 font-bold cursor-pointer",onClick:Ke=>e(ge)(ye==null?void 0:ye[0],R.ticket.tktType,R.ticketManagementOrganizationCode??"",R==null?void 0:R.refundPrintNumber,R.ticket.secondFactor,e(X).get(ye==null?void 0:ye[0])??"")},n(e(le)((R==null?void 0:R.ticketManagementOrganizationCode)??"",e(X).get(ye==null?void 0:ye[0])??"")||"--"),9,Eu)])):W("",!0)])):W("",!0),t("div",{class:Ve(["w-full px-[10px] flex-col justify-start items-start inline-flex",Number(R.conjunction)>1?"py-[4px]":"py-[8px]"])},[(s(!0),m(xe,null,Ee(ye==null?void 0:ye[1],(Ke,At)=>{var Et,en,gt;return s(),m("div",{key:At,class:"self-stretch h-7 justify-between items-center inline-flex"},[Ke.segmentType!=="2"?(s(),m("div",Ou,[Ke.segmentType==="3"&&Ke.airline?(s(),m("span",Fu,n(Ke.airline),1)):W("",!0),t("span",Vu,n(Ke.segmentType==="3"?"OPEN":"ARNK"),1),t("span",Mu,n(Ke.cabinCode),1)])):(s(),m("div",Lu,[t("div",Bu,n(Ke.flightNo),1),t("div",Qu,n(Ke.cabinCode),1)])),Ke.segmentType!=="2"?(s(),m("div",Uu,[t("span",ju,n(Ke.segmentType==="3"?"OPEN":"ARNK"),1)])):W("",!0),Ke.departureDate||Ke.departureTime?(s(),m("div",Iu,[t("div",zu,n(Ke.departureDate),1),t("div",qu,n(((Et=Ke.departureTime)==null?void 0:Et.substring(0,5))??""),1)])):W("",!0),t("div",Gu,[t("div",Hu,n(Ke.departureCode)+"-"+n(Ke.arriveCode),1),t("div",Ku,[z(n(R.ticket.pnr),1),R.ticket.pnr&&R.ticket.crsPnrNo?(s(),m("span",Yu,"/")):W("",!0),z(n(R.ticket.crsPnrNo),1)])]),t("div",Wu,[t("div",{class:Ve(["text-sm font-bold leading-snug",e(St)[((en=Ke.ticketStatus)==null?void 0:en.trim())??""]?e(St)[((gt=Ke.ticketStatus)==null?void 0:gt.trim())??""].color:""])},n(Ke.ticketStatus),3)])])}),128))],2),R.ticket.tktType==="D"&&(d.isFinishManualRefund||d.partSuccess)&&Number(R.conjunction)>1?(s(),m("div",Xu,[t("div",Ju,[t("div",Zu,[t("div",ed,n(d.$t("app.agentTicketRefund.totalTicketAmount")),1),(qe=e(V)(ye==null?void 0:ye[0]))!=null&&qe.totalAmount?(s(),m("div",nd,n(e(A)(ye==null?void 0:ye[0]))+" "+n(((Qe=e(V)(ye==null?void 0:ye[0]))==null?void 0:Qe.totalAmount)??""),1)):(s(),m("div",td,"--"))]),t("div",ad,[t("div",sd,n(d.$t("app.agentTicketRefund.totalTaxAmount")),1),(s(),re(Ln,{key:`DomesticRefundTaxDetailPopover${pe}_${Ye}`,ticket:R.ticket,taxs:e(u)((ae=e(V)(ye==null?void 0:ye[0]))==null?void 0:ae.taxs,(ot=e(V)(ye==null?void 0:ye[0]))==null?void 0:ot.currency),"is-finish-manual-refund":d.isFinishManualRefund,"part-success":d.partSuccess},{"tax-deatil":i(()=>{var Ke;return[t("div",od,n(e(A)(ye==null?void 0:ye[0]))+" "+n(e(te)((Ke=e(V)(ye==null?void 0:ye[0]))==null?void 0:Ke.taxs)),1)]}),_:2},1032,["ticket","taxs","is-finish-manual-refund","part-success"]))])]),t("div",id,[t("div",ld,[t("div",rd,n(!Number((S=e(V)(ye==null?void 0:ye[0]))==null?void 0:S.commision)&&((G=e(V)(ye==null?void 0:ye[0]))==null?void 0:G.commisionRate)!==""?d.$t("app.agentTicketRefund.commissionRate"):d.$t("app.agentTicketRefund.commission")),1),t("div",cd,n(!Number((Ne=e(V)(ye==null?void 0:ye[0]))==null?void 0:Ne.commision)&&((Ae=e(V)(ye==null?void 0:ye[0]))==null?void 0:Ae.commisionRate)!==""?`${((Fe=e(V)(ye==null?void 0:ye[0]))==null?void 0:Fe.commisionRate)??""}%`:`${(We=e(V)(ye==null?void 0:ye[0]))==null?void 0:We.currency} ${Number((Je=e(V)(ye==null?void 0:ye[0]))==null?void 0:Je.commision).toFixed(2)}`),1)]),t("div",ud,[t("div",dd,n(d.$t("app.agentTicketRefund.charge")),1),t("div",pd,n(e(A)(ye==null?void 0:ye[0]))+" "+n(((tt=e(V)(ye==null?void 0:ye[0]))==null?void 0:tt.otherDeduction)??""),1)]),t("div",fd,[t("div",md,n(d.$t("app.agentTicketRefund.totalRefund")),1),t("div",gd,n(e(A)(ye==null?void 0:ye[0]))+" "+n(((Xe=e(V)(ye==null?void 0:ye[0]))==null?void 0:Xe.netRefund)??""),1)])]),R.ticket.isRefundFail?(s(),m("div",kd,[t("span",yd,n(d.$t("app.agentTicketRefund.refundFailMsg")),1)])):W("",!0)])):W("",!0)],64)}),128)),R.ticket.tktType==="D"&&(d.isFinishManualRefund||d.partSuccess)&&Number(R.conjunction)>1?W("",!0):(s(),m(xe,{key:0},[hd,t("div",vd,[t("div",_d,[d.isAutoRefundFinished?(s(),m("div",Td,[t("div",bd,n(d.$t("app.agentTicketRefund.totalTicketAmount")),1),t("div",xd,n(`${R.ticket.currency??"CNY"} ${e(y)(((B=(ne=R.ticket.segment)==null?void 0:ne[0])==null?void 0:B.tktTag)??"","totalAmount")}`),1)])):(s(),m("div",Nd,[t("div",$d,n(d.$t("app.agentTicketRefund.totalTicketAmount")),1),R.ticket.totalAmount?(s(),m("div",Cd,n(d.isFinishManualRefund||d.partSuccess?e(D)(R.ticket.ticketSegment,"totalAmount"):`${R.ticket.currency??"CNY"} ${R.ticket.totalAmount}`),1)):(s(),m("div",Rd,"--"))])),t("div",wd,[t("div",Sd,n(d.$t("app.agentTicketRefund.totalTaxAmount")),1),(s(),re(Ln,{key:`InternationalRefundTaxDetailPopover${pe}`,"is-auto-refund-finished":d.isAutoRefundFinished,"batch-auto-taxes":e(C)(((N=(P=R.ticket.segment)==null?void 0:P[0])==null?void 0:N.tktTag)??"","taxes"),ticket:R.ticket,taxs:e($)(R.ticket.ticketSegment),"is-finish-manual-refund":d.isFinishManualRefund,"part-success":d.partSuccess},{"tax-deatil":i(()=>{var ye,Ye;return[d.isAutoRefundFinished?(s(),m("div",Pd,n(`${R.ticket.currency??"CNY"} ${e(y)(((Ye=(ye=R.ticket.segment)==null?void 0:ye[0])==null?void 0:Ye.tktTag)??"","totalTaxs")}`),1)):(s(),m("div",Dd,n(d.isFinishManualRefund||d.partSuccess?e(D)(R.ticket.ticketSegment,"tax"):`${R.ticket.currency??"CNY"} ${R.ticket.totalTaxs}`),1))]}),_:2},1032,["is-auto-refund-finished","batch-auto-taxes","ticket","taxs","is-finish-manual-refund","part-success"]))]),!d.refundTicketSuccess&&!((R==null?void 0:R.ticketManagementOrganizationCode)??"").includes("CDS")?(s(),m("div",Ad,[o(se,{model:R,"require-asterisk-position":"right",class:"device-form"},{default:i(()=>[o(Te,{prop:"printNo",error:R.printError},{default:i(()=>[t("div",Ed,[z(n(d.$t("app.agentTicketRefund.prntNo")),1),Od]),o(It,{modelValue:R.printerNo,"onUpdate:modelValue":ye=>R.printerNo=ye,"select-class":"w-[100px]",onBlur:ye=>e(q)(pe)},null,8,["modelValue","onUpdate:modelValue","onBlur"])]),_:2},1032,["error"])]),_:2},1032,["model"])])):W("",!0)]),R.ticket.isRefundFail?W("",!0):(s(),m("div",Fd,[t("div",Vd,[t("div",Md,n(e(D)(R.ticket.ticketSegment,"commision").includes("%")?d.$t("app.agentTicketRefund.commissionRate"):d.$t("app.agentTicketRefund.commission")),1),!d.isFinishManualRefund&&!d.partSuccess?(s(),m("div",Ld,n(e(y)(((w=(_=R.ticket.segment)==null?void 0:_[0])==null?void 0:w.tktTag)??"","commision")&&e(y)(((ie=(ce=R.ticket.segment)==null?void 0:ce[0])==null?void 0:ie.tktTag)??"","commision")!=="--"?`${e(E)(R.ticket)} ${e(y)(((Se=(oe=R.ticket.segment)==null?void 0:oe[0])==null?void 0:Se.tktTag)??"","commision")}`:"--"),1)):(s(),m("div",Bd,n(e(D)(R.ticket.ticketSegment,"commision")),1))]),t("div",Qd,[!d.isFinishManualRefund&&!d.partSuccess?(s(),m(xe,{key:0},[t("div",Ud,n(d.$t("app.agentTicketRefund.commissionRate")),1),t("div",jd,n(isNaN(Number(e(y)(((I=(de=R.ticket.segment)==null?void 0:de[0])==null?void 0:I.tktTag)??"","commisionRate")))?e(y)(((Q=(Z=R.ticket.segment)==null?void 0:Z[0])==null?void 0:Q.tktTag)??"","commisionRate"):`${e(y)(((F=(ve=R.ticket.segment)==null?void 0:ve[0])==null?void 0:F.tktTag)??"","commisionRate")??""}%`),1)],64)):W("",!0)]),t("div",Id,[t("div",zd,n(d.$t("app.agentTicketRefund.charge")),1),!d.isFinishManualRefund&&!d.partSuccess?(s(),m("div",qd,n(e(E)(R.ticket))+" "+n(e(y)(((fe=(ee=R.ticket.segment)==null?void 0:ee[0])==null?void 0:fe.tktTag)??"","otherDeduction")),1)):(s(),m("div",Gd,n(e(D)(R.ticket.ticketSegment,"otherDeduction")),1))]),t("div",Hd,[t("div",Kd,n(d.$t("app.agentTicketRefund.totalRefund")),1),!d.isFinishManualRefund&&!d.partSuccess?(s(),m("div",Yd,n(e(E)(R.ticket))+" "+n(e(y)(((Oe=(Ce=R.ticket.segment)==null?void 0:Ce[0])==null?void 0:Oe.tktTag)??"","netRefund")),1)):(s(),m("div",Wd,n(e(D)(R.ticket.ticketSegment,"netRefund")),1))])])),R.ticket.isRefundFail?(s(),m("div",Xd,[t("span",Jd,n(d.$t("app.agentTicketRefund.refundFailMsg")),1)])):W("",!0)])],64))],2)),[[x,e(l),void 0,{fullscreen:!0,lock:!0}]])}),128)),e(Y)?(s(),re(Qa,{key:0,modelValue:e(Y),"onUpdate:modelValue":j[0]||(j[0]=R=>Ue(Y)?Y.value=R:null),"ticket-management-organization-code":e(r).ticketManagementOrganizationCode??"",onOpenDialog:e(L)},null,8,["modelValue","ticket-management-organization-code","onOpenDialog"])):W("",!0),e(k)?(s(),re(zn,{key:1,modelValue:e(k),"onUpdate:modelValue":j[1]||(j[1]=R=>Ue(k)?k.value=R:null),"printer-no":e(a),"printer-type":e(v),"is-supplement-refund":!1,"refund-operation-condition":e(r),"refund-ticket-data":e(g)},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data"])):W("",!0)],64)}}});const ep=vt(Zd,[["__scopeId","data-v-94d2cbfe"]]),tp={class:"flex text-[16px] text-[#000]"},np={class:"ml-[10px] w-full"},ap={class:"pb-[6px]"},sp={key:0,class:"flex"},op={class:"w-[294px]"},ip={key:1,class:"text-green-1"},lp={key:2,class:"flex"},rp={class:"w-[294px]"},cp={key:3,class:"text-green-1"},up={key:4,class:"mt-[12px] mb-[12px]"},dp={class:"text-end w-full"},pp=He({__name:"interRefundSuccessdialog",props:{newPnr:{},oldPnr:{},ticketDetailList:{}},emits:["update:modelValue","refresh"],setup(c,{emit:p}){const l=c,f=p,{t:T}=et(),k=bn(),a=O(!0),v=O(!1),r=O(!1),g=O(!1),b=async V=>{k.push({name:"PnrManagement",query:{pnrNo:V,time:new Date().getTime()}});const u=De("091Q0104");f("refresh",!0,u),f("update:modelValue",!1)},h=(V,u)=>{ut({message:V?T("app.agentTicketRefund.cancelPnrSuccess",{pnr:u}):T("app.agentTicketRefund.manualDeleteSuccess"),type:"success"})},E=V=>{const u={pnrNo:V?"":l.oldPnr,xePnr:V?l.newPnr:"",passengerInfoList:[],pnrHandleType:V?"C":"D"};return(l.ticketDetailList??[]).forEach(te=>{!V&&te.ticket.psgType==="INF"&&u.passengerInfoList.push({name:Xt.encode(te.ticket.name),psgType:te.ticket.psgType,ticketNo:te.ticket.ticketNo})}),u},y=async V=>{g.value=!0;let u;try{const te=De("09200113");u=(await _n(E(V),te)).data.value}finally{g.value=!1}V&&(u!=null&&u.xePnrExecutionStatus.includes("S"))?(h(V,l.newPnr),r.value=!0):!V&&(u!=null&&u.deleteInfantExecutionStatus.includes("S"))&&(h(V),v.value=!0)},D=()=>{y(!1)},$=()=>{y(!0)},A=()=>{const V=De("091Q0104");f("refresh",!0,V),f("update:modelValue",!1)};return(V,u)=>{const te=at,X=st,ge=ft,q=wt;return $e((s(),m("div",null,[o(ge,{modelValue:a.value,"onUpdate:modelValue":u[2]||(u[2]=Y=>a.value=Y),"show-close":!1,"close-on-click-modal":!1,modal:!1,"close-on-press-escape":!1,class:"refund-success-dialog",width:"440"},{default:i(()=>[t("div",tp,[t("div",null,[o(te,{class:"top-[calc(50%-30px)]",size:30,color:"var(--bkc-color-special-green-2)"},{default:i(()=>[o(e(Qn))]),_:1})]),t("div",np,[t("p",ap,n(e(T)("app.agentTicketRefund.autoRefundSuccess")),1),v.value?(s(),m("div",ip,"PNR : "+n(V.oldPnr)+n(e(T)("app.agentTicketRefund.passengerBeDelete")),1)):(s(),m("div",sp,[t("div",op,[z(n(e(T)("app.agentTicketRefund.whetherAutoDelete")),1),t("span",{class:"underline text-brand-2 cursor-pointer",onClick:u[0]||(u[0]=Y=>b(V.oldPnr))},"PNR : "+n(V.oldPnr),1),z(n(e(T)("app.agentTicketRefund.refundTicketTip")),1)]),o(X,{class:"operate-btn",type:"primary",onClick:D},{default:i(()=>[z(n(e(T)("app.agentTicketRefund.deletePassengerOperate")),1)]),_:1})])),r.value?(s(),m("div",cp,"PNR : "+n(V.newPnr)+" "+n(e(T)("app.agentTicketRefund.canceled")),1)):(s(),m("div",lp,[t("div",rp,[z(n(e(T)("app.agentTicketRefund.whetherAutoCancel")),1),t("span",{class:"underline text-brand-2 cursor-pointer",onClick:u[1]||(u[1]=Y=>b(V.newPnr))},"PNR : "+n(V.newPnr),1),z(" ？ ")]),o(X,{class:"operate-btn",type:"primary",onClick:$},{default:i(()=>[z(n(e(T)("app.agentTicketRefund.cancelPnr")),1)]),_:1})])),r.value&&v.value?W("",!0):(s(),m("div",up,n(e(T)("app.agentTicketRefund.goToOrder")),1))])]),t("div",dp,[r.value&&v.value?(s(),re(X,{key:0,type:"primary",onClick:A},{default:i(()=>[z(n(e(T)("app.agentTicketRefund.sure")),1)]),_:1})):(s(),re(X,{key:1,onClick:A},{default:i(()=>[z(n(e(T)("app.agentTicketRefund.cancel")),1)]),_:1}))])]),_:1},8,["modelValue"])])),[[q,g.value,void 0,{fullscreen:!0,lock:!0}]])}}});const fp={class:"bg-[var(--bkc-el-bg-color)] p-[10px] rounded-lg mt-[10px] min-h-[calc(100vh_-_33vh)] shadow-[0_0_8px_0_rgba(109,117,151,0.2)]"},mp={class:"flex items-center"},gp={class:"text-gray-1 text-base font-bold leading-normal"},kp={key:0,class:"ml-[14px] p-[4px] rounded-[2px] bg-brand-3 text-brand-1 text-[14px]"},yp={class:"inline-block h-full border-yellow-2 border-solid bg-yellow-3 text-yellow-1 text-[12px] rounded-[2px] py-[8px] px-[10px] flex items-center"},hp={key:1,class:"ml-[14px] p-[4px] rounded-[2px] bg-brand-3 text-brand-1 text-[14px]"},vp={class:"footer"},_p=He({__name:"TicketRefundContainer",props:{tktNo:{},factor:{}},emits:["addNewTab","removeTab"],setup(c,{emit:p}){const l=c,f=p,{splitPnrNo:T,isDragonBoatOffice:k,pnrNo:a,amountRef:v,fullscreenLoading:r,allPassengerList:g,ticketDetailList:b,manualDialogVisible:h,packageData:E,clacAmountInfosRes:y,clacAmountInfo:D,isAutoRefundFinished:$,isXePnr:A,currentTktNo:V,deviceNum:u,isFinishManualRefund:te,manualRefundAmountInfo:X,partSuccess:ge,isCanRefund:q,queryTicketDetail:Y,manualRefund:L,handleAutoRefund:le,getCalcInfo:C,queryTicketAndCalcAmount:d,getManualRefundAmountDetail:j,refresh:Te,ticketTrfdNoDetails:se,isShowTrfdNo:x,getTicketTrfdNoDetailsFromManualRefund:R,refundType:pe,isRelatedCorrectPnr:Re,updatePnrForm:we,updatePnrFormData:me,updatePnrFormRules:he,queryAllPassenger:ue,queryPnrTip:U,showInterRefundSuccess:J,refreshTicketDetail:ne,refundTicketSuccess:B,batchAutoAmount:P}=qi(l,f);return(N,_)=>{const w=xt,ce=dt,ie=st,oe=at,Se=pt,de=ht("trimUpper"),I=ht("permission"),ve=wt;return $e((s(),m("div",fp,[t("div",mp,[t("div",gp,n(N.$t("app.agentTicketRefund.refund")),1),t("div",null,[e(a)?(s(),m(xe,{key:0},[e(Re)?(s(),m("span",kp,"PNR："+n(e(a)),1)):(s(),re(Se,{key:1,ref_key:"updatePnrForm",ref:we,model:e(me),inline:!0,rules:e(he),"require-asterisk-position":"right",class:"ml-[14px] h-[32px] flex items-center updatePnrForm"},{default:i(()=>[o(ce,{prop:"pnrNo",label:N.$t("app.agentTicketQuery.pnrNumber")},{default:i(()=>[$e(o(w,{modelValue:e(me).pnrNo,"onUpdate:modelValue":_[0]||(_[0]=F=>e(me).pnrNo=F),clearable:""},null,8,["modelValue"]),[[de]])]),_:1},8,["label"]),o(ce,null,{default:i(()=>[o(ie,{type:"primary",onClick:_[1]||(_[1]=F=>e(ue)(e(me).pnrNo,"091Q0101",e(we)))},{default:i(()=>[z(n(N.$t("app.agentTicketQuery.queryBtn")),1)]),_:1})]),_:1}),t("span",yp,[o(oe,{size:"14px",class:"mr-[5px]"},{default:i(()=>[o(e(Un))]),_:1}),z(n(e(U)),1)])]),_:1},8,["model","rules"]))],64)):(s(),m("span",hp,"PNR：-"))])]),o(ec,{"all-passenger-list":e(g),"clac-amount-info":e(D),"refund-ticket-success":e(B),onQueryTicketDetail:e(Y),onQueryTicketAndCalcAmount:e(d)},null,8,["all-passenger-list","clac-amount-info","refund-ticket-success","onQueryTicketDetail","onQueryTicketAndCalcAmount"]),e(b).length?(s(),re(ep,{key:0,"ticket-detail-list":e(b),"refund-ticket-success":e(B),"is-finish-manual-refund":e(te),"is-show-trfd-no":e(x),"part-success":e(ge),"manual-refund-amount-info":e(X),"ticket-amount-list":e(y).queryRefundFeeAggregateRespDTOList,"ticket-trfd-no-details":e(se),"refund-type":e(pe),"device-num":e(u),"is-auto-refund-finished":e($),"batch-auto-amount":e(P)},null,8,["ticket-detail-list","refund-ticket-success","is-finish-manual-refund","is-show-trfd-no","part-success","manual-refund-amount-info","ticket-amount-list","ticket-trfd-no-details","refund-type","device-num","is-auto-refund-finished","batch-auto-amount"])):W("",!0),o(yc,{ref_key:"amountRef",ref:v,"batch-auto-amount":e(P),"amount-data":e(y).queryRefundFeeAggregateRespDTOList,status:e(y).status,"is-auto-refund-finished":e($),"is-finish-manual-refund":e(te),"manual-refund-amount-info":e(X),"is-can-refund":e(q),onGetCalcInfo:e(C)},null,8,["batch-auto-amount","amount-data","status","is-auto-refund-finished","is-finish-manual-refund","manual-refund-amount-info","is-can-refund","onGetCalcInfo"]),t("div",vp,[e(B)?W("",!0):(s(),m(xe,{key:0},[$e((s(),re(ie,{"data-gid":"091Q0105",disabled:!e(D).isAlreadySuccessSearch||!e(q),onClick:e(le)},{default:i(()=>[z(n(N.$t("app.agentTicketRefund.refund")),1)]),_:1},8,["disabled","onClick"])),[[I,"crs-ticket-manage-refund-page-refund-button"]]),$e((s(),re(ie,{disabled:!e(q),onClick:e(L)},{default:i(()=>[z(n(N.$t("app.agentTicketRefund.manualRefundBtn")),1)]),_:1},8,["disabled","onClick"])),[[I,"crs-ticket-manage-refund-page-manual-refund-button"]])],64)),o(ie,{"data-gid":"091Q0104",onClick:e(Te)},{default:i(()=>[z(n(N.$t("app.agentTicketRefund.refresh")),1)]),_:1},8,["onClick"])]),e(h)?(s(),re(Gr,{key:1,modelValue:e(h),"onUpdate:modelValue":_[2]||(_[2]=F=>Ue(h)?h.value=F:null),"ticket-detail-list":e(b),"onUpdate:ticketDetailList":_[3]||(_[3]=F=>Ue(b)?b.value=F:null),isShowTrfdNo:e(x),"onUpdate:isShowTrfdNo":_[4]||(_[4]=F=>Ue(x)?x.value=F:null),"all-passengers":e(g),"is-dragon-boat-office":e(k),"pnr-no":e(a),"is-xepnr":e(A),"tkt-no":e(V),"package-data":e(E),"device-num":e(u),onQueryTicketDetail:e(Y),onGetManualRefundAmountDetail:e(j),onGetTicketTrfdNoDetails:e(R)},null,8,["modelValue","ticket-detail-list","isShowTrfdNo","all-passengers","is-dragon-boat-office","pnr-no","is-xepnr","tkt-no","package-data","device-num","onQueryTicketDetail","onGetManualRefundAmountDetail","onGetTicketTrfdNoDetails"])):W("",!0),e(J)?(s(),re(pp,{key:2,modelValue:e(J),"onUpdate:modelValue":_[5]||(_[5]=F=>Ue(J)?J.value=F:null),"ticket-detail-list":e(b),"new-pnr":e(T),"old-pnr":e(a),onRefresh:e(ne)},null,8,["modelValue","ticket-detail-list","new-pnr","old-pnr","onRefresh"])):W("",!0)])),[[ve,e(r),void 0,{fullscreen:!0,lock:!0}]])}}});const Tn=vt(_p,[["__scopeId","data-v-24068121"]]),Tp=(c,p)=>{const{t:l}=et(),f=Dt(),T=Le(()=>{var P;return(P=f.state.user)==null?void 0:P.entityType}),k=yt({pnrNo:""}),a=O(),v=O(),r=O({travellerInfoList:[]}),g=O([]),b=O(!1),h=Be(!1),E=O([]),y={ticketManagementOrganizationCode:[{required:!0,message:l("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:["change","blur"]}],ticketMachineNumber:[{required:!0,message:l("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber"),trigger:["change","blur"]},{pattern:Vt,trigger:["change","blur"],message:l("app.agentTicketQuery.repelTicket.ticketMachineNumberReg")}],pnrNo:[{required:!0,message:l("app.agentTicketQuery.repelTicket.pnrNotAllowNull"),trigger:["change","blur"]},{pattern:dn,message:l("app.agentTicketQuery.repelTicket.pnrReg"),trigger:["change","blur"]}]},D=Le(()=>{var P;return!(k.pnrNo&&((P=g.value)==null?void 0:P.length)>0)}),$=Le(()=>{var N,_;const P=r.value.travellerInfoList.filter(w=>w.alreadyInvalid)??[];return!!((_=(N=g.value)==null?void 0:N[0])!=null&&_.crsPnrNo)&&r.value.travellerInfoList.length>0&&g.value.length>0&&r.value.travellerInfoList.length===g.value.length+P.length}),A=Le(()=>{const P=r.value.travellerInfoList.filter(N=>N.invalid)??[];return r.value.travellerInfoList.length>0&&g.value.length>0&&P.length===g.value.length}),V=Le(()=>r.value.travellerInfoList.every(P=>!P.invalid)),u=Le(()=>!k.pnrNo),te=P=>{if(!P){g.value=[];return}g.value=r.value.travellerInfoList.filter(N=>N.invalid)??[]},X=()=>({pnrNo:k.pnrNo??""}),ge=P=>{var _,w;return(E.value??[]).some(ce=>P===ce.value)&&P?P:((w=(_=E.value)==null?void 0:_[0])==null?void 0:w.value)??""},q=P=>{var N;[r.value.travellerInfoList,g.value,h.value]=[[],[],!0],r.value.travellerInfoList=rt(P)??[],r.value.travellerInfoList.forEach(_=>{_.ticketManagementOrganizationCode=ge(_.ticketManagementOrganizationCode),_.ticketMachineNumber="",_.ticketMachineNumberError="",_.ticketOrganizationError=""}),((N=r.value.travellerInfoList)==null?void 0:N.length)===1&&r.value.travellerInfoList[0].invalid&&g.value.push(r.value.travellerInfoList[0])},Y=async()=>{a.value.validate(async P=>{if(!P)return;const N=De("09200129"),_=(await Wt(qs(X(),N))).data.value;q(_)})},L=()=>g.value.map(P=>({etNo:P.ticketNo??"",passengerName:Xt.encode(P.passengerNameSuffix)??"",etType:P.etType??"",passengerType:P.specialPassengerType??"",printerNo:P.ticketMachineNumber??"",ticketTypeCode:P.ticketTypeCode,governmentPurchase:P.governmentPurchase,pnrNo:P.crsPnrNo||k.pnrNo,paymentBOP:P.paymentBOP,ticketManagementOrganizationCode:P.ticketManagementOrganizationCode})),le=()=>({invalidTicketDetails:L(),xePnr:b.value}),C=()=>{nt.close();const P=`/v2/crs/pnrManagement?pnrNo=${k.pnrNo}`;mn.setLink(P),c("update:modelValue",!1)},d=async P=>nt({message:P,icon:Pe("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"success-message-common crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,showCancelButton:!1,confirmButtonText:l("app.agentTicketQuery.repelTicket.confirm")}),j=async P=>{b.value&&P?await un(l("app.agentTicketQuery.repelTicket.repelTicketXEPnrSuccess")):b.value&&!P?await d(Pe("div",null,[Pe("span",{class:"block"},l("app.agentTicketQuery.repelTicket.repelTicketSuccess")),Pe("span",null,l("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_1")),Pe("span",{class:"text-brand-2 cursor-pointer",onClick:C},l("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_2")),Pe("span",null,l("app.agentTicketQuery.repelTicket.repelTicketSuccessXEPnrFail_3"))])):await d(Pe("div",null,[Pe("span",{class:"block"},l("app.agentTicketQuery.repelTicket.repelTicketSuccess")),Pe("span",null,l("app.agentTicketQuery.repelTicket.repelTicketSuccessNoXEPnrTip_1"))])),c("update:modelValue",!1),c("reQueryTicket")},Te=P=>(P??[]).reduce((N,_)=>{let w="";N||(w=`<p class="text-gray-1 text-lg font-normal pb-2.5">${l("app.agentTicketQuery.repelTicket.partRepelTicketSuccess")}</p>`);const ce=_.vtSuccess?'<i class="iconfont icon-ticket text-emerald-600 mr-2.5"></i>':'<i class="iconfont icon-close text-rose-600 mr-2.5"></i>',ie=`${w}<p class="mt-4">${ce}${_.passengerNameSuffix}<span class="text-center text-gray-3 text-xs font-normal inline-block bg-gray-7 px-1 py-0.5 text-gray-3 leading-tight ml-2.5"><span>${Ct(_.specialPassengerType)}</span></span></p>`;return N+ie},""),se=P=>{nt.confirm(Te(P),{icon:Pe(at,{color:"#FF3636",size:32},()=>Pe(ua)),customClass:"invalidated-warning-msg",closeOnClickModal:!1,showClose:!0,showCancelButton:!1,confirmButtonText:l("app.agentTicketQuery.repelTicket.confirm"),dangerouslyUseHTMLString:!0,draggable:!0}).then(()=>{c("update:modelValue",!1),c("reQueryTicket")})},x=P=>{r.value.travellerInfoList[P].ticketMachineNumberError="",r.value.travellerInfoList=[...r.value.travellerInfoList]},R=()=>{const P=g.value.map(N=>N.ticketNo);r.value.travellerInfoList.forEach(N=>{P.includes(N.ticketNo)||(N.ticketMachineNumberError="")})},pe=()=>{const P=g.value.map(N=>N.ticketNo);for(let N=0;N<r.value.travellerInfoList.length;N++){const _=r.value.travellerInfoList[N];if(P.includes(_.ticketNo)&&he(_.ticketManagementOrganizationCode)){if(!_.ticketMachineNumber){_.ticketMachineNumberError=l("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber");continue}if(!Vt.test(_.ticketMachineNumber)){_.ticketMachineNumberError=l("app.agentTicketQuery.repelTicket.ticketMachineNumberReg");continue}}else _.ticketMachineNumberError=""}return r.value.travellerInfoList.every(N=>!N.ticketMachineNumberError)},Re=()=>{const P=g.value.map(N=>N.ticketNo);for(let N=0;N<r.value.travellerInfoList.length;N++){const _=r.value.travellerInfoList[N];if(P.includes(_.ticketNo)&&!_.ticketManagementOrganizationCode){_.ticketOrganizationError=l("app.agentTicketQuery.repelTicket.plsInputTicketMachineNumber");continue}}return r.value.travellerInfoList.every(N=>!N.ticketOrganizationError)},we=()=>{const P=De("091P0102");g.value.length!==0&&v.value.validate(async N=>{if(!N||!Re()||!pe())return;const{entireSuccess:_,invalidTicketItems:w,xePnrSuccess:ce}=(await Wt(Gs(le(),P))).data.value;_?j(ce):!_&&w.length>0&&se(w)})},me=()=>{c("update:modelValue",!1)},he=P=>!["CDS","GPCDS"].includes(P),ue={BSP:{label:l("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:l("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:l("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:l("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:l("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:l("app.agentTicketQuery.OWNTicket"),value:"ARL"}},U=()=>{var P,N,_,w,ce,ie,oe,Se,de,I;((P=T.value)!=null&&P.includes("$$$")||(N=T.value)!=null&&N.includes("BSP"))&&(E.value.push(ue.BSP),E.value.push(ue.GPBSP)),!((_=T.value)!=null&&_.includes("BSP"))&&((w=T.value)!=null&&w.includes("GP"))&&E.value.push(ue.GPBSP),((ce=T.value)!=null&&ce.includes("$$$")||(ie=T.value)!=null&&ie.includes("BOP"))&&E.value.push(ue.BOPBSP),((oe=T.value)!=null&&oe.includes("$$$")||(Se=T.value)!=null&&Se.includes("CDS"))&&(E.value.push(ue.CDS),E.value.push(ue.GPCDS)),((de=T.value)!=null&&de.includes("$$$")||(I=T.value)!=null&&I.includes("本票"))&&E.value.push(ue.ARL)},J=P=>{var _,w,ce,ie,oe,Se,de,I,ve,F,Z,Q,ee;return{passengerName:((_=P==null?void 0:P[0])==null?void 0:_.passengerName)??"",passengerNameSuffix:((w=P==null?void 0:P[0])==null?void 0:w.passengerNameSuffix)??"",passengerType:((ce=P==null?void 0:P[0])==null?void 0:ce.passengerType)??"",pnrPsgType:"",specialPassengerType:((ie=P==null?void 0:P[0])==null?void 0:ie.specialPassengerType)??"",ticketNo:((oe=P==null?void 0:P[0])==null?void 0:oe.ticketNo)??"",invalid:(Se=P==null?void 0:P[0])==null?void 0:Se.invalid,etType:(de=P==null?void 0:P[0])==null?void 0:de.etType,alreadyInvalid:(I=P==null?void 0:P[0])==null?void 0:I.alreadyInvalid,ticketMachineNumber:"",crsPnrNo:((ve=P==null?void 0:P[0])==null?void 0:ve.crsPnrNo)??"",ticketTypeCode:((F=P==null?void 0:P[0])==null?void 0:F.ticketTypeCode)??"D",governmentPurchase:((Z=P==null?void 0:P[0])==null?void 0:Z.governmentPurchase)??!1,paymentBOP:((Q=P==null?void 0:P[0])==null?void 0:Q.paymentBOP)??!1,ticketManagementOrganizationCode:ge((ee=P==null?void 0:P[0])==null?void 0:ee.ticketManagementOrganizationCode),ticketMachineNumberError:"",ticketOrganizationError:""}},ne=async()=>{var ie,oe;const P=p.invalidatedTicketQueryGid,{etNumber:N,secondFactor:_}=p.ticketOperationCondition,w=(await Wt(Hs({ticketNo:N,secondFactor:_},P))).data.value,ce=J(w);ce.invalid&&(r.value.travellerInfoList.push(ce),g.value.push(ce),k.pnrNo=((oe=(ie=g.value)==null?void 0:ie[0])==null?void 0:oe.crsPnrNo)??"")},B=async(P,N)=>{var ce;const _=De("091P0103"),w=((ce=(await Wt(xa(N.split("-").length>2?N.substring(0,14):N,_))).data.value)==null?void 0:ce.data)??{};r.value.travellerInfoList[P].ticketMachineNumber=w.ticket.printNumber,r.value.travellerInfoList[P].paymentBOP=w.ticket.ticketManagementOrganizationCode==="BOP",r.value.travellerInfoList[P].governmentPurchase=w.ticket.ticketManagementOrganizationCode==="GP",r.value.travellerInfoList=[...r.value.travellerInfoList],w.ticket.printNumber&&(r.value.travellerInfoList[P].ticketMachineNumberError="")};return ct(async()=>{var P,N;U(),(P=p.ticketOperationCondition)!=null&&P.pnrNo&&(k.pnrNo=(N=p.ticketOperationCondition)==null?void 0:N.pnrNo),ne()}),bt(()=>$.value,()=>{$.value||(b.value=!1)}),{closeDialog:me,formData:k,REPEL_TICKET_RULES:y,travellerInfos:r,repelTicketBtnDisabled:D,isCheckAll:A,isCheckAllDisabled:V,queryBtnDisabled:u,showXEPnr:$,checkedXEPnr:b,isQueriedTravelers:h,ticketOrganizationList:E,dealCheckAllOperate:te,checkTravellerIndexList:g,repelTicketFormRef:a,repelTicketPassengerFormRef:v,confirmSearchOperate:Y,confirmRepelTicketOperate:we,queryRtkt:B,clearRepelTicketFormItemValidate:x,clearRepelTicketFormValidate:R,isShowPrintNo:he}},bp=t("i",{class:"iconfont icon-close"},null,-1),xp=[bp],Np={class:"w-full px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 inline-flex"},$p={class:"justify-start items-center gap-1 inline-flex"},Rp=t("div",{class:"w-4 h-4 relative"},[t("em",{class:"u-icon iconfont icon-warning-circle-fill text-yellow-1 relative bottom-1"})],-1),Cp={class:"text-yellow-1 text-xs font-normal leading-tight"},wp={class:"mt-5 mb-[10px]"},Sp={class:"w-full h-[auto] p-2.5 bg-gray-0 rounded border border-brand-3 flex-col justify-start items-start inline-flex"},Pp={key:0},Dp=t("span",null,null,-1),Ap={class:"flex items-center w-[176px]"},Ep={class:"text-center text-gray-3 text-xs font-normal inline-block bg-gray-7 px-1 py-0.5 text-gray-3 leading-tight"},Op={class:"h-[auto] text-gray-2 text-sm font-normal leading-snug grow flex flex-inline items-center"},Fp=t("i",{class:"iconfont icon-ticket-fill mr-1.5 text-gray-400"},null,-1),Vp={class:"carType-option-panel"},Mp={key:1,class:"w-full text-center text-gray-4"},Lp={class:"text-center mt-4 flex justify-center items-center"},Bp={class:"crs-btn-dialog-ui repel-ticket"},Qp=He({__name:"RepelTicketDialog",props:{invalidatedTicketQueryGid:{},ticketOperationCondition:{}},emits:["update:modelValue","reQueryTicket"],setup(c,{emit:p}){const l=p,f=c,{closeDialog:T,formData:k,REPEL_TICKET_RULES:a,travellerInfos:v,repelTicketBtnDisabled:r,queryBtnDisabled:g,isCheckAll:b,isCheckAllDisabled:h,showXEPnr:E,checkedXEPnr:y,isQueriedTravelers:D,dealCheckAllOperate:$,checkTravellerIndexList:A,repelTicketFormRef:V,repelTicketPassengerFormRef:u,ticketOrganizationList:te,confirmSearchOperate:X,confirmRepelTicketOperate:ge,queryRtkt:q,clearRepelTicketFormItemValidate:Y,clearRepelTicketFormValidate:L,isShowPrintNo:le}=Tp(l,f);return(C,d)=>{const j=xt,Te=dt,se=st,x=pt,R=Ht,pe=qn,Re=Nt,we=at,me=Jt,he=Zt,ue=Ra,U=zt,J=qt,ne=ft,B=ht("trimUpper");return s(),re(ne,{title:C.$t("app.agentTicketQuery.repelTicket.repelTicket"),width:"720px","close-on-click-modal":!1,"show-close":!1,"align-center":!0,class:"repel-tikect-dialog tc-input-pad-init",onClose:e(T)},{default:i(()=>{var P;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:d[0]||(d[0]=(...N)=>e(T)&&e(T)(...N))},xp),t("div",Np,[t("div",$p,[Rp,t("div",Cp,n(C.$t("app.agentTicketQuery.repelTicket.repelTicketTip")),1)])]),t("div",wp,[o(x,{ref_key:"repelTicketFormRef",ref:V,model:e(k),rules:e(a),inline:!0,"label-position":"left","require-asterisk-position":"right",class:"w-full"},{default:i(()=>[o(Te,{prop:"pnrNo",label:"PNR"},{default:i(()=>[$e(o(j,{modelValue:e(k).pnrNo,"onUpdate:modelValue":d[1]||(d[1]=N=>e(k).pnrNo=N),placeholder:"PNR",clearable:""},null,8,["modelValue"]),[[B]])]),_:1}),o(Te,null,{default:i(()=>[o(se,{type:"primary","data-gid":"091P0101",disabled:e(g),onClick:e(X)},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.queryBtn")),1)]),_:1},8,["disabled","onClick"])]),_:1})]),_:1},8,["model","rules"])]),t("div",Sp,[((P=e(v).travellerInfoList)==null?void 0:P.length)>0?(s(),m(xe,{key:0},[e(D)?(s(),m(xe,{key:0},[e(D)?(s(),m("p",Pp,[o(R,{modelValue:e(b),"onUpdate:modelValue":d[2]||(d[2]=N=>Ue(b)?b.value=N:null),disabled:e(h),label:C.$t("app.agentTicketQuery.repelTicket.checkAll"),onChange:e($)},null,8,["modelValue","disabled","label","onChange"])])):W("",!0),o(pe,{"border-style":"dashed"})],64)):W("",!0),o(ue,{modelValue:e(A),"onUpdate:modelValue":d[3]||(d[3]=N=>Ue(A)?A.value=N:null),onChange:e(L)},{default:i(()=>[o(x,{ref_key:"repelTicketPassengerFormRef",ref:u,model:e(v),rules:e(a),"require-asterisk-position":"right",class:"passengr-form"},{default:i(()=>[(s(!0),m(xe,null,Ee(e(v).travellerInfoList,(N,_)=>(s(),m("div",{key:_+new Date,class:"h-auto w-[100%] flex items-center justify-between mb-[10px] last:mb-0"},[o(R,{label:N,class:"single-check",disabled:!N.invalid},{default:i(()=>[Dp]),_:2},1032,["label","disabled"]),t("div",Ap,[o(Re,{effect:"dark",placement:"top",content:N.passengerNameSuffix},{default:i(()=>[t("span",{class:Ve(["text-gray-1 text-sm font-normal leading-snug mr-2 inline-block overflow-hidden whitespace-nowrap text-ellipsis",e(Ct)(N.specialPassengerType).length>2?"max-w-[100px]":"max-w-[120px]"])},n(N.passengerNameSuffix),3)]),_:2},1032,["content"]),t("span",Ep,n(e(Ct)(N.specialPassengerType)),1)]),t("div",Op,[Fp,z(" "+n(N.ticketNo||"--")+" ",1),o(Te,{prop:"travellerInfoList."+_+".ticketManagementOrganizationCode",error:N.ticketOrganizationError,class:"w-[90px] ml-2.5"},{default:i(()=>[o(he,{modelValue:N.ticketManagementOrganizationCode,"onUpdate:modelValue":w=>N.ticketManagementOrganizationCode=w,class:"ticket-management-organization",disabled:N.ticketManagementOrganizationCode==="",placeholder:N.ticketManagementOrganizationCode===""?C.$t("app.agentTicketQuery.noData"):""},{default:i(()=>[(s(!0),m(xe,null,Ee(e(te),w=>(s(),re(me,{key:w.value,label:w.label,value:w.value},{default:i(()=>[t("div",Vp,[t("div",{class:Ve(N.ticketManagementOrganizationCode===w.value?"show-select":"hidden-select")},[o(we,null,{default:i(()=>[o(e(fn))]),_:1})],2),t("span",null,n(w.label),1)])]),_:2},1032,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","disabled","placeholder"])]),_:2},1032,["prop","error"])]),e(le)(N.ticketManagementOrganizationCode)?(s(),re(Te,{key:0,prop:"travellerInfoList."+_+".ticketMachineNumber",label:C.$t("app.agentTicketQuery.repelTicket.ticketMachineNumber"),error:N.ticketMachineNumberError},{default:i(()=>[o(It,{modelValue:e(v).travellerInfoList[_].ticketMachineNumber,"onUpdate:modelValue":w=>e(v).travellerInfoList[_].ticketMachineNumber=w,"select-class":"w-[120px]",onBlur:w=>e(Y)(_)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),o(Re,{content:C.$t("app.agentTicketQuery.repelTicket.queryIssueDeviceNo"),placement:"top"},{default:i(()=>[o(we,{class:"ml-[4px] cursor-pointer",color:"#455AF7","data-gid":"091P0103",onClick:w=>e(q)(_,N.ticketNo??"")},{default:i(()=>[o(e(fs))]),_:2},1032,["onClick"])]),_:2},1032,["content"])]),_:2},1032,["prop","label","error"])):W("",!0)]))),128))]),_:1},8,["model","rules"])]),_:1},8,["modelValue","onChange"])],64)):(s(),m("div",Mp,n(C.$t("app.agentTicketRefund.noPassengers")),1))]),t("div",Lp,[$e(o(J,{modelValue:e(y),"onUpdate:modelValue":d[4]||(d[4]=N=>Ue(y)?y.value=N:null),class:"ml-4"},{default:i(()=>[o(U,{label:!1},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.repelTicket.onlyRepelTicket")),1)]),_:1}),o(U,{label:!0},{default:i(()=>{var N;return[z(n(C.$t("app.agentTicketQuery.repelTicket.xePnrAndRepelTicket"))+"(PNR:"+n((N=e(A)[0])==null?void 0:N.crsPnrNo)+")",1)]}),_:1})]),_:1},8,["modelValue"]),[[hn,e(E)]]),t("span",Bp,[o(se,{type:"primary",disabled:e(r),onClick:e(ge)},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.repelTicket.confirmRepelTicket")),1)]),_:1},8,["disabled","onClick"]),o(se,{onClick:e(T)},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.repelTicket.cancel")),1)]),_:1},8,["onClick"])])])]}),_:1},8,["title","onClose"])}}});const Up=c=>{const{t:p}=et(),l=Dt(),f=O(!0),T=O(),k=O(""),a=O(!1),v=O(),r=O({authLevel:"oneLevel",office:""}),g={office:[{required:!0,message:p("app.agentTicketQuery.ticketAuth.officeTipOne"),trigger:"blur"},{pattern:vn,message:p("app.agentTicketQuery.ticketAuth.officeTipTwo")}]},b=O(!1),h=O(),E=Le(()=>{var q;return((q=l.state.user)==null?void 0:q.agent)??""}),y=async()=>{var q,Y,L,le,C,d,j;try{const Te=De("091N0203");a.value=!0;const se={ticketNumber:c.ticketNo},x=await Na(se,Te);((q=x==null?void 0:x.statusCode)==null?void 0:q.value)===200&&(k.value=((le=(L=(Y=x.data)==null?void 0:Y.value)==null?void 0:L.data.ticketDisplayAuthInfo)==null?void 0:le.bookOffice)??"",T.value=(j=(d=(C=x.data)==null?void 0:C.value)==null?void 0:d.data.ticketDisplayAuthInfo)==null?void 0:j.authInfoList.filter(R=>R.authTo!==""))}finally{a.value=!1}},D=async q=>{var Y;try{a.value=!0;const L={removeOffice:q,ticketNumber:c.ticketNo},le=De("091N0202");((Y=(await Vn(L,le)).data.value)==null?void 0:Y.code)==="200"&&(await ut({message:p("app.agentTicketQuery.ticketAuth.removeAuthSuccess"),type:"success"}),await y())}finally{a.value=!1}},$=()=>{b.value=!0},A=async q=>{q&&await q.validate(async Y=>{if(Y)try{const L=De("091N0201");a.value=!0;let le=!0;r.value.authLevel==="oneLevel"?le=!1:le=!0;const C={accreditOffice:r.value.office,reAuth:le,ticketNumber:c.ticketNo},d=[];d.push(Mn(C,L)),(await Promise.allSettled(d)).filter(se=>se.value.data.value.code!=="200").length<1&&(await ut({message:p("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await y())}finally{a.value=!1}})},V=async()=>{b.value=!1},u=()=>`TICKET_AUTH_OFFICE_${E.value}_`??"",te=()=>{try{return(JSON.parse(localStorage.getItem(`${u()}add`)??"")||[]).map(Y=>({value:Y}))}catch{return[]}},X=()=>{if(vn.test(r.value.office)){const q=te().map(L=>L.value).filter(L=>L!==r.value.office);q.unshift(r.value.office);const Y=q.slice(0,5);localStorage.setItem(`${u()}add`,JSON.stringify(Y))}},ge=q=>{var Y;r.value.office=q.value,(Y=v.value)==null||Y.blur()};return ct(async()=>{f.value=c.modelValue,y()}),bt(()=>r.value.office,q=>{r.value.office=(q==null?void 0:q.toUpperCase())??""}),{dialogTableVisible:f,tableData:T,authDelete:D,authFormRules:g,authForm:r,authSubmit:A,authTicketFormRef:h,showAdd:$,addAuthVisible:b,office:k,authCancel:V,showLoading:a,officeHistoryRef:v,loadOfficeHistory:te,saveOfficeHistory:X,selectOfficeHistory:ge}},Ua=c=>(Ut("data-v-3b5cf850"),c=c(),jt(),c),jp=Ua(()=>t("i",{class:"iconfont icon-close"},null,-1)),Ip=[jp],zp={class:"w-full px-0 pt-0.5 pb-5 bg-gray-0 rounded-md flex-col justify-start gap-5 inline-flex"},qp={class:"self-stretch justify-between items-center inline-flex"},Gp={class:"text-gray-1 text-lg font-bold leading-normal"},Hp={class:"self-stretch h-full flex-col justify-center items-center gap-2.5 flex w-[632px]"},Kp={class:"self-stretch justify-between items-center inline-flex"},Yp={class:"justify-start items-center gap-[68px] flex"},Wp={class:"text-gray-1 text-sm font-normal leading-snug"},Xp={class:"text-gray-1 text-sm font-normal leading-snug"},Jp={class:"flex-col justify-start items-start inline-flex"},Zp={class:"self-stretch p-2.5 rounded border border-brand-3 justify-start items-start inline-flex"},ef={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},tf={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},nf={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},af={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},sf={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},of={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},lf={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},rf={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},cf={key:0},uf={key:1},df={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},pf={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},ff={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},mf=Ua(()=>t("i",{class:"cursor-pointer primary-color iconfont icon-delete text-brand-2"},null,-1)),gf={key:0,class:"self-stretch flex-col justify-start items-start gap-3.5 flex add-auth-tkt"},kf={class:"self-stretch h-9 px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 flex"},yf={class:"h-5 justify-start items-center inline-flex text-yellow-1"},hf={class:"text-yellow-1 text-xs font-normal leading-tight"},vf={class:"w-[632px] justify-start items-center gap-2.5 inline-flex"},_f={class:"justify-start items-center gap-2.5 flex"},Tf={class:"justify-start items-center flex"},bf={class:"text-gray-2 text-xs font-normal leading-tight"},xf={class:"text-gray-2 text-xs font-normal leading-tight"},Nf={class:"justify-start items-center flex crs-btn-dialog-ui"},$f=He({__name:"AuthTicketDialog",props:{ticketNo:{}},emits:["update:modelValue"],setup(c){const p=c,{dialogTableVisible:l,tableData:f,authDelete:T,authForm:k,authFormRules:a,authSubmit:v,authTicketFormRef:r,showAdd:g,addAuthVisible:b,office:h,authCancel:E,showLoading:y,officeHistoryRef:D,loadOfficeHistory:$,saveOfficeHistory:A,selectOfficeHistory:V}=Up(p);return(u,te)=>{const X=st,ge=at,q=$a,Y=zt,L=qt,le=dt,C=La,d=pt,j=ft,Te=wt;return s(),re(j,{modelValue:e(l),"onUpdate:modelValue":te[4]||(te[4]=se=>Ue(l)?l.value=se:null),"align-center":"true",width:"680px","close-on-click-modal":!1,class:"preview-dialog tickets-empower","show-close":!1},{default:i(()=>[$e((s(),m("div",null,[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:te[0]||(te[0]=se=>u.$emit("update:modelValue",!1))},Ip),t("div",zp,[t("div",qp,[t("div",Gp,n(u.$t("app.agentTicketQuery.ticketAuth.title")),1)]),t("div",Hp,[t("div",Kp,[t("div",Yp,[t("div",Wp,n(u.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+"："+n(u.ticketNo),1),t("div",Xp,n(u.$t("app.agentTicketQuery.ticketAuth.ticketingOfficeNumber"))+"："+n(e(h)),1)]),t("div",Jp,[o(X,{size:"small",type:"primary",link:"",onClick:e(g)},{default:i(()=>[z(n(u.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1},8,["onClick"])])]),t("div",Zp,[t("div",ef,[t("div",tf,[t("div",nf,n(u.$t("app.agentTicketQuery.ticketAuth.officeNum")),1)]),(s(!0),m(xe,null,Ee(e(f),(se,x)=>(s(),m("div",{key:x,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",af,n(se.authTo),1)]))),128))]),t("div",sf,[t("div",of,[t("div",lf,n(u.$t("app.agentTicketQuery.ticketAuth.authAuthority")),1)]),(s(!0),m(xe,null,Ee(e(f),(se,x)=>(s(),m("div",{key:x,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",rf,[se.reAuth?(s(),m("span",cf,[o(ge,null,{default:i(()=>[o(e(fa))]),_:1})])):(s(),m("span",uf,n(u.$t("app.agentTicketQuery.ticketAuth.noAuth")),1))])]))),128))]),t("div",df,[t("div",pf,[t("div",ff,n(u.$t("app.agentTicketQuery.ticketAuth.operation")),1)]),(s(!0),m(xe,null,Ee(e(f),(se,x)=>(s(),m("div",{key:x,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-5 inline-flex"},[o(q,{title:u.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),icon:e(an),teleported:!1,width:"160","icon-color":"var(--bkc-el-color-primary)","cancel-button-type":"button",onConfirm:R=>e(T)(se.authTo)},{reference:i(()=>[mf]),_:2},1032,["title","icon","onConfirm"])]))),128))])]),e(b)?(s(),m("div",gf,[t("div",kf,[t("div",yf,[o(ge,{class:"tooltip-icon mr-[8px]"},{default:i(()=>[o(e(Un))]),_:1}),t("div",hf,n(u.$t("app.agentTicketQuery.ticketAuth.addTipsTwo")),1)])]),t("div",vf,[t("div",_f,[o(d,{ref_key:"authTicketFormRef",ref:r,model:e(k),inline:"",size:"small",rules:e(a),"require-asterisk-position":"right"},{default:i(()=>[o(le,null,{default:i(()=>[o(L,{modelValue:e(k).authLevel,"onUpdate:modelValue":te[1]||(te[1]=se=>e(k).authLevel=se)},{default:i(()=>[t("div",Tf,[o(Y,{label:"oneLevel",size:"large"},{default:i(()=>[t("span",bf,n(u.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),o(Y,{label:"twoLevel",size:"large"},{default:i(()=>[t("span",xf,n(u.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1}),o(le,{label:u.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:i(()=>[o(C,{ref_key:"officeHistoryRef",ref:D,modelValue:e(k).office,"onUpdate:modelValue":te[2]||(te[2]=se=>e(k).office=se),modelModifiers:{trim:!0},"fetch-suggestions":e($),placeholder:u.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e(V),onBlur:e(A)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),t("div",Nf,[e(b)?(s(),re(X,{key:0,type:"primary","data-gid":"091N0206",onClick:te[3]||(te[3]=se=>e(v)(e(r)))},{default:i(()=>[z(n(u.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})):W("",!0),e(b)?(s(),re(X,{key:1,onClick:e(E)},{default:i(()=>[z(n(u.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1},8,["onClick"])):W("",!0)])])])):W("",!0)])])])),[[Te,e(y)]])]),_:1},8,["modelValue"])}}});const Rf=vt($f,[["__scopeId","data-v-3b5cf850"]]),Cf="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";let wf=(c=21)=>{let p="",l=crypto.getRandomValues(new Uint8Array(c|=0));for(;c--;)p+=Cf[l[c]&63];return p};const Sf=(c,p)=>{const{t:l}=et(),{copy:f,isSupported:T}=jn({legacy:!0}),k=O({}),a=Be(!1),v=O(!1),r=O(""),g=O(!1),b=O([]),h=O([]),E=H=>be=>{be&&(b.value[H]={el:be.$el||be})},y=H=>be=>{be&&(h.value[H]=be)},D=O([]),$=H=>{D.value[H]=!0},A=H=>{D.value[H]=!1},V=O(!1),u=Le(()=>Y.value.tssType=="Suspend"?l("app.ticketStatus.suspendFlightConfirm"):l("app.ticketStatus.unsuspendFlightConfirm")),te=Le(()=>Y.value.tssType=="Suspend"?l("app.ticketStatus.suspendFlightCheck"):l("app.ticketStatus.unsuspendFlightCheck")),X=O({pnrTss:!1,issueDate:""}),ge=O(),q=O(""),Y=O({tssType:"",ticketNo:"",pnrNo:""}),L=Be({render(){return Pe("em",{class:"iconfont icon-calendar"})}}),le={issueDate:[{validator:(H,be,Ie)=>ae(!!X.value.pnrTss,H,be,Ie),trigger:"blur"}]},C=Be(!1),d=O(!1),j=Be(!1),Te=Be(!1),se=O(""),x=O(""),R=O(""),pe=O(""),Re=O({}),we={segId:0,couponStatus:"",ticketNumber:"",printNo:"",ticketManagementOrganizationCode:""},me=O({}),he=Be(""),ue=Be(""),U=H=>H==null?void 0:H.some(be=>be==="OPEN FOR USE"),J=H=>H==null?void 0:H.some(be=>pa.includes(be)),ne=H=>H==null?void 0:H.every(be=>be==="OPEN FOR USE"),B=H=>H==null?void 0:H.some(be=>["REFUNDED","USED/FLOWN"].includes(be)),P=H=>H==null?void 0:H.every(be=>be==="REFUNDED"),N=H=>H==null?void 0:H.every(be=>["EXCHANGED","FIM EXCH"].includes(be)),_=H=>H==null?void 0:H.some(be=>be==="SUSPENDED"),w=H=>H==null?void 0:H.every(be=>be==="VOID"),ce=H=>H.includes("CHECKED IN")||H.includes("LIFT/BOARDED"),ie=H=>(H==null?void 0:H.some(be=>be==="OPEN FOR USE"))&&(H==null?void 0:H.every(be=>!["EXCHANGED","REFUNDED"].includes(be))),oe=H=>(H==null?void 0:H.some(be=>["OPEN FOR USE","AIRPORT CNTL"].includes(be)))&&(H==null?void 0:H.every(be=>!["EXCHANGED","REFUNDED"].includes(be))),Se=H=>["OPEN FOR USE","EXCHANGED"].every(be=>H.includes(be))||["OPEN FOR USE","FIM EXCH"].every(be=>H.includes(be)),de=H=>J(H),I=H=>ne(H)||P(H)||N(H)||U(H),ve=H=>ne(H)||Se(H)||ie(H),F=H=>H==null?void 0:H.every(be=>["VOID","REFUNDED","USED/FLOWN","EXCHANGED","SUSPENDED"].includes(be)),Z=ms({ticketType:"",ticketNo:"",ticketManagementOrganizationCode:"",secondFactor:{secondFactorCode:"",secondFactorValue:""}}),Q=O([]),ee=O([]),fe=O({}),Ce=O({}),Oe=yt({pnrNo:"",ticketTypeCode:"",etNumber:"",secondFactor:{secondFactorCode:"",secondFactorValue:""}}),ye=Be(!1),Ye=H=>(H||"").replace(/\sINF\((.+?)\)/g,""),qe=(H,be)=>{var Ie;switch(q.value=De("091P0101"),Oe.etNumber=H.etNumber,Oe.ticketTypeCode=H.ticketTypeCode??"D",Oe.secondFactor=H.secondFactor,Oe.pnrNo=((Ie=H.airSeg.find(je=>je.crsPnrNo&&je.flightNo!=="ARNK"))==null?void 0:Ie.crsPnrNo)??"",be){case"repealTicket":d.value=!0;break}},Qe=O(!1),ae=(H,be,Ie,je)=>{if(!Ie&&H){je(new Error(l("app.agentTicketRefund.must")));return}je()},ot=H=>{pe.value=H,Qe.value=!0},S=(H,be)=>{const Ie={title:`${l("app.agentTicketRefund.refund")}${H}`,name:`refund?type=ticketNo&sign=${H}`,content:sn(Tn)};p("addNewTab",Ie,"",be)},G=(H,be)=>{Y.value.tssType=be,Y.value.ticketNo=H.etNumber??"",Y.value.pnrNo=H.airSegCrsPnr??"",X.value.issueDate=H.issueTicketDate?it(H.issueTicketDate).format("YYYY-MM-DD"):"",V.value=!0},Ne=async()=>{const H=De("091T0202");c.queryType==="2"&&X.value.pnrTss?ge.value.validate(async be=>{be&&(await Wt(Ks({pnrNo:Y.value.pnrNo,tssType:Y.value.tssType,issueDate:it(X.value.issueDate).format("YYYY-MM-DD")},H)),await Ke(),V.value=!1)}):(await Wt(Ys({ticketNumber:Y.value.ticketNo,tssType:Y.value.tssType},H)),await Ke(),V.value=!1)},Ae=async(H,be)=>{var je,mt;const Ie=Ft.service({fullscreen:!0});try{const lt=await Zs(H,be);return Z.ticketManagementOrganizationCode=((mt=(je=lt==null?void 0:lt.data)==null?void 0:je.value)==null?void 0:mt.data)??"",!0}catch{return!0}finally{Ie.close()}},Fe=async(H,be)=>{se.value="changeTicketStatus",we.ticketNumber=H,we.segId=be.segmentIndex,we.couponStatus=be.ticketStatus==="AIRPORT CNTL"?"OPEN FOR USE":be.ticketStatus;const Ie=De("091T0204");if(!await Ae(H,Ie))return;const je=be.ticketStatus==="REFUNDED"?'<span class="status-open"> OPEN FOR USE </span>':'<span class="status-refunded"> REFUNDED </span>',mt=be.ticketStatus==="OPEN FOR USE"||be.ticketStatus==="AIRPORT CNTL"?`${l("app.ticketStatus.generate")}`:`${l("app.ticketStatus.delete")}`;await nt.confirm(`<div class="message-header w-[508px]">
        <em class="iconfont icon-warning-circle-fill"></em>
        <span>${l("app.ticketStatus.tip")}${mt}。</span>
      </div>
     <div class="message-cotent mb-[18px]">
      <em class="iconfont icon-info-circle-line"></em>
      <span style="color: var(--bkc-color-gray-1);font-size: 18px !important;">${l("app.ticketStatus.changeStatus")}<span/>
      <span>${je}${l("app.ticketStatus.status")}?</span>
     </div>`,{customClass:"ticket-status-dialog crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.ticketStatus.confirmBtn"),cancelButtonText:l("app.ticketStatus.cancelBtn"),dangerouslyUseHTMLString:!0,showClose:!1}).then(()=>{C.value=!0})},We=async(H,be,Ie,je)=>{var lt,Mt;x.value=be??"",R.value=Ie??"",Z.ticketManagementOrganizationCode=je;const mt=Ft.service({fullscreen:!0});if(se.value==="ticketRefundForm"){const _t={ticketNo:Z.ticketNo,ticketType:Ie||Z.ticketType,printerNo:be,ticketManagementOrganizationCode:je,secondFactor:Z.secondFactor};try{if(ye.value){const{data:Tt}=await Ws(_t,H);me.value=(lt=Tt.value)==null?void 0:lt.data,Te.value=!0}else{const{data:Tt}=await on(_t,H);me.value=(Mt=Tt.value)==null?void 0:Mt.data,me.value.ticketManagementOrganizationCode=je,g.value=!0}}finally{mt.close()}}else{we.printNo=x.value,we.ticketManagementOrganizationCode=je??"";try{const _t=De("091T0201");(await Xs(we,_t)).data.value==="OK"&&Ke()}finally{mt.close()}}},Je=(H,be)=>{const Ie=De("091T0104");Xe(H,be,Ie)},tt=(H,be)=>{const Ie=De("091T0103");Xe(H,be,Ie)},Xe=async(H,be,Ie)=>{const je=H&&be.ticketTypeCode==="I"&&be.conjunctiveTicket?be.conjunctiveTicket.slice(0,14):be.etNumber;!H&&!await Ae(je,Ie)||(Z.ticketNo=je,Z.ticketType=be.ticketTypeCode??"",Z.secondFactor=be.secondFactor,ye.value=H,C.value=!H,se.value="ticketRefundForm",H&&We(Ie))},Ke=()=>{p("reQueryTicket")},At=async H=>{const be=De("091T0109"),{etNumber:Ie,secondFactor:je}=H,mt=Ft.service({fullscreen:!0});try{const{data:lt}=await _o({tktNo:Ie,secondFactor:je},be);k.value=lt.value,a.value=!0}finally{mt.close()}},Et=H=>{const be=`/v2/crs/pnrManagement?pnrNo=${H}`;mn.setLink(be)},en=(H,be,Ie)=>{Te.value=!1,he.value=H,ue.value=be,Z.ticketManagementOrganizationCode=Ie,j.value=!0},gt=async(H,be,Ie)=>{var lt;ye.value=!1,se.value="ticketRefundForm";const je=Ft.service({fullscreen:!0}),mt={ticketNo:Z.ticketNo,ticketType:R.value?R.value:Z.ticketType,printerNo:be,ticketManagementOrganizationCode:Ie,refundNo:H,secondFactor:Z.secondFactor};try{const Mt=De("091T0104"),{data:_t}=await on(mt,Mt);me.value=(lt=_t.value)==null?void 0:lt.data,me.value.ticketManagementOrganizationCode=Z.ticketManagementOrganizationCode,g.value=!0}finally{je.close()}},xn=async(H,be,Ie)=>{const je=`<p class="text-gray-1 text-lg font-normal">${l("app.agentTicketQuery.pullControlTip")}<span class="text-green-1 text-lg font-bold pl-1">OPEN FOR USE</span></p>
      <p class="text-gray-1 text-lg font-normal">${l("app.agentTicketQuery.pullControlConfirmTip")}</p>`;await nt.confirm(je,{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.ticketStatus.confirmBtn"),cancelButtonText:l("app.ticketStatus.cancelBtn"),dangerouslyUseHTMLString:!0,showClose:!1});const mt={couponNo:[H+1],remoteAirline:be??"",ticketNo:Ie??""},lt=De("091T0203");await Wt(Js(mt,lt)),await Ke()},Nn=(H,be,Ie)=>{if(be==="INF"){nt.confirm(l("app.agentTicketRefund.notAllowInfantOnlyChange"),{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",showCancelButton:!1,confirmButtonText:l("app.agentTicketRefund.sure"),showClose:!1});return}v.value=!0,r.value=H,Re.value=Ie},$n=H=>{T&&(f(H),ut({message:l("app.batchRefund.copySuccess"),type:"success"}))},Rn=()=>{var H;(H=ge.value)==null||H.resetFields(),Y.value.tssType="",Y.value.ticketNo="",Y.value.pnrNo="",X.value.issueDate="",X.value.pnrTss=!1,V.value=!1},Cn=H=>{Q.value[H].showTktPopover=!1};return bt(()=>c.queryTicketRes,()=>{c.queryType==="6"?Ce.value=rt(c.queryTicketRes):c.queryType==="5"?fe.value=rt(c.queryTicketRes):c.queryType!=="4"?(Q.value=rt(c.queryTicketRes),(Q.value??[]).forEach((H,be)=>{var lt,Mt;const Ie=[...new Set((lt=H.airSeg)==null?void 0:lt.filter(_t=>_t.flightNo!=="ARNK").map(_t=>{var Tt;return(Tt=_t.ticketStatus)==null?void 0:Tt.trim()}))],je=[...new Set((Mt=H.airSeg)==null?void 0:Mt.map(_t=>{var Tt;return(Tt=_t.ticketStatus)==null?void 0:Tt.trim()}))];H.key=wf(),H.isOpen=ne(je),H.showTktPopover=c.queryType==="1"&&be===0,w(je)||ce(je)||(H.canRefund=de(je),H.canRebook=ve(je),H.canVoid=ne(je)||oe(je),H.canTktAuth=I(Ie),H.canSuspended=ne(je)||ie(je),H.canUnSuspended=_(je),H.canQueryAndApplyRefund=B(je),H.canSupplementaryRefundApply=B(je),H.canChange=!F(je))})):ee.value=rt(c.queryTicketRes)},{immediate:!0,deep:!0}),{showSupplementRefundDialog:Te,showPreview:a,previewInfo:k,openPreview:At,openChange:Nn,ticketList:Q,ticketListByName:ee,getPassengerName:Ye,ticketOperation:qe,printerType:R,isShowRepealTicketDialog:d,ticketOperationCondition:Oe,refundOperationCondition:Z,goToNewRefund:S,reQueryTicket:Ke,goToPnrManage:Et,authTicketClick:ot,authTicketShow:Qe,stssChangeTicketStatusClick:G,changeStatus:Fe,printNoDialog:C,openDialog:We,showTicketRefundFormDialog:g,viewRefundFormWithGid:Je,viewSupplementRefundWithGid:tt,printerNo:x,isSupplementRefund:ye,refundTicketData:me,showSupplementSuccessDialog:j,openSupplementSuccessDialog:en,invoiceNumber:he,openRefundDialog:gt,tktNumber:pe,pullControlPower:xn,changeDialogShow:v,changeTicketNo:r,copyInfo:$n,changeFactor:Re,tssForm:X,tssFormRef:ge,datePrefix:L,showTssDialog:V,closeDialog:Rn,TSS_FORM_RULES:le,confirmTss:Ne,tssTip:u,tssCheckLabel:te,refundPrintNo:ue,invalidatedTicketQueryGid:q,realRefs:b,setRealRefs:E,setPopoverRefs:y,showTicketOriginalContainerList:D,show:$,hide:A,closePopover:Cn,ticketListByNameDetl:fe,ticketListByTicketDetl:Ce}},Pf=c=>{const{t:p}=et(),l=O(),f=O(),T=y=>l.value=(y==null?void 0:y.$el)||y,k=O(!1),a=()=>{k.value=!0},v=()=>{k.value=!1},r=O(""),g=O(!1),b=O(""),h=(y="")=>{var D,$,A;y&&(g.value=(D=y==null?void 0:y.toUpperCase())==null?void 0:D.startsWith("CC"),r.value=`${(($=y.split("/"))==null?void 0:$[1])??""}${((A=y.split("/"))==null?void 0:A[2])??""}`)},E=(y="")=>{var D;(D=y==null?void 0:y.toUpperCase())!=null&&D.startsWith("CC")?(b.value=p("app.agentTicketRefund.tcCredit"),h(y)):b.value=Ro(y)};return ct(()=>{var y;E((y=c.previewInfo.refundTicketOrder)==null?void 0:y.ticket.payType)}),{creditCardNo:r,isCreditCard:g,payMethod:b,realRef:l,setRealRef:T,popoverRef:f,showTicketOriginalContainer:k,show:a,hide:v}},Df={class:"w-full p-2.5 bg-brand-4 flex-wrap flex justify-between"},Af={class:"main-title"},Ef={class:"mb-1 flex items-center"},Of={key:0,class:"text-brand-2 text-base font-bold leading-normal"},Ff={key:1},Vf={key:2,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] ml-2.5"},Mf={key:3,class:"iconfont icon-inf mr-[4px] text-gray-4 ml-5"},Lf={key:4,class:"iconfont icon-user-fill mr-[4px] text-gray-4 ml-5"},Bf={class:"text-base font-bold text-gray-1"},Qf={class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs ml-[4px]"},Uf={class:"text-sm text-gray-3"},jf={class:"text-gray-1 text-sm mr-3.5"},If={class:"text-sm text-gray-3"},zf={class:"text-gray-1 text-sm mr-3.5"},qf={class:"text-sm text-gray-3"},Gf={class:"text-gray-1 text-sm mr-3.5"},Hf={key:0},Kf={class:"text-sm text-gray-3"},Yf={class:"text-sm text-gray-1"},Wf=He({__name:"RefundInfo",props:{previewInfo:{}},setup(c){const p=c,{creditCardNo:l,isCreditCard:f,payMethod:T}=Pf(p);return(k,a)=>{var v,r,g,b,h,E,y,D,$,A,V;return s(),m("div",Df,[t("div",Af,[t("span",Ef,[Number(k.previewInfo.refundTicketOrder.conjunction)>1?(s(),m("span",Of,n(e(Qt)((v=k.previewInfo.refundTicketOrder)==null?void 0:v.ticket.ticketNo)),1)):(s(),m("div",Ff,[o(rn,{"tkt-index":(r=k.previewInfo.refundTicketOrder)==null?void 0:r.ticket.ticketNo,"ticket-number":(g=k.previewInfo.refundTicketOrder)==null?void 0:g.ticket.ticketNo,"second-factor":k.previewInfo.secondFactor,"refund-class-type":"0"},null,8,["tkt-index","ticket-number","second-factor"])])),(b=k.previewInfo.refundTicketOrder)!=null&&b.ticket.governmentPurchase?(s(),m("div",Vf,"GP")):W("",!0),((E=(h=k.previewInfo.refundTicketOrder)==null?void 0:h.ticket)==null?void 0:E.specialPassengerType)==="INF"?(s(),m("em",Mf)):(s(),m("em",Lf)),t("span",Bf,n((y=k.previewInfo.refundTicketOrder)==null?void 0:y.ticket.passengerNameSuffix),1),t("span",Qf,n(e(Ct)((($=(D=k.previewInfo.refundTicketOrder)==null?void 0:D.ticket)==null?void 0:$.specialPassengerType)??"ADT")),1)])]),t("div",{class:Ve([e(f)?"w-full":""])},[t("span",Uf,n(k.$t("app.agentTicketRefund.oldTicketNo"))+"：",1),t("span",jf,n(e(Qt)(((A=k.previewInfo.refundTicketOrder.ticket)==null?void 0:A.exchangeTktNo)??"")||"-"),1),t("span",If,n(k.$t("app.agentTicketRefund.electronic"))+"：",1),t("span",zf,n(((V=k.previewInfo.refundTicketOrder)==null?void 0:V.ticket.etTag)==="1"?k.$t("app.agentTicketRefund.yes"):k.$t("app.agentTicketRefund.no")),1),t("span",qf,n(k.$t("app.agentTicketRefund.payment"))+"：",1),t("span",Gf,n(e(T)),1),e(f)?(s(),m("span",Hf,[t("span",Kf,n(`${k.$t("app.agentTicketRefund.creditCard")}：`),1),t("span",Yf,n(e(l)),1)])):W("",!0)],2)])}}}),ja=c=>(Ut("data-v-dcf8aa94"),c=c(),jt(),c),Xf=ja(()=>t("i",{class:"iconfont icon-close"},null,-1)),Jf=[Xf],Zf={class:"w-full"},em={class:"mt-2.5 mb-1 text-sm font-bold text-gray-1 leading-[22px]"},tm={class:"border rounded border-brand-3 mb-2.5"},nm={key:0,class:"w-full h-5 justify-start items-center gap-2.5 inline-flex align-c"},am=ja(()=>t("div",{class:"w-full border-t-[1px] border-dashed border-gray-6"},null,-1)),sm={class:"w-[22%] flex items-center"},om={key:0,class:"mr-2.5 text-gray-1"},im={key:1},lm={class:"mr-2.5 text-gray-1"},rm={key:2,class:"text-yellow-1 w-[42px] px-1 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},cm={class:"text-gray-1"},um={class:"w-[22%]"},dm={key:0},pm={class:"mr-5 text-gray-1"},fm={class:"text-gray-3"},mm={key:1,class:"text-yellow-1 w-[42px] px-1 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},gm={class:"w-[28%] text-gray-1"},km={class:"mr-2.5"},ym={class:"w-[28%] items-center"},hm={class:"p-2.5 flex justify-between h-12 items-center border-t border-dashed border-brand-6"},vm={class:"flex items-center text-sm"},_m={class:"text-gray-3 mr-3.5"},Tm={class:"text-gray-1"},bm={class:"mr-1 text-gray-3"},xm={class:"text-gray-1 cursor-pointer border-b-[1.5px] border-dashed tax-detail"},Nm={class:"text-gray-1 mr-2.5"},$m={class:"text-red-1"},Rm={class:"text-gray-1 mr-2.5"},Cm={class:"text-red-1"},wm={class:"text-gray-1 mr-2.5"},Sm={class:"text-red-1"},Pm={class:"text-gray-1 mr-2.5"},Dm={class:"text-red-1"},Am={class:"text-sm font-bold text-gray-1"},Em={class:"text-base text-red-1 font-bold mr-2.5"},Om={class:"text-sm font-bold text-gray-1"},Fm={class:"text-base font-bold text-red-1"},Vm={class:"mt-5 text-center crs-btn-dialog-ui"},Mm=He({__name:"PreviewDialog",props:{previewInfo:{}},emits:["update:modelValue"],setup(c){const p=f=>{const T=[];return f.forEach(k=>{const a=T.findIndex(v=>v.find(r=>r.tktTag===k.tktTag));a>-1?T[a].push(k):T.push([k])}),T},l=f=>{const T=f.trim()??"";return St[T]&&St[T].color||""};return(f,T)=>{const k=st,a=ft;return s(),re(a,{title:f.$t("app.agentTicketRefund.preview"),class:"preview-dialog tc-refund-preview-dialog",width:"1040px","align-center":"true","close-on-click-modal":!1,"show-close":!1,onClose:T[2]||(T[2]=v=>f.$emit("update:modelValue",!1))},{default:i(()=>{var v;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:T[0]||(T[0]=r=>f.$emit("update:modelValue",!1))},Jf),t("div",Zf,[t("div",em,n(f.$t("app.refundForm.refundPassenger")),1),t("div",tm,[o(Wf,{"preview-info":f.previewInfo},null,8,["preview-info"]),(s(!0),m(xe,null,Ee(p(((v=f.previewInfo.refundTicketOrder)==null?void 0:v.ticket.segment)??[]),(r,g)=>(s(),m("div",{key:g+"seg",class:"px-2.5 pt-2"},[Number(f.previewInfo.refundTicketOrder.conjunction)>1?(s(),m("div",nm,[o(rn,{"tkt-index":r[0].tktTag,"ticket-number":r[0].tktTag,"second-factor":f.previewInfo.secondFactor,"refund-class-type":"1"},null,8,["tkt-index","ticket-number","second-factor"]),am])):W("",!0),(s(!0),m(xe,null,Ee(r,(b,h)=>(s(),m("div",{key:h,class:"flex py-1"},[t("div",sm,[b.segmentType==="3"&&b.airline?(s(),m("div",om,n(b.airline),1)):W("",!0),b.segmentType==="2"?(s(),m("div",im,[t("span",lm,n(b.flightNo),1)])):(s(),m("div",rm,n(b.segmentType==="3"?"OPEN":"ARNK"),1)),t("div",cm,n(b.cabinCode),1)]),t("div",um,[b.segmentType==="2"?(s(),m("div",dm,[t("span",pm,n(b.departureDate),1),t("span",fm,n(b.departureTime),1)])):(s(),m("div",mm,n(b.segmentType==="3"?"OPEN":"ARNK"),1))]),t("div",gm,[t("span",km,n(`${b.departureCode}-${b.arriveCode}`),1)]),t("div",ym,[t("span",{class:Ve(["font-bold mr-2.5",l(b.ticketStatus??"")])},n(b.ticketStatus),3)])]))),128))]))),128)),t("div",hm,[t("div",vm,[t("div",_m,[z(n(f.$t("app.agentTicketRefund.totalTicketAmount")),1),t("span",Tm,n(f.previewInfo.refundTicketOrder.ticket.currency)+" "+n(f.previewInfo.refundTicketOrder.ticket.totalAmount),1)]),t("div",bm,[z(n(f.$t("app.agentTicketRefund.totalTaxAmount"))+" ",1),o(Ln,{class:"!mt-0",ticket:f.previewInfo.refundTicketOrder.ticket,index:0},{"tax-deatil":i(()=>[t("span",xm,n(f.previewInfo.refundTicketOrder.ticket.currency)+" "+n(f.previewInfo.refundTicketOrder.ticket.totalTaxs),1)]),_:1},8,["ticket"])])]),t("div",null,[t("span",Nm,[z(n(f.$t("app.agentTicketRefund.commission"))+" ",1),t("span",$m,n(f.previewInfo.refundCompute.amount.commision?`${f.previewInfo.refundTicketOrder.ticket.currency} ${f.previewInfo.refundCompute.amount.commision}`:"--"),1)]),t("span",Rm,[z(n(f.$t("app.agentTicketRefund.commissionRate"))+" ",1),t("span",Cm,n(f.previewInfo.refundCompute.amount.commisionRate?`${f.previewInfo.refundCompute.amount.commisionRate}%`:"--"),1)]),t("span",wm,[z(n(f.$t("app.agentTicketRefund.charge"))+" ",1),t("span",Sm,n(f.previewInfo.refundTicketOrder.ticket.currency)+" "+n(f.previewInfo.refundCompute.amount.otherDeduction),1)]),t("span",Pm,[z(n(f.$t("app.agentTicketRefund.totalRefund"))+" ",1),t("span",Dm,n(f.previewInfo.refundTicketOrder.ticket.currency)+" "+n(f.previewInfo.refundCompute.amount.netRefund),1)])])])]),t("span",Am,n(f.$t("app.agentTicketRefund.charge")),1),t("span",Em,n(f.previewInfo.refundTicketOrder.ticket.currency)+" "+n(f.previewInfo.refundCompute.amount.otherDeduction),1),t("span",Om,n(f.$t("app.agentTicketRefund.totalRefund")),1),t("span",Fm,n(f.previewInfo.refundTicketOrder.ticket.currency)+" "+n(f.previewInfo.refundCompute.amount.netRefund),1),t("div",Vm,[o(k,{onClick:T[1]||(T[1]=r=>f.$emit("update:modelValue",!1))},{default:i(()=>[z(n(f.$t("app.agentTicketRefund.close")),1)]),_:1})])])]}),_:1},8,["title"])}}});const Lm=vt(Mm,[["__scopeId","data-v-dcf8aa94"]]),Bm=(c,p)=>{const l=()=>{p("update:modelValue",!1)};return{queryTkt:()=>{p("toQueryTicket"),l()},closeDialog:l,queryTSL:()=>{mn.setLink("/v2/crs/salesDaily"),l()},checkingReturnTkt:()=>{p("openRefundDialog",c.invoiceNumber,c.refundPrinterNo,c.ticketManagementOrganizationCode),l()}}},Qm=t("i",{class:"iconfont icon-close"},null,-1),Um=[Qm],jm={class:"flex text-[18px] items-center"},Im={class:"text-[var(--bkc-color-gray-1)] leading-6"},zm={class:"text-[14px] text-[var(--bkc-color-gray-1)]"},qm={class:"flex items-center pl-[30px]"},Gm={class:"flex justify-end"},Hm=He({__name:"SupplementSuccess",props:{invoiceNumber:{},refundPrinterNo:{},ticketManagementOrganizationCode:{}},emits:["openRefundDialog","toQueryTicket","update:modelValue"],setup(c,{emit:p}){const l=p,f=c,{closeDialog:T,queryTkt:k,queryTSL:a,checkingReturnTkt:v}=Bm(f,l);return(r,g)=>{const b=at,h=st,E=ft;return s(),re(E,{"close-on-press-escape":!1,"close-on-click-modal":!1,width:"500px","show-close":!1,class:"supplement-success","align-center":"",onClose:e(T)},{header:i(()=>[t("div",jm,[o(b,{size:"36px",class:"success-color mr-2.5"},{default:i(()=>[o(e(Qn))]),_:1}),t("span",Im,n(r.$t("app.refundForm.supplementaryRefundSuccess")),1)])]),default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:g[0]||(g[0]=(...y)=>e(T)&&e(T)(...y))},Um),t("div",zm,[t("div",qm,[t("span",null,n(r.$t("app.refundForm.invoiceNumber")),1),t("span",{class:"text-[var(--bkc-el-color-primary)] cursor-pointer mr-2.5",onClick:g[1]||(g[1]=(...y)=>e(v)&&e(v)(...y))},n(f.invoiceNumber),1),o(h,{onClick:e(a)},{default:i(()=>[z(n(r.$t("app.refundForm.checkTslSettlement")),1)]),_:1},8,["onClick"])]),t("div",Gm,[o(h,{type:"primary",onClick:g[2]||(g[2]=y=>e(k)())},{default:i(()=>[z(n(r.$t("app.refundForm.confirmBtn")),1)]),_:1})])])]),_:1},8,["onClose"])}}});const Km=(c,p)=>{const{t:l}=et(),f=Be(!1),T=O(),k=O({}),a=O([]),v=O([]),r=()=>{p("update:modelValue",!1)},g=(L,le,C)=>{const d=L.split("");return d[le]=C,d.join("")},b=L=>{const le={};L.forEach(j=>{le[j.conjunctionIndex]?le[j.conjunctionIndex].push(j):le[j.conjunctionIndex]=[j]});const C=["0000","0000","0000","0000"];let d=0;for(const j in le)le[j].forEach(Te=>{C[d]=g(C[d],Number(Te.etSegIndex)-1,Te.etSegIndex)}),d++;return C},h=L=>{var C;return{refund:L.etTagNew?"Y":"N",currency:L.currency,payMethod:L.payType,remark:L.remarkInfo??"",creditCard:L.creditCard?L.creditCard:"",couponNos:b(L.checkedSeg??[]),name:(C=L.name)==null?void 0:C.trim(),airlineCode:L.airline}},E=L=>{const le=[];return L.forEach(C=>{if(C.taxAmount&&C.taxCode){const d={taxCode:C.taxCode,taxAmount:Number(C.taxAmount)};le.push(d)}}),le},y=L=>({commission:L.commision&&Number(L.commision)>0?L.commision.toString():"0",commissionRate:L.commisionRate.toString()??"",grossRefund:L.totalAmount.toString(),deduction:L.otherDeduction.toString(),netRefund:L.netRefund.toString(),taxInfos:E(L.taxs)}),D=()=>c.refundTicketData.ticketType==="I"&&c.refundTicketData.ticketNo!==c.refundTicketData.ticketNoEnd?`${c.refundTicketData.ticketNo}-${c.refundTicketData.ticketNoEnd}`:c.refundTicketData.ticketNo,$=L=>({ticketNo:D(),ticketType:L.tktType,printerNo:L.prntNo,ticketManagementOrganizationCode:L.ticketManagementOrganizationCode??"",refundFormPassengerItem:h(L),refundFormPriceItem:y(L)}),A=async L=>{var d,j,Te;f.value=!0;const le=$(L);let C;try{C=(await eo(le,"091T0108")).data.value;let se=((d=C==null?void 0:C.data)==null?void 0:d.refundNumber)??"";L.ticketManagementOrganizationCode!=="ARL"&&(se=(j=C==null?void 0:C.data)!=null&&j.refundNumber?(Te=C==null?void 0:C.data)==null?void 0:Te.refundNumber.substring(3):""),p("openSupplementSuccessDialog",se,L.prntNo??"",L.ticketManagementOrganizationCode??"")}finally{f.value=!1}},V=async()=>{var C,d;const L=(C=T.value)==null?void 0:C.getFormDate();if((L.checkedSeg??[]).length<1){ut({type:"warning",message:l("app.agentTicketRefund.selSeg")});return}await((d=T.value)==null?void 0:d.validate())&&A(L)},u=()=>{var L;(L=T.value)==null||L.resetForm()},te=L=>{const le=[];for(const C in L)L[C].forEach(d=>{if(d.ticketStatus==="REFUNDED"){const j={etSegIndex:d.etSegIndex,deptCity:d.deptCity,arrivalCity:d.arrivalCity,ticketStatus:d.ticketStatus,select:d.select,etNo:d.etNo,conjunctionIndex:d.conjunctionIndex};le.push(j)}});return le},X=L=>{let le=[];return le=L.map(C=>({taxCode:C.taxCode,taxAmount:C.taxAmount})),le.length<10?le.concat(new Array(10-le.length).fill({taxCode:"",taxAmount:""})).map(C=>({...C})):le},ge=L=>{let le=L;return(L.startsWith("CC")||L.startsWith("TC"))&&(le="TC"),le},q=L=>{v.value=X(L.taxInfos),k.value={refundNo:"",refundType:l("app.refundForm.manualRefundType"),iata:L.iataNo,agent:L.agent,office:L.office,refundDate:it(new Date).format("DDMMMYY/HHmm").toUpperCase(),volunteer:"supplement",createUser:L.operator,prntNo:c.printerNo,name:L.passengerName,psgType:L.passengerType,remark:L.remark?L.remark.substring(2):"",remarkCode:L.remark?L.remark.substring(0,2):"",creditCard:L.creditCard,conjunction:L.conjunction,airline:L.airlineCode,tktType:L.ticketType,ticketManagementOrganizationCode:L.ticketManagementOrganizationCode,payType:ge(L.payMethod),ticketNo:L.ticketNo!==L.ticketNoEnd&&L.ticketType==="I"?`${L.ticketNo}-${L.ticketNoEnd.slice(-2)}`:L.ticketNo,ticketNoView:L.ticketNoView,totalAmount:L.grossRefund,commision:L.commission,commisionRate:Number(L.commissionRate)>0?L.commissionRate:"",otherDeduction:L.deduction,netRefund:L.netRefund,totalTaxs:L.totalTaxs,taxs:X(L.taxInfos),rate:L.commission>0||L.commissionRate<=0?"0":"1",currency:L.currency,etTagNew:!1,checkedSeg:te(c.refundTicketData.segmentInfos),international:"",couponNos:[]}},Y=L=>{a.value=[];for(const le in L){const C=[];L[le].forEach(d=>{const j={etSegIndex:d.etSegIndex,deptCity:d.deptCity,arrivalCity:d.arrivalCity,ticketStatus:d.ticketStatus,select:d.select,etNo:d.etNo,conjunctionIndex:d.conjunctionIndex};C.push(j)}),a.value.push(C)}};return ct(()=>{q(c.refundTicketData),Y(c.refundTicketData.segmentInfos)}),{taxsHistory:v,supplementRefundData:k,supplementSegmentData:a,refundFormRef:T,fullscreenLoading:f,handleCommit:V,closeSupplementDialog:r,initFormData:u}},Ym=t("i",{class:"iconfont icon-close"},null,-1),Wm=[Ym],Xm={class:"mt-[10px] pt-[10px] flex justify-center border-[var(--bkc-color-gray-6)] crs-btn-dialog-ui"},Jm=He({__name:"SupplementRefund",props:{printerNo:{},refundTicketData:{}},emits:["update:modelValue","openSupplementSuccessDialog"],setup(c,{emit:p}){const l=c,f=p,{taxsHistory:T,supplementRefundData:k,supplementSegmentData:a,refundFormRef:v,fullscreenLoading:r,closeSupplementDialog:g,handleCommit:b,initFormData:h}=Km(l,f);return(E,y)=>{const D=st,$=ft,A=wt;return s(),re($,{title:E.$t("app.refundForm.supplementaryRefundInfo"),"show-close":!1,width:"1040px",class:"refund-form-dialog","close-on-click-modal":!1,onClose:e(g)},{default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:y[0]||(y[0]=(...V)=>e(g)&&e(g)(...V))},Wm),o(Co,{ref_key:"refundFormRef",ref:v,data:e(k),"taxs-history":e(T),"refund-tickets":e(a),"is-supplement-refund":!0},null,8,["data","taxs-history","refund-tickets"]),t("div",Xm,[$e((s(),re(D,{type:"primary",onClick:e(b)},{default:i(()=>[z(n(E.$t("app.agentTicketRefund.submit")),1)]),_:1},8,["onClick"])),[[A,e(r),void 0,{fullscreen:!0,lock:!0}]]),o(D,{onClick:e(h)},{default:i(()=>[z(n(E.$t("app.agentTicketRefund.reset")),1)]),_:1},8,["onClick"]),o(D,{onClick:e(g)},{default:i(()=>[z(n(E.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])])]),_:1},8,["title","onClose"])}}}),Zm=(c,p)=>{const l=O(""),f=O(!1),T=O(!1),k=()=>{const g=[];return(c.flightList??[]).forEach(b=>{b.airSeg.forEach(h=>{h.isSelected&&g.push(h)})}),g},a=()=>{p("getChosenSegment",k()),p("queryPassenger")},v=()=>{const g=c.flightList.map(b=>b.etNumber);p("queryRtkt",g)},r=g=>g.some(b=>b.flightNo==="OPEN"&&!["VOID","REFUNDED","USED/FLOWN","EXCHANGED"].includes(b.ticketStatus));return ct(()=>{var g,b;if((c.flightList??[]).length>1){const h=c.flightList.length-1,E=c.flightList[h].etNumber.slice(-2);l.value=`${c.flightList[0].etNumber}-${E}`}else l.value=((b=(g=c.flightList)==null?void 0:g[0])==null?void 0:b.etNumber)??"";f.value=c.flightList.some(h=>h.governmentPurchase),T.value=c.flightList.some(h=>r(h.airSeg))}),{ticketNo:l,isGovernmentPurchase:f,isShowRtktBtn:T,changeSelected:a,handleQueryRtkt:v}},eg={class:"w-[1012px] pb-2 rounded border border-brand-3 flex-col justify-start items-start gap-2 inline-flex change-flight-info"},tg={class:"self-stretch p-2.5 bg-brand-4 rounded-tl rounded-tr border border-brand-3 justify-between items-center inline-flex"},ng={class:"justify-start items-center flex"},ag={class:"justify-start items-start gap-2 flex mr-2.5"},sg={class:"text-brand-2 text-base font-bold leading-normal"},og={key:0,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] mr-2.5"},ig={class:"justify-start items-center gap-2.5 flex ml-2.5"},lg={class:"justify-start items-center gap-1 flex"},rg={key:0,class:"iconfont icon-inf mr-[4px] text-gray-4"},cg={key:1,class:"iconfont icon-user-fill mr-[4px] text-gray-4"},ug={class:"text-gray-1 text-base font-bold leading-normal"},dg={class:"px-1 bg-gray-7 rounded-sm justify-center items-center gap-2.5 flex h-5"},pg={class:"text-center text-gray-3 text-xs font-normal leading-tight"},fg={key:0,class:"self-stretch px-2.5 justify-start items-center gap-2.5 inline-flex"},mg={class:"text-brand-2 text-xs font-bold leading-tight"},gg=t("div",{class:"grow shrink basis-0 h-[0px] border border-dotted border-gray-6"},null,-1),kg={class:"self-stretch px-2.5 flex-col justify-start items-start flex"},yg={class:"w-[200px] self-stretch justify-start items-center gap-5 flex"},hg={class:"justify-start items-center gap-1 flex"},vg={class:"justify-start items-center gap-2.5 flex"},_g={class:"w-[110px] justify-start items-center flex"},Tg={key:0,class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},bg={class:"text-gray-1 text-sm font-normal leading-snug"},xg={class:"w-[172px] justify-start items-center flex"},Ng={class:"text-gray-1 text-sm font-normal leading-snug mr-2.5"},$g={class:"text-gray-3 text-sm font-normal leading-snug"},Rg={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-sm"},Cg={class:"w-[280px] self-stretch justify-start items-center gap-2.5 flex"},wg={class:"w-24 text-gray-1 text-sm font-normal leading-snug"},Sg={key:0,class:"px-1 bg-yellow-2 rounded-sm justify-start items-start gap-2.5 flex"},Pg={class:"text-center text-yellow-1 text-xs font-normal leading-tight"},Dg={key:1,class:"w-7 h-5 relative"},Ag={class:"justify-start items-start gap-0.5 flex"},Eg={key:0,class:"text-gray-1 text-sm font-normal leading-snug"},Og={key:1,class:"text-gray-1 text-sm font-normal leading-snug"},Fg={key:2,class:"text-brand-2 text-sm font-normal leading-snug"},Vg={class:"w-[300px] self-stretch px-1 justify-start items-center gap-3 flex"},Mg=He({__name:"FlightInfo",props:{flightList:{}},emits:["queryPassenger","getChosenSegment","queryRtkt"],setup(c,{emit:p}){const l=p,f=c,{ticketNo:T,isGovernmentPurchase:k,isShowRtktBtn:a,changeSelected:v,handleQueryRtkt:r}=Zm(f,l);return(g,b)=>{const h=st,E=Ht;return s(),m("div",eg,[t("div",tg,[t("div",ng,[t("div",ag,[t("div",sg,n(e(T)),1)]),e(k)?(s(),m("div",og,"GP")):W("",!0),t("div",ig,[t("div",lg,[g.flightList[0].specialPassengerType==="INF"?(s(),m("em",rg)):(s(),m("em",cg)),t("div",ug,n(g.flightList[0].passengerNameSuffix),1),t("div",dg,[t("div",pg,n(e(Ct)(g.flightList[0].specialPassengerType)),1)])])])]),e(a)?(s(),re(h,{key:0,"data-gid":"091V0801",onClick:b[0]||(b[0]=y=>e(r)())},{default:i(()=>[z("RTKT")]),_:1})):W("",!0)]),(s(!0),m(xe,null,Ee(g.flightList,(y,D)=>(s(),m(xe,{key:D},[g.flightList.length>1?(s(),m("div",fg,[t("div",mg,n(y.etNumber),1),gg])):W("",!0),t("div",kg,[(s(!0),m(xe,null,Ee(y.airSeg,$=>{var A,V;return s(),m("div",{key:$.segmentIndex,class:"self-stretch h-9 rounded justify-between items-center inline-flex"},[t("div",yg,[t("div",hg,[t("div",vg,[o(E,{modelValue:$.isSelected,"onUpdate:modelValue":u=>$.isSelected=u,value:!0,disabled:["VOID","REFUNDED","USED/FLOWN","EXCHANGED"].includes($.ticketStatus)||$.flightNo==="ARNK",size:"large",onChange:e(v)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])])]),t("div",_g,[$.flightNo==="OPEN"&&$.airline?(s(),m("div",Tg,n($.airline),1)):W("",!0),t("div",{class:Ve(["text-gray-1 text-sm font-normal leading-snug mr-2.5",["OPEN","ARNK"].includes($.flightNo)?"text-yellow-1 rounded-sm bg-yellow-2 px-1.5 py-0.5":""])},n($.flightNo),3),t("div",bg,n($.cabin),1)])]),t("div",xg,[$.depTime?(s(),m(xe,{key:0},[t("div",Ng,n($.departureDate?e(it)($.departureDate).format("YYYY-MM-DD"):""),1),t("div",$g,n($.depTime?e(it)($.depTime).format("HH:mm"):""),1)],64)):(s(),m("span",Rg,n($.flightNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",Cg,[t("div",wg,n(`${$.depAirportCode}-${$.arrAirportCode}`),1),$.changeReason?(s(),m("div",Sg,[t("div",Pg,n($.changeReason),1)])):(s(),m("div",Dg)),t("div",Ag,[$.pnrNo?(s(),m("div",Eg,n($.pnrNo),1)):W("",!0),$.pnrNo?(s(),m("div",Og,"/")):W("",!0),$.crsPnrNo?(s(),m("div",Fg,n($.crsPnrNo),1)):W("",!0)])]),t("div",Vg,[t("div",{class:Ve([e(St)[(A=$.ticketStatus)==null?void 0:A.trim()]?e(St)[(V=$.ticketStatus)==null?void 0:V.trim()].color:"","text-sm font-bold leading-snug"])},n($.ticketStatus),3)])])}),128))])],64))),128))])}}}),Lg=(c,p)=>{const{t:l}=et(),f=gs(),{orderInfo:T}=ks(f),k=bn(),a=O(!1),v=O(),r=O([]),g=O(!0),b=Be({}),h=Be({}),E=Be([]),y=O([]),D=O("voluntary"),$=O("new"),A=O(!1),V=O(!1),u=O(!1),te=O(!1),X=O(!1),ge=O(),q=O(""),Y=O(""),L=O(0),le=O(!1),C=Le(()=>{var S;return y.value.length===0?!1:$.value==="exist"&&!!((S=x.pnrNo)!=null&&S.trim().length)&&le.value}),d=O(!1),j=Le(()=>{var S;return y.value.length===0?!1:$.value==="exist"&&!!((S=x.pnrNo)!=null&&S.trim().length)&&d.value}),Te=()=>y.value.every(S=>!S.crsPnrNo&&S.flightNo!=="ANRK"),se=Le(()=>{const S=y.value.length===0,G=$.value==="exist"&&!x.pnrNo,Ne=$.value==="exist"&&x.pnrNo!==Y.value&&Te(),Ae=$.value==="exist"&&X.value;return S||G||Ne||Ae}),x=yt({pnrNo:""}),R={},pe=yt({flight:[],pnrNo:"",issueStatus:"",passengers:[],remarkArea:{remarks:[],ckins:[],clids:[],remarkOsis:[],others:[],ssrContents:[]},originalLineContents:[],historyLineContents:[],basicArea:{},pnrCanceled:!1,international:!1,key:"",rightComponentName:"",government:!1}),Re={pnrNo:[{required:!0,message:l("app.pnrManagement.validate.required")},{pattern:dn,message:l("app.change.inputCorrectPnr"),trigger:["change","blur"]}]},we=()=>({ticketNo:c.tktNo,detrType:"GET_ARR_TIME_TICKET",secondFactor:c.factor}),me=async S=>{try{a.value=!0;const{data:G}=await ba(we(),S);r.value=G.value??[]}finally{a.value=!1}},he=async()=>{if(E.value=[],r.value.forEach(S=>{S.airSeg.forEach(G=>{G.isSelected&&G.crsPnrNo&&!E.value.includes(G.crsPnrNo)&&E.value.push(G.crsPnrNo)})}),E.value.length!=1){b.value.passengers=[];return}if(!(b.value.passengers&&b.value.passengers.length))try{a.value=!0;const S={pnrNo:E.value[0]},G=De("09200104"),{data:Ne}=await sa(S,G);b.value=(Ne==null?void 0:Ne.value)??{passengers:[],canceled:!1,nonExistent:!1,basicArea:{}},h.value=b.value}catch{b.value={passengers:[],canceled:!1,nonExistent:!1,basicArea:{}}}finally{a.value=!1}},ue=S=>{x.pnrNo=q.value,g.value=!0,S==="involuntary"&&($.value="exist"),u.value=S==="involuntary",te.value=X.value=$.value==="exist"&&Te()},U=S=>{var Ae;if(le.value=!1,d.value=!1,S!=null&&S.canceled||S!=null&&S.nonExistent){te.value=!0,X.value=!0,d.value=!0;return}const G=(S==null?void 0:S.passengers)??[];qe(G,r.value)?(te.value=!1,X.value=!1):(te.value=!0,X.value=!0,(Ae=x.pnrNo)!=null&&Ae.trim()&&(le.value=!0))},J=S=>{x.pnrNo=q.value,g.value=!0,S==="exist"&&U((h==null?void 0:h.value)??{})},ne=S=>y.value=S,B=S=>{var G;return((G=R[S])==null?void 0:G.airportCnName)??""},P=async()=>{const S=await ys("searchLocalData");(S?JSON.parse(S.localData):[]).forEach(Ne=>{R[Ne.airportCode]=Ne})},N=(S,G)=>S==="ARNK"?S:S==="VOID"?"ARNK":(S??"").includes(G)?S.slice(2):S,_=S=>({airCN:"",airCode:S.airline,airService:[],alliance:"",aviationDepartmentGeneral:{},flightNo:N(S.flightNo,S.airline),isShared:"",planeType:"",ocFlightNumber:"",ocAirline:S.operationAirline}),w=S=>({cabinName:S.cabin,state:""}),ce=(S,G)=>({tktNum:G,airlines:_(S),arrDays:"",arrivalAirportCN:B(S.arrAirportCode),arrivalAirportCode:S.arrAirportCode,arrivalDate:S.arrTime?it(S.arrTime).format("YYYY-MM-DD HH:mm:ss"):"",arrivalTerminal:S.arrAirportTerminal,arrivalTime:it(S.arrTime).isValid()?it(S.arrTime).format("HH:mm"):"",asr:"",cabins:[w(S)],connectLevel:"",departureAirportCN:B(S.depAirportCode),departureAirportCode:S.depAirportCode,departureDate:S.depTime?it(S.depTime).format("YYYY-MM-DD HH:mm:ss"):"",departureTerminal:S.depAirportTerminal,departureTime:it(S.depTime).isValid()?it(S.depTime).format("HH:mm"):"",flightDistance:"",flightTime:"",stopCity:"",marriedSegmentNumber:"",commonMeal:"",ticketStatus:S.ticketStatus,pnrNo:S.pnrNo,segmentType:"0",crsPnrNo:S.crsPnrNo}),ie=(S,G)=>G==="arrive"?{code:S.arrAirportCode,name:B(S.arrAirportCode)}:{code:S.depAirportCode,name:B(S.depAirportCode)},oe=S=>{const G=[];return r.value.forEach(Ne=>{Ne.airSeg.forEach(Ae=>{const Fe={arriveCity:ie(Ae,"arrive"),departureCity:ie(Ae,"departure"),date:{departureDate:Ae.departureDate},segments:[ce(Ae,Ne.etNumber)],tktType:Ne.ticketTypeCode??"",openFlag:S?Ae.flightNo==="OPEN":!Ae.crsPnrNo&&Ae.flightNo!=="ANRK",isSelected:!!Ae.isSelected};Fe.key=hs(Fe),G.push(Fe)})}),G},Se=S=>{let G="";return(S??"").includes("NI")?G=S?`${S}_I`:"":G=S?`PP_${S}`:"",G},de=(S,G)=>{const Ne={docsName:(S==null?void 0:S.docsName)??"",holder:(S==null?void 0:S.holder)??"",documentType:Se(S==null?void 0:S.documentType),ssrType:(S==null?void 0:S.ssrType)??"",idCardNumber:(S==null?void 0:S.idCardNumber)??"",visaIssueCountry:(S==null?void 0:S.visaIssueCountry)??"CN",passengerNationality:(S==null?void 0:S.passengerNationality)??"CN",visaExpiryDate:S==null?void 0:S.visaExpiryDate,birthday:(S==null?void 0:S.birthday)??"",gender:(S==null?void 0:S.gender)??"M"};return G==="PP"?Ne:{...Ne,docsName:(S==null?void 0:S.docsName)??""}},I=(S,G)=>`${it(new Date).format("YYYYMMDDHHmmssSSS")}-${S}-${G}`,ve=(S,G,Ne)=>{var Ae,Fe;return{id:I(G,Ne),birthday:S.birthday??"",chineseName:S.chineseName??"",fullName:S.fullName,nameSuffix:S.nameSuffix??"",lastName:S.lastName??"",firstName:S.firstName??"",segments:[],passengerId:S.passengerId,document:de(S.document,""),documentPP:de(S.documentPP,"PP"),niForDocs:((Ae=S.document)==null?void 0:Ae.niForDocs)??"",ppForDocs:((Fe=S.documentPP)==null?void 0:Fe.ppForDocs)??"",docaInfoR:S.docaInfoR??{},docaInfoD:S.docaInfoD??{},inftSuffix:S.inftSuffix??""}},F=(S,G,Ne)=>{var Ae,Fe;return{id:I(G,S.passengerType??""),airlineType:Ne?"I":"D",foreign:!1,chineseName:S.chineseName??"",birthday:S.birthday??"",passengerType:S.passengerType??"",specialPassengerType:S.specialPassengerType??"",fullName:S.passengerNameInPnr??"",passengerNameInPnr:S.passengerNameInPnr??"",firstName:S.firstName??"",lastName:S.lastName??"",unMinor:!!S.unMinor,unMinorAge:S.unMinor?S.unMinorAge??0:0,nameSuffix:S.nameSuffix??"",vipType:"",vipText:"",osiCtcm:(S==null?void 0:S.osiCtcm)??"",ssrCtcm:(S==null?void 0:S.ssrCtcm)??"",passengerId:S.passengerId,segments:[],document:de(S.document??{},""),documentPP:de(S.documentPP??{},"PP"),niForDocs:((Ae=S.document)==null?void 0:Ae.niForDocs)??"",ppForDocs:((Fe=S.documentPP)==null?void 0:Fe.ppForDocs)??"",infantDetail:S.infantDetail?ve(S.infantDetail,G,S.passengerType??""):null,childSsrTypeTextInPnr:[],docaInfoR:S.docaInfoR??{},docaInfoD:S.docaInfoD??{},docsName:S.chineseName?"":S.fullName??""}},Z=(S,G)=>S.map((Ne,Ae)=>F(Ne,Ae,G)),Q=()=>{var S,G,Ne,Ae,Fe,We;return{changeModel:$.value,changeType:D.value,pnrNo:x.pnrNo,ticketNumberJoin:((Ne=(G=(S=v.value)==null?void 0:S.choosePassengers)==null?void 0:G[0])==null?void 0:Ne.ticketNumberJoin)??"",ticketNumbersForTN:((We=(Fe=(Ae=v.value)==null?void 0:Ae.choosePassengers)==null?void 0:Fe[0])==null?void 0:We.ticketNumbersForTN)??[]}},ee=()=>({orderInfo:pe,...Q()}),fe=()=>{var S,G,Ne,Ae,Fe,We,Je,tt;return{cts:((G=(S=b.value)==null?void 0:S.basicArea)==null?void 0:G.cts)??[],contact:((Ae=(Ne=b.value)==null?void 0:Ne.basicArea)==null?void 0:Ae.contact)??[],contactorEmail:((We=(Fe=b.value)==null?void 0:Fe.basicArea)==null?void 0:We.contactorEmail)??"",responsibilityGroup:"",issueLimitCrs:"",airlinePnr:"",issueLimitIcs:"",officeName:"",officePhone:"",officeAddress:"",airline:"",contactPhones:((tt=(Je=b.value)==null?void 0:Je.basicArea)==null?void 0:tt.contactPhones)??[],authorizes:[],firstDepartureTime:"",issueWarn:!1,issueAbnormal:!1,issueLimitOffice:""}},Ce=async()=>{var Ae;pe.basicArea=await fe();const S=await oe();pe.flight=S,pe.government=r.value.some(Fe=>Fe.governmentPurchase);const G=await la(S.filter(Fe=>Fe.isSelected));la(S.filter(Fe=>Fe.isSelected));const Ne=await Z(((Ae=v.value)==null?void 0:Ae.choosePassengers)??[],G);pe.passengers=Ne,pe.changeModelInOrder={...Q()}},Oe=()=>{var S;return(((S=v.value)==null?void 0:S.choosePassengers)??[]).map(G=>({paxId:G.passengerId,unMinor:!!G.unMinor,unMinorAge:G.unMinor?G.unMinorAge??0:0,fullName:G.passengerNameInPnr}))},ye=()=>{if(T.value.size<1)return!1;const S=Array.from(T.value.keys());return S==null?void 0:S.find(Ne=>{var Ae,Fe;return(Fe=(Ae=T.value.get(Ne))==null?void 0:Ae.occupySpecialContent)==null?void 0:Fe.includes(l("app.basic.occupy"))})},Ye=async()=>{var S;try{if(a.value=!0,$.value==="exist")(S=ge.value)==null||S.validate(async G=>{var Ae;if(!G)return;await Ce();let Ne=!0;b.value.passengers.length>0&&b.value.passengers.length!==(((Ae=v.value)==null?void 0:Ae.choosePassengers)??[]).length&&await nt.confirm(l("app.change.splitPassengerTips"),{icon:Pe("em",{class:"iconfont icon-info-circle-line"}),confirmButtonText:l("app.intlPassengerForm.del.confirm"),cancelButtonText:l("app.intlPassengerForm.del.cancel"),customClass:"agent-refund-msg-box crs-btn-ui crs-btn-message-ui",showClose:!1}).then(async()=>{var tt,Xe;const Fe={travellers:Oe(),count:0,orderId:"",passengerRecordLocator:x.pnrNo},We=De("091V0802"),Je=await In(Fe,We);x.pnrNo=((Xe=(tt=Je.data.value)==null?void 0:tt.splitedOrder)==null?void 0:Xe.passengerRecordLocator)??"",Ne=!0}).catch(()=>{Ne=!1}),Ne&&(await k.push({name:"PnrManagement",query:{pnrNo:x.pnrNo,time:it().unix(),type:D.value}}),await f.setCurrentRebookInfo(ee()),await p("update:modelValue",!1))});else{await Ce(),Reflect.set(pe,"rebookInfos",rt(ee())),await f.setCurrentRebookInfo(ee());const G=rt(pe),Ne=["VOID","REFUNDED","USED/FLOWN","EXCHANGED"];G.flight=(oe(!0)??[]).filter(Fe=>{var We,Je,tt;return((We=Fe==null?void 0:Fe.segments)==null?void 0:We[0].airlines.flightNo)!=="ARNK"&&!Ne.includes(((tt=(Je=Fe==null?void 0:Fe.segments)==null?void 0:Je[0])==null?void 0:tt.ticketStatus)??"")}),ye()||await f.pushOrderInfo("2",l("app.basic.ungeneted"),G),await k.push({name:"PnrManagement",query:{time:it().unix(),type:D.value}}),await p("update:modelValue",!1)}}finally{a.value=!1}},qe=(S,G)=>S.some(Ne=>v.value.isMatchMainPassenger(Ne,G)),Qe=()=>{var S;(S=ge.value)==null||S.validate(async G=>{var Ne,Ae,Fe,We;if(G)try{a.value=!0;const Je={pnrNo:x.pnrNo};v.value.selectOnlyMainPassenger();const tt=De("09200105"),{data:Xe}=await sa(Je,tt);if(U((Xe==null?void 0:Xe.value)??{}),!((Ne=Xe==null?void 0:Xe.value)!=null&&Ne.canceled)&&(g.value=qe(((Ae=Xe==null?void 0:Xe.value)==null?void 0:Ae.passengers)??[],r.value),!g.value)||(b.value=(Xe==null?void 0:Xe.value)??{passengers:[],canceled:!1,nonExistent:!1,basicArea:{}},(Fe=Xe==null?void 0:Xe.value)!=null&&Fe.canceled||(We=Xe==null?void 0:Xe.value)!=null&&We.nonExistent))return;Y.value=x.pnrNo}catch{b.value={passengers:[],canceled:!1,nonExistent:!1,basicArea:{}},g.value=!1}finally{a.value=!1}})};bt(()=>y.value,()=>{if(L.value===y.value.length)return;Y.value="";const S=y.value.every(Ne=>Ne.changeReason.trim()==="IRR");S||(D.value="voluntary"),A.value=!S,u.value=D.value==="involuntary",te.value=Te(),X.value=Te()&&$.value==="exist";const G=E.value.length<=1;V.value=!G,G||(A.value=!0,u.value=!1,D.value="voluntary",$.value="new"),q.value=E.value[0],x.pnrNo=E.value[0],L.value=y.value.length},{deep:!0});const ae=(S,G)=>{var Ae;const Ne=(Ae=S.passenger)==null?void 0:Ae.segments;r.value.forEach(Fe=>{Fe.etNumber===G&&Ne.length===Fe.airSeg.length&&Fe.airSeg.forEach((We,Je)=>{var tt,Xe,Ke,At,Et;We.flightNo==="OPEN"&&Ne[Je].departureDateTime.replace(/\s/g,"")&&(We.departureDate=((tt=Ne==null?void 0:Ne[Je])==null?void 0:tt.departureDateTime)??"",We.depTime=((Xe=Ne==null?void 0:Ne[Je])==null?void 0:Xe.departureDateTime)??"",We.arrTime=((Ke=Ne==null?void 0:Ne[Je])==null?void 0:Ke.arrivalDateTime)??"",We.flightNo=((At=Ne==null?void 0:Ne[Je])==null?void 0:At.flightNo)??"",We.cabin=((Et=Ne==null?void 0:Ne[Je])==null?void 0:Et.cabin)??"")})})},ot=async S=>{try{const G=De("091V0801");a.value=!0,S.forEach(async Ne=>{var Fe;const Ae=((Fe=(await xa(Ne,G)).data.value)==null?void 0:Fe.data)??{};ae(Ae,Ne)}),await ln()}finally{a.value=!1}};return ct(async()=>{const S=De("091T0102");await me(S),await P()}),{loading:a,flightList:r,changeType:D,changeModel:$,disableInvoluntarily:A,disableExist:V,disableNew:u,changeInfoForm:x,FORM_RULES:Re,changeModelFormRef:ge,isAllowChangePnr:te,pnrNoList:E,passengerResult:b,checkSegmentList:y,passengerRef:v,orderInfoTemplate:pe,isShowAccompanyPassenger:X,isDisableConfirmButton:se,searchAccompanyPassenger:Qe,buildFlightData:oe,getChosenSegment:ne,queryPassenger:he,handleChangeType:ue,handleChangeModel:J,confirmChange:Ye,queryRtkt:ot,isShowTicketNotInPNRTip:C,isShowCanceledOrNoPNRTip:j}},Bg=c=>{const p=O([]),l=Be([]),f=h=>{const E=h.replace(/\s+/g,""),y=E.indexOf("("),D=E.indexOf("*");if(y>-1&&D>-1){const $=Math.min(y,D);return(E==null?void 0:E.substring(0,$))??""}else{if(y===-1&&D>-1)return(E==null?void 0:E.substring(0,D))??"";if(y>-1&&D===-1)return(E==null?void 0:E.substring(0,y))??""}return E},T=(h,E)=>{var V;const y=(E??[]).map(u=>{var te;return(te=u.etNumber)==null?void 0:te.replace(/-/g,"")}),$=h.ticketNumbersForTN.map(u=>u==null?void 0:u.replace(/-/g,"")).some(u=>y.includes(u)),A=f(h.passengerNameInPnr)===f(((V=E[0])==null?void 0:V.passengerNameSuffix)??"");return $||A},k=()=>{l.value=[],p.value=[],c.passengerList.forEach(h=>{T(h,c.flightList)&&l.value.unshift(h)})},a=()=>{l.value=[],c.passengerList.forEach(h=>{T(h,c.flightList)&&l.value.unshift(h)}),p.value.forEach(h=>{T(c.passengerList[h],c.flightList)||l.value.push(c.passengerList[h])})},v=()=>{var h,E;if((c.flightList??[]).length>1){const y=c.flightList.length-1,D=c.flightList[y].etNumber.slice(-2);return`${c.flightList[0].etNumber}-${D}`}else return((E=(h=c.flightList)==null?void 0:h[0])==null?void 0:E.etNumber)??""},r=h=>{const E=h.indexOf("(");return E!==-1?h.substring(0,E).trim():h},g=()=>({birthday:"",chineseName:"",firstName:"",lastName:"",nameSuffix:c.flightList[0].nameSuffix,unMinor:!1,unMinorAge:0,passengerId:1,passengerPhone:"",fullName:c.flightList[0].fullName,passengerNameInPnr:c.flightList[0].passengerNameSuffix,passengerType:c.flightList[0].passengerType,specialPassengerType:c.flightList[0].specialPassengerType,ticketNumberJoin:v(),ticketNumbersForTN:c.flightList.map(E=>E.etNumber),segments:[]}),b=()=>{if(l.value=[],c.passengerList.length===0){l.value.push(g());return}c.passengerList.forEach((h,E)=>{T(h,c.flightList)&&h.infantDetail&&!p.value.includes(E)&&p.value.push(E),T(h,c.flightList)&&l.value.unshift(h)})};return ct(()=>{b()}),bt(()=>c.passengerList,()=>{b()}),{choosePassengerIndex:p,choosePassengers:l,selectPassenger:a,selectOnlyMainPassenger:k,getInftName:r,isMatchMainPassenger:T}},Qg={key:0,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2"},Ug={class:"cancel-tip ml-[10px]"},jg=t("em",{class:"iconfont icon-warning-circle-fill"},null,-1),Ig={key:1,class:"texv-if=t-base text-gray-1 font-bold mt-[15px]"},zg={class:"flex"},qg={class:"bg-gray-7 ml-[5px]"},Gg={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},Hg={key:0,class:"iconfont icon-connect ml-[5px] mr-[5px]"},Kg={key:1,class:"bg-gray-7 w-[34px] ml-[5px]"},Yg={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},Wg={class:"flex"},Xg={class:"bg-gray-7 w-[34px] ml-[5px]"},Jg={class:"text-gray-3 text-xs ml-[5px] mr-[5px] mt-[2px]"},Zg=He({__name:"Passenger",props:{mainTicketNo:{},flightList:{},passengerList:{},pnrNoList:{}},setup(c,{expose:p}){const l=c,{choosePassengerIndex:f,choosePassengers:T,selectPassenger:k,selectOnlyMainPassenger:a,isMatchMainPassenger:v}=Bg(l);return p({choosePassengers:T,isMatchMainPassenger:v,selectOnlyMainPassenger:a}),(r,g)=>{const b=Nt,h=Ht,E=Ra;return s(),m(xe,null,[l.pnrNoList.length>1?(s(),m("div",Qg,[t("span",Ug,[jg,z(n(r.$t("app.agentTicketQuery.differentTip")),1)])])):W("",!0),l.passengerList.length>1||l.passengerList.length&&l.passengerList[0].infantDetail?(s(),m("div",Ig,n(r.$t("app.agentTicketQuery.peersPassenger")),1)):W("",!0),o(E,{modelValue:e(f),"onUpdate:modelValue":g[0]||(g[0]=y=>Ue(f)?f.value=y:null),class:"flex flex-wrap"},{default:i(()=>[(s(!0),m(xe,null,Ee(l.passengerList,(y,D)=>(s(),m("div",{key:y.passengerNameInPnr},[t("label",null,[e(v)(y,r.flightList)?W("",!0):(s(),m("div",{key:0,class:Ve([{"w-[390px]":y.infantDetail&&!e(v)(y,r.flightList),"bg-brand-4 border-brand-2":e(f).includes(D),"border-inherit":!e(f).includes(D)},"pr-[4px] mr-[10px] mt-[10px] rounded border"])},[o(h,{label:D,class:"ml-[8px]",onChange:e(k)},{default:i(()=>{var $;return[t("div",zg,[o(b,{placement:"top",trigger:"hover",content:(y==null?void 0:y.passengerNameInPnr)??"",teleported:!1},{default:i(()=>[t("div",{class:Ve([{"w-[107px]":y.passengerNameInPnr.length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(y.passengerNameInPnr),3)]),_:2},1032,["content"]),t("div",qg,[t("div",Gg,n(e(Ct)(y.specialPassengerType??"ADT")),1)]),y.infantDetail?(s(),m("div",Hg)):W("",!0),o(b,{placement:"top",trigger:"hover",content:(($=y==null?void 0:y.infantDetail)==null?void 0:$.passengerNameInPnr)??"",teleported:!1},{default:i(()=>{var A;return[t("div",{class:Ve([{"w-[120px]":(y==null?void 0:y.infantDetail)&&y.infantDetail.passengerNameInPnr.length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(((A=y.infantDetail)==null?void 0:A.passengerNameInPnr)??""),3)]}),_:2},1032,["content"]),y.infantDetail?(s(),m("div",Kg,[t("div",Yg,n(r.$t("app.agentTicketQuery.passengerEN.type_INF")),1)])):W("",!0)])]}),_:2},1032,["label","onChange"])],2))]),e(v)(y,r.flightList)&&y.infantDetail?(s(),m("div",{key:0,class:Ve([{"w-[390px]":y.infantDetail&&!e(v)(y,r.flightList),"bg-brand-4 border-brand-2":e(f).includes(D),"border-inherit":!e(f).includes(D)},"main-baby w-[190px] mr-[10px] mt-[10px] rounded border"])},[o(h,{modelValue:e(f)[D],"onUpdate:modelValue":$=>e(f)[D]=$,label:D,class:"ml-[8px]",disabled:"",onChange:e(k)},{default:i(()=>{var $;return[o(b,{placement:"top",trigger:"hover",content:(($=y.infantDetail)==null?void 0:$.passengerNameInPnr)??"",teleported:!1},{default:i(()=>{var A;return[t("div",Wg,[t("div",{class:Ve([{"w-[102px]":(y==null?void 0:y.infantDetail)&&(y.infantDetail.passengerNameInPnr??"").length>11},"whitespace-nowrap overflow-hidden text-ellipsis font-bold leading-normal"])},n(((A=y.infantDetail)==null?void 0:A.passengerNameInPnr)??""),3),t("div",Xg,[t("div",Jg,n(r.$t("app.agentTicketQuery.passengerEN.type_INF")),1)])])]}),_:2},1032,["content"])]}),_:2},1032,["modelValue","onUpdate:modelValue","label","onChange"])],2)):W("",!0)]))),128))]),_:1},8,["modelValue"])],64)}}});const Kn=c=>(Ut("data-v-3c9f96c2"),c=c(),jt(),c),ek=Kn(()=>t("i",{class:"iconfont icon-close"},null,-1)),tk=[ek],nk={class:"mt-[15px]"},ak={key:1,class:"mt-[10px]"},sk={class:"flex"},ok={key:2,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2 mt-[7px] mb-[20px]"},ik={class:"cancel-tip ml-[10px]"},lk=Kn(()=>t("em",{class:"iconfont icon-warning-circle-fill"},null,-1)),rk={key:3,class:"pnr-cancel text-yellow-1 bg-yellow-3 border-yellow-2 mt-[7px] mb-[20px]"},ck={class:"cancel-tip ml-[10px]"},uk=Kn(()=>t("em",{class:"iconfont icon-warning-circle-fill"},null,-1)),dk={class:"flex justify-center crs-btn-dialog-ui"},pk={key:1,class:"h-[300px] w-full flex-col justify-center items-center gap-2.5 inline-flex"},fk={class:"flex-col justify-center items-center gap-[19px] flex"},mk=["alt"],gk={class:"flex-col justify-center items-center gap-2.5 flex"},kk={class:"text-center text-gray-2 text-lg font-bold leading-normal"},yk=He({__name:"ChangeDialog",props:{tktNo:{},factor:{}},emits:["update:modelValue"],setup(c,{emit:p}){const l=c,f=p,{loading:T,flightList:k,changeType:a,changeModel:v,disableNew:r,disableExist:g,disableInvoluntarily:b,changeInfoForm:h,FORM_RULES:E,changeModelFormRef:y,isAllowChangePnr:D,passengerResult:$,pnrNoList:A,checkSegmentList:V,passengerRef:u,isShowAccompanyPassenger:te,isDisableConfirmButton:X,searchAccompanyPassenger:ge,getChosenSegment:q,queryPassenger:Y,handleChangeType:L,handleChangeModel:le,confirmChange:C,queryRtkt:d,isShowTicketNotInPNRTip:j,isShowCanceledOrNoPNRTip:Te}=Lg(l,f);return(se,x)=>{const R=zt,pe=qt,Re=xt,we=dt,me=st,he=pt,ue=ft,U=ht("trimUpper"),J=wt;return s(),re(ue,{title:se.$t("app.change.select"),width:"1040px","close-on-click-modal":!1,"show-close":!1,"align-center":"true",tabindex:"-1","custom-class":"change-dialog",onClose:x[4]||(x[4]=ne=>se.$emit("update:modelValue",!1))},{default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:x[0]||(x[0]=ne=>se.$emit("update:modelValue",!1))},tk),$e((s(),m("div",null,[e(k).length>0?(s(),m(xe,{key:0},[e(k).length>0?(s(),re(Mg,{key:0,"flight-list":e(k),onQueryPassenger:e(Y),onGetChosenSegment:e(q),onQueryRtkt:e(d)},null,8,["flight-list","onQueryPassenger","onGetChosenSegment","onQueryRtkt"])):W("",!0),t("div",nk,[e($).passengers?(s(),re(Zg,{key:0,ref_key:"passengerRef",ref:u,"pnr-no-list":e(A),"flight-list":e(k),"passenger-list":e($).passengers,"main-ticket-no":e(k)[0].etNumber},null,8,["pnr-no-list","flight-list","passenger-list","main-ticket-no"])):W("",!0)]),e(V).length>0?(s(),m("div",ak,[t("div",null,[o(pe,{modelValue:e(a),"onUpdate:modelValue":x[1]||(x[1]=ne=>Ue(a)?a.value=ne:null),onChange:e(L)},{default:i(()=>[o(R,{label:"voluntary"},{default:i(()=>[z(n(se.$t("app.change.voluntarilyChange")),1)]),_:1}),o(R,{disabled:e(b),label:"involuntary"},{default:i(()=>[z(n(se.$t("app.change.involuntarilyChange")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","onChange"])]),t("div",sk,[o(pe,{modelValue:e(v),"onUpdate:modelValue":x[2]||(x[2]=ne=>Ue(v)?v.value=ne:null),onChange:e(le)},{default:i(()=>[o(R,{disabled:e(r),label:"new"},{default:i(()=>[z(n(se.$t("app.change.newChange")),1)]),_:1},8,["disabled"]),o(R,{disabled:e(g),label:"exist"},{default:i(()=>[z(n(se.$t("app.change.existChange")),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue","onChange"]),e(v)==="exist"?(s(),re(he,{key:0,ref_key:"changeModelFormRef",ref:y,rules:e(E),"hide-required-asterisk":!0,model:e(h),inline:!0,class:"form-style inline-block h-[32px] w-[200px]"},{default:i(()=>[o(we,{prop:"pnrNo",class:"w-[66px]"},{default:i(()=>[$e(o(Re,{modelValue:e(h).pnrNo,"onUpdate:modelValue":x[3]||(x[3]=ne=>e(h).pnrNo=ne),disabled:!e(D)},null,8,["modelValue","disabled"]),[[U]])]),_:1}),e(te)?(s(),re(we,{key:0},{default:i(()=>[o(me,{onClick:e(ge)},{default:i(()=>[z(n(se.$t("app.change.search")),1)]),_:1},8,["onClick"])]),_:1})):W("",!0)]),_:1},8,["rules","model"])):W("",!0)])])):W("",!0),e(j)&&!e(Te)?(s(),m("div",ok,[t("span",ik,[lk,z(" "+n(se.$t("app.agentTicketQuery.ticketNotInPNR")),1)])])):W("",!0),e(Te)?(s(),m("div",rk,[t("span",ck,[uk,z(" "+n(se.$t("app.agentTicketQuery.changePnrTip")),1)])])):W("",!0),t("div",dk,[o(me,{type:"primary",disabled:e(X),onClick:e(C)},{default:i(()=>[z(n(se.$t("app.button.ensure")),1)]),_:1},8,["disabled","onClick"])])],64)):(s(),m("div",pk,[t("div",fk,[t("img",{src:Gn,alt:se.$t("app.emptyTip")},null,8,mk),t("div",gk,[t("div",kk,n(se.$t("app.change.notRtData")),1)])])]))])),[[J,e(T)]])]),_:1},8,["title"])}}});const hk=vt(yk,[["__scopeId","data-v-3c9f96c2"]]),vk=c=>{const p=O(),l=O({pageSize:10,pageNumber:1}),f=O([]),T=Le(()=>{var r;return((r=c.detlInfos)==null?void 0:r.name)??""}),k=Le(()=>{var r,g;return((g=(r=c.detlInfos)==null?void 0:r.items)==null?void 0:g.length)??0}),a=()=>{var b;const r=(l.value.pageNumber-1)*l.value.pageSize,g=r+l.value.pageSize;f.value=(((b=c.detlInfos)==null?void 0:b.items)??[]).slice(r,g)},v=(r,g)=>{l.value={pageNumber:r,pageSize:g},a()};return bt(()=>c.detlInfos,()=>{v(1,l.value.pageSize)},{immediate:!0,deep:!0}),ct(()=>{a()}),{total:k,passengerName:T,tableData:f,pageInfo:l,tableRef:p,handleChangePage:v}},_k={class:"w-full h-full mt-[20px] p-2.5 bg-gray-0 rounded-lg shadow flex-col justify-start items-start gap-2.5 inline-flex"},Tk={class:"text-gray-1 text-base font-bold leading-normal"},bk={class:"w-full flex justify-end"},xk=He({__name:"NameDetlInfos",props:{detlInfos:{}},setup(c){const p=c,{tableRef:l,pageInfo:f,tableData:T,passengerName:k,total:a,handleChangePage:v}=vk(p);return(r,g)=>{const b=Fa,h=Va;return s(),m("div",_k,[t("div",Tk,n(e(k)),1),o(h,{ref_key:"tableRef",ref:l,data:e(T),stripe:"",class:"w-full text-gray-1 text-sm font-normal"},{default:i(()=>[o(b,{prop:"ticketNumber",label:"TKT NUMBER"}),o(b,{prop:"orgDst",label:"ORG-DST"}),o(b,{prop:"state",label:"STATE"}),o(b,{prop:"pnr",label:"PNR"}),o(b,{prop:"office",label:"TKT-OFFICE"}),o(b,{prop:"ticketType",label:"TKT-TYPE"})]),_:1},8,["data"]),t("div",bk,[o(e(Oo),{total:e(a),"page-size":e(f).pageSize,"current-page":e(f).pageNumber,onHandleChange:e(v)},null,8,["total","page-size","current-page","onHandleChange"])])])}}});const Nk=vt(xk,[["__scopeId","data-v-95262a4f"]]),{copy:$k,isSupported:Rk}=jn({legacy:!0}),Ck=c=>{const{t:p}=et(),l=O(),f=Le(()=>c.detlInfos);return{tableRef:l,tableData:f,copyInfo:k=>{Rk&&($k(k),ut({message:p("app.batchRefund.copySuccess"),type:"success"}))}}},wk={key:0,class:"w-full h-full mt-[20px] p-2.5 bg-gray-0 rounded-lg shadow flex-col justify-start items-start gap-2.5 inline-flex"},Sk={class:"text-gray-1 text-base font-bold leading-normal"},Pk=He({__name:"TicketDetlInfo",props:{detlInfos:{}},setup(c){const p=c,{tableData:l,copyInfo:f}=Ck(p);return(T,k)=>{var b;const a=Fo,v=Vo,r=Fa,g=Va;return e(l).ticketNo?(s(),m("div",wk,[t("div",Sk,[t("span",null,n(e(l).ticketNo),1),t("em",{class:"iconfont icon-copy text-brand-2 relative left-[5px] cursor-pointer",onClick:k[0]||(k[0]=yn(h=>{var E;return e(f)(((E=e(l))==null?void 0:E.ticketNo)??"")},["stop"]))})]),o(v,{class:"w-full text-gray-4 pl-[10px]"},{default:i(()=>[o(a,{span:2},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.ticketType")),1)]),_:1}),o(a,{span:3},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.managementOrg")),1)]),_:1}),o(a,{span:3},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.international")),1)]),_:1}),o(a,{span:2},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.conjunctionTotalTicket")),1)]),_:1}),o(a,{span:3},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.issueDate")),1)]),_:1}),o(a,{span:3},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.officeId")),1)]),_:1}),o(a,{span:3},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.customerNbr")),1)]),_:1}),o(a,{span:3},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.agentId")),1)]),_:1}),o(a,{span:2},{default:i(()=>[z(n(T.$t("app.agentTicketQuery.agentRole")),1)]),_:1})]),_:1}),o(v,{class:"w-full text-gray-1 pl-[10px]"},{default:i(()=>[o(a,{span:2},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.ticketTypeCode)||"-"),1)]}),_:1}),o(a,{span:3},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.customerCode)||"-"),1)]}),_:1}),o(a,{span:3},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.international)||"-"),1)]}),_:1}),o(a,{span:2},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.conjunctionTotalTicket)||"-"),1)]}),_:1}),o(a,{span:3},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.ticketingDate)||"-"),1)]}),_:1}),o(a,{span:3},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.issueOfficeId)||"-"),1)]}),_:1}),o(a,{span:3},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.customerNo)||"-"),1)]}),_:1}),o(a,{span:3},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.issueAgentId)||"-"),1)]}),_:1}),o(a,{span:2},{default:i(()=>{var h;return[z(n(((h=e(l))==null?void 0:h.agentRole)||"-"),1)]}),_:1})]),_:1}),o(g,{data:(b=e(l))==null?void 0:b.segInfos,class:"country-table customer-table border-t border-dashed border-gray-6","header-row-class-name":"header-row","header-cell-class-name":"header-cell","row-class-name":"table-row",stripe:""},{default:i(()=>[o(r,{label:T.$t("app.agentTicketQuery.couponNbr"),prop:"couponNumber"},null,8,["label"]),o(r,{label:T.$t("app.agentTicketQuery.departureAirport")},{default:i(({row:h})=>[t("div",null,n(h.departureAirport)+"-"+n(h.arrivalAirport),1)]),_:1},8,["label"]),o(r,{label:T.$t("app.agentTicketQuery.airlineCode"),prop:"airline"},{default:i(({row:h})=>[z(n(h.airline||"-"),1)]),_:1},8,["label"]),o(r,{label:T.$t("app.agentTicketQuery.couponStatus"),prop:"couponStatus"},{default:i(({row:h})=>{var E,y;return[t("span",{class:Ve([e(St)[(E=h.couponStatus)==null?void 0:E.trim()]?e(St)[(y=h.couponStatus)==null?void 0:y.trim()].color:"","font-bold mr-2.5"])},n(h.couponStatus||"-"),3)]}),_:1},8,["label"])]),_:1},8,["data"])])):W("",!0)}}});const Dk=vt(Pk,[["__scopeId","data-v-c4865f76"]]),Ak=c=>(Ut("data-v-3932cd5e"),c=c(),jt(),c),Ek={class:"ticket-card mt-[10px] py-2.5 px-5 min-h-58 bg-gray-0 rounded-lg"},Ok={class:"flex items-center justify-between relative"},Fk={class:"flex items-center justify-start"},Vk={key:0,class:"inline-flex h-[20px] px-[4px] py-[0px] rounded-[2px] text-yellow-1 bg-yellow-2 items-center text-[12px] mr-2.5"},Mk={key:1,class:"iconfont icon-inf ml-2.5 mr-[4px] text-gray-4"},Lk={key:2,class:"iconfont icon-user-fill ml-2.5 mr-[4px] text-gray-4"},Bk={class:"mr-[4px] text-base font-bold text-gray-1"},Qk={key:4,class:"mr-[4px] text-base font-bold text-gray-1"},Uk={key:5,class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs"},jk={key:6,class:"text-yellow-1 mr-2.5 cursor-pointer text-xs"},Ik={class:"text-xs text-right"},zk={key:2,class:"ml-[12px]"},qk={class:"mt-[10px] text-sm"},Gk={class:"w-[22%]"},Hk={key:0,class:"mr-2.5"},Kk={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Yk={key:2,class:"text-gray-1 mr-2.5"},Wk={class:"text-gray-1"},Xk={class:"w-[22%]"},Jk={class:"text-gray-1 mr-2.5"},Zk={class:"text-gray-3"},ey={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},ty={class:"w-[28%] text-gray-1"},ny={class:"mr-2.5"},ay={key:0,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 text-xs"},sy={key:1,class:"text-gray-1"},oy={key:2,class:"text-gray-1 mx-[2px]"},iy=["onClick"],ly={class:"w-[28%] flex items-center"},ry={key:0},cy={class:"ticket-card mt-[10px] py-2.5 px-5 min-h-58 bg-gray-0 rounded-lg"},uy={class:"flex items-center justify-between relative"},dy={class:"flex items-center justify-start"},py={class:"w-[170px] mr-2.5 font-bold text-brand-2"},fy={key:0,class:"iconfont icon-inf ml-2.5 mr-[4px] text-gray-4"},my={key:1,class:"iconfont icon-user-fill ml-2.5 mr-[4px] text-gray-4"},gy={class:"mr-[4px] text-base font-bold text-gray-1"},ky={key:3,class:"mr-[4px] text-base font-bold text-gray-1"},yy={key:4,class:"text-gray-3 py-0.5 px-1.5 rounded-sm bg-gray-7 mr-1 text-xs"},hy={class:"mt-[10px] text-sm"},vy={class:"w-[6%]"},_y={key:0,class:"mr-2.5"},Ty={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},by={key:2,class:"text-gray-1 mr-2.5"},xy={class:"w-[11%]"},Ny={key:0,class:"text-gray-1 mr-2.5"},$y={key:1,class:"text-yellow-1 px-1.5 py-0.5 rounded-sm bg-yellow-2 mr-2.5 min-w-50 text-xs"},Ry={class:"w-[15%] text-gray-1"},Cy={class:"w-[24%] flex items-center"},wy={class:"h-9 justify-start items-center gap-4 mb-2.5 inline-flex"},Sy=Ak(()=>t("em",{class:"iconfont icon-info-circle-line text-brand-2 !text-[36px] w-9 h-9 flex items-center"},null,-1)),Py={class:"grow shrink basis-0 text-gray-1 text-lg font-normal leading-normal"},Dy=He({__name:"TicketQueryResult",props:{queryTicketRes:{},refundTicketData:{},queryType:{}},emits:["addNewTab","reQueryTicket"],setup(c,{emit:p}){const l=p,f=c,{showSupplementRefundDialog:T,authTicketShow:k,showSupplementSuccessDialog:a,ticketList:v,ticketListByName:r,isShowRepealTicketDialog:g,ticketOperationCondition:b,printerNo:h,isSupplementRefund:E,refundOperationCondition:y,refundTicketData:D,printNoDialog:$,tktNumber:A,invoiceNumber:V,showTicketRefundFormDialog:u,changeDialogShow:te,changeTicketNo:X,printerType:ge,ticketOperation:q,goToPnrManage:Y,stssChangeTicketStatusClick:L,changeStatus:le,viewRefundFormWithGid:C,viewSupplementRefundWithGid:d,authTicketClick:j,openSupplementSuccessDialog:Te,openRefundDialog:se,openDialog:x,goToNewRefund:R,reQueryTicket:pe,showPreview:Re,previewInfo:we,openPreview:me,openChange:he,pullControlPower:ue,copyInfo:U,changeFactor:J,tssForm:ne,tssFormRef:B,datePrefix:P,showTssDialog:N,closeDialog:_,TSS_FORM_RULES:w,confirmTss:ce,tssTip:ie,tssCheckLabel:oe,refundPrintNo:Se,invalidatedTicketQueryGid:de,closePopover:I,ticketListByNameDetl:ve,ticketListByTicketDetl:F}=Sf(f,l);return(Z,Q)=>{const ee=Nt,fe=st,Ce=Ht,Oe=dt,ye=Mo,Ye=pt,qe=ft,Qe=ht("permission");return s(),m(xe,null,[!["4","5","6"].includes(Z.queryType)&&e(v).length>0?(s(!0),m(xe,{key:0},Ee(e(v),(ae,ot)=>{var S;return s(),m("div",{key:ae.etNumber+ot},[t("div",Ek,[t("div",Ok,[t("div",Fk,[o(rn,{"tkt-info":ae,"ticket-number":ae==null?void 0:ae.etNumber,"second-factor":ae==null?void 0:ae.secondFactor,"tkt-index":ot,onClosePopover:e(I)},null,8,["tkt-info","ticket-number","second-factor","tkt-index","onClosePopover"]),ae.governmentPurchase?(s(),m("div",Vk,"GP")):W("",!0),ae.specialPassengerType==="INF"?(s(),m("em",Mk)):(s(),m("em",Lk)),ae.passengerNameSuffix&&((S=ae.passengerNameSuffix)==null?void 0:S.length)>30?(s(),re(ee,{key:3,class:"item",effect:"dark",content:ae.passengerNameSuffix,placement:"top-start"},{default:i(()=>[t("span",Bk,n(ae.passengerNameSuffix),1)]),_:2},1032,["content"])):(s(),m("span",Qk,n(ae.passengerNameSuffix),1)),ae.passengerType?(s(),m("span",Uk,n(e(Ct)(ae.specialPassengerType)),1)):W("",!0),ae.receiptPrinted?(s(),m("span",jk,"ReceiptPrinted")):W("",!0)]),t("div",Ik,[ae!=null&&ae.canQueryAndApplyRefund?(s(),re(fe,{key:0,onClick:G=>e(C)(!1,ae)},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.queryRefundBtn")),1)]),_:2},1032,["onClick"])):W("",!0),o(ee,{class:"box-item",effect:"dark",content:Z.$t("app.agentTicketQuery.supplementaryRefundApplyTip"),placement:"top-start","popper-class":"ticket-conditon-popper"},{content:i(()=>[t("div",null,n(Z.$t("app.agentTicketQuery.supplementaryRefundApplyTip")),1)]),default:i(()=>[ae!=null&&ae.canSupplementaryRefundApply?(s(),m("span",{key:0,class:Ve({"button-spacing":ae==null?void 0:ae.canSupplementaryRefundApply})},[$e((s(),re(fe,{onClick:G=>e(d)(!0,ae)},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.supplementaryRefundApplyBtn")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-supplement-button"]])],2)):W("",!0)]),_:2},1032,["content"]),ae!=null&&ae.canChange?(s(),m("span",{key:1,class:Ve({"button-spacing":ae==null?void 0:ae.canChange})},[$e((s(),re(fe,{"data-gid":"091T0102",onClick:G=>e(he)(ae.etNumber,ae.specialPassengerType,ae.secondFactor)},{default:i(()=>[z(n(Z.$t("app.agentTicketRefund.change")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-ticketChange-button"]])],2)):W("",!0),ae!=null&&ae.canRefund?(s(),m("span",zk,[o(fe,{onClick:G=>e(me)(ae)},{default:i(()=>[z(n(Z.$t("app.agentTicketRefund.preview")),1)]),_:2},1032,["onClick"])])):W("",!0),ae!=null&&ae.canRefund?(s(),m("span",{key:3,class:Ve({"button-spacing":ae==null?void 0:ae.canRefund})},[$e((s(),re(fe,{"data-gid":"091Q0101",onClick:G=>e(R)(ae.etNumber,ae.secondFactor)},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.refundBtn")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-refundTicket-button"]])],2)):W("",!0),ae!=null&&ae.canVoid?(s(),m("span",{key:4,class:Ve({"button-spacing":ae==null?void 0:ae.canVoid})},[$e((s(),re(fe,{onClick:G=>e(q)(ae,"repealTicket")},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.invalidBtn")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-repealTicket-button"]])],2)):W("",!0),ae!=null&&ae.canSuspended?(s(),m("span",{key:5,class:Ve({"button-spacing":ae==null?void 0:ae.canSuspended})},[$e((s(),re(fe,{onClick:G=>e(L)(ae,"Suspend")},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.suspendBtn")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-suspendOrUnSuspend-button"]])],2)):W("",!0),ae!=null&&ae.canUnSuspended?(s(),m("span",{key:6,class:Ve({"button-spacing":ae==null?void 0:ae.canUnSuspended})},[$e((s(),re(fe,{onClick:G=>e(L)(ae,"Resume")},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.unsuspendBtn")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-suspendOrUnSuspend-button"]])],2)):W("",!0),ae!=null&&ae.canTktAuth?(s(),m("span",{key:7,class:Ve({"button-spacing":ae==null?void 0:ae.canTktAuth})},[$e((s(),re(fe,{"data-gid":"091N0203",onClick:G=>e(j)(ae.etNumber)},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.ticketAuthBtn")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-ticketAuth-button"]])],2)):W("",!0)])]),t("div",qk,[(s(!0),m(xe,null,Ee(ae.airSeg??[],(G,Ne)=>{var Ae,Fe,We,Je;return s(),m("div",{key:`${ae.etNumber}${G.depAirportCode}`,class:"flex justify-between seg-info"},[t("div",Gk,[G.flightNo==="OPEN"&&G.airline?(s(),m("span",Hk,n(G.airline),1)):W("",!0),["OPEN","ARNK"].includes(G.flightNo)||(Ae=G.flightNo)!=null&&Ae.includes("VOID")?(s(),m("span",Kk,n(G.flightNo==="OPEN"?"OPEN":"ARNK"),1)):(s(),m("span",Yk,n(e(ta)((G==null?void 0:G.airline)??"",(G==null?void 0:G.operationAirline)??"")?`*${G.flightNo}`:G.flightNo),1)),t("span",Wk,n(G.cabin),1)]),t("div",Xk,[G.depTime?(s(),m(xe,{key:0},[t("span",Jk,n(e(it)(G.depTime).format("YYYY-MM-DD")??""),1),t("span",Zk,n(e(it)(G.depTime).format("HH:mm")??""),1)],64)):(s(),m("span",ey,n(G.flightNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",ty,[t("span",ny,n(G.depAirportCode)+"-"+n(G.arrAirportCode),1),G.changeReason?(s(),m("span",ay,n(G.changeReason),1)):W("",!0),G.pnrNo?(s(),m("span",sy,n(G.pnrNo),1)):W("",!0),G.pnrNo?(s(),m("span",oy,"/")):W("",!0),G.crsPnrNo?(s(),re(fe,{key:3,link:"",type:"primary",onClick:tt=>e(Y)(G.crsPnrNo)},{default:i(()=>[z(n(G.crsPnrNo),1)]),_:2},1032,["onClick"])):W("",!0),G.crsPnrNo?(s(),m("em",{key:4,class:"iconfont icon-copy text-brand-2 relative top-[2px] cursor-pointer",onClick:tt=>e(U)(G.crsPnrNo)},null,8,iy)):W("",!0)]),t("div",ly,[t("span",{class:Ve([e(St)[(Fe=G.ticketStatus)==null?void 0:Fe.trim()]?e(St)[(We=G.ticketStatus)==null?void 0:We.trim()].color:"","font-bold mr-2.5"])},n(G.ticketStatus),3),["REFUNDED","OPEN FOR USE","AIRPORT CNTL"].includes((Je=G.ticketStatus)==null?void 0:Je.trim())?(s(),re(ee,{key:0,class:"box-item",effect:"dark",content:Z.$t("app.agentTicketQuery.changeStatusTips"),placement:"bottom-start"},{default:i(()=>[G.airline!=="CZ"?(s(),m("span",ry,[$e((s(),re(fe,{link:"",type:"primary",size:"small",onClick:tt=>e(le)(ae.etNumber,G)},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.changeStatusBtn")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-updateStatus-button"]])])):W("",!0)]),_:2},1032,["content"])):W("",!0),G.ticketStatus.includes("AIRPORT CNTL")?$e((s(),re(fe,{key:1,link:"",type:"primary",size:"small",onClick:tt=>e(ue)(Ne,G.airline,ae.etNumber)},{default:i(()=>[z(n(Z.$t("app.agentTicketQuery.pullControlPower")),1)]),_:2},1032,["onClick"])),[[Qe,"crs-tc-ticketOperation-ticketQuery-pullControlPower-button"]]):W("",!0)])])}),128))])])])}),128)):W("",!0),Z.queryType==="4"&&e(r).length>0?(s(!0),m(xe,{key:1},Ee(e(r),(ae,ot)=>{var S;return s(),m("div",{key:ae.etNumber+ot},[t("div",cy,[t("div",uy,[t("div",dy,[t("span",py,n(e(Qt)(ae.etNumber)),1),ae.passengerType&&ae.passengerType==="INF"?(s(),m("em",fy)):W("",!0),ae.passengerType&&ae.passengerType!=="INF"?(s(),m("em",my)):W("",!0),ae.passengerName&&((S=ae.passengerName)==null?void 0:S.length)>30?(s(),re(ee,{key:2,class:"item",effect:"dark",content:ae.passengerName,placement:"top-start"},{default:i(()=>[t("span",gy,n(ae.passengerName),1)]),_:2},1032,["content"])):(s(),m("span",ky,n(ae.passengerName),1)),ae.passengerType?(s(),m("span",yy,n(e(Ct)(ae.passengerType)),1)):W("",!0)])]),t("div",hy,[(s(!0),m(xe,null,Ee(ae.airSeg??[],G=>{var Ne,Ae,Fe;return s(),m("div",{key:`${ae.etNumber}${G.depAirportCode}`,class:"flex justify-between seg-info"},[t("div",vy,[G.fltNo==="OPEN"&&G.airlineCode?(s(),m("span",_y,n(G.airlineCode),1)):W("",!0),["OPEN","ARNK"].includes(G.fltNo)||(Ne=G.fltNo)!=null&&Ne.includes("VOID")?(s(),m("span",Ty,n(G.fltNo==="OPEN"?"OPEN":"ARNK"),1)):(s(),m("span",by,n(e(ta)((G==null?void 0:G.airlineCode)??"",(G==null?void 0:G.operateAirline)??"")?`*${G.airlineCode}${G.fltNo}`:`${G.airlineCode}${G.fltNo}`),1))]),t("div",xy,[G.depDate?(s(),m("span",Ny,n(e(it)(G.depDate).format("YYYY-MM-DD")??""),1)):(s(),m("span",$y,n(G.fltNo==="OPEN"?"OPEN":"ARNK"),1))]),t("div",Ry,[t("span",null,n(G.depAirportCode)+"-"+n(G.arrAirportCode),1)]),t("div",Cy,[t("span",{class:Ve([e(St)[(Ae=G.status)==null?void 0:Ae.trim()]?e(St)[(Fe=G.status)==null?void 0:Fe.trim()].color:"","font-bold mr-2.5"])},n(G.status),3)])])}),128))])])])}),128)):W("",!0),Z.queryType==="5"?(s(),re(Nk,{key:2,"detl-infos":e(ve)},null,8,["detl-infos"])):W("",!0),Z.queryType==="6"?(s(),re(Dk,{key:3,"detl-infos":e(F)},null,8,["detl-infos"])):W("",!0),e(g)?(s(),re(Qp,{key:4,modelValue:e(g),"onUpdate:modelValue":Q[0]||(Q[0]=ae=>Ue(g)?g.value=ae:null),"invalidated-ticket-query-gid":e(de),"ticket-operation-condition":e(b),onReQueryTicket:e(pe)},null,8,["modelValue","invalidated-ticket-query-gid","ticket-operation-condition","onReQueryTicket"])):W("",!0),e(k)?(s(),re(Rf,{key:5,modelValue:e(k),"onUpdate:modelValue":Q[1]||(Q[1]=ae=>Ue(k)?k.value=ae:null),"ticket-no":e(A)},null,8,["modelValue","ticket-no"])):W("",!0),e($)?(s(),re(Qa,{key:6,modelValue:e($),"onUpdate:modelValue":Q[2]||(Q[2]=ae=>Ue($)?$.value=ae:null),"ticket-management-organization-code":e(y).ticketManagementOrganizationCode??"",onOpenDialog:e(x)},null,8,["modelValue","ticket-management-organization-code","onOpenDialog"])):W("",!0),e(u)?(s(),re(zn,{key:7,modelValue:e(u),"onUpdate:modelValue":Q[3]||(Q[3]=ae=>Ue(u)?u.value=ae:null),"printer-no":e(h),"printer-type":e(ge),"is-supplement-refund":e(E),"refund-operation-condition":e(y),"refund-ticket-data":e(D),onReQueryTicket:e(pe)},null,8,["modelValue","printer-no","printer-type","is-supplement-refund","refund-operation-condition","refund-ticket-data","onReQueryTicket"])):W("",!0),e(T)?(s(),re(Jm,{key:8,modelValue:e(T),"onUpdate:modelValue":Q[4]||(Q[4]=ae=>Ue(T)?T.value=ae:null),"printer-no":e(h),"refund-ticket-data":e(D),onOpenSupplementSuccessDialog:e(Te)},null,8,["modelValue","printer-no","refund-ticket-data","onOpenSupplementSuccessDialog"])):W("",!0),e(a)?(s(),re(Hm,{key:9,modelValue:e(a),"onUpdate:modelValue":Q[5]||(Q[5]=ae=>Ue(a)?a.value=ae:null),"ticket-management-organization-code":e(y).ticketManagementOrganizationCode??"","invoice-number":e(V),"refund-printer-no":e(Se),onOpenRefundDialog:e(se),onToQueryTicket:e(pe)},null,8,["modelValue","ticket-management-organization-code","invoice-number","refund-printer-no","onOpenRefundDialog","onToQueryTicket"])):W("",!0),e(Re)?(s(),re(Lm,{key:10,modelValue:e(Re),"onUpdate:modelValue":Q[6]||(Q[6]=ae=>Ue(Re)?Re.value=ae:null),"preview-info":e(we)},null,8,["modelValue","preview-info"])):W("",!0),e(te)?(s(),re(hk,{key:11,modelValue:e(te),"onUpdate:modelValue":Q[7]||(Q[7]=ae=>Ue(te)?te.value=ae:null),"tkt-no":e(X),factor:e(J)},null,8,["modelValue","tkt-no","factor"])):W("",!0),o(qe,{modelValue:e(N),"onUpdate:modelValue":Q[10]||(Q[10]=ae=>Ue(N)?N.value=ae:null),width:"500","close-on-click-modal":!1,"show-close":!1,class:"ticket-pnr-dialog",onClose:e(_)},{footer:i(()=>[o(fe,{type:"primary",onClick:e(ce)},{default:i(()=>[z(n(Z.$t("app.agentTicketRefund.sure")),1)]),_:1},8,["onClick"]),o(fe,{onClick:e(_)},{default:i(()=>[z(n(Z.$t("app.agentTicketRefund.cancel")),1)]),_:1},8,["onClick"])]),default:i(()=>[t("div",wy,[Sy,t("div",Py,n(e(ie)),1)]),Z.queryType==="2"?(s(),re(Ye,{key:0,ref_key:"tssFormRef",ref:B,model:e(ne),rules:e(w),"require-asterisk-position":"right",inline:!0,class:"form"},{default:i(()=>[o(Oe,{prop:"pnrTss",class:"ml-[52px]"},{default:i(()=>[o(Ce,{modelValue:e(ne).pnrTss,"onUpdate:modelValue":Q[8]||(Q[8]=ae=>e(ne).pnrTss=ae),label:e(oe)},null,8,["modelValue","label"])]),_:1}),e(ne).pnrTss?(s(),re(Oe,{key:0,label:Z.$t("app.ticketStatus.issueDate"),prop:"issueDate"},{default:i(()=>[o(ye,{modelValue:e(ne).issueDate,"onUpdate:modelValue":Q[9]||(Q[9]=ae=>e(ne).issueDate=ae),class:"date-picker","prefix-icon":e(P),type:"date",format:"YYYY-MM-DD",placeholder:Z.$t("app.ticketStatus.issueDate")},null,8,["modelValue","prefix-icon","placeholder"])]),_:1},8,["label"])):W("",!0)]),_:1},8,["model","rules"])):W("",!0)]),_:1},8,["modelValue","onClose"])],64)}}});const Ay=vt(Dy,[["__scopeId","data-v-3932cd5e"]]),Ey=(c,p)=>{const{t:l}=et(),{copy:f,isSupported:T}=jn({legacy:!0});return{ticketList:Le(()=>rt(c.batchRefundRes.refundResult)??[]),goTicketQuery:r=>{p("addNewTab",{title:l("app.agentTicketQuery.ticketList"),name:"ticketQuery",content:null},r)},doCopy:r=>{T&&(f(r),ut({message:l("app.batchRefund.copySuccess"),type:"success",duration:2*1e3,grouping:!0,repeatNum:1}))}}},Oy={class:"batch-refund-result mt-[10px] py-2.5 px-2.5 min-h-58 bg-gray-0 rounded-lg"},Fy={key:0,class:"text-xs"},Vy={key:1,class:"text-xs"},My={key:2,class:"text-xs"},Ly={class:"flex flex-wrap justify-start items-center"},By={class:"max-w-[400px]"},Qy={key:0},Uy=["onClick"],jy=["onClick"],Iy=He({__name:"BatchRefundResult",props:{batchRefundRes:{}},emits:["addNewTab"],setup(c,{emit:p}){const l=p,f=c,{ticketList:T,doCopy:k,goTicketQuery:a}=Ey(f,l);return(v,r)=>{const g=at,b=Nt;return s(),m("div",Oy,[t("div",{class:Ve(["leading-tight justify-start items-center gap-1 inline-flex border rounded p-2.5 min-w-full",v.batchRefundRes.allSuccess?"text-green-2 bg-green-4 border-green-3":"text-red-1 bg-red-3 border-red-2"])},[o(g,null,{default:i(()=>[v.batchRefundRes.allSuccess?(s(),re(e(En),{key:0,class:"text-base leading-tight text-green-2"})):(s(),re(e(On),{key:1,class:"text-base leading-tight text-red-1"}))]),_:1}),v.batchRefundRes.allSuccess?(s(),m("span",Fy,n(v.$t("app.batchRefund.successTip")),1)):v.batchRefundRes.allFail?(s(),m("span",Vy,n(v.$t("app.batchRefund.failedTip")),1)):(s(),m("span",My,n(v.$t("app.batchRefund.partialFailedTip")),1))],2),t("div",Ly,[(s(!0),m(xe,null,Ee(e(T),(h,E)=>(s(),m("div",{key:E,class:"w-[1/10] flex justify-between items-center mt-2.5 mr-2.5 py-3.5 px-2.5 border rounded border-brand-3"},[h.success?(s(),re(g,{key:0,class:"mr-1.5"},{default:i(()=>[o(e(En),{class:"text-base leading-tight text-green-2"})]),_:1})):W("",!0),h.success?W("",!0):(s(),re(b,{key:1,placement:"top"},{content:i(()=>[t("div",By,[t("div",null,n(v.$t("app.batchRefund.errorCode"))+n(h.errorCode||"--"),1),t("div",null,n(v.$t("app.batchRefund.description"))+n(h.description||"--"),1),t("div",null,n(v.$t("app.batchRefund.transactionNo"))+n(h.transactionNo||"--"),1),h.satTransactionNo?(s(),m("div",Qy,n(v.$t("app.batchRefund.satTransactionNo"))+n(h.satTransactionNo||"--"),1)):W("",!0)])]),default:i(()=>[h.success?W("",!0):(s(),re(g,{key:0,class:"mr-1.5"},{default:i(()=>[o(e(On),{class:"text-base leading-tight text-red-1 cursor-pointer"})]),_:1}))]),_:2},1024)),t("span",{class:"text-brand-2 text-xs leading-tight mr-2.5 pt-[1px] cursor-pointer",onClick:y=>e(a)(h.ticketNo)},n(h.ticketNo),9,Uy),t("em",{class:"iconfont icon-copy cursor-pointer leading-none text-brand-2 w-[14px] h-[14px]",onClick:y=>e(k)(h.ticketNo)},null,8,jy)]))),128))])])}}});const zy=vt(Iy,[["__scopeId","data-v-215dd92e"]]),qy=()=>{const{t:c}=et(),p=Be(!1),l=wa(),f=vs(),T=bn(),k=O("ticketQuery"),a=O([]),v=O(),r=Be(""),g=O(),b=Be(!1),h=O({}),E=Be(!1),y=Be(""),D=Be(!1),$=Be(!1),A=Be(!1),V=Be(!1),u=Be(!1),te=O({}),X=Be(""),ge=Be(""),q=Be(!1),Y=O({ticketType:"",ticketNo:""}),L=O({}),le=O([{title:c("app.agentTicketQuery.ticketList"),name:"ticketQuery",content:sn(Ay)}]),C=(N,_,w)=>{var ce;te.value=w,_?((ce=v.value)==null||ce.queryTktByRoute(_,"1"),k.value="ticketQuery"):(le.value.findIndex(oe=>oe.name===N.name)===-1&&le.value.push(N),k.value=N.name,N.name.indexOf("?type=ticketNo&sign=")!==-1&&(r.value=N.name.split("?type=ticketNo&sign=")[1]))},d=(N,_)=>{const w=le.value;let ce;N===-1&&(ce=le.value.findIndex(ie=>ie.name===_)),k.value===_&&le.value.forEach((ie,oe)=>{ie.name===_&&(k.value=w[oe-1].name)}),le.value.splice(ce||N,1)},j=N=>{k.value=le.value[N].name},Te=()=>{var N;(N=v.value)==null||N.queryTkt()},se=(N,_)=>{k.value="ticketQuery",a.value=N,y.value=_,v.value.closeLoading()},x=()=>{b.value=!0},R=N=>{var w;b.value=!1;const _={title:`${c("app.batchRefund.batchRefundResult")}`,name:`${c("app.batchRefund.batchRefundResult")}`,content:sn(zy)};C(_),h.value=N,(w=g.value)==null||w.closeLoading()},pe=()=>{E.value=!0},Re=()=>{V.value=!0},we=()=>{u.value=!0},me=()=>{E.value=!1,nt.alert(c("app.agentTicketQuery.bopSuccessTips"),{icon:Pe("em",{class:"iconfont icon-check-circle"}),customClass:"u-msg-box-icon",confirmButtonText:c("app.ticketStatus.confirmBtn"),showClose:!1})},he=N=>({ticketNo:Y.value.ticketNo,ticketType:ge.value||Y.value.ticketType,ticketManagementOrganizationCode:Y.value.ticketManagementOrganizationCode,printerNo:X.value,refundNo:N??""}),ue=async(N,_)=>{var ce;ge.value=_,X.value=N;const w=he();try{p.value=!0;const ie=De("091T0104"),{data:oe}=await on(w,ie);L.value=(ce=oe.value)==null?void 0:ce.data,A.value=!0}finally{p.value=!1}},U=async N=>{var ie;const{ticketNo:_,ticketType:w,printerNo:ce}=N;Y.value={ticketNo:_,ticketType:w},X.value=ce;try{p.value=!0;const oe=De("091T0104"),{data:Se}=await on(N,oe);L.value=(ie=Se.value)==null?void 0:ie.data,L.value.ticketManagementOrganizationCode=N.ticketManagementOrganizationCode??"",A.value=!0}finally{p.value=!1}},J=(N,_,w,ce,ie)=>({ticketNo:N,refundNo:_,ticketType:w?"D":"I",printerNo:ce,ticketManagementOrganizationCode:ie}),ne=async(N,_,w,ce,ie)=>{var oe,Se,de;try{p.value=!0;const I=De("091T0107"),{data:ve}=await on(J(N,_,w,ce,ie),I),F=w?"D":"I",Z=((oe=ve.value)==null?void 0:oe.data.ticketNo)??"";Y.value={ticketNo:Z,ticketType:F,ticketManagementOrganizationCode:ie},X.value=ce,(Se=v.value)==null||Se.closeRefundView(),L.value=(de=ve.value)==null?void 0:de.data,L.value.ticketManagementOrganizationCode=ie??"",A.value=!0,p.value=!1}finally{p.value=!1}},B=()=>{D.value=!0},P=()=>{$.value=!0};return _s(async()=>{var N;try{p.value=!0;const _=De("091M0111"),w=await to("TC_DETR_NEWAPI",_);q.value=((N=w==null?void 0:w.data.value)==null?void 0:N.data)??!1}finally{p.value=!1}}),bt(()=>D.value,N=>{l.setShowManualRefund(N)}),da(()=>{var N;if(f.query.ticketNo&&f.query.type){const _={title:`${c("app.agentTicketRefund.refund")}${f.query.ticketNo}`,name:`refund?type=ticketNo&sign=${f.query.ticketNo}`,content:sn(Tn)};C(_,"",{secondFactorCode:"CN",secondFactorValue:(N=f==null?void 0:f.query)==null?void 0:N.refundPnrNo}),T.replace("/v2/crs/ticketOperation")}}),{loading:p,printNo:X,printType:ge,showRefundFormDialog:A,refundOperationCondition:Y,refundFormData:L,editableTabs:le,currentTab:k,refundEtNumber:r,queryType:y,queryTicketRes:a,ticketQueryConditionRef:v,showBatchRefund:b,showAuthOffice:$,showManualRefund:D,batchRefundRes:h,batchRefundRef:g,factor:te,showBopRefund:E,showRtktDialog:V,showCccfDialog:u,addTab:C,changeTab:j,removeTab:d,handleQueryTicket:se,reQueryTicket:Te,openBatchRefund:x,handleBatchRefund:R,openBopRefund:pe,bopRefundSuccess:me,openManualRefund:B,openAuthOffice:P,openRefundFormDialog:ue,deliverRefundData:U,openRefundDialog:ne,openRtkt:Re,openCccf:we,isOneFactor:q}},Gy=qy,Hy={class:"h-[27px] p-[4px] mr-2.5"},Ky=["href","download"],Yy=t("i",{class:"iconfont icon-download text-[16px] text-[var(--bkc-el-color-primary)]"},null,-1),Wy=He({__name:"DownloadTemplate",setup(c){const{t:p}=et(),l=O(Ts()),f=Be(""),T=Be(""),k=()=>{f.value=`${bs}/SGUI-Batch-Refund-Template-CRS${l.value==="en"?"-EN":""}.xlsx`,T.value=p("app.batchRefund.batchRefundTemplate")};return(a,v)=>(s(),m("div",Hy,[t("a",{href:f.value,download:T.value,class:"no-underline text-[var(--bkc-el-color-primary)]"},[Yy,t("span",{class:"text-[12px]",onClick:k},n(a.$t("app.batchRefund.downloadTemplate")),1)],8,Ky)]))}}),Xy={salesTicketManagementRefundedAig:"ticket-management-refunded-aig",salesTicketManagementDelRefundTicketAig:"ticket-management-del-refund-ticket-aig",salesTicketManagementEditRefundAig:"ticket-management-edit-refund-aig"},Jy=()=>{const c=O(""),p=O([]),l=O([]),f=k=>new Promise(a=>{const v=new FileReader;v.readAsBinaryString(k),v.onload=r=>{var g;a((g=r==null?void 0:r.target)==null?void 0:g.result)}});return{fileName:c,resolveFile:async k=>{const a=k.raw;if(a){c.value=a.name;const v=await f(a),b=(await new Bo.Workbook().xlsx.load(v)).getWorksheet(1),h=[],E=[];b==null||b.eachRow({includeEmpty:!0},function(y,D){if(D===1)y.eachCell({includeEmpty:!0},function($){E.push($.value)});else{const $={};y.eachCell({includeEmpty:!0},function(A,V){(A.value||A.value===0)&&($[E[V-1]]=A.value)}),xs($)||h.push($)}}),l.value=E,p.value=h}},jsonData:p,fileContentTitle:l}},Zy=c=>{const{t:p}=et(),{fileName:l,jsonData:f,resolveFile:T,fileContentTitle:k}=Jy(),{postToAIGPersonalization:a}=Lo(Xy.salesTicketManagementRefundedAig),v=Be(!0),r=Be(!1),g=Be(!1),b=Be(!1),h=O("part"),E=O(),y=O({batchTkt:[{ticketNo:"",domestic:!0,voteCounterNo:"",commissionAmount:"",commissionRate:"",netRefund:"",currency:"",otherDeduction:"",failure:!1,errorCode:"",description:"",satTransactionNo:"",transactionNo:""}]}),D={ticketNo:[{required:!0,message:p("app.batchRefund.ticketNoNotEmpty"),trigger:"blur"},{pattern:pn,message:p("app.batchRefund.formatError"),trigger:"blur"}],voteCounter:[{required:!0,message:p("app.batchRefund.printNoNotEmpty"),trigger:"blur"},{pattern:Vt,message:p("app.batchRefund.formatError"),trigger:"blur"}]},$=Le(()=>y.value.batchTkt.every(x=>!x.ticketNo)),A=Le(()=>y.value.batchTkt.every(x=>!x.netRefund||x.failure)),V=()=>{v.value=!0,r.value=!1,c("update:modelValue",!1)},u=async x=>{var R;y.value.batchTkt.splice(x,1),(R=E.value)==null||R.validate()},te=()=>{const x={ticketNo:"",domestic:!0,voteCounterNo:"",commissionAmount:"",commissionRate:"",netRefund:"",currency:"",otherDeduction:"",failure:!1,errorCode:"",description:"",satTransactionNo:"",transactionNo:""};y.value.batchTkt.length>0&&(x.voteCounterNo=y.value.batchTkt[0].voteCounterNo),y.value.batchTkt.push(x)},X=()=>{const x=l.value.substring(l.value.lastIndexOf("."));return[".xls",".xlsx"].includes(x)},ge=()=>{var x,R,pe,Re;return((x=k.value)==null?void 0:x.length)===3&&((R=k.value)==null?void 0:R[0])===p("app.batchRefund.ticketNo")&&((pe=k.value)==null?void 0:pe[1])===p("app.batchRefund.printNo")&&((Re=k.value)==null?void 0:Re[2])===p("app.batchRefund.ticketType")},q=(x,R)=>x[R],Y=()=>f.value.map(x=>{const[R,pe,Re]=[q(x,p("app.batchRefund.ticketNo")),q(x,p("app.batchRefund.printNo")),q(x,p("app.batchRefund.ticketType"))];if(R||Re||pe||pe===0)return{ticketNo:R??"",domestic:Re?Re.trim()===p("app.batchRefund.domestic"):!0,voteCounterNo:pe||pe===0?`${pe}`:""}}),L=()=>{var R;if([v.value,r.value]=[!0,!1],(R=E.value)==null||R.clearValidate(),!X()){v.value=!1;return}if(!ge()){r.value=!0;return}let x=Y();if(x.length<=0){r.value=!0;return}x=x.length<=20?x:x.slice(0,20),y.value.batchTkt=[...x]},le=()=>{g.value=!1},C=()=>y.value.batchTkt.map(x=>({ticketNo:x.ticketNo,domestic:x.domestic,printNo:x.voteCounterNo})),d=async()=>{var x;(x=E.value)==null||x.validate(async R=>{if(R){let pe;try{g.value=!0;const Re=De("091U0104");pe=(await no({ticketList:C()},Re)).data.value,c("handleBatchRefund",pe)}catch{g.value=!1}try{if(pe!=null&&pe.refundOrder){const Re=pe==null?void 0:pe.refundOrder;await a(Re)}}catch(Re){console.error("批量退票入库异常:",Re.message)}}else{await nt.alert(p("app.batchRefund.formatErrorTips"),{icon:Pe("em",{class:"iconfont icon-close-circle-line text-[var(--bkc-color-special-red-1)]"}),customClass:"u-msg-box-icon",confirmButtonText:p("app.batchRefund.confirm"),showClose:!1,draggable:!0});return}})},j=x=>x==="all"?y.value.batchTkt.map(pe=>({ticketNo:pe.ticketNo,domestic:pe.domestic,printNo:pe.voteCounterNo})):y.value.batchTkt.filter(pe=>!pe.netRefund||pe.failure).map(pe=>({ticketNo:pe.ticketNo,domestic:pe.domestic,printNo:pe.voteCounterNo})),Te=async x=>{var R;b.value=!1,(R=E.value)==null||R.validate(async pe=>{if(!pe){await nt.alert(p("app.batchRefund.formatErrorTips"),{icon:Pe("em",{class:"iconfont icon-close-circle-line text-[var(--bkc-color-special-red-1)]"}),customClass:"u-msg-box-icon",confirmButtonText:p("app.batchRefund.confirm"),showClose:!1,draggable:!0});return}if(x&&se()){b.value=!0;return}try{g.value=!0;const Re=x?"all":h.value,we=De("091U0101"),me=(await ao({ticketList:j(Re)},we)).data.value;g.value=!1,(y.value.batchTkt??[]).forEach(he=>{(me??[]).forEach(ue=>{var U,J,ne,B,P;Qt(he.ticketNo)===Qt(ue.ticketNo)&&(he.commissionAmount=((U=ue.refundFeeDTO)==null?void 0:U.commissionAmount)??"",he.commissionRate=((J=ue.refundFeeDTO)==null?void 0:J.commissionRate)??"",he.otherDeduction=((ne=ue.refundFeeDTO)==null?void 0:ne.otherDeduction)??"",he.netRefund=((B=ue.refundFeeDTO)==null?void 0:B.netRefund)??"",he.currency=((P=ue.refundFeeDTO)==null?void 0:P.currency)??"",he.failure=ue.failure,he.errorCode=ue.errorCode??"",he.description=ue.description??"",he.satTransactionNo=ue.satTransactionNo??"",he.transactionNo=ue.transactionNo??"")})})}catch{g.value=!1}})},se=()=>{const x=y.value.batchTkt.filter(R=>!R.netRefund||R.failure);return x.length!==0&&x.length!==y.value.batchTkt.length};return{scopeType:h,showQueryScope:b,disabledBatchRefund:A,batchDataRule:D,confirmBatchRefund:d,closeBatchRefund:V,deleteBatchTktInfo:u,addBatchTktInfo:te,disabledBatch:$,fileName:l,resolveFile:T,jsonData:f,importBatchTktInfo:L,isFailure:r,isFileType:v,loading:g,closeLoading:le,formRef:E,batchRefundFormData:y,queryRefundFee:Te}},eh=Zy,th=t("i",{class:"iconfont icon-close"},null,-1),nh=[th],ah={class:"text-[18px] font-[700] text-[var(--bkc-color-gray-1)]"},sh={class:"flex item-center batch-tips h-[36px] p-2 mb-[10px] border border-solid border-yellow-2 rounded-[4px] bg-yellow-3 text-yellow-1"},oh=t("em",{class:"iconfont icon-warning-circle-fill mr-[8px] leading-5"},null,-1),ih={class:"text-xs leading-5"},lh={class:"flex justify-between"},rh=t("i",{class:"icon-plus-square iconfont text-[var(--bkc-el-color-primary)]"},null,-1),ch={key:0,class:"px-[5px] py-[10px] text-[var(--bkc-color-special-red-1)]"},uh={key:1,class:"px-[5px] py-[10px] text-[var(--bkc-color-special-red-1)]"},dh={class:"p-[10px] pl-[10px] border border-solid border-[var(--bkc-theme-2)] rounded-[8px] mt-[20px] mb-[10px]"},ph={class:"max-h-[450px] overflow-y-scroll batch-refund-form"},fh={class:"flex text-[12px] mb-[5px]"},mh={class:"w-[140px] mr-[40px] text-[var(--bkc-color-gray-1)]"},gh=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),kh={class:"w-[60px] mr-[40px] text-[var(--bkc-color-gray-1)]"},yh=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),hh={class:"w-[120px] mr-[40px] text-[var(--bkc-color-gray-1)]"},vh=t("span",{class:"text-[var(--bkc-color-special-red-1)]"},"*",-1),_h={class:"w-[100px] mr-[40px] text-[var(--bkc-color-gray-1)]"},Th={class:"w-[100px] mr-[40px] text-[var(--bkc-color-gray-1)]"},bh={class:"w-[192px] text-[var(--bkc-color-gray-1)]"},xh={key:0,class:"w-5"},Nh={class:"w-[100px] h-8 mr-[40px] justify-start items-center inline-flex"},$h={key:0,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Rh={key:1,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Ch={key:2,class:"text-center text-gray-2 text-xs font-normal leading-tight"},wh={class:"w-[100px] h-8 mr-[40px] justify-start items-center inline-flex"},Sh={key:0,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Ph={key:1,class:"text-center text-gray-2 text-xs font-normal leading-tight"},Dh={class:"w-[192px] h-8 justify-start items-center inline-flex"},Ah={key:0,class:"text-red-1 text-xs font-bold leading-tight"},Eh={class:"max-w-[400px] max-h-[200px] overflow-y-auto"},Oh={key:0},Fh={class:"flex align-center cursor-pointer"},Vh={class:"text-red-1 text-xs font-bold leading-tight"},Mh={key:2,class:"text-red-1 text-xs font-bold leading-tight"},Lh=["onClick"],Bh=t("em",{class:"iconfont icon-delete text-[20px] relative text-brand-2"},null,-1),Qh=[Bh],Uh={key:0,class:"w-full text-center text-[var(--bkc-el-color-primary)] cursor-pointer"},jh={class:"crs-btn-dialog-ui"},Ih={class:"flex items-center"},zh=t("div",{class:"iconfont icon-info-circle-line text-brand-2 mr-[16px]"},null,-1),qh={class:"text-lg text-gray-1"},Gh=He({__name:"BatchRefundDialog",emits:["handleBatchRefund","update:modelValue"],setup(c,{expose:p,emit:l}){const f=l,{scopeType:T,showQueryScope:k,disabledBatchRefund:a,batchDataRule:v,confirmBatchRefund:r,closeBatchRefund:g,deleteBatchTktInfo:b,addBatchTktInfo:h,disabledBatch:E,fileName:y,isFailure:D,isFileType:$,resolveFile:A,importBatchTktInfo:V,closeLoading:u,loading:te,formRef:X,batchRefundFormData:ge,queryRefundFee:q}=eh(f);return p({closeLoading:u}),(Y,L)=>{const le=xt,C=Qo,d=st,j=qn,Te=dt,se=gn,x=at,R=Nt,pe=pt,Re=zt,we=qt,me=ft,he=wt;return s(),re(me,{"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,class:"batch-refund-dialog tc-input-pad-init","align-center":!0,width:"1040px",onClose:e(g)},{header:i(()=>[t("span",ah,n(Y.$t("app.batchRefund.batchRefund")),1)]),footer:i(()=>[t("span",jh,[$e((s(),re(d,{disabled:e(E),type:"primary","data-gid":"091U0101",onClick:L[2]||(L[2]=ue=>e(q)(!0))},{default:i(()=>[z(n(Y.$t("app.batchRefund.checkTheRefundFee")),1)]),_:1},8,["disabled"])),[[he,e(te),void 0,{fullscreen:!0,lock:!0}]]),$e((s(),re(d,{"data-gid":"091U0104",disabled:e(a),type:"primary",onClick:e(r)},{default:i(()=>[z(n(Y.$t("app.batchRefund.batchRefundBtn")),1)]),_:1},8,["disabled","onClick"])),[[he,e(te),void 0,{fullscreen:!0,lock:!0}]]),o(d,{onClick:e(g)},{default:i(()=>[z(n(Y.$t("app.batchRefund.cancel")),1)]),_:1},8,["onClick"])])]),default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:L[0]||(L[0]=(...ue)=>e(g)&&e(g)(...ue))},nh),t("div",sh,[oh,t("span",ih,n(Y.$t("app.batchRefund.batchRefundTips")),1)]),t("div",lh,[o(C,{accept:".xlsx, .xls",action:"","show-file-list":!1,"auto-upload":!1,"on-change":e(A)},{default:i(()=>[o(le,{modelValue:e(y),"onUpdate:modelValue":L[1]||(L[1]=ue=>Ue(y)?y.value=ue:null),placeholder:Y.$t("app.batchRefund.pleaseSelectImportFile"),class:Ve(["mr-[10px]","cursor-pointer",e(D)||!e($)?"upload-box-error":""])},{suffix:i(()=>[rh]),_:1},8,["modelValue","placeholder","class"])]),_:1},8,["on-change"]),o(Wy),o(d,{disabled:!e(y),type:"primary",onClick:e(V)},{default:i(()=>[z(n(Y.$t("app.batchRefund.importBtn")),1)]),_:1},8,["disabled","onClick"])]),e(D)?(s(),m("span",ch,n(Y.$t("app.batchRefund.importFailureTips")),1)):W("",!0),e($)?W("",!0):(s(),m("span",uh,n(Y.$t("app.batchRefund.fileTypeTips")),1)),o(j,{"border-style":"dashed"}),t("div",dh,[t("div",ph,[t("div",fh,[t("p",mh,[z(n(Y.$t("app.batchRefund.ticketNo")),1),gh]),t("p",kh,[z(n(Y.$t("app.refundForm.ticketType")),1),yh]),t("p",hh,[z(n(Y.$t("app.batchRefund.printNo")),1),vh]),t("p",_h,n(Y.$t("app.batchRefund.agencyFeeRate")),1),t("p",Th,n(Y.$t("app.batchRefund.commission")),1),t("p",bh,n(Y.$t("app.batchRefund.refundTotal")),1),e(ge).batchTkt.length>1?(s(),m("p",xh)):W("",!0)]),o(pe,{ref_key:"formRef",ref:X,model:e(ge)},{default:i(()=>[(s(!0),m(xe,null,Ee(e(ge).batchTkt,(ue,U)=>(s(),m("div",{key:U,class:"flex"},[o(Te,{class:"inline-flex mr-[40px] mb-[10px] w-[140px]",prop:"batchTkt."+U+".ticketNo",rules:e(v).ticketNo},{default:i(()=>[o(le,{modelValue:ue.ticketNo,"onUpdate:modelValue":J=>ue.ticketNo=J,modelModifiers:{trim:!0},clearable:""},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop","rules"]),o(Te,{class:"inline-flex mr-[40px] mb-[10px] w-[60px]",prop:"batchTkt."+U+".domestic"},{default:i(()=>[o(se,{modelValue:ue.domestic,"onUpdate:modelValue":J=>ue.domestic=J,"inline-prompt":"","active-text":Y.$t("app.issue.dom"),"inactive-text":Y.$t("app.issue.intr")},null,8,["modelValue","onUpdate:modelValue","active-text","inactive-text"])]),_:2},1032,["prop"]),o(Te,{class:"inline-flex mr-[40px] w-[120px] mb-[10px]",prop:"batchTkt."+U+".voteCounterNo",rules:e(v).voteCounter},{default:i(()=>[(s(),re(It,{key:ue.voteCounterNo+U,modelValue:ue.voteCounterNo,"onUpdate:modelValue":[J=>ue.voteCounterNo=J,J=>{var ne;return(ne=e(X))==null?void 0:ne.validateField(`batchTkt.${U}.voteCounterNo`)}],modelModifiers:{trim:!0},"select-class":"w-[140px]"},null,8,["modelValue","onUpdate:modelValue"]))]),_:2},1032,["prop","rules"]),t("div",Nh,[!ue.commissionAmount&&!ue.commissionRate?(s(),m("div",$h,"-")):Number(ue.commissionAmount)>0||Number(ue.commissionRate)===0?(s(),m("div",Rh,n(ue.currency)+" "+n(Number(ue.commissionAmount).toFixed(2)),1)):(s(),m("div",Ch,n(ue.commissionRate)+"%",1))]),t("div",wh,[ue.otherDeduction?(s(),m("div",Ph,n(ue.currency)+" "+n(Number(ue.otherDeduction).toFixed(2)),1)):(s(),m("div",Sh,"-"))]),t("div",Dh,[!ue.netRefund&&!ue.failure?(s(),m("div",Ah,"-")):ue.failure?(s(),re(R,{key:1,placement:"top"},{content:i(()=>[t("div",Eh,[t("div",null,n(Y.$t("app.batchRefund.errorCode"))+n(ue.errorCode||"--"),1),t("div",null,n(Y.$t("app.batchRefund.description"))+n(ue.description||"--"),1),t("div",null,n(Y.$t("app.batchRefund.transactionNo"))+n(ue.transactionNo||"--"),1),ue.satTransactionNo?(s(),m("div",Oh,n(Y.$t("app.batchRefund.satTransactionNo"))+n(ue.satTransactionNo||"--"),1)):W("",!0)])]),default:i(()=>[t("span",Fh,[o(x,{class:"mr-1"},{default:i(()=>[o(e(On),{class:"text-base leading-tight text-red-1 cursor-pointer"})]),_:1}),t("span",Vh,n(Y.$t("app.batchRefund.countFailed")),1)])]),_:2},1024)):(s(),m("div",Mh,n(ue.currency)+" "+n(Number(ue.netRefund).toFixed(2)),1))]),e(ge).batchTkt.length>1?(s(),m("div",{key:0,class:"cursor-pointer w-5 h-8 justify-start items-center gap-2.5 inline-flex",onClick:J=>e(b)(U)},Qh,8,Lh)):W("",!0)]))),128))]),_:1},8,["model"]),e(ge).batchTkt.length<20?(s(),m("div",Uh,[o(d,{link:"",type:"primary",onClick:e(h)},{default:i(()=>[z(n(Y.$t("app.batchRefund.addTicketNo")),1)]),_:1},8,["onClick"])])):W("",!0)])]),o(me,{modelValue:e(k),"onUpdate:modelValue":L[5]||(L[5]=ue=>Ue(k)?k.value=ue:null),class:"scope-dialog","show-close":!1,width:"500px",top:"40vh","close-on-click-modal":!0},{footer:i(()=>[o(we,{modelValue:e(T),"onUpdate:modelValue":L[3]||(L[3]=ue=>Ue(T)?T.value=ue:null)},{default:i(()=>[o(Re,{label:"part",size:"large"},{default:i(()=>[z(n(Y.$t("app.batchRefund.partialTicketsd")),1)]),_:1}),o(Re,{label:"all",size:"large",class:"mr-[20px]"},{default:i(()=>[z(n(Y.$t("app.batchRefund.allTicket")),1)]),_:1})]),_:1},8,["modelValue"]),$e((s(),re(d,{type:"primary",onClick:L[4]||(L[4]=ue=>e(q)(!1))},{default:i(()=>[z(n(Y.$t("app.batchRefund.checkTheRefundFee")),1)]),_:1})),[[he,e(te),void 0,{fullscreen:!0,lock:!0}]])]),default:i(()=>[t("div",Ih,[zh,t("div",qh,n(Y.$t("app.batchRefund.queryScope")),1)])]),_:1},8,["modelValue"])]),_:1},8,["onClose"])}}});const Hh=c=>{const{t:p}=et(),l=yt({refundFormNumber:"",deviceNum:""}),f=O(),T={refundFormNumber:[{required:!0,message:p("app.agentTicketQuery.rtNumEmpty"),trigger:"change"},{pattern:Ds,message:p("app.agentTicketQuery.rtNumError"),trigger:"change"}],deviceNum:[{required:!0,message:p("app.agentTicketRefund.prntNoNotEmpty"),trigger:"change"},{pattern:Vt,message:p("app.agentTicketRefund.onlySupportDigits"),trigger:"change"}]};return{bopFromData:l,rules:T,bopFrom:f,reFundBop:async()=>{f.value&&await f.value.validate(async v=>{var r;if(v){const g=Ft.service({fullscreen:!0});try{const b=De("091U0103");((r=(await so(l,b)).data.value)==null?void 0:r.resCode)==="SUCCESS"&&c("bopRefundSuccess")}finally{g.close()}}})},cancel:()=>{c("update:modelValue",!1)}}},Kh=Hh,Yh=t("i",{class:"iconfont icon-close"},null,-1),Wh=[Yh],Xh={class:"flex item-center justify-start h-[36px] px-2.5 py-2 mt-[20px] mb-[10px] bg-yellow-3 rounded border border-solid border-yellow-2 text-yellow-1"},Jh=t("em",{class:"iconfont icon-warning-circle-fill leading-5"},null,-1),Zh={class:"ml-[5px] text-xs leading-5"},ev={class:"mt-[10px] px-[100px]"},tv={class:"flex justify-center crs-btn-dialog-ui"},nv=He({__name:"BopRefundDialog",emits:["bopRefundSuccess","update:modelValue"],setup(c,{emit:p}){const l=p,{bopFromData:f,rules:T,bopFrom:k,reFundBop:a,cancel:v}=Kh(l);return(r,g)=>{const b=xt,h=dt,E=st,y=pt,D=ft,$=ht("trimUpper");return s(),re(D,{title:r.$t("app.agentTicketQuery.bopRefund"),"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,"align-center":"true",class:"preview-dialog bop-preview-dialog tc-input-pad-init",width:"680px",onClose:e(v)},{default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:g[0]||(g[0]=(...A)=>e(v)&&e(v)(...A))},Wh),t("div",null,[t("div",Xh,[Jh,t("span",Zh,n(r.$t("app.agentTicketQuery.bopRefundTips")),1)]),t("div",ev,[o(y,{ref_key:"bopFrom",ref:k,rules:e(T),"require-asterisk-position":"right",model:e(f)},{default:i(()=>[o(h,{label:r.$t("app.agentTicketQuery.rtNum"),prop:"refundFormNumber"},{default:i(()=>[$e(o(b,{modelValue:e(f).refundFormNumber,"onUpdate:modelValue":g[1]||(g[1]=A=>e(f).refundFormNumber=A),onInput:g[2]||(g[2]=A=>{var V;return e(f).refundFormNumber=((V=e(f).refundFormNumber)==null?void 0:V.toUpperCase())??""})},null,8,["modelValue"]),[[$]])]),_:1},8,["label"]),o(h,{label:r.$t("app.agentTicketQuery.repelTicket.ticketMachineNumber"),prop:"deviceNum"},{default:i(()=>[o(It,{modelValue:e(f).deviceNum,"onUpdate:modelValue":[g[3]||(g[3]=A=>e(f).deviceNum=A),g[4]||(g[4]=A=>{var V;return(V=e(k))==null?void 0:V.validateField("deviceNum")})],modelModifiers:{trim:!0},"select-class":"w-[382.54px]"},null,8,["modelValue"])]),_:1},8,["label"]),t("div",tv,[o(E,{"data-gid":"091U0103",type:"primary",onClick:g[5]||(g[5]=A=>e(a)())},{default:i(()=>[z(n(r.$t("app.ticketStatus.confirmBtn")),1)]),_:1}),o(E,{onClick:e(v)},{default:i(()=>[z(n(r.$t("app.ticketStatus.cancelBtn")),1)]),_:1},8,["onClick"])])]),_:1},8,["rules","model"])])])]),_:1},8,["title","onClose"])}}});const av=c=>{const{t:p}=et(),l=Dt(),f=O(!0),T=O(),k=O(""),a=O(""),v=O(!1),r=O(),g=O(),b=O("1"),h=O(!1),E=O(!0),y=O(),D=O(!1),$=O({authLevel:"oneLevel",office:"",ticketNo:"",ticketNoEnd:""}),A=Le(()=>{var me;return((me=l.state.user)==null?void 0:me.agent)??""}),V=async()=>{var me,he,ue,U,J,ne,B,P,N,_;try{const w=De("091N0206");D.value=!0;const ce={ticketNumber:$.value.ticketNo},ie=await Na(ce,w);((me=ie.data.value)==null?void 0:me.code)==="200"?(k.value=((U=(ue=(he=ie==null?void 0:ie.data)==null?void 0:he.value)==null?void 0:ue.data.ticketDisplayAuthInfo)==null?void 0:U.bookOffice)??"",a.value=((B=(ne=(J=ie==null?void 0:ie.data)==null?void 0:J.value)==null?void 0:ne.data.ticketDisplayAuthInfo)==null?void 0:B.ticketNumber)??"",T.value=(_=(N=(P=ie==null?void 0:ie.data)==null?void 0:P.value)==null?void 0:N.data.ticketDisplayAuthInfo)==null?void 0:_.authInfoList.filter(oe=>oe.authTo!=="")):h.value=!1}finally{D.value=!1}},u=async me=>{var he;try{D.value=!0;const ue={removeOffice:me,ticketNumber:$.value.ticketNo},U=De("091N0102");((he=(await Vn(ue,U)).data.value)==null?void 0:he.code)==="200"&&(await ut({message:p("app.intlPassengerForm.del.delSuccess"),type:"success"}),await V())}finally{D.value=!1}},te=()=>{v.value=!0},X=async()=>{var me,he;try{D.value=!0;let ue=!0;$.value.authLevel==="oneLevel"?ue=!1:ue=!0;const U={accreditOffice:$.value.office,reAuth:ue,ticketNumber:$.value.ticketNo},J=De("091N0201"),ne=await Mn(U,J);((he=(me=ne==null?void 0:ne.data)==null?void 0:me.value)==null?void 0:he.code)==="200"&&(await ut({message:p("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await V())}finally{D.value=!1}},ge=async me=>{me&&await me.validate(async he=>{he&&X()})},q=(me,he,ue)=>{var B;const U=(B=$.value.ticketNo)==null?void 0:B.replace("-",""),J=U==null?void 0:U.substring(U.length-3),ne=parseInt($.value.ticketNoEnd,10)-parseInt(J,10);if(ne<0||ne>29){ue(p("app.agentTicketQuery.ticketAuth.lastThreeDigitsTips"));return}ue()},Y={office:[{required:!0,message:p("app.agentTicketQuery.ticketAuth.officeTipOne"),trigger:["change","blur"]},{pattern:vn,message:p("app.agentTicketQuery.ticketAuth.officeTipTwo"),trigger:["change","blur"]}],ticketNo:[{required:!0,message:p("app.agentTicketQuery.ticketAuth.ticketTipOne"),trigger:["change","blur"]},{pattern:/(^\d{3}(-)\d{10}$)|(^\d{13}$)/,message:p("app.agentTicketQuery.ticketAuth.ticketTipTwo"),trigger:["change","blur"]}],ticketNoEnd:[{pattern:/^[0-9]{3}$/,message:p("app.agentTicketQuery.ticketAuth.ticketNoEndTipOne"),trigger:["change","blur"]},{validator:q,trigger:["change","blur"]}]},L=()=>{var me;h.value=!1,(me=y.value)==null||me.resetFields(),$.value.office="",$.value.authLevel="oneLevel",$.value.ticketNoEnd=""},le=async()=>{try{D.value=!0,h.value=!0,await V()}finally{D.value=!1}},C=async()=>{h.value=!1},d=()=>{const me=[],he=$.value.ticketNo.replace("-","");if(me.push(he),!$.value.ticketNoEnd)return me;const ue=parseInt(he.substring(10),10),J=parseInt($.value.ticketNoEnd,10)-ue+1;for(let ne=1;ne<J;ne++){const B=(Number(he)+ne).toString();me.push(B)}return me},j=async()=>{try{const me=De("091N0204");D.value=!0;const he=[];(d()??[]).forEach(J=>{const ne={accreditOffice:$.value.office,reAuth:$.value.authLevel==="twoLevel",ticketNumber:J};he.push(Mn(ne,me))}),(await Promise.allSettled(he)).filter(J=>J.value.data.value.code!=="200").length<1&&(await ut({message:p("app.agentTicketQuery.ticketAuth.addAuthSuccess"),type:"success"}),await V())}finally{D.value=!1}},Te=async()=>{try{const me=De("091N0205");D.value=!0;const he=[];(d()??[]).forEach(J=>{const ne={removeOffice:$.value.office,ticketNumber:J};he.push(Vn(ne,me))}),(await Promise.allSettled(he)).filter(J=>J.value.data.value.code!=="200").length<1&&ut({message:p("app.intlPassengerForm.del.delSuccess"),type:"success"})}finally{D.value=!1}},se=()=>{var me;(me=y.value)==null||me.validate(he=>{if(he)switch(b.value){case"1":j();break;case"2":Te();break;case"3":le();break}})},x=()=>{c("update:modelValue",!1)},R=()=>`TICKET_AUTH_OFFICE_${A.value}_`??"",pe=()=>{try{const me=b.value==="2"?"delete":"add";return(JSON.parse(localStorage.getItem(`${R()}${me}`)??"")||[]).map(ue=>({value:ue}))}catch{return[]}},Re=()=>{if(vn.test($.value.office)){const me=b.value==="2"?"delete":"add",he=pe().map(U=>U.value).filter(U=>U!==$.value.office);he.unshift($.value.office);const ue=he.slice(0,5);localStorage.setItem(`${R()}${me}`,JSON.stringify(ue))}},we=me=>{var he;$.value.office=me.value,(he=g.value)==null||he.blur()};return bt(()=>$.value.office,me=>{$.value.office=(me==null?void 0:me.toUpperCase())??""}),{authType:b,authForm:$,authFormRules:Y,authTicketShow:h,handleConfirm:se,handleCancel:x,ticketAuthFormRef:y,authChange:L,dialogTableVisible:f,tableData:T,authDelete:u,authSubmit:ge,authTicketFormRef:r,showAdd:te,addAuthVisible:v,office:k,showAuthOffice:E,closeShowAuth:C,ticketNum:a,showLoading:D,officeHistoryRef:g,loadOfficeHistory:pe,saveOfficeHistory:Re,selectOfficeHistory:we}},sv=av,Yn=c=>(Ut("data-v-baf2007c"),c=c(),jt(),c),ov={class:"pb-[20px]"},iv=Yn(()=>t("i",{class:"iconfont icon-close"},null,-1)),lv=[iv],rv={class:"w-full px-0 pt-0.5 pb-[5px] bg-gray-0 rounded-md flex-col justify-start items-center gap-2.5 inline-flex mt-[-12px] query-auth-ticket"},cv={class:"self-stretch justify-between items-center inline-flex"},uv={class:"text-gray-1 text-lg font-bold leading-normal"},dv={class:"self-stretch h-full flex-col justify-center items-center flex"},pv={class:"self-stretch h-full flex-col justify-center items-center gap-2.5 flex"},fv={class:"h-full flex-col justify-center items-start gap-5 flex"},mv={class:"self-stretch flex-col justify-center items-start gap-2.5 flex"},gv={class:"justify-start items-center gap-2.5 inline-flex h-[20px] w-[420px]"},kv={class:"text-gray-2 text-xs font-normal leading-tight"},yv={class:"text-gray-2 text-xs font-normal leading-tight"},hv={class:"text-gray-2 text-xs font-normal leading-tight"},vv={key:0,class:"justify-start items-center gap-2.5 inline-flex h-[20px] authType"},_v={class:"text-gray-2 text-xs font-normal leading-tight"},Tv={class:"text-gray-2 text-xs font-normal leading-tight"},bv={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},xv={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},Nv={class:"justify-start items-center flex w-full"},$v={class:"self-stretch justify-start items-center gap-1.5 inline-flex w-full"},Rv=Yn(()=>t("div",{class:"text-gray-2 text-xs font-normal leading-tight"},"-",-1)),Cv={class:"self-stretch justify-center items-center gap-2.5 inline-flex w-full mt-5 crs-btn-dialog-ui"},wv={class:"justify-start items-start flex"},Sv={key:0,class:"w-full auth-data"},Pv={class:"self-stretch h-full flex-col justify-center items-center flex w-full"},Dv={class:"flex-col justify-center items-center gap-2.5 inline-flex w-full"},Av={class:"self-stretch justify-between items-center inline-flex"},Ev={class:"justify-start items-center gap-[68px] flex"},Ov={class:"text-gray-1 text-sm font-normal leading-snug"},Fv={class:"text-gray-1 text-sm font-normal leading-snug"},Vv={class:"flex-col justify-start items-start inline-flex"},Mv={class:"self-stretch p-2.5 rounded border border-brand-3 justify-start items-start inline-flex"},Lv={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Bv={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},Qv={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},Uv={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},jv={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Iv={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},zv={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},qv={class:"grow shrink basis-0 h-[22px] text-gray-1 text-sm font-normal leading-snug"},Gv={key:0},Hv={key:1},Kv={class:"grow shrink basis-0 flex-col justify-start items-start inline-flex"},Yv={class:"self-stretch h-[30px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},Wv={class:"grow shrink basis-0 h-[22px] text-gray-4 text-sm font-normal leading-snug"},Xv=Yn(()=>t("i",{class:"cursor-pointer primary-color iconfont icon-delete text-brand-2"},null,-1)),Jv={key:0,class:"self-stretch h-[82px] flex-col justify-start items-start gap-3.5 flex add-auth-tkt"},Zv={class:"self-stretch h-9 px-2.5 py-2 bg-yellow-3 rounded border border-yellow-2 flex-col justify-start items-start gap-2.5 flex"},e_={class:"h-5 justify-start items-center inline-flex text-yellow-1"},t_={class:"text-xs font-normal leading-tight"},n_={class:"w-[632px] justify-start items-center gap-2.5 inline-flex"},a_={class:"justify-start items-center gap-2.5 flex"},s_={class:"justify-start items-center flex"},o_={class:"text-gray-2 text-xs font-normal leading-tight"},i_={class:"text-gray-2 text-xs font-normal leading-tight"},l_={class:"justify-start items-center flex crs-btn-dialog-ui"},r_=He({__name:"TicketAuth",emits:["update:modelValue"],setup(c,{emit:p}){const l=p,{authType:f,authForm:T,authFormRules:k,handleConfirm:a,ticketAuthFormRef:v,authChange:r,authTicketShow:g,handleCancel:b,tableData:h,authDelete:E,authSubmit:y,authTicketFormRef:D,showAdd:$,addAuthVisible:A,office:V,closeShowAuth:u,ticketNum:te,showAuthOffice:X,showLoading:ge,officeHistoryRef:q,loadOfficeHistory:Y,saveOfficeHistory:L,selectOfficeHistory:le}=sv(l);return(C,d)=>{const j=zt,Te=qt,se=dt,x=La,R=at,pe=Nt,Re=xt,we=st,me=$a,he=pt,ue=qn,U=ft,J=ht("trimUpper"),ne=wt;return s(),re(U,{modelValue:e(X),"onUpdate:modelValue":d[11]||(d[11]=B=>Ue(X)?X.value=B:null),"close-on-click-modal":!1,"align-center":"true",width:"680px",class:"preview-dialog","close-on-press-escape":!1,"show-close":!1,onClose:e(b)},{default:i(()=>[$e((s(),m("div",ov,[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:d[0]||(d[0]=(...B)=>e(b)&&e(b)(...B))},lv),t("div",rv,[t("div",cv,[t("div",uv,n(C.$t("app.agentTicketQuery.ticketAuth.title")),1)]),t("div",dv,[t("div",pv,[t("div",fv,[o(he,{ref_key:"ticketAuthFormRef",ref:v,model:e(T),rules:e(k),"require-asterisk-position":"right",class:"w-[400px] auth-form"},{default:i(()=>[t("div",mv,[t("div",gv,[o(Te,{modelValue:e(f),"onUpdate:modelValue":d[1]||(d[1]=B=>Ue(f)?f.value=B:null),class:"h-[20px]",onChange:e(r)},{default:i(()=>[o(j,{label:"1"},{default:i(()=>[t("span",kv,n(C.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1}),o(j,{label:"2"},{default:i(()=>[t("span",yv,n(C.$t("app.agentTicketQuery.ticketAuth.deletingEntitlement")),1)]),_:1}),o(j,{label:"3"},{default:i(()=>[t("span",hv,n(C.$t("app.agentTicketQuery.ticketAuth.viewingAuthorization")),1)]),_:1})]),_:1},8,["modelValue","onChange"])]),e(f)==="1"?(s(),m("div",vv,[o(se,null,{default:i(()=>[o(Te,{modelValue:e(T).authLevel,"onUpdate:modelValue":d[2]||(d[2]=B=>e(T).authLevel=B)},{default:i(()=>[o(j,{label:"oneLevel",size:"large"},{default:i(()=>[t("span",_v,n(C.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),o(j,{label:"twoLevel",size:"large"},{default:i(()=>[t("span",Tv,n(C.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})]),_:1},8,["modelValue"])]),_:1})])):W("",!0),t("div",{class:Ve(["flex-col justify-center items-start gap-2.5 inline-flex w-full",{"mt-[-14px] authType-3":e(f)==="3"}])},[t("div",bv,[e(f)==="1"||e(f)==="2"?(s(),re(se,{key:0,class:"w-full",label:C.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:i(()=>[o(x,{ref_key:"officeHistoryRef",ref:q,modelValue:e(T).office,"onUpdate:modelValue":d[3]||(d[3]=B=>e(T).office=B),modelModifiers:{trim:!0},"fetch-suggestions":e(Y),placeholder:C.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e(le),onBlur:e(L)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])):W("",!0)]),t("div",xv,[t("div",Nv,[o(se,{label:C.$t("app.agentTicketQuery.ticketAuth.ticketNo"),class:"ticket-no w-full ticketNo-div",prop:"ticketNo"},{default:i(()=>[t("div",$v,[e(f)!=="3"?(s(),re(pe,{key:0,content:e(f)==="1"?C.$t("app.agentTicketQuery.ticketAuth.addTips"):C.$t("app.agentTicketQuery.ticketAuth.addTipsThree"),placement:"top-start","popper-class":"ticket-auth-popper"},{default:i(()=>[o(R,{class:"tooltip-icon ml-[-4px] mr-[13px] rotate-180"},{default:i(()=>[o(e(an))]),_:1})]),_:1},8,["content"])):W("",!0),$e(o(Re,{modelValue:e(T).ticketNo,"onUpdate:modelValue":d[4]||(d[4]=B=>e(T).ticketNo=B),class:"w-full",placeholder:C.$t("app.agentTicketQuery.ticketAuth.ticketTipOne")},null,8,["modelValue","placeholder"]),[[J]])])]),_:1},8,["label"])]),e(f)==="1"||e(f)==="2"?(s(),m(xe,{key:0},[Rv,o(se,{label:"",class:"ticket-no-end flex-nowrap",prop:"ticketNoEnd"},{default:i(()=>[$e(o(Re,{modelValue:e(T).ticketNoEnd,"onUpdate:modelValue":d[5]||(d[5]=B=>e(T).ticketNoEnd=B),placeholder:C.$t("app.agentTicketQuery.ticketAuth.ticketNoEnd"),maxlength:"3"},null,8,["modelValue","placeholder"]),[[J]])]),_:1})],64)):W("",!0)])],2)]),t("div",Cv,[t("div",wv,[e(f)==="2"?(s(),re(me,{key:0,title:C.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),teleported:!1,width:"160",icon:e(an),"icon-color":"var(--bkc-el-color-primary)",placement:"top","cancel-button-type":"button","data-gid":"091N0205",onConfirm:e(a)},{reference:i(()=>[o(we,{type:"primary"},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})]),_:1},8,["title","icon","onConfirm"])):(s(),re(we,{key:1,type:"primary",onClick:e(a)},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1},8,["onClick"])),o(we,{class:Ve([e(f)==="2"?"ml-[12px]":""]),onClick:e(b)},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1},8,["class","onClick"])])])]),_:1},8,["model","rules"])])]),e(g)&&e(h)?(s(),m("div",Sv,[t("div",Pv,[o(ue,{"border-style":"dashed"}),t("div",Dv,[t("div",Av,[t("div",Ev,[t("div",Ov,n(C.$t("app.agentTicketQuery.ticketAuth.ticketNo"))+"："+n(e(te)),1),t("div",Fv,n(C.$t("app.agentTicketQuery.ticketAuth.ticketingOfficeNumber"))+"："+n(e(V)),1)]),t("div",Vv,[t("div",{class:"justify-center items-center gap-1 inline-flex",onClick:d[6]||(d[6]=(...B)=>e($)&&e($)(...B))},[o(we,{link:"",type:"primary",size:"small"},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketAuth.addingEntitlement")),1)]),_:1})])])]),t("div",Mv,[t("div",Lv,[t("div",Bv,[t("div",Qv,n(C.$t("app.agentTicketQuery.ticketAuth.officeNum")),1)]),(s(!0),m(xe,null,Ee(e(h),(B,P)=>(s(),m("div",{key:P,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",Uv,n(B.authTo),1)]))),128))]),t("div",jv,[t("div",Iv,[t("div",zv,n(C.$t("app.agentTicketQuery.ticketAuth.authAuthority")),1)]),(s(!0),m(xe,null,Ee(e(h),(B,P)=>(s(),m("div",{key:P,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-2.5 inline-flex"},[t("div",qv,[B.reAuth?(s(),m("span",Gv,[o(R,null,{default:i(()=>[o(e(fa))]),_:1})])):(s(),m("span",Hv,n(C.$t("app.agentTicketQuery.ticketAuth.noAuth")),1))])]))),128))]),t("div",Kv,[t("div",Yv,[t("div",Wv,n(C.$t("app.agentTicketQuery.ticketAuth.operation")),1)]),(s(!0),m(xe,null,Ee(e(h),(B,P)=>(s(),m("div",{key:P,class:"self-stretch h-[50px] px-1.5 py-[7px] bg-gray-0 justify-start items-center gap-5 inline-flex"},[o(me,{title:C.$t("app.agentTicketQuery.ticketAuth.removeConfirm"),icon:e(an),teleported:!1,width:"160","icon-color":"var(--bkc-el-color-primary)","cancel-button-type":"button",onConfirm:N=>e(E)(B.authTo)},{reference:i(()=>[Xv]),_:2},1032,["title","icon","onConfirm"])]))),128))])]),e(A)?(s(),m("div",Jv,[t("div",Zv,[t("div",e_,[o(R,{class:"tooltip-icon mr-[8px]"},{default:i(()=>[o(e(Un))]),_:1}),t("div",t_,n(C.$t("app.agentTicketQuery.ticketAuth.addTipsTwo")),1)])]),t("div",n_,[t("div",a_,[o(he,{ref_key:"authTicketFormRef",ref:D,model:e(T),inline:"",size:"small",rules:e(k),"require-asterisk-position":"right"},{default:i(()=>[o(se,null,{default:i(()=>[o(Te,{modelValue:e(T).authLevel,"onUpdate:modelValue":d[7]||(d[7]=B=>e(T).authLevel=B)},{default:i(()=>[t("div",s_,[o(j,{label:"oneLevel",size:"large"},{default:i(()=>[t("span",o_,n(C.$t("app.agentTicketQuery.ticketAuth.oneAuthorization")),1)]),_:1}),o(j,{label:"twoLevel",size:"large"},{default:i(()=>[t("span",i_,n(C.$t("app.agentTicketQuery.ticketAuth.secondaryAuthorization")),1)]),_:1})])]),_:1},8,["modelValue"])]),_:1}),o(se,{label:C.$t("app.agentTicketQuery.ticketAuth.officeNum"),prop:"office"},{default:i(()=>[o(x,{ref_key:"officeHistoryRef",ref:q,modelValue:e(T).office,"onUpdate:modelValue":d[8]||(d[8]=B=>e(T).office=B),modelModifiers:{trim:!0},"fetch-suggestions":e(Y),placeholder:C.$t("app.agentTicketQuery.ticketAuth.officeTipOne"),class:"w-full",debounce:"",onSelect:e(le),onBlur:e(L)},null,8,["modelValue","fetch-suggestions","placeholder","onSelect","onBlur"])]),_:1},8,["label"])]),_:1},8,["model","rules"])]),t("div",l_,[e(A)?(s(),re(we,{key:0,type:"primary","data-gid":"091N0101",onClick:d[9]||(d[9]=B=>e(y)(e(D)))},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketAuth.confirm")),1)]),_:1})):W("",!0),e(A)?(s(),re(we,{key:1,onClick:d[10]||(d[10]=B=>e(u)())},{default:i(()=>[z(n(C.$t("app.agentTicketQuery.ticketAuth.cancel")),1)]),_:1})):W("",!0)])])])):W("",!0)])])])):W("",!0)])])])),[[ne,e(ge)]])]),_:1},8,["modelValue","onClose"])}}});const c_=vt(r_,[["__scopeId","data-v-baf2007c"]]),u_={class:"flex justify-between"},d_={key:0,class:"text-xs h-6 justify-start items-start inline-flex cursor-pointer mt-[8px]"},p_={class:"mt-[8px] h-[476px]"},f_={key:1,class:"flex justify-center items-center flex-col pt-[156px]"},m_=["alt"],g_={class:"mt-[19px] text-lg leading-[24px] font-bold text-gray-2"},k_={class:"text-base text-gray-4"},y_=He({__name:"RtktDialog",emits:["update:modelValue"],setup(c,{emit:p}){const l=p,{t:f}=et(),T=O(!1),k=O(),a=O("original"),v=Be({}),r={ticketNo:[{required:!0,message:f("app.agentTicketQuery.validate.tktNoNull"),trigger:["change","blur"]},{pattern:pn,message:f("app.agentTicketQuery.validate.tktNoError"),trigger:["change","blur"]}]},g=yt({ticketNo:""}),b=()=>{var y;(y=k.value)==null||y.validate(async D=>{var $;if(D)try{T.value=!0;const A=De("091U0105"),V=await oo(g.ticketNo,A,!1);a.value="original",v.value=(($=V.data.value)==null?void 0:$.data)??{}}catch{v.value={}}finally{T.value=!1}})},h=y=>{a.value=y},E=()=>{var y;(y=k.value)==null||y.resetFields()};return(y,D)=>{const $=xt,A=dt,V=st,u=pt,te=ft,X=ht("trimUpper"),ge=wt;return s(),re(te,{title:"RTKT",width:"890px","close-on-click-modal":!1,"align-center":!0,class:"repel-tikect-dialog tc-input-pad-init crs-new-ui-init-cls",onClose:D[4]||(D[4]=()=>l("update:modelValue",!1))},{default:i(()=>{var q,Y,L;return[t("div",u_,[o(u,{ref_key:"formRef",ref:k,model:g,rules:r,inline:!0,"require-asterisk-position":"right"},{default:i(()=>[o(A,{prop:"ticketNo",label:e(f)("app.agentTicketQuery.ticketAuth.ticketNo")},{default:i(()=>[$e(o($,{modelValue:g.ticketNo,"onUpdate:modelValue":D[0]||(D[0]=le=>g.ticketNo=le),clearable:!0},null,8,["modelValue"]),[[X]])]),_:1},8,["label"]),o(A,null,{default:i(()=>[o(V,{type:"primary","data-gid":"091U0105",onClick:b},{default:i(()=>[z(n(y.$t("app.agentTicketQuery.queryBtn")),1)]),_:1})]),_:1}),o(A,null,{default:i(()=>[o(V,{onClick:E},{default:i(()=>[z(n(y.$t("app.agentTicketQuery.reset")),1)]),_:1})]),_:1})]),_:1},8,["model"]),(q=v.value)!=null&&q.ticket?(s(),re(To,{key:0,"rtkt-detailed-info":v.value,onClick:D[1]||(D[1]=le=>l("update:modelValue",!1))},null,8,["rtkt-detailed-info"])):W("",!0)]),(Y=v.value)!=null&&Y.ticket?(s(),m("div",d_,[t("div",{class:Ve(["px-2 py-0.5 rounded-tl-sm rounded-bl-sm border",[a.value==="original"?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:D[2]||(D[2]=le=>h("original"))},n(y.$t("app.queryRtkt.rtktOriginalTab")),3),t("div",{class:Ve(["px-2 py-0.5 rounded-tr-sm rounded-br-sm border",[a.value==="details"?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:D[3]||(D[3]=le=>h("details"))},n(y.$t("app.queryRtkt.rtktDetailsTab")),3)])):W("",!0),$e((s(),m("div",p_,[(L=v.value)!=null&&L.ticket?(s(),m(xe,{key:0},[a.value==="original"?(s(),re(Uo,{key:0,"rtkt-detail":v.value},null,8,["rtkt-detail"])):W("",!0),a.value==="details"?(s(),re(bo,{key:1,"rtkt-detailed-info":v.value},null,8,["rtkt-detailed-info"])):W("",!0)],64)):(s(),m("div",f_,[t("img",{src:Gn,alt:y.$t("app.fastQuery.netPrice.nodata")},null,8,m_),t("p",g_,n(y.$t("app.fastQuery.netPrice.noOrderInfo")),1),t("p",k_,n(y.$t("app.agentTicketQuery.plsInputTicketToSearch")),1)]))])),[[ge,T.value]])]}),_:1})}}});const h_=c=>{var ve;const{t:p}=et(),l=Dt(),f=27,T=O("ONLY_REFUND"),k=O(""),a=Le(()=>u.currency==="CNY"),v=O(!1),r=O(),{defaultOffice:g,office:b,defaultRoleWithPid:h,agent:E,defaultOfficeIataNum:y}=l.state.user,D=h?g:((ve=b==null?void 0:b.split(";"))==null?void 0:ve[0])??"",$=O([]),A=O([]),V={BSP:{label:p("app.agentTicketQuery.BSPTicket"),value:"BSP"},GPBSP:{label:p("app.agentTicketQuery.GPTicket"),value:"GPBSP"},BOPBSP:{label:p("app.agentTicketQuery.BOPTicket"),value:"BOPBSP"},CDS:{label:p("app.agentTicketQuery.CDSTicket"),value:"CDS"},GPCDS:{label:p("app.agentTicketQuery.GP_CDSTicket"),value:"GPCDS"},ARL:{label:p("app.agentTicketQuery.OWNTicket"),value:"ARL"}},u=yt({iata:y,agent:E,office:D,volunteer:"",createUser:"",printNo:"",check:"",marketAirline:"",currency:"",name:"",psgType:"",etTag:"1",remark:"",remarkCode:"",remarkInfo:"",creditCard:"",conjunction:"",airline:"",tktType:"D",payType:"CASH",ticketNo:"",totalAmount:"",commision:"",commisionRate:"",otherDeduction:"",otherDeductionRate:"",netRefund:"0.00",totalTaxs:"",taxs:[],rate:"",receiptPrinted:"",segment:[],crsPnrNo:"",pnr:"",isCoupon:"",isDragonBoatOffice:g===wo,refundDate:it().format("DDMMMYY/HHmm").toUpperCase(),conjunctionTicketNos:[],ticketManagementOrganizationCode:"",ticketStatus:!0}),te=Le(()=>{const F=`${u.airline}${u.ticketNo}`;return!Ns.test(F)}),X=Le(()=>{var F;return(F=l.state.user)==null?void 0:F.entityType}),ge=Le(()=>!["CDS","GPCDS"].includes(u.ticketManagementOrganizationCode)),q=()=>{ge.value||(u.printNo="")},Y=(F,Z,Q)=>{var fe;const ee=Number(F.field.split(".")[1]);u.taxs[ee].name&&!Z?Q(p("app.agentTicketRefund.taxAmount")):!u.taxs[ee].name&&!Z&&((fe=r.value)==null||fe.clearValidate(`taxs.${ee}.name`),Q()),Q()},L=(F,Z,Q)=>{var fe;const ee=Number(F.field.split(".")[1]);u.taxs[ee].value&&!Z?Q(p("app.agentTicketRefund.taxes")):!u.taxs[ee].value&&!Z&&((fe=r.value)==null||fe.clearValidate(`taxs.${ee}.value`),Q()),Q()},le=(F,Z,Q)=>{u.payType==="TC"&&(Z?!u.isDragonBoatOffice&&!va.test(Z)?Q(p("app.agentTicketRefund.creditCardInput")):u.isDragonBoatOffice&&!_a.test(Z)&&Q(p("app.agentTicketRefund.dragonBoatOfficeInput")):Q(p("app.agentTicketRefund.creditCardNotEmpty"))),Q()},C=()=>($.value=new Array(4).fill(""),u.segment.some(F=>F)?u.segment.forEach((F,Z)=>{F&&!Fs.test(F)&&($.value[Z]=p("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:4}))}):$.value[0]=p("app.pnrManagement.validate.required"),$.value.every(F=>!F)),d=Le(()=>({tktType:[{required:!0,message:p("app.agentTicketRefund.ticketTypeNotEmpty"),trigger:["change","blur"]}],printNo:[{required:!0,message:p("app.agentTicketRefund.prntNoNotEmpty"),trigger:"blur"},{pattern:Vt,message:p("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]}],airline:[{required:!0,message:p("app.agentTicketRefund.refundAirlineSettlementCodeNotEmpty"),trigger:"blur"},{pattern:Ta,message:p("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:3}),trigger:["change","blur"]}],ticketNo:[{required:!0,message:p("app.agentTicketRefund.refundTicketNoNotEmpty"),trigger:"blur"},{pattern:As,message:p("app.agentTicketRefund.supportDigitsAndHorizontalBar"),trigger:["change","blur"]}],conjunction:[{required:!0,message:p("app.agentTicketRefund.numberOfCombinedTicketsNotEmpty"),trigger:"blur"},{pattern:Es,message:p("app.agentTicketRefund.onlySupportFixedQuantityDigits",{num:1}),trigger:["change","blur"]}],currency:[{required:!0,message:p("app.agentTicketRefund.currencyNotEmpty"),trigger:"blur"},{pattern:Os,message:p("app.agentTicketRefund.onlySupportLetters"),trigger:["change","blur"]}],payType:[{required:!0,message:p("app.agentTicketRefund.paymentSel"),trigger:["change","blur"]},{pattern:ga,message:p("app.agentTicketRefund.paymentInput"),trigger:["change","blur"]}],totalAmount:[{pattern:a.value?$t:Rt,message:a.value?p("app.agentTicketRefund.cnyTip"):p("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]}],taxValue:[{pattern:a.value?ka:ya,message:a.value?p("app.agentTicketRefund.cnyTip"):p("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]},{validator:Y,trigger:["change","blur"]}],taxName:[{pattern:ha,message:p("app.agentTicketRefund.taxes"),trigger:["change","blur"]},{validator:L,trigger:["change","blur"]}],commisionRate:[{pattern:a.value?$t:Rt,message:a.value?p("app.agentTicketRefund.cnyTip"):p("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]}],otherDeductionRate:[{pattern:a.value?$t:Rt,message:a.value?p("app.agentTicketRefund.cnyTip"):p("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]}],otherDeduction:[{pattern:a.value?$t:Rt,message:a.value?p("app.agentTicketRefund.cnyTip"):p("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]}],commision:[{pattern:a.value?$t:Rt,message:a.value?p("app.agentTicketRefund.cnyTip"):p("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]}],psdName:[{validator:Da,trigger:["change","blur"]}],creditCard:[{validator:le,trigger:["change","blur"]}],remarkInfo:[{pattern:Fn,message:p("app.agentTicketRefund.remarkHint"),trigger:["change","blur"]}],netRefund:[{required:!0,message:p("app.agentTicketRefund.prntNoNotEmpty"),trigger:["change","blur"]},{pattern:a.value?$t:Rt,message:a.value?p("app.agentTicketRefund.cnyTip"):p("app.agentTicketRefund.onlySupportDigits"),trigger:["change","blur"]}],ticketManagementOrganizationCode:[{required:!0,message:p("app.agentTicketRefund.prntNoNotEmpty"),trigger:["change","blur"]}]})),j=()=>{const{totalAmount:F,totalTaxs:Z,otherDeduction:Q,commision:ee,commisionRate:fe}=u;if(!he())if(ee){const Ce=new Ze(kt(Number(F),Number(Z),"+")).minus(new Ze(kt(Number(Q),Number(ee),"+"))).toString();u.netRefund=Ce??""}else{const Ce=new Ze(new Ze(Number(F)).times(Number(fe))).dividedBy(100).toString(),Oe=Number(Ce).toString(),ye=new Ze(kt(Number(F),Number(Z),"+")).minus(new Ze(kt(Number(Q),Number(Oe),"+"))).toString();u.netRefund=ye??""}},Te=()=>{if(u.taxs.length===f)return;const F=u.taxs.length+5>f?f-u.taxs.length:5,Z=new Array(F).fill({name:"",value:""});u.taxs=u.taxs.concat(Z).map(Q=>({...Q}))},se=()=>{let F=new Ze(0);u.taxs.forEach((Z,Q)=>{var ee;(ee=r.value)==null||ee.validateField(`taxs.${Q}.value`).then(fe=>{fe&&(F=F.add(new Ze(Z.value?Z.value:0)),u.totalTaxs=F.toString(),j())})})},x=F=>F.length<10?F.concat(new Array(10-F.length).fill({name:"",value:""})).map(Z=>({...Z})):F,R=async()=>{var F,Z;try{v.value=!0;let Q=`${u.airline}${u.ticketNo}`;const ee=Q.indexOf("-");Q=ee!==-1?Q.substring(0,ee):Q;const fe=De("091Q0203"),Ce=(Z=(F=await Sa({ticketNo:Q},fe))==null?void 0:F.data)==null?void 0:Z.value;u.taxs=[],((Ce==null?void 0:Ce.rtKTTaxes)??[]).forEach(Oe=>{u.taxs.push({name:Oe.taxType,value:Oe.taxAmount})}),u.taxs=x(u.taxs),u.totalAmount=(Ce==null?void 0:Ce.ticketAmount)??"",u.currency=(Ce==null?void 0:Ce.currency)??"",se()}finally{v.value=!1}},pe=async()=>{const F=[];u.taxs.forEach((Z,Q)=>{var ee,fe;F.push((ee=r.value)==null?void 0:ee.validateField(`taxs.${Q}.name`)),F.push((fe=r.value)==null?void 0:fe.validateField(`taxs.${Q}.value`))}),await Promise.all(F),u.taxs.forEach((Z,Q)=>{u.taxs[Q].value&&(u.taxs[Q].value=u.taxs[Q].value??0)}),se()},Re=async()=>{var F;we(),u.segment=new Array(4).fill(""),$.value=new Array(4).fill(""),await ln(),u.ticketNo&&((F=r.value)==null||F.validateField("ticketNo"))},we=()=>{u.tktType==="I"&&(u.ticketStatus=u.etTag==="1")},me=F=>F&&(a.value?!$t.test(F):!Rt.test(F)),he=()=>{const{totalAmount:F,otherDeductionRate:Z,otherDeduction:Q,commision:ee,commisionRate:fe}=u;return me(F??"")||me(Z??"")||me(Q??"")||me(ee??"")||me(fe??"")},ue=F=>{if(!he()){if(F==="otherDeductionRate"&&u.otherDeductionRate){u.otherDeductionRate=u.otherDeductionRate??"";const Z=new Ze(new Ze(Number(u.totalAmount)).times(Number(u.otherDeductionRate))).dividedBy(100).toString();u.otherDeduction=Z??""}F==="commisionRate"&&u.commisionRate&&(u.commision=new Ze(new Ze(Number(u.totalAmount)).times(Number(u.commisionRate))).dividedBy(100).toString()),j()}},U=()=>{u.commisionRate||(u.commision="")},J=()=>{r.value.clearValidate("creditCard"),u.payType.toUpperCase()==="TC"&&(u.creditCard=u.isDragonBoatOffice?So:"")},ne=F=>{F.target.value&&!Aa.some(Z=>Z.label===F.target.value)&&(u.payType=F.target.value)},B=F=>({commision:F.commision&&Number(F.commision)>0?F.commision:"0",commisionRate:F.commisionRate??"",netRefund:F.netRefund,otherDeduction:F.otherDeduction||"0",taxs:F.taxs.filter(Z=>!!Z.value),totalAmount:F.totalAmount||"0",totalTaxs:F.totalTaxs}),P=F=>{const Z=F.segment.filter(ee=>!!ee),Q=new Array(4-(Z==null?void 0:Z.length)).fill("0000");return[...Z,...Q]},N=F=>{var Z;return{airline:F.airline,crsPnrNo:"",currency:F.currency,etTag:F.etTag,marketAirline:"",name:(Z=F.name)==null?void 0:Z.trim(),payType:F.payType.toUpperCase(),pnr:"",psgType:"",segment:[],couponNos:P(F),ticketNo:`${F.airline}-${F.ticketNo}`,tktType:F.tktType,ticketStatus:F.tktType==="I"&&F.airline!=="784"?F.ticketStatus:!1}},_=F=>({modificationType:"ONLY_REFUND",prntNo:F.printNo,ticketManagementOrganizationCode:F.ticketManagementOrganizationCode,resultpre:{amount:B(F),conjunction:F.conjunction,creditCard:F.creditCard,isCoupon:F.isCoupon,office:F.office,operator:F.createUser,remark:F.remarkInfo??"",segList:[],ticket:N(F),volunteer:"NON_VOLUNTEER_MANUAL"}}),w=()=>{c("update:modelValue",!1)},ce=F=>({ticketNo:F,ticketType:u.tktType,printerNo:u.printNo??"",refundNo:k.value??"",ticketManagementOrganizationCode:u.ticketManagementOrganizationCode??""}),ie=async()=>{const F=`${u.airline}${u.ticketNo.includes("-")?u.ticketNo.split("-")[0]:u.ticketNo}`;await c("deliverRefundData",ce(F)),await w(),nt.close()},oe=async()=>{var F;(F=r.value)==null||F.validate(async Z=>{var ee,fe,Ce,Oe;const Q=C();if(!(!Z||!Q)){v.value=!0;try{if(T.value==="ONLY_REFUND"){const ye=_(u),Ye=De("091Q0202"),qe=(fe=(ee=await xo(ye,Ye))==null?void 0:ee.data)==null?void 0:fe.value,Qe=(Ce=qe==null?void 0:qe.data)==null?void 0:Ce.refundNumber.replace("-","");k.value=Qe??"",u.ticketManagementOrganizationCode!=="ARL"&&(k.value=Qe?Qe.substring(3):"");const ae=Pe("p",{className:"flex",style:{"margin-top":"10px"}},[Pe("span",{className:"text-[14px]"},`${p("app.agentTicketRefund.refundTicketNumber")}：`),Pe("span",{className:"text-[14px] text-brand-2 cursor-pointer",onClick:ie},k.value)]),ot=(Oe=qe==null?void 0:qe.data)!=null&&Oe.refundStatus.includes("success")?p("app.agentTicketRefund.refundSuccess"):p("app.agentTicketRefund.refundSuccessButStatusFailed");uo(ot,ae).then(w).catch(w)}}finally{v.value=!1}}})},Se=()=>{var F,Z,Q,ee,fe,Ce,Oe,ye,Ye,qe,Qe,ae;((F=X.value)!=null&&F.includes("$$$")||(Z=X.value)!=null&&Z.includes("BSP"))&&(A.value.push(V.BSP),A.value.push(V.GPBSP)),!((Q=X.value)!=null&&Q.includes("BSP"))&&((ee=X.value)!=null&&ee.includes("GP"))&&A.value.push(V.GPBSP),((fe=X.value)!=null&&fe.includes("$$$")||(Ce=X.value)!=null&&Ce.includes("BOP"))&&A.value.push(V.BOPBSP),((Oe=X.value)!=null&&Oe.includes("$$$")||(ye=X.value)!=null&&ye.includes("CDS"))&&(A.value.push(V.CDS),A.value.push(V.GPCDS)),((Ye=X.value)!=null&&Ye.includes("$$$")||(qe=X.value)!=null&&qe.includes("本票"))&&A.value.push(V.ARL),u.ticketManagementOrganizationCode=((ae=(Qe=A.value)==null?void 0:Qe[0])==null?void 0:ae.value)??""},de=()=>{var F;(F=r.value)==null||F.validate(async Z=>{})};return(()=>{u.taxs=x([]),u.segment=new Array(4).fill(""),$.value=new Array(4).fill(""),Se()})(),{refundType:T,fullscreenLoading:v,refundFormData:u,FORM_RULES:d,refundFormRef:r,MAX_TAX_NUM:f,segmentErrorMessage:$,queryRTKTDisabled:te,getTaxAll:R,validSegment:C,addTax:Te,checkTax:pe,calcAmount:ue,changePayType:J,bindPaymentValue:ne,submitRefund:oe,closeDialog:w,changeTicketType:Re,commisionRateChange:U,ticketOrganizationList:A,isShowPrintNo:ge,changeTicketManagementOrganizationCode:q,changeET:we,getCurrency:de}},v_=h_,__=t("i",{class:"iconfont icon-close"},null,-1),T_=[__],b_={class:"ticket-refund-form"},x_={class:"h-[24px] my-[10px] flex justify-center items-center text-gray-2 text-[16px] font-bold"},N_={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6"},$_={class:"self-stretch justify-start items-start gap-5 inline-flex"},R_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},C_={class:"w-[90px] text-gray-3 text-xs shrink-0"},w_=t("div",{class:"justify-start items-start flex text-gray-2 text-xs font-bold"},"-",-1),S_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},P_={key:1,class:"inline-block w-[12px]"},D_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},A_={key:1},E_={class:"inline-block w-[84px] text-[12px] text-gray-2"},O_=t("span",null,"-",-1),F_={class:"self-stretch justify-start items-start gap-5 inline-flex"},V_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},M_={class:"w-[90px] text-gray-3 text-xs shrink-0"},L_={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},B_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Q_={class:"w-[84px] text-gray-3 text-xs shrink-0"},U_={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},j_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},I_={class:"w-[84px] text-gray-3 text-xs shrink-0"},z_={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},q_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},G_={class:"w-[84px] text-gray-3 text-xs shrink-0"},H_={class:"justify-start items-start flex text-gray-2 text-xs font-bold"},K_={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},Y_={class:"self-stretch justify-start items-start gap-5 inline-flex"},W_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] conjunction-num"},X_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] relative"},J_=t("span",{class:"iconfont icon-info-circle-line absolute left-[58px]"},null,-1),Z_={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] conjunction-num"},e1=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),t1={class:"self-stretch justify-start items-start gap-5 inline-flex"},n1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] passenger-name"},a1={class:"grow shrink basis-0 min-h-[32px] justify-start flex mb-[10px]"},s1={class:"w-[84px] text-gray-3 text-xs h-[32px] flex items-center shrink-0 refund-segment"},o1={class:"self-stretch justify-start items-start gap-5 inline-flex"},i1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require amount"},l1={key:0,class:"not-required-tip"},r1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require"},c1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] require refund-type"},u1=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),d1={class:"w-full flex-col justify-start items-start inline-flex border-b border-dashed border-gray-6 mt-[10px]"},p1={class:"self-stretch justify-start items-start gap-5 inline-flex"},f1={class:"shrink h-[32px] justify-start items-center gap-1 flex mb-[10px]"},m1={key:0,class:"shrink ml-[122px] h-[32px] justify-start items-center gap-1 flex mb-[10px]"},g1={class:"text-gray-2 text-xs font-bold leading-tight"},k1={class:"w-full mb-[10px]"},y1={class:"flex justify-between text-gray-3 text-xs leading-[20px] mb-[6px]"},h1={class:"ml-[20px]"},v1={class:"text-gray-2 font-[700]"},_1={class:"w-full grow self-stretch justify-start items-start gap-[10px] gap-x-[20px] flex flex-wrap"},T1={class:"w-[20px] text-gray-3 text-xs shrink-0 leading-8"},b1={class:"w-[40px] mr-[6px] shrink-0"},x1={class:"w-full flex-col justify-start items-start inline-flex mt-[10px]"},N1={class:"self-stretch justify-start items-start gap-5 inline-flex"},$1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},R1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},C1=t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1),w1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},S1=t("div",{class:"text-[var(--bkc-color-gray-3)] text-xs font-normal leading-tight ml-[4px]"},"%",-1),P1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},D1={class:"self-stretch justify-start items-start gap-5 inline-flex"},A1={class:"grow shrink basis-0 h-[32px] justify-start items-center flex mb-[10px]"},E1={class:"w-[90px] text-gray-3 text-xs shrink-0"},O1={class:"justify-start items-center flex text-gray-2 text-xs relative"},F1=t("span",{class:"iconfont icon-info-circle-line absolute left-[-40px]"},null,-1),V1={class:"grow shrink basis-0 min-h-[32px] justify-start items-center gap-1 flex mb-[10px] rate"},M1={class:"text-gray-2 font-[700]"},L1=t("span",null,[t("i",{class:"iconfont icon-info-circle-line text-[20px] font-normal ml-[10px] text-gray-4"})],-1),B1={class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},Q1=t("div",{class:"grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px]"},null,-1),U1={class:"flex justify-center w-full mt-[10px] footer items-center"},j1=He({__name:"ManualRefund",emits:["update:modelValue","deliverRefundData"],setup(c,{emit:p}){const l=p,{refundType:f,fullscreenLoading:T,refundFormRef:k,refundFormData:a,FORM_RULES:v,MAX_TAX_NUM:r,segmentErrorMessage:g,queryRTKTDisabled:b,getTaxAll:h,validSegment:E,addTax:y,checkTax:D,calcAmount:$,changePayType:A,bindPaymentValue:V,submitRefund:u,closeDialog:te,changeTicketType:X,commisionRateChange:ge,ticketOrganizationList:q,isShowPrintNo:Y,changeTicketManagementOrganizationCode:L,changeET:le,getCurrency:C}=v_(l);return(d,j)=>{const Te=Jt,se=Zt,x=dt,R=at,pe=xt,Re=Nt,we=gn,me=Ht,he=st,ue=pt,U=zt,J=qt,ne=ft,B=ht("trimUpper"),P=wt;return s(),re(ne,{width:"1040",title:d.$t("app.agentTicketRefund.manualRefundBtn"),"show-close":!1,"close-on-click-modal":!1,class:"crs-new-ui-init-cls ticket-manual-refund-dialog","align-center":!0,onClose:e(te)},{default:i(()=>[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:j[0]||(j[0]=(...N)=>e(te)&&e(te)(...N))},T_),$e((s(),m("div",null,[t("div",b_,[t("div",null,[t("div",x_,n(d.$t("app.agentTicketRefund.refundInformationForm")),1)]),o(ue,{ref_key:"refundFormRef",ref:k,model:e(a),"require-asterisk-position":"right"},{default:i(()=>{var N,_,w,ce,ie;return[t("div",N_,[t("div",$_,[t("div",R_,[t("div",C_,n(d.$t("app.agentTicketRefund.refundTicketNumber")),1),w_]),t("div",S_,[o(x,{label:d.$t("app.refundForm.ticketType"),prop:"tktType",rules:e(v).tktType},{default:i(()=>[o(se,{modelValue:e(a).tktType,"onUpdate:modelValue":j[1]||(j[1]=oe=>e(a).tktType=oe),placeholder:d.$t("app.agentTicketRefund.choose"),clearable:"",onChange:e(X)},{default:i(()=>[(s(!0),m(xe,null,Ee(e(Po),oe=>(s(),re(Te,{key:oe.value,label:oe.label,value:oe.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange"])]),_:1},8,["label","rules"])]),t("div",{class:Ve(["grow shrink basis-0 h-[32px] justify-start items-center gap-1 flex mb-[10px] ticketManagementOrganizationCode",e($s)()==="en"?"ticket-organization-en":""])},[o(x,{label:d.$t("app.refundForm.ticketManagementOrganizationCode"),prop:"ticketManagementOrganizationCode",rules:e(v).ticketManagementOrganizationCode},{default:i(()=>[o(se,{modelValue:e(a).ticketManagementOrganizationCode,"onUpdate:modelValue":j[2]||(j[2]=oe=>e(a).ticketManagementOrganizationCode=oe),disabled:!e(a).ticketManagementOrganizationCode,placeholder:e(a).ticketManagementOrganizationCode?"":d.$t("app.agentTicketQuery.noData"),onChange:e(L)},{default:i(()=>[(s(!0),m(xe,null,Ee(e(q),oe=>(s(),re(Te,{key:oe.value,label:oe.label,value:oe.value},{default:i(()=>[t("span",null,[e(a).ticketManagementOrganizationCode===oe.value?(s(),re(R,{key:0,size:12,class:"iconfont icon-right-line"})):(s(),m("span",P_))]),z(" "+n(oe.label),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","disabled","placeholder","onChange"])]),_:1},8,["label","rules"])],2),t("div",D_,[e(Y)?(s(),re(x,{key:0,label:d.$t("app.agentTicketRefund.prntNo"),prop:"printNo",rules:e(v).printNo},{default:i(()=>[$e(o(It,{modelValue:e(a).printNo,"onUpdate:modelValue":[j[3]||(j[3]=oe=>e(a).printNo=oe),j[4]||(j[4]=oe=>e(k).validateField("printNo"))],"need-distinguish":!1,"select-class":"w-[150px]"},null,8,["modelValue"]),[[B]])]),_:1},8,["label","rules"])):(s(),m("span",A_,[t("span",E_,n(d.$t("app.agentTicketRefund.prntNo")),1),O_]))])]),t("div",F_,[t("div",V_,[t("div",M_,n(d.$t("app.agentTicketRefund.refundAgent")),1),t("div",L_,n(((N=e(a))==null?void 0:N.agent)??"-"),1)]),t("div",B_,[t("div",Q_,n(d.$t("app.agentTicketRefund.refundIataNo")),1),t("div",U_,n(((_=e(a))==null?void 0:_.iata)??"-"),1)]),t("div",j_,[t("div",I_,n(d.$t("app.agentTicketRefund.refundOffice")),1),t("div",z_,n(((w=e(a))==null?void 0:w.office)??"-"),1)]),t("div",q_,[t("div",G_,n(d.$t("app.agentTicketRefund.refundDate")),1),t("div",H_,n(((ce=e(a))==null?void 0:ce.refundDate)??"-"),1)])])]),t("div",K_,[t("div",Y_,[t("div",W_,[o(x,{label:d.$t("app.agentTicketRefund.refundAirlineSettlementCode"),class:"w90",prop:"airline",rules:e(v).airline},{default:i(()=>[$e(o(pe,{modelValue:e(a).airline,"onUpdate:modelValue":j[5]||(j[5]=oe=>e(a).airline=oe),clearable:""},null,8,["modelValue"]),[[B]])]),_:1},8,["label","rules"])]),t("div",X_,[o(x,{label:d.$t("app.agentTicketRefund.refundTicketNo"),prop:"ticketNo",rules:e(v).ticketNo},{default:i(()=>[$e(o(pe,{modelValue:e(a).ticketNo,"onUpdate:modelValue":j[6]||(j[6]=oe=>e(a).ticketNo=oe),clearable:""},null,8,["modelValue"]),[[B]])]),_:1},8,["label","rules"]),o(Re,{placement:"top",content:d.$t("app.agentTicketRefund.internationalTicketNoTips"),"popper-class":"w-[255px]"},{default:i(()=>[J_]),_:1},8,["content"])]),t("div",Z_,[o(x,{label:d.$t("app.agentTicketRefund.numberOfCombinedTickets"),prop:"conjunction",rules:e(v).conjunction},{default:i(()=>[$e(o(pe,{modelValue:e(a).conjunction,"onUpdate:modelValue":j[7]||(j[7]=oe=>e(a).conjunction=oe),clearable:""},null,8,["modelValue"]),[[B]])]),_:1},8,["label","rules"])]),e1]),t("div",t1,[t("div",n1,[o(x,{label:d.$t("app.agentTicketRefund.passName"),prop:"name",class:"w90",rules:e(v).psdName},{default:i(()=>[$e(o(pe,{modelValue:e(a).name,"onUpdate:modelValue":j[8]||(j[8]=oe=>e(a).name=oe),clearable:"",onInput:j[9]||(j[9]=oe=>e(a).name=e(a).name.toUpperCase())},null,8,["modelValue"]),[[B]])]),_:1},8,["label","rules"])]),t("div",a1,[t("div",s1,n(d.$t("app.agentTicketRefund.refundSeg")),1),(s(!0),m(xe,null,Ee(e(a).segment,(oe,Se)=>(s(),re(x,{key:Se,prop:"segment."+Se,error:e(g)[Se],class:"mr10"},{default:i(()=>[$e(o(pe,{modelValue:e(a).segment[Se],"onUpdate:modelValue":de=>e(a).segment[Se]=de,clearable:"",onBlur:e(E)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),[[B]])]),_:2},1032,["prop","error"]))),128))])]),t("div",o1,[t("div",i1,[o(x,{label:d.$t("app.agentTicketRefund.totalTicketAmount"),prop:"totalAmount",class:Ve(["w90",{"not-required-container":!e(a).totalAmount}]),rules:e(v).totalAmount},{default:i(()=>[$e(o(pe,{modelValue:e(a).totalAmount,"onUpdate:modelValue":j[10]||(j[10]=oe=>e(a).totalAmount=oe),clearable:"",onBlur:j[11]||(j[11]=oe=>e($)("totalAmount"))},null,8,["modelValue"]),[[B]]),e(a).totalAmount?W("",!0):(s(),m("div",l1,n(d.$t("app.agentTicketRefund.totalAmountNotRequired")),1))]),_:1},8,["label","rules","class"])]),t("div",r1,[o(x,{label:d.$t("app.agentTicketRefund.refundPayType"),prop:"payType",rules:e(v).payType},{default:i(()=>[o(se,{modelValue:e(a).payType,"onUpdate:modelValue":j[12]||(j[12]=oe=>e(a).payType=oe),modelModifiers:{trim:!0},filterable:"","allow-create":"","default-first-option":"","automatic-dropdown":"",placeholder:d.$t("app.agentTicketRefund.choose"),clearable:"",onChange:e(A),onBlur:e(V)},{default:i(()=>[(s(!0),m(xe,null,Ee(e(Aa),(oe,Se)=>(s(),re(Te,{key:Se,label:oe.label,value:oe.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","onChange","onBlur"])]),_:1},8,["label","rules"])]),t("div",c1,[o(x,{label:d.$t("app.agentTicketRefund.refundCurrency"),prop:"currency",rules:e(v).currency},{default:i(()=>[$e(o(pe,{modelValue:e(a).currency,"onUpdate:modelValue":j[13]||(j[13]=oe=>e(a).currency=oe),clearable:"",onInput:j[14]||(j[14]=oe=>e(a).currency=e(a).currency.toUpperCase()),onBlur:e(C)},null,8,["modelValue","onBlur"]),[[B]])]),_:1},8,["label","rules"])]),u1])]),t("div",d1,[t("div",p1,[t("div",f1,[o(x,{label:d.$t("app.agentTicketRefund.etTag")},{default:i(()=>[o(we,{modelValue:e(a).etTag,"onUpdate:modelValue":j[15]||(j[15]=oe=>e(a).etTag=oe),"inline-prompt":"","active-text":"Y","inactive-text":"N","active-value":"1","inactive-value":"0",onChange:e(le)},null,8,["modelValue","onChange"])]),_:1},8,["label"])]),e(a).tktType==="I"&&e(a).airline!=="784"?(s(),m("div",m1,[o(x,null,{default:i(()=>[o(me,{modelValue:e(a).ticketStatus,"onUpdate:modelValue":j[16]||(j[16]=oe=>e(a).ticketStatus=oe)},{default:i(()=>[t("span",g1,n(d.$t("app.agentTicketRefund.modifyTicketStatus")),1)]),_:1},8,["modelValue"])]),_:1})])):W("",!0)]),t("div",k1,[t("div",y1,[t("div",null,[t("span",null,n(d.$t("app.agentTicketRefund.refundTax")),1),t("span",h1,n(d.$t("app.fare.singleFare.totalTax")),1),t("span",v1," "+n(e(a).currency)+" "+n(e(a).totalTaxs),1)]),t("div",null,[o(he,{link:"",type:"primary",size:"small",disabled:e(b),onClick:e(h)},{default:i(()=>[z(n(d.$t("app.agentTicketRefund.rtktTax")),1)]),_:1},8,["disabled","onClick"]),o(he,{link:"",type:"primary",size:"small",disabled:((ie=e(a).taxs)==null?void 0:ie.length)===e(r),onClick:e(y)},{default:i(()=>[z(n(d.$t("app.agentTicketRefund.addTaxs")),1)]),_:1},8,["disabled","onClick"])])]),t("div",_1,[(s(!0),m(xe,null,Ee(e(a).taxs,(oe,Se)=>(s(),m("div",{key:Se,class:"grow shrink-0 basis-0 h-[32px] justify-start flex w-[calc((100%_-_80px)_/_5)] min-w-[calc((100%_-_80px)_/_5)] max-w-[calc((100%_-_80px)_/_5)]"},[t("div",T1,n(Se+1),1),t("div",b1,[o(x,{prop:"taxs."+Se+".name",rules:e(v).taxName},{default:i(()=>[$e(o(pe,{modelValue:oe.name,"onUpdate:modelValue":de=>oe.name=de,onInput:de=>oe.name=oe.name.toUpperCase(),onBlur:e(D)},null,8,["modelValue","onUpdate:modelValue","onInput","onBlur"]),[[B]])]),_:2},1032,["prop","rules"])]),o(x,{prop:"taxs."+Se+".value",rules:e(v).taxValue},{default:i(()=>[$e(o(pe,{modelValue:oe.value,"onUpdate:modelValue":de=>oe.value=de,clearable:"",onBlur:e(D)},null,8,["modelValue","onUpdate:modelValue","onBlur"]),[[B]])]),_:2},1032,["prop","rules"])]))),128))])])]),t("div",x1,[t("div",N1,[t("div",$1,[o(x,{label:d.$t("app.agentTicketRefund.commision"),prop:"commision",class:"w90",rules:e(v).commision},{default:i(()=>[$e(o(pe,{modelValue:e(a).commision,"onUpdate:modelValue":j[17]||(j[17]=oe=>e(a).commision=oe),clearable:"",placeholder:"0.00",onBlur:j[18]||(j[18]=oe=>e($)("commision"))},null,8,["modelValue"]),[[B]])]),_:1},8,["label","rules"])]),t("div",R1,[o(x,{label:d.$t("app.agentTicketRefund.commissionRate"),prop:"commisionRate",rules:e(v).commisionRate},{default:i(()=>[$e(o(pe,{modelValue:e(a).commisionRate,"onUpdate:modelValue":j[19]||(j[19]=oe=>e(a).commisionRate=oe),clearable:"",placeholder:"0.00",onBlur:j[20]||(j[20]=oe=>e($)("commisionRate")),onInput:e(ge)},null,8,["modelValue","onInput"]),[[B]]),C1]),_:1},8,["label","rules"])]),t("div",w1,[o(x,{label:d.$t("app.agentTicketRefund.inputOtherDeductionRate"),prop:"otherDeductionRate",class:"w170",rules:e(v).otherDeductionRate},{default:i(()=>[$e(o(pe,{modelValue:e(a).otherDeductionRate,"onUpdate:modelValue":j[21]||(j[21]=oe=>e(a).otherDeductionRate=oe),placeholder:"1-100",clearable:"",onBlur:j[22]||(j[22]=oe=>e($)("otherDeductionRate"))},null,8,["modelValue"]),[[B]]),S1]),_:1},8,["label","rules"])]),t("div",P1,[o(x,{label:d.$t("app.agentTicketRefund.otherDeduction"),prop:"otherDeduction",class:"w110",rules:e(v).otherDeduction},{default:i(()=>[$e(o(pe,{modelValue:e(a).otherDeduction,"onUpdate:modelValue":j[23]||(j[23]=oe=>e(a).otherDeduction=oe),clearable:"",placeholder:"0.00",onBlur:j[24]||(j[24]=oe=>e($)("otherDeduction"))},null,8,["modelValue"]),[[B]])]),_:1},8,["label","rules"])])]),t("div",D1,[t("div",A1,[t("div",E1,n(d.$t("app.agentTicketRefund.remark")),1),t("div",O1,[o(x,{prop:"remarkInfo",rules:e(v).remarkInfo},{default:i(()=>[$e(o(pe,{modelValue:e(a).remarkInfo,"onUpdate:modelValue":j[25]||(j[25]=oe=>e(a).remarkInfo=oe),clearable:"",placeholder:d.$t("app.agentTicketRefund.remarkPleaceHolder"),onInput:j[26]||(j[26]=oe=>e(a).remarkInfo=e(a).remarkInfo.toUpperCase())},null,8,["modelValue","placeholder"]),[[B]])]),_:1},8,["rules"]),o(Re,{placement:"top",content:d.$t("app.agentTicketRefund.remarkTips")},{default:i(()=>[F1]),_:1},8,["content"])])]),t("div",V1,[o(x,{label:d.$t("app.agentTicketRefund.totalRefund"),prop:"netRefund"},{default:i(()=>[t("span",M1,n(e(a).currency)+" "+n(e(a).netRefund),1),o(Re,{placement:"top",effect:"dark"},{content:i(()=>[z(n(d.$t("app.agentTicketRefund.netRefundTip")),1)]),default:i(()=>[L1]),_:1})]),_:1},8,["label"])]),t("div",B1,[o(x,{label:d.$t("app.agentTicketRefund.creditCardInfo"),prop:"creditCard",rules:e(v).creditCard},{default:i(()=>[$e(o(pe,{modelValue:e(a).creditCard,"onUpdate:modelValue":j[27]||(j[27]=oe=>e(a).creditCard=oe),clearable:"",onInput:j[28]||(j[28]=oe=>e(a).creditCard=e(a).creditCard.toUpperCase())},null,8,["modelValue"]),[[B]])]),_:1},8,["label","rules"])]),Q1])])]}),_:1},8,["model"])]),t("div",U1,[o(J,{modelValue:e(f),"onUpdate:modelValue":j[29]||(j[29]=N=>Ue(f)?f.value=N:null),class:"mr-[10px]"},{default:i(()=>[o(U,{disabled:"",label:"ONLY_REFUND"},{default:i(()=>[z(n(d.$t("app.agentTicketRefund.onlyRt")),1)]),_:1})]),_:1},8,["modelValue"]),o(he,{type:"primary",onClick:e(u)},{default:i(()=>[z(n(d.$t("app.agentTicketRefund.refund")),1)]),_:1},8,["onClick"])])])),[[P,e(T)]])]),_:1},8,["title","onClose"])}}});const I1=async(c,p)=>{const l=c.cloneNode(!0),f=document.createElement("div");f.style.cssText=`
    position: fixed;
    top: 0;
    left: 0;
    width: ${c.offsetWidth}px;
    height: ${c.offsetHeight+100}px;
    overflow: visible;
    z-index: -9999;
  `,f.appendChild(l),document.body.appendChild(f);try{const T=await Rs(l,{useCORS:!0,scale:3,windowWidth:l.scrollWidth,windowHeight:l.scrollHeight,backgroundColor:"#FFFFFF",allowTaint:!0,logging:!0}),k=new po("p","pt",[1500,1500]);k.addImage(T,"PNG",0,0,1500,1500),k.save(p)}finally{document.body.removeChild(f)}},z1=c=>{const{t:p}=et(),l=O(),f=yt({printNo:"",airlineSettlementCode:"",startTicketNo:"",endTicketNo:""}),T=O(),k=O(!1),a=O(!1),v=O({}),r={printNo:[{required:!0,message:p("app.cccf.required"),trigger:["change","blur"]},{pattern:Vt,message:p("app.cccf.printNoTip"),trigger:["change","blur"]}],airlineSettlementCode:[{required:!0,message:p("app.cccf.required"),trigger:["change","blur"]},{pattern:Ta,message:p("app.cccf.airlineSettlementCodeTip"),trigger:["change","blur"]}],startTicketNo:[{required:!0,message:p("app.cccf.required"),trigger:["change","blur"]},{pattern:Vs,message:p("app.cccf.startTicketNoTip"),trigger:["change","blur"]}],endTicketNo:[{pattern:Ms,message:p("app.cccf.endTicketNoTip"),trigger:["change","blur"]}]},g=$=>({beginNumber:$.startTicketNo,endNumber:$.endTicketNo,airlineNumber:$.airlineSettlementCode,deviceNumber:$.printNo});return{cccfFromRef:l,cccfFromData:f,rules:r,creditCardReceiptData:v,printRef:T,printStyleControl:k,downloadPdfStyleControl:a,handleQuery:async()=>{var $;($=l==null?void 0:l.value)==null||$.validate(async A=>{var u;if(!A)return;const V=Ft.service({fullscreen:!0});try{const te=De("091U0106"),X=await io(g(f),te);v.value=((u=X==null?void 0:X.data)==null?void 0:u.value)??{}}finally{V.close()}})},handleReset:()=>{f.printNo="",f.airlineSettlementCode="",f.startTicketNo="",f.endTicketNo=""},handlePrint:async()=>{k.value=!0,await ln(),await Do(T.value,{paging:!0,style:`<style>
        .print-refund-form-panel {
          width: 1050px
        }
      </style>`}),k.value=!1},handlePdf:async()=>{a.value=!0,await ln(),await I1(T.value,`${f.airlineSettlementCode}-${f.startTicketNo}-${f.endTicketNo}`),a.value=!1},cancel:()=>{c("update:modelValue",!1)}}},q1=z1,G1=t("i",{class:"iconfont icon-close"},null,-1),H1=[G1],K1={class:"border-separate border-spacing-0 border-gray-2 w-full"},Y1={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},W1=t("span",{class:"font-bold"},"PASSENGER NAME : ",-1),X1=t("td",{class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},[t("span",null,"-")],-1),J1={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},Z1=t("span",{class:"font-bold"},"COUNTRY OF SELL : ",-1),eT={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},tT=t("span",{class:"font-bold"},"ORIGIN/DESTINATION : ",-1),nT={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},aT=t("span",{class:"font-bold"},"BOOKING : ",-1),sT=t("span",null,"-",-1),oT={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},iT=t("span",null,"TIME DATE AND PLACE OF ISSUE ",-1),lT={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},rT=t("span",{class:"font-bold"},"TICKET # : ",-1),cT={class:"w-1/2 border-t border-l border-r border-gray-2 p-1.5"},uT={class:"flex"},dT={class:"w-[80px]"},pT={class:"w-1/2 border-t border-l border-gray-2 p-1.5"},fT=t("span",{class:"font-bold"},"IATA # : ",-1),mT={class:"w-1/2 p-0 border-t border-l border-r border-gray-2"},gT={class:"flex h-full"},kT={class:"w-1/2 border-r border-gray-2 p-1.5"},yT={class:"w-1/2 p-1.5"},hT=t("span",{class:"font-bold"},"AGENT : ",-1),vT={class:"h-full"},_T={class:"w-full h-full p-0 border border-gray-2",colspan:"2"},TT={class:"flex h-full"},bT={class:"w-[26%] h-full p-1.5 border-r border-gray-2 flex flex-col"},xT=t("div",{class:"font-bold"},"ITINERARY",-1),NT={key:0,class:"flex"},$T=t("div",{class:"w-[50px]"},"FORM",-1),RT={class:"flex"},CT=t("div",{class:"w-[50px]"},"TO",-1),wT={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},ST=t("div",{class:"font-bold"},"CARRIER",-1),PT={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},DT=t("div",{class:"font-bold"},"FLIGHT",-1),AT=t("div",null,"VOID",-1),ET={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},OT=t("div",{class:"font-bold"},"CLASS",-1),FT={class:"w-[19%] p-1.5 border-r border-gray-2 flex flex-col"},VT=t("div",{class:"font-bold"},"DATE",-1),MT={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},LT=t("div",{class:"font-bold"},"STATUS",-1),BT={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},QT=t("div",{class:"font-bold"},"FARE",-1),UT=t("div",null,"-",-1),jT=[UT],IT={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},zT=t("div",{class:"font-bold"},"BASIS/TKT",-1),qT={class:"w-1/5 p-1.5 border-r border-gray-2 flex flex-col"},GT=t("div",{class:"font-bold"},"DESIGNATOR",-1),HT=t("div",null,"-",-1),KT=[HT],YT={class:"w-1/5 p-1.5 flex flex-col"},WT=t("div",{class:"font-bold"},"STOPOVER",-1),XT={class:"border-separate border-spacing-0 border-gray-2 w-full mt-2.5"},JT={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},ZT=t("div",{class:"font-bold"},"CARD HOLDER",-1),eb={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},tb=t("div",{class:"font-bold"},"CREDIT CARD CODE",-1),nb=t("td",{class:"w-1/3 border-t border-l border-r border-gray-2 p-1.5"},[t("div",{class:"font-bold"},"BANK SEQUENCE"),t("div",null,"-")],-1),ab={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},sb=t("div",{class:"font-bold"},"EXPIRE DATE",-1),ob={class:"w-1/3 border-t border-l border-gray-2 p-1.5"},ib=t("div",{class:"font-bold"},"APPROVAL CODE",-1),lb={class:"w-1/3 border-t border-l border-r border-gray-2 p-1.5"},rb=t("div",{class:"font-bold"},"AMOUNT",-1),cb=t("tr",null,[t("td",{class:"w-full border border-gray-2 p-1.5",colspan:"3"},[t("div",{class:"font-bold"},"CARDHOLDER SIGNATURE : ")])],-1),ub={key:1,class:"flex justify-center flex-col items-center min-h-[400px]"},db=["alt"],pb={class:"mt-[20px] text-lg leading-[24px] font-bold text-gray-2"},fb={class:"text-base text-gray-4"},mb={key:2,class:"flex justify-center crs-btn-dialog-ui"},gb=He({__name:"CreditCardReceiptPrintDialog",emits:["update:modelValue"],setup(c,{emit:p}){const l=p,{cccfFromRef:f,cccfFromData:T,rules:k,creditCardReceiptData:a,printRef:v,printStyleControl:r,downloadPdfStyleControl:g,handleQuery:b,handleReset:h,handlePrint:E,handlePdf:y,cancel:D}=q1(l);return($,A)=>{const V=dt,u=xt,te=st,X=pt,ge=ft,q=ht("trimUpper");return s(),re(ge,{title:`${$.$t("app.cccf.creditCardReceiptPrint")}CCCF`,"close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1,"align-center":"true",class:"cccf-dialog crs-new-ui-init-cls",width:"1040px",onClose:e(D)},{default:i(()=>{var Y,L,le,C,d,j,Te,se;return[t("div",{class:"cursor-pointer text-gray-4 inline-block absolute right-[14px] top-3 z-10",onClick:A[0]||(A[0]=(...x)=>e(D)&&e(D)(...x))},H1),t("div",null,[t("div",null,[o(X,{ref_key:"cccfFromRef",ref:f,rules:e(k),model:e(T),"require-asterisk-position":"right",class:"flex cccf-from"},{default:i(()=>[o(V,{label:$.$t("app.cccf.printNo"),prop:"printNo",class:"mr-2.5"},{default:i(()=>[o(It,{modelValue:e(T).printNo,"onUpdate:modelValue":[A[1]||(A[1]=x=>e(T).printNo=x),A[2]||(A[2]=x=>{var R;return(R=e(f))==null?void 0:R.validateField("printNo")})],modelModifiers:{trim:!0},"select-class":"w-[100px]"},null,8,["modelValue"])]),_:1},8,["label"]),o(V,{label:$.$t("app.cccf.airlineSettlementCode"),prop:"airlineSettlementCode",class:"mr-2.5"},{default:i(()=>[$e(o(u,{modelValue:e(T).airlineSettlementCode,"onUpdate:modelValue":A[3]||(A[3]=x=>e(T).airlineSettlementCode=x),class:"airline-settlement-code"},null,8,["modelValue"]),[[q]])]),_:1},8,["label"]),o(V,{label:$.$t("app.cccf.startTicketNo"),prop:"startTicketNo",class:"mr-2.5"},{default:i(()=>[$e(o(u,{modelValue:e(T).startTicketNo,"onUpdate:modelValue":A[4]||(A[4]=x=>e(T).startTicketNo=x),class:"start-ticket-no"},null,8,["modelValue"]),[[q]])]),_:1},8,["label"]),o(V,{label:$.$t("app.cccf.endTicketNo"),prop:"endTicketNo",class:"mr-2.5"},{default:i(()=>[$e(o(u,{modelValue:e(T).endTicketNo,"onUpdate:modelValue":A[5]||(A[5]=x=>e(T).endTicketNo=x),class:"end-ticket-no"},null,8,["modelValue"]),[[q]])]),_:1},8,["label"]),o(te,{type:"primary","data-gid":"091U0106",onClick:A[6]||(A[6]=x=>e(b)())},{default:i(()=>[z(n($.$t("app.cccf.query")),1)]),_:1}),o(te,{onClick:A[7]||(A[7]=x=>e(h)())},{default:i(()=>[z(n($.$t("app.cccf.reset")),1)]),_:1})]),_:1},8,["rules","model"])]),(Y=e(a))!=null&&Y.pnr?(s(),m("div",{key:0,ref_key:"printRef",ref:v,class:Ve(["text-gray-2 min-h-[400px] w-[1008px]",e(g)?"p-[20px]":""])},[t("table",K1,[t("tbody",null,[t("tr",null,[t("td",Y1,[W1,t("span",null,n(e(a).fullName||"-"),1)]),X1]),t("tr",null,[t("td",J1,[Z1,t("span",null,n(e(a).countryCode),1)]),t("td",eT,[tT,t("span",null,n(e(a).orgArrivalCityCode),1)])]),t("tr",null,[t("td",nT,[aT,t("span",null,n(e(a).pnr||"-"),1),sT]),t("td",oT,[iT,t("span",null,n(e(a).ticketIssueTime||"-"),1)])]),t("tr",null,[t("td",lT,[rT,t("span",null,n(e(a).bspCurrencyCode||"-"),1)]),t("td",cT,[t("div",uT,[t("div",dT,n(e(a).ticketIssueDate||"-"),1),z(n(e(a).locationSubTypeCode||"-"),1)])])]),t("tr",null,[t("td",pT,[fT,t("span",null,n(e(a).agentIataNumber||"-"),1)]),t("td",mT,[t("div",gT,[t("div",kT,[t("span",null,n(e(a).issueOfficeId||"-"),1),z(" DEV-"),t("span",null,n(e(a).deviceNumber||"-"),1)]),t("div",yT,[hT,t("span",null,n(e(a).issueAgentId||"-"),1)])])])]),t("tr",vT,[t("td",_T,[t("div",TT,[t("div",bT,[xT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},[R===0?(s(),m("div",NT,[$T,z(n(x.originCityNumericCode||"-"),1)])):W("",!0),t("div",RT,[CT,z(n(x.destinationCityCode||"-"),1)])]))),128))]),t("div",wT,[ST,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},[t("div",null,n(x.ocAirlineCode||"-"),1)]))),128))]),t("div",PT,[DT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},[t("div",null,n(x.ocFlightNumber||"-"),1)]))),128)),AT]),t("div",ET,[OT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},[t("div",null,n(x.ocClassId||"-"),1)]))),128))]),t("div",FT,[VT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},[t("div",null,n(x.departureDate||"-"),1)]))),128))]),t("div",MT,[LT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},[t("div",null,n(x.reservationStatusCode||"-"),1)]))),128))]),t("div",BT,[QT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},jT))),128))]),t("div",IT,[zT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},[t("div",null,n(x.seatValueLevel||"-"),1)]))),128))]),t("div",qT,[GT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},KT))),128))]),t("div",YT,[WT,(s(!0),m(xe,null,Ee(e(a).itineraryInfoList,(x,R)=>(s(),m("div",{key:R},[t("div",null,n(x.stopoverCode||"-"),1)]))),128))])])])])])]),t("table",XT,[t("tbody",null,[t("tr",null,[t("td",JT,[ZT,t("div",null,n(((L=e(a).paymentInfo)==null?void 0:L.creditCardNumber)||"-"),1)]),t("td",eb,[tb,t("div",null,n(((le=e(a).paymentInfo)==null?void 0:le.operatingCompany)||"-"),1)]),nb]),t("tr",null,[t("td",ab,[sb,t("div",null,n(((C=e(a).paymentInfo)==null?void 0:C.endDate)||"-"),1)]),t("td",ob,[ib,t("div",null,n(((d=e(a).paymentInfo)==null?void 0:d.checkCode)||"-"),1)]),t("td",lb,[rb,t("div",null,[z(n(((j=e(a).paymentInfo)==null?void 0:j.paymentAmount)||"-")+" ",1),t("span",null,n(((Te=e(a).paymentInfo)==null?void 0:Te.currencyCode)||"-"),1)])])]),cb])]),t("div",{class:Ve(["mt-2.5",e(r)?"":"text-xs"])},"CARDHOLDER ACKNOWLEDGES RECEIPT OF GOODS AND/OR SERVICE IN THE AMOUNT OF THE TOTAL SHOWN HEREON AND AGRESS TO PERFORM THE OBLIGATIONS SET FORTH IN THE CARDHOLDER' S AGREEMENT WITH THE ISSUER.",2)],2)):(s(),m("div",ub,[t("img",{src:Gn,alt:$.$t("app.fastQuery.skQuerys.nodata")},null,8,db),t("div",pb,n($.$t("app.commonProblems.noQueryData")),1),t("div",fb,n($.$t("app.commonProblems.enterCriteriaQuery")),1)])),(se=e(a))!=null&&se.pnr?(s(),m("div",mb,[o(te,{type:"primary",onClick:A[8]||(A[8]=x=>e(y)())},{default:i(()=>[z(n($.$t("app.cccf.download")),1)]),_:1}),o(te,{type:"primary",onClick:A[9]||(A[9]=x=>e(E)())},{default:i(()=>[z(n($.$t("app.cccf.print")),1)]),_:1}),o(te,{onClick:e(D)},{default:i(()=>[z(n($.$t("app.cccf.close")),1)]),_:1},8,["onClick"])])):W("",!0)])]}),_:1},8,["title","onClose"])}}});const kb={class:"ticket-operation-container crs-new-ui-init-cls"},yb={class:"bg-gray-0 rounded-lg shadow-[0_0_8px_0_rgba(109,117,151,0.2)]"},hb={class:"bg-gray-8 h-[50px] flex"},vb=["onClick"],bx=He({__name:"TicketOperationContainer",setup(c){const{loading:p,printNo:l,printType:f,showRefundFormDialog:T,refundOperationCondition:k,refundFormData:a,currentTab:v,editableTabs:r,refundEtNumber:g,queryTicketRes:b,ticketQueryConditionRef:h,showBatchRefund:E,showManualRefund:y,showAuthOffice:D,queryType:$,batchRefundRes:A,batchRefundRef:V,factor:u,showBopRefund:te,showRtktDialog:X,showCccfDialog:ge,addTab:q,changeTab:Y,removeTab:L,handleQueryTicket:le,reQueryTicket:C,openBatchRefund:d,handleBatchRefund:j,openBopRefund:Te,bopRefundSuccess:se,openManualRefund:x,openAuthOffice:R,deliverRefundData:pe,openRefundDialog:Re,openRtkt:we,openCccf:me,isOneFactor:he}=Gy();return(ue,U)=>{var B,P;const J=at,ne=wt;return $e((s(),m("div",null,[t("div",kb,[t("div",yb,[t("div",hb,[(s(!0),m(xe,null,Ee(e(r),(N,_)=>{var w,ce;return s(),m("div",{key:N.name,class:Ve([_===0?"p-[14px]":"p-[10px] pr-[0px]",e(v)===(((ce=(w=e(r))==null?void 0:w[0])==null?void 0:ce.name)??"ticketQuery")&&_===0?"bg-gray-0":"","h-full cursor-pointer text-[14px]"]),onClick:ie=>e(Y)(_)},[t("span",{class:Ve([_===0?"":"rounded-[4px] px-[10px] h-[32px] border-[1px] border-solid",_!==0?e(v)===N.name?"border-brand-2 bg-brand-4":"border-gray-6 bg-gray-0":"","flex items-center text-gray-2"])},[t("span",{class:Ve([e(v)===N.name?"text-brand-2 font-[700]":""])},n(N.title),3),_!==0?(s(),re(J,{key:0,class:"ml-[6px]",onClick:yn(ie=>e(L)(_,N.name),["stop"])},{default:i(()=>[o(e(Cs))]),_:2},1032,["onClick"])):W("",!0)],2)],10,vb)}),128))]),$e(t("div",null,[o(zi,{ref_key:"ticketQueryConditionRef",ref:h,"is-one-factor":e(he),onHandleQueryTicket:e(le),onAddNewTab:e(q),onOpenAuthOffice:e(R),onOpenBatchRefund:e(d),onOpenBopRefund:e(Te),onOpenRefundDialog:e(Re),onOpenManualRefund:e(x),onOpenRtkt:e(we),onOpenCccf:e(me)},null,8,["is-one-factor","onHandleQueryTicket","onAddNewTab","onOpenAuthOffice","onOpenBatchRefund","onOpenBopRefund","onOpenRefundDialog","onOpenManualRefund","onOpenRtkt","onOpenCccf"])],512),[[hn,e(v)===(((P=(B=e(r))==null?void 0:B[0])==null?void 0:P.name)??"ticketQuery")]])]),(s(!0),m(xe,null,Ee(e(r),N=>$e((s(),m("div",{key:N.name},[(s(),re(ws(N.content),{key:N.name,"query-ticket-res":e(b),"tkt-no":e(g),"batch-refund-res":e(A),"query-type":e($),factor:e(u),onAddNewTab:e(q),onRemoveTab:e(L),onReQueryTicket:e(C)},null,40,["query-ticket-res","tkt-no","batch-refund-res","query-type","factor","onAddNewTab","onRemoveTab","onReQueryTicket"]))])),[[hn,e(v)===N.name]])),128))]),e(te)?(s(),re(nv,{key:0,modelValue:e(te),"onUpdate:modelValue":U[0]||(U[0]=N=>Ue(te)?te.value=N:null),onBopRefundSuccess:e(se)},null,8,["modelValue","onBopRefundSuccess"])):W("",!0),e(E)?(s(),re(Gh,{key:1,ref_key:"batchRefundRef",ref:V,modelValue:e(E),"onUpdate:modelValue":U[1]||(U[1]=N=>Ue(E)?E.value=N:null),onHandleBatchRefund:e(j)},null,8,["modelValue","onHandleBatchRefund"])):W("",!0),e(D)?(s(),re(c_,{key:2,modelValue:e(D),"onUpdate:modelValue":U[2]||(U[2]=N=>Ue(D)?D.value=N:null)},null,8,["modelValue"])):W("",!0),e(y)?(s(),re(j1,{key:3,modelValue:e(y),"onUpdate:modelValue":U[3]||(U[3]=N=>Ue(y)?y.value=N:null),onDeliverRefundData:e(pe)},null,8,["modelValue","onDeliverRefundData"])):W("",!0),e(T)?(s(),re(zn,{key:4,modelValue:e(T),"onUpdate:modelValue":U[4]||(U[4]=N=>Ue(T)?T.value=N:null),"printer-no":e(l),"printer-type":e(f),"is-supplement-refund":!1,"refund-operation-condition":e(k),"refund-ticket-data":e(a),onReQueryTicket:e(C)},null,8,["modelValue","printer-no","printer-type","refund-operation-condition","refund-ticket-data","onReQueryTicket"])):W("",!0),e(X)?(s(),re(y_,{key:5,modelValue:e(X),"onUpdate:modelValue":U[5]||(U[5]=N=>Ue(X)?X.value=N:null)},null,8,["modelValue"])):W("",!0),e(ge)?(s(),re(gb,{key:6,modelValue:e(ge),"onUpdate:modelValue":U[6]||(U[6]=N=>Ue(ge)?ge.value=N:null)},null,8,["modelValue"])):W("",!0)])),[[ne,e(p),void 0,{fullscreen:!0,lock:!0}]])}}});export{bx as default};
