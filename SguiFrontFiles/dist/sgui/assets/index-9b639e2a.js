import{iO as de,iP as be,em as ce,iQ as ve,ht as me,eF as J,eQ as G,ec as M,eB as U,iy as N,O as I,w as h,eG as z,f6 as fe,eJ as O,e9 as W,ac as H,eO as Y,aI as Z,r as D,U as E,e5 as ke,eH as A,eN as Q,eK as pe,iR as j,q as L,Y as X,v as R,x as C,y as _,z as ee,P as q,D as x,A as t,a5 as $,B as y,b9 as F,E as V,iS as w,F as T,ai as he,ak as le,Q as ae,J as P,bf as ne,_ as K,C as ge,L as xe,M as Ce,W as ye,d7 as Se,K as Le,Z as te}from"./index-18f146fc.js";import{h as Be,i as Ee}from"./isEqual-9c56e106.js";import{f as Ie}from"./flatten-1142070a.js";function $e(e){return de(be(e,void 0,Ie),e+"")}function Fe(e,i,u){for(var l=-1,k=i.length,b={};++l<k;){var c=i[l],o=ce(e,c);u(o,c)&&ve(b,me(c,e),o)}return b}function Ve(e,i){return Fe(e,i,function(u,l){return Be(e,l)})}var we=$e(function(e,i){return e==null?{}:Ve(e,i)});const Ge=we,oe={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object],default:void 0},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},controls:{type:String,default:void 0},border:Boolean,size:J,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0}},se={[G]:e=>M(e)||U(e)||N(e),change:e=>M(e)||U(e)||N(e)},B=Symbol("checkboxGroupContextKey"),Ne=({model:e,isChecked:i})=>{const u=I(B,void 0),l=h(()=>{var b,c;const o=(b=u==null?void 0:u.max)==null?void 0:b.value,v=(c=u==null?void 0:u.min)==null?void 0:c.value;return!z(o)&&e.value.length>=o&&!i.value||!z(v)&&e.value.length<=v&&i.value});return{isDisabled:fe(h(()=>(u==null?void 0:u.disabled.value)||l.value)),isLimitDisabled:l}},ze=(e,{model:i,isLimitExceeded:u,hasOwnLabel:l,isDisabled:k,isLabeledByFormItem:b})=>{const c=I(B,void 0),{formItem:o}=O(),{emit:v}=W();function a(n){var d,f;return n===e.trueLabel||n===!0?(d=e.trueLabel)!=null?d:!0:(f=e.falseLabel)!=null?f:!1}function m(n,d){v("change",a(n),d)}function p(n){if(u.value)return;const d=n.target;v("change",a(d.checked),n)}async function S(n){u.value||!l.value&&!k.value&&b.value&&(n.composedPath().some(r=>r.tagName==="LABEL")||(i.value=a([!1,e.falseLabel].includes(i.value)),await Z(),m(i.value,n)))}const s=h(()=>(c==null?void 0:c.validateEvent)||e.validateEvent);return H(()=>e.modelValue,()=>{s.value&&(o==null||o.validate("change").catch(n=>Y()))}),{handleChange:p,onClickRoot:S}},De=e=>{const i=D(!1),{emit:u}=W(),l=I(B,void 0),k=h(()=>z(l)===!1),b=D(!1);return{model:h({get(){var o,v;return k.value?(o=l==null?void 0:l.modelValue)==null?void 0:o.value:(v=e.modelValue)!=null?v:i.value},set(o){var v,a;k.value&&E(o)?(b.value=((v=l==null?void 0:l.max)==null?void 0:v.value)!==void 0&&o.length>(l==null?void 0:l.max.value),b.value===!1&&((a=l==null?void 0:l.changeEvent)==null||a.call(l,o))):(u(G,o),i.value=o)}}),isGroup:k,isLimitExceeded:b}},Pe=(e,i,{model:u})=>{const l=I(B,void 0),k=D(!1),b=h(()=>{const a=u.value;return N(a)?a:E(a)?ke(e.label)?a.map(A).some(m=>Ee(m,e.label)):a.map(A).includes(e.label):a!=null?a===e.trueLabel:!!a}),c=Q(h(()=>{var a;return(a=l==null?void 0:l.size)==null?void 0:a.value}),{prop:!0}),o=Q(h(()=>{var a;return(a=l==null?void 0:l.size)==null?void 0:a.value})),v=h(()=>!!i.default||!pe(e.label));return{checkboxButtonSize:c,isChecked:b,isFocused:k,checkboxSize:o,hasOwnLabel:v}},Oe=(e,{model:i})=>{function u(){E(i.value)&&!i.value.includes(e.label)?i.value.push(e.label):i.value=e.trueLabel||!0}e.checked&&u()},ie=(e,i)=>{const{formItem:u}=O(),{model:l,isGroup:k,isLimitExceeded:b}=De(e),{isFocused:c,isChecked:o,checkboxButtonSize:v,checkboxSize:a,hasOwnLabel:m}=Pe(e,i,{model:l}),{isDisabled:p}=Ne({model:l,isChecked:o}),{inputId:S,isLabeledByFormItem:s}=j(e,{formItemContext:u,disableIdGeneration:m,disableIdManagement:k}),{handleChange:n,onClickRoot:d}=ze(e,{model:l,isLimitExceeded:b,hasOwnLabel:m,isDisabled:p,isLabeledByFormItem:s});return Oe(e,{model:l}),{inputId:S,isLabeledByFormItem:s,isChecked:o,isDisabled:p,isFocused:c,checkboxButtonSize:v,checkboxSize:a,hasOwnLabel:m,model:l,handleChange:n,onClickRoot:d}},Re=["id","indeterminate","name","tabindex","disabled","true-value","false-value"],Te=["id","indeterminate","disabled","value","name","tabindex"],Ke=L({name:"ElCheckbox"}),Me=L({...Ke,props:oe,emits:se,setup(e){const i=e,u=X(),{inputId:l,isLabeledByFormItem:k,isChecked:b,isDisabled:c,isFocused:o,checkboxSize:v,hasOwnLabel:a,model:m,handleChange:p,onClickRoot:S}=ie(i,u),s=R("checkbox"),n=h(()=>[s.b(),s.m(v.value),s.is("disabled",c.value),s.is("bordered",i.border),s.is("checked",b.value)]),d=h(()=>[s.e("input"),s.is("disabled",c.value),s.is("checked",b.value),s.is("indeterminate",i.indeterminate),s.is("focus",o.value)]);return(f,r)=>(C(),_(ne(!t(a)&&t(k)?"span":"label"),{class:x(t(n)),"aria-controls":f.indeterminate?f.controls:null,onClick:t(S)},{default:ee(()=>[q("span",{class:x(t(d))},[f.trueLabel||f.falseLabel?$((C(),y("input",{key:0,id:t(l),"onUpdate:modelValue":r[0]||(r[0]=g=>F(m)?m.value=g:null),class:x(t(s).e("original")),type:"checkbox",indeterminate:f.indeterminate,name:f.name,tabindex:f.tabindex,disabled:t(c),"true-value":f.trueLabel,"false-value":f.falseLabel,onChange:r[1]||(r[1]=(...g)=>t(p)&&t(p)(...g)),onFocus:r[2]||(r[2]=g=>o.value=!0),onBlur:r[3]||(r[3]=g=>o.value=!1),onClick:r[4]||(r[4]=V(()=>{},["stop"]))},null,42,Re)),[[w,t(m)]]):$((C(),y("input",{key:1,id:t(l),"onUpdate:modelValue":r[5]||(r[5]=g=>F(m)?m.value=g:null),class:x(t(s).e("original")),type:"checkbox",indeterminate:f.indeterminate,disabled:t(c),value:f.label,name:f.name,tabindex:f.tabindex,onChange:r[6]||(r[6]=(...g)=>t(p)&&t(p)(...g)),onFocus:r[7]||(r[7]=g=>o.value=!0),onBlur:r[8]||(r[8]=g=>o.value=!1),onClick:r[9]||(r[9]=V(()=>{},["stop"]))},null,42,Te)),[[w,t(m)]]),q("span",{class:x(t(s).e("inner"))},null,2)],2),t(a)?(C(),y("span",{key:0,class:x(t(s).e("label"))},[T(f.$slots,"default"),f.$slots.default?P("v-if",!0):(C(),y(he,{key:0},[le(ae(f.label),1)],64))],2)):P("v-if",!0)]),_:3},8,["class","aria-controls","onClick"]))}});var Ue=K(Me,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox.vue"]]);const Ae=["name","tabindex","disabled","true-value","false-value"],Qe=["name","tabindex","disabled","value"],qe=L({name:"ElCheckboxButton"}),Je=L({...qe,props:oe,emits:se,setup(e){const i=e,u=X(),{isFocused:l,isChecked:k,isDisabled:b,checkboxButtonSize:c,model:o,handleChange:v}=ie(i,u),a=I(B,void 0),m=R("checkbox"),p=h(()=>{var s,n,d,f;const r=(n=(s=a==null?void 0:a.fill)==null?void 0:s.value)!=null?n:"";return{backgroundColor:r,borderColor:r,color:(f=(d=a==null?void 0:a.textColor)==null?void 0:d.value)!=null?f:"",boxShadow:r?`-1px 0 0 0 ${r}`:void 0}}),S=h(()=>[m.b("button"),m.bm("button",c.value),m.is("disabled",b.value),m.is("checked",k.value),m.is("focus",l.value)]);return(s,n)=>(C(),y("label",{class:x(t(S))},[s.trueLabel||s.falseLabel?$((C(),y("input",{key:0,"onUpdate:modelValue":n[0]||(n[0]=d=>F(o)?o.value=d:null),class:x(t(m).be("button","original")),type:"checkbox",name:s.name,tabindex:s.tabindex,disabled:t(b),"true-value":s.trueLabel,"false-value":s.falseLabel,onChange:n[1]||(n[1]=(...d)=>t(v)&&t(v)(...d)),onFocus:n[2]||(n[2]=d=>l.value=!0),onBlur:n[3]||(n[3]=d=>l.value=!1),onClick:n[4]||(n[4]=V(()=>{},["stop"]))},null,42,Ae)),[[w,t(o)]]):$((C(),y("input",{key:1,"onUpdate:modelValue":n[5]||(n[5]=d=>F(o)?o.value=d:null),class:x(t(m).be("button","original")),type:"checkbox",name:s.name,tabindex:s.tabindex,disabled:t(b),value:s.label,onChange:n[6]||(n[6]=(...d)=>t(v)&&t(v)(...d)),onFocus:n[7]||(n[7]=d=>l.value=!0),onBlur:n[8]||(n[8]=d=>l.value=!1),onClick:n[9]||(n[9]=V(()=>{},["stop"]))},null,42,Qe)),[[w,t(o)]]),s.$slots.default||s.label?(C(),y("span",{key:2,class:x(t(m).be("button","inner")),style:ge(t(k)?t(p):void 0)},[T(s.$slots,"default",{},()=>[le(ae(s.label),1)])],6)):P("v-if",!0)],2))}});var ue=K(Je,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox-button.vue"]]);const We=xe({modelValue:{type:Ce(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:J,label:String,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0}}),He={[G]:e=>E(e),change:e=>E(e)},Ye=L({name:"ElCheckboxGroup"}),Ze=L({...Ye,props:We,emits:He,setup(e,{emit:i}){const u=e,l=R("checkbox"),{formItem:k}=O(),{inputId:b,isLabeledByFormItem:c}=j(u,{formItemContext:k}),o=async a=>{i(G,a),await Z(),i("change",a)},v=h({get(){return u.modelValue},set(a){o(a)}});return ye(B,{...Ge(Se(u),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:v,changeEvent:o}),H(()=>u.modelValue,()=>{u.validateEvent&&(k==null||k.validate("change").catch(a=>Y()))}),(a,m)=>{var p;return C(),_(ne(a.tag),{id:t(b),class:x(t(l).b("group")),role:"group","aria-label":t(c)?void 0:a.label||"checkbox-group","aria-labelledby":t(c)?(p=t(k))==null?void 0:p.labelId:void 0},{default:ee(()=>[T(a.$slots,"default")]),_:3},8,["id","class","aria-label","aria-labelledby"])}}});var re=K(Ze,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox-group.vue"]]);const el=Le(Ue,{CheckboxButton:ue,CheckboxGroup:re});te(ue);const ll=te(re);export{el as E,ll as a,$e as f};
