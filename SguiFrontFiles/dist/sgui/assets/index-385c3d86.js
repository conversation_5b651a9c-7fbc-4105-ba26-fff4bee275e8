import{L as W,ee as ae,q as M,N as te,O as j,iB as ne,w as T,iT as ie,x as E,B as _,P as R,F as D,D as y,A as e,Q as re,G as O,z as k,y as q,bf as ue,H as de,J as K,C as X,_ as H,iU as ce,M as Y,eQ as x,iy as fe,e9 as me,ih as pe,ib as Z,r as I,iV as ve,a3 as ye,iW as ge,ac as G,aI as Ce,o as be,iX as he,iY as J,eC as ke,Y as De,a2 as Q,v as Ie,W as Te,a5 as Be,$ as Ee,a0 as Ae,X as Se,e0 as Fe,a6 as $e,T as we,a7 as Pe,iZ as Le,K as Re}from"./index-18f146fc.js";import{c as Oe}from"./refs-649593ac.js";import{i as Me}from"./isUndefined-aa0326a0.js";const ee=Symbol("dialogInjectionKey"),oe=W({center:Boolean,alignCenter:Boolean,closeIcon:{type:ae},customClass:{type:String,default:""},draggable:Boolean,fullscreen:Boolean,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),Ne={close:()=>!0},ze=["aria-level"],Ve=["aria-label"],Ue=["id"],_e=M({name:"ElDialogContent"}),qe=M({..._e,props:oe,emits:Ne,setup(o){const t=o,{t:d}=te(),{Close:F}=ce,{dialogRef:n,headerRef:c,bodyId:B,ns:a,style:g}=j(ee),{focusTrapRef:i}=j(ne),f=T(()=>[a.b(),a.is("fullscreen",t.fullscreen),a.is("draggable",t.draggable),a.is("align-center",t.alignCenter),{[a.m("center")]:t.center},t.customClass]),m=Oe(i,n),C=T(()=>t.draggable);return ie(n,c,C),(s,u)=>(E(),_("div",{ref:e(m),class:y(e(f)),style:X(e(g)),tabindex:"-1"},[R("header",{ref_key:"headerRef",ref:c,class:y(e(a).e("header"))},[D(s.$slots,"header",{},()=>[R("span",{role:"heading","aria-level":s.ariaLevel,class:y(e(a).e("title"))},re(s.title),11,ze)]),s.showClose?(E(),_("button",{key:0,"aria-label":e(d)("el.dialog.close"),class:y(e(a).e("headerbtn")),type:"button",onClick:u[0]||(u[0]=$=>s.$emit("close"))},[O(e(de),{class:y(e(a).e("close"))},{default:k(()=>[(E(),q(ue(s.closeIcon||e(F))))]),_:1},8,["class"])],10,Ve)):K("v-if",!0)],2),R("div",{id:e(B),class:y(e(a).e("body"))},[D(s.$slots,"default")],10,Ue),s.$slots.footer?(E(),_("footer",{key:0,class:y(e(a).e("footer"))},[D(s.$slots,"footer")],2)):K("v-if",!0)],6))}});var Ke=H(qe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog-content.vue"]]);const je=W({...oe,appendToBody:Boolean,appendTo:{type:Y(String),default:"body"},beforeClose:{type:Y(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:{type:Boolean,default:!1},headerAriaLevel:{type:String,default:"2"}}),Ye={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[x]:o=>fe(o),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},Ze=(o,t)=>{var d;const n=me().emit,{nextZIndex:c}=pe();let B="";const a=Z(),g=Z(),i=I(!1),f=I(!1),m=I(!1),C=I((d=o.zIndex)!=null?d:c());let s,u;const $=ve("namespace",he),N=T(()=>{const r={},h=`--${$.value}-dialog`;return o.fullscreen||(o.top&&(r[`${h}-margin-top`]=o.top),o.width&&(r[`${h}-width`]=ye(o.width))),r}),z=T(()=>o.alignCenter?{display:"flex"}:{});function w(){n("opened")}function V(){n("closed"),n(x,!1),o.destroyOnClose&&(m.value=!1)}function U(){n("close")}function P(){u==null||u(),s==null||s(),o.openDelay&&o.openDelay>0?{stop:s}=J(()=>L(),o.openDelay):L()}function A(){s==null||s(),u==null||u(),o.closeDelay&&o.closeDelay>0?{stop:u}=J(()=>l(),o.closeDelay):l()}function S(){function r(h){h||(f.value=!0,i.value=!1)}o.beforeClose?o.beforeClose(r):A()}function p(){o.closeOnClickModal&&S()}function L(){ke&&(i.value=!0)}function l(){i.value=!1}function v(){n("openAutoFocus")}function b(){n("closeAutoFocus")}function le(r){var h;((h=r.detail)==null?void 0:h.focusReason)==="pointer"&&r.preventDefault()}o.lockScroll&&ge(i);function se(){o.closeOnPressEscape&&S()}return G(()=>o.modelValue,r=>{r?(f.value=!1,P(),m.value=!0,C.value=Me(o.zIndex)?c():C.value++,Ce(()=>{n("open"),t.value&&(t.value.scrollTop=0)})):i.value&&A()}),G(()=>o.fullscreen,r=>{t.value&&(r?(B=t.value.style.transform,t.value.style.transform=""):t.value.style.transform=B)}),be(()=>{o.modelValue&&(i.value=!0,m.value=!0,P())}),{afterEnter:w,afterLeave:V,beforeLeave:U,handleClose:S,onModalClick:p,close:A,doClose:l,onOpenAutoFocus:v,onCloseAutoFocus:b,onCloseRequested:se,onFocusoutPrevented:le,titleId:a,bodyId:g,closed:f,style:N,overlayDialogStyle:z,rendered:m,visible:i,zIndex:C}},Ge=["aria-label","aria-labelledby","aria-describedby"],Je=M({name:"ElDialog",inheritAttrs:!1}),Qe=M({...Je,props:je,emits:Ye,setup(o,{expose:t}){const d=o,F=De();Q({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},T(()=>!!F.title)),Q({scope:"el-dialog",from:"custom-class",replacement:"class",version:"2.3.0",ref:"https://element-plus.org/en-US/component/dialog.html#attributes",type:"Attribute"},T(()=>!!d.customClass));const n=Ie("dialog"),c=I(),B=I(),a=I(),{visible:g,titleId:i,bodyId:f,style:m,overlayDialogStyle:C,rendered:s,zIndex:u,afterEnter:$,afterLeave:N,beforeLeave:z,handleClose:w,onModalClick:V,onOpenAutoFocus:U,onCloseAutoFocus:P,onCloseRequested:A,onFocusoutPrevented:S}=Ze(d,c);Te(ee,{dialogRef:c,headerRef:B,bodyId:f,ns:n,rendered:s,style:m});const p=Le(V),L=T(()=>d.draggable&&!d.fullscreen);return t({visible:g,dialogContentRef:a}),(l,v)=>(E(),q(Pe,{to:l.appendTo,disabled:l.appendTo!=="body"?!1:!l.appendToBody},[O(we,{name:"dialog-fade",onAfterEnter:e($),onAfterLeave:e(N),onBeforeLeave:e(z),persisted:""},{default:k(()=>[Be(O(e(Ee),{"custom-mask-event":"",mask:l.modal,"overlay-class":l.modalClass,"z-index":e(u)},{default:k(()=>[R("div",{role:"dialog","aria-modal":"true","aria-label":l.title||void 0,"aria-labelledby":l.title?void 0:e(i),"aria-describedby":e(f),class:y(`${e(n).namespace.value}-overlay-dialog`),style:X(e(C)),onClick:v[0]||(v[0]=(...b)=>e(p).onClick&&e(p).onClick(...b)),onMousedown:v[1]||(v[1]=(...b)=>e(p).onMousedown&&e(p).onMousedown(...b)),onMouseup:v[2]||(v[2]=(...b)=>e(p).onMouseup&&e(p).onMouseup(...b))},[O(e(Ae),{loop:"",trapped:e(g),"focus-start-el":"container",onFocusAfterTrapped:e(U),onFocusAfterReleased:e(P),onFocusoutPrevented:e(S),onReleaseRequested:e(A)},{default:k(()=>[e(s)?(E(),q(Ke,Se({key:0,ref_key:"dialogContentRef",ref:a},l.$attrs,{"custom-class":l.customClass,center:l.center,"align-center":l.alignCenter,"close-icon":l.closeIcon,draggable:e(L),fullscreen:l.fullscreen,"show-close":l.showClose,title:l.title,"aria-level":l.headerAriaLevel,onClose:e(w)}),Fe({header:k(()=>[l.$slots.title?D(l.$slots,"title",{key:1}):D(l.$slots,"header",{key:0,close:e(w),titleId:e(i),titleClass:e(n).e("title")})]),default:k(()=>[D(l.$slots,"default")]),_:2},[l.$slots.footer?{name:"footer",fn:k(()=>[D(l.$slots,"footer")])}:void 0]),1040,["custom-class","center","align-center","close-icon","draggable","fullscreen","show-close","title","aria-level","onClose"])):K("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,Ge)]),_:3},8,["mask","overlay-class","z-index"]),[[$e,e(g)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["to","disabled"]))}});var We=H(Qe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog.vue"]]);const eo=Re(We);export{eo as E,Ye as a,je as d,Ze as u};
