import{_ as C,q as O,F as T,r as _,W as E,O as S,o as b,A as m,eA as L,L as f,M as t,ee as $,V as o}from"./index-18f146fc.js";import{b as h,u as I}from"./index-6ea30548.js";const M=O({inheritAttrs:!1});function w(n,l,c,s,a,u){return T(n.$slots,"default")}var A=C(M,[["render",w],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collection/src/collection.vue"]]);const P=O({name:"ElCollectionItem",inheritAttrs:!1});function v(n,l,c,s,a,u){return T(n.$slots,"default")}var B=C(P,[["render",v],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collection/src/collection-item.vue"]]);const K="data-el-collection-item",x=n=>{const l=`El${n}Collection`,c=`${l}Item`,s=Symbol(l),a=Symbol(c),u={...A,name:l,setup(){const d=_(null),i=new Map;E(s,{itemMap:i,getItems:()=>{const r=m(d);if(!r)return[];const e=Array.from(r.querySelectorAll(`[${K}]`));return[...i.values()].sort((y,N)=>e.indexOf(y.ref)-e.indexOf(N.ref))},collectionRef:d})}},g={...B,name:c,setup(d,{attrs:i}){const p=_(null),r=S(s,void 0);E(a,{collectionItemRef:p}),b(()=>{const e=m(p);e&&r.itemMap.set(e,{ref:e,...i})}),L(()=>{const e=m(p);r.itemMap.delete(e)})}};return{COLLECTION_INJECTION_KEY:s,COLLECTION_ITEM_INJECTION_KEY:a,ElCollection:u,ElCollectionItem:g}},R=f({trigger:h.trigger,effect:{...I.effect,default:"light"},type:{type:t(String)},placement:{type:t(String),default:"bottom"},popperOptions:{type:t(Object),default:()=>({})},id:String,size:{type:String,default:""},splitButton:Boolean,hideOnClick:{type:Boolean,default:!0},loop:{type:Boolean,default:!0},showTimeout:{type:Number,default:150},hideTimeout:{type:Number,default:150},tabindex:{type:t([Number,String]),default:0},maxHeight:{type:t([Number,String]),default:""},popperClass:{type:String,default:""},disabled:{type:Boolean,default:!1},role:{type:String,default:"menu"},buttonProps:{type:t(Object)},teleported:I.teleported}),D=f({command:{type:[Object,String,Number],default:()=>({})},disabled:Boolean,divided:Boolean,textValue:String,icon:{type:$}}),V=f({onKeydown:{type:t(Function)}}),Y=[o.down,o.pageDown,o.home],j=[o.up,o.pageUp,o.end],q=[...Y,...j],{ElCollection:U,ElCollectionItem:W,COLLECTION_INJECTION_KEY:z,COLLECTION_ITEM_INJECTION_KEY:G}=x("Dropdown");export{G as C,U as E,q as F,j as L,D as a,K as b,x as c,R as d,W as e,V as f,z as g};
