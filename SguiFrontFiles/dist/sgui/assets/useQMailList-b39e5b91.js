import{ao as D,b8 as y,c3 as L,ab as V,r,bC as U,jO as G,av as $,au as w,aJ as _,d5 as z,q as K,x as F,y as J,z as R,a5 as H,B as W,G as b,A as c,D as X,ax as Y,P as Z,ak as P,Q as I,ah as ee,al as ae,b3 as te,w as T,jP as se,b2 as k}from"./index-18f146fc.js";import{E as ne,a as le}from"./index-c5503643.js";import{E as oe}from"./index-385c3d86.js";const ie=(e,t)=>D(`${y}/queue/delete`,{headers:{gid:t}}).post(e).json(),Ee=(e,t)=>D(`${y}/queue/crs/delete`,{headers:{gid:t}}).post(e).json(),Se=(e,t)=>D(`${y}/queue/getQueueInfo`,{headers:{gid:t}}).post(e).json(),Te=(e,t)=>D(`${y}/queue/getcrsq`,{headers:{gid:t}}).post(e).json(),ce=(e,t)=>L.post(`${y}/queue/sendQ`,e,{headers:{gid:t}}),re=(e,t)=>L.post(`${y}/queue/moveQ`,e,{headers:{gid:t}}),Oe=(e,t)=>D(`${y}/queue/crs/create`,{headers:{gid:t}}).post(e).json(),$e=(e,t)=>D(`${y}/queue/crs/update`,{headers:{gid:t}}).post(e).json(),ue=(e,t)=>{const{t:l}=V(),g=r(!1),m=r(),f=r({officeNum:e.office??"",mailName:""}),C={officeNum:[{required:!0,message:l("app.intlPassengerForm.required"),trigger:"blur"},{pattern:U,message:l("app.qMessage.agentErrorTips"),trigger:"blur"}],mailName:[{required:!0,message:l("app.intlPassengerForm.required"),trigger:"blur"},{pattern:G,message:l("app.qMessage.agentErrorTips"),trigger:"blur"}]},i=()=>t("update:modelValue",!1);return{FORM_RULES:C,formRef:m,closeDialog:i,relayForm:f,handleSave:()=>{var N;(N=m.value)==null||N.validate(async u=>{if(u)try{g.value=!0;const o={office:f.value.officeNum,queueNm:f.value.mailName};e.type==="relay"?(o.qid=e.relayData.qid,o.orgQueueNm=e.queueNm,o.orgOffice=e.relayData.office):(o.qcontent=e.relayData.content,o.qtype=e.relayData.qtype);const h=$("122C0108"),p=$("122C0109"),s=e.type==="relay"?await await re(o,h):await await ce(o,p);if(s.data==="OK"){g.value=!1;const v=e.type==="relay"?l("app.qMessage.relaySuccess"):l("app.qMessage.sendSuccess");await w.confirm(_("div",{class:"batch-delete-tip-box"},v),{icon:_("em",{class:"iconfont icon-check-circle text-green-2"}),customClass:"crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.button.ensure"),showCancelButton:!1,showClose:!1}).then(()=>{i(),t("reSearch")})}else z(s==null?void 0:s.code,s==null?void 0:s.extResponseIDList,s==null?void 0:s.transactionID,s==null?void 0:s.time,s==null?void 0:s.firstError,s==null?void 0:s.satTransactionID)}finally{g.value=!1}})},handleCancel:()=>{i()},loading:g}},de=ue,ge={class:"text-center mt-2.5"},we=K({__name:"RelayDialog",props:{queueNm:{},type:{},relayData:{},office:{}},emits:["update:modelValue","reSearch"],setup(e,{emit:t}){const l=e,g=t,{formRef:m,closeDialog:f,FORM_RULES:C,relayForm:i,handleSave:x,handleCancel:d,loading:N}=de(l,g);return(u,o)=>{const h=ee,p=ne,s=le,v=ae,q=oe,O=te;return F(),J(q,{class:"relay-dialog",width:"680px",title:u.type==="relay"?u.$t("app.qMessage.relayTitle"):u.$t("app.qMessage.sendTitle"),onClose:c(f)},{default:R(()=>[H((F(),W("div",null,[b(s,{ref_key:"formRef",ref:m,class:"relay-form","label-width":"60px","label-position":"left","require-asterisk-position":"right",rules:c(C),model:c(i)},{default:R(()=>[b(p,{class:X([c(Y)()==="en"?"two-line":""]),label:u.$t("app.qMessage.officeNum"),prop:"officeNum"},{default:R(()=>[b(h,{modelValue:c(i).officeNum,"onUpdate:modelValue":o[0]||(o[0]=M=>c(i).officeNum=M),placeholder:u.$t("app.qMessage.enterOfficeNum"),clearable:"",onInput:o[1]||(o[1]=M=>c(i).officeNum=c(i).officeNum.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["class","label"]),b(p,{label:u.$t("app.qMessage.mailName"),prop:"mailName"},{default:R(()=>[b(h,{modelValue:c(i).mailName,"onUpdate:modelValue":o[2]||(o[2]=M=>c(i).mailName=M),placeholder:u.$t("app.qMessage.enterMailName"),clearable:"",onInput:o[3]||(o[3]=M=>c(i).mailName=c(i).mailName.toUpperCase())},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["rules","model"]),Z("div",ge,[b(v,{type:"primary",class:"w-[80px]",onClick:c(x)},{default:R(()=>[P(I(u.$t("app.button.ensure")),1)]),_:1},8,["onClick"]),b(v,{class:"w-[80px]",onClick:c(d)},{default:R(()=>[P(I(u.$t("app.button.cancel")),1)]),_:1},8,["onClick"])])])),[[O,c(N)]])]),_:1},8,["title","onClose"])}}});const me=(e,t)=>{const{t:l}=V(),g=r(!1),m=r(!1),f=r(""),C=r(""),i=T(()=>e.currentPage??1),x=T(()=>(e.qMailRes??[]).length===200?(e.qMailRes??[]).length-1:(e.qMailRes??[]).length),d=r([]),N=T(()=>(e.qMailRes??[]).length===200?(e.qMailRes??[]).slice(0,-1):e.qMailRes??[]),u=T(()=>e.hasCustomMailAuth),o=r([]),h=r(),p=r(!1),s=r({}),v=r(),q=r({agent:""}),O={agent:[{pattern:se,message:l("app.qMessage.agentErrorTips"),trigger:"blur"}]},M=a=>(a==="pre"||!a)&&i.value===1?!1:!(a==="next"&&(e.qMailRes??[]).length<200),j=a=>{a.length?(m.value=!0,d.value=[],d.value=a.map(n=>n.qid)):(m.value=!1,d.value=[])},Q=async a=>{g.value=!0;try{const n={targetOffice:e.office,targetQueueNm:e.queueNm,qids:a==="clear"?[]:d.value,agent:0,xflag:0},A=$("122C0103"),{data:E}=await ie(n,A);if(E.value==="OK"){const S=a==="clear"?l("app.qMessage.clearCustomSuccess",{code:e.queueNm}):l("app.qMessage.deleteSuccess");await k({message:S,type:"success"}),m.value=!1,d.value=[],t("refresh",e.operationAgent??"")}}finally{g.value=!1}};return{isSeleted:m,currentPage:i,totalSize:x,loading:g,qMailData:N,handleSelectionChange:j,deleteOperation:async a=>{if(!a&&!d.value.length)return;const n=l(a?"app.qMessage.deleteTips":"app.qMessage.batchDeleteTips");await w.confirm(_("div",{class:"batch-delete-tip-box"},n),{icon:_("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.button.ensure"),cancelButtonText:l("app.pnrManagement.flight.cancel"),showClose:!1}).then(()=>{a&&(d.value=[])&&d.value.push(a.qid),Q("del")})},doCopy:async a=>{const n=document.createElement("input");n.value=a,document.body.appendChild(n),n.select(),document.execCommand("Copy"),n.style.display="none",k({message:l("app.agentReport.tip"),type:"success",duration:2*1e3})},goOrderPage:async a=>{a.isRead||(a.isRead=!a.isRead),f.value=a.content,C.value=a.qid},isAllowTurn:M,turnPre:()=>{if(i.value===1)return;const a=o.value.pop(),n={agent:e.operationAgent?e.operationAgent:q.value.agent,qid:a,page:"P",asc:e.sortType};t("search",n)},turnNext:()=>{var E,S,B;if((e.qMailRes??[]).length<200)return;const a=(e.qMailRes??[]).length,n=((S=(E=e.qMailRes??[])==null?void 0:E[a-1])==null?void 0:S.qid)??"";o.value.push(((B=e.qMailRes??[])==null?void 0:B[0].qid)??"");const A={agent:e.operationAgent?e.operationAgent:q.value.agent,qid:n,page:"N",asc:e.sortType};t("search",A)},clickPnr:f,clickQid:C,formRef:h,queryForm:q,FORM_RULES:O,queryAgentQMail:()=>{h.value.validate(a=>{if(!a)return;const n={agent:q.value.agent,qid:"",page:"",asc:e.sortType};t("search",n)})},resetAgent:()=>{q.value.agent=""},relayOperation:(a,n)=>{p.value=!0,s.value=a,v.value=n},showRelayDialog:p,relaySearch:()=>{t&&t("refresh",e.operationAgent??"")},relayData:s,relayType:v,relayOperationForPnrDetail:(a,n)=>{p.value=!0,s.value.qtype=n??"",s.value.content=a??""},clearOperation:async()=>{await w.confirm(_("div",{class:"batch-delete-tip-box"},l("app.qMessage.clearCustomTips",{code:e.queueNm})),{icon:_("em",{class:"iconfont icon-info-circle-line text-brand-2"}),customClass:"crs-btn-ui crs-btn-message-ui",confirmButtonText:l("app.button.ensure"),cancelButtonText:l("app.pnrManagement.flight.cancel"),showClose:!1}).then(()=>{Q("clear")})},hasCustomAuth:u,onSortChange:a=>{const n=(a.order??"")==="ascending"?"1":"0",A={agent:e.operationAgent?e.operationAgent:q.value.agent,qid:"",page:e.sortType!==n?"S":"",asc:n};t("search",A)}}},Qe=me;export{we as _,Ee as a,Se as b,Te as c,Oe as d,$e as e,ie as q,Qe as u};
