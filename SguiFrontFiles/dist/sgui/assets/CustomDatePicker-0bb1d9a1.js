import{R as n,q as Z,ab as ee,w as x,r as b,ac as te,x as ae,B as se,G as oe,e0 as ne,aj as re,z as ce,F as le,X as ie,aI as F,i8 as de,i9 as ue,ia as me}from"./index-18f146fc.js";import{E as pe}from"./index-28cc82cf.js";import{_ as De}from"./_plugin-vue_export-helper-c27b6911.js";const ve=c=>{var l,d;if(!c)return"";const u=parseInt(((l=c==null?void 0:c.split(":"))==null?void 0:l[0])??0,10),t=parseInt(((d=c==null?void 0:c.split(":"))==null?void 0:d[1])??0,10);return`${u!==0?`${u}h`:""}${t!==0?`${t}m`:""}`},Me=()=>{const c=new Date;return c.setHours(0),c.setMinutes(0),c.setSeconds(0),c.setMilliseconds(0),n(c)},Ce=(c,u)=>{const t=n(u).diff(c,"minute"),l=t%60,d=Math.floor(t/60);return d>0?l>0?`${d}h${l}m`:`${d}h`:`${l}m`},D=(c,u)=>{let t=c&&n(c).isValid()?n(c).format("YYYY-MM-DD"):"";if(!t)return t;const l=new Date;return t=u&&n(t).isBefore(n(l),"day")?n(t).add(1,"year").format("YYYY-MM-DD"):n(t).format("YYYY-MM-DD"),t},fe=["id"],y="year-disabled-status",T="month-disabled-status",_="disabled-status",P="custom-checkbox-container",q="custom-checkbox",he=Z({__name:"CustomDatePicker",props:{modelValue:{default:""},disabledDate:{},disabled:{type:Boolean},disableEndDate:{},autoComputeDate:{type:Boolean},disabledDatePickerPreEl:{type:Boolean,default:!0},showSwitch:{type:Boolean},roundTripSwitch:{type:Boolean}},emits:["update:modelValue","update:roundTripSwitch","change"],setup(c,{emit:u}){const t=c,l=u,{t:d}=ee(),O=new Date,Y=x({get:()=>t.roundTripSwitch?t.modelValue.length?[D(t.modelValue[0],t.autoComputeDate),D(t.modelValue[1],t.autoComputeDate)]:[]:D(t.modelValue,t.autoComputeDate),set:e=>{if(t.roundTripSwitch){e&&l("update:modelValue",[D(e[0],t.autoComputeDate),D(e[1],t.autoComputeDate)]),!e&&l("update:modelValue","");return}l("update:modelValue",D(e,t.autoComputeDate))}}),R=x(()=>t.disabled),m=b(),f=b();let p=[];const v=`custom-date-picker-panel-${new Date().getTime().toString()}`,M=b(`datePickerId-${new Date().getTime().toString()}`),U=b(`${v} custom-popover-picker-panel`),H=e=>{const a={valid:!1,date:""};if(me.test(e)){a.valid=!0;const s=e.split("-"),o=O.getFullYear().toString();let r="",i="";s.length>1?(r=s[0],i=s[1]):(r=e.slice(0,2),i=e.slice(2));const h=`${o}-${r}-${i}`;a.date=D(h,t.autoComputeDate)}return a},w=e=>{if(!e)return{valid:!1,date:""};let a=!1,s="";if(de.test(e)){const o=e.split("-"),r=o[0],i=o[1].length>1?o[1]:`0${o[1]}`,h=o[2].length>1?o[2]:`0${o[2]}`;s=`${r}-${i}-${h}`,a=!0}else if(ue.test(e))a=!0,s=`${e.slice(0,4)}-${e.slice(4,6)}-${e.slice(6,8)}`;else{const{valid:o,date:r}=H(e);a=o,s=r}if(n(s).isValid()){const o=new Date(n(s).format("YYYY-MM-DD"));s=t.disabledDate&&t.disabledDate(o)?"":n(s).format("YYYY-MM-DD"),a=!!s}return{valid:a,date:s}},S=e=>e?e.replace(/\s+/g,""):"",Q=()=>{const e=document.getElementById(M.value),a=e==null?void 0:e.getElementsByTagName("input"),o=[S(a==null?void 0:a[0].value),S(a==null?void 0:a[1].value)].map(i=>w(i));if(o.some(i=>!i.valid)){l("update:modelValue",[]);return}l("update:modelValue",o.map(i=>i.date))},X=()=>{var r;const e=document.getElementById(M.value),a=((r=e==null?void 0:e.getElementsByTagName("input")[0].value)==null?void 0:r.trim())??"",{valid:s,date:o}=w(a);if(s){l("update:modelValue",o);return}l("update:modelValue","")},j=()=>{if(t.roundTripSwitch){Q();return}X()},K=e=>{p=e,l("change",e)},z=e=>{var h,k,E,B,$,A,N,L,I,V;const a=n(t.disableEndDate).isValid()?n(t.disableEndDate):n(new Date),s=(h=m.value)==null?void 0:h.children[0],o=(k=m.value)==null?void 0:k.children[1],r=(E=f.value)==null?void 0:E.children[0],i=(B=f.value)==null?void 0:B.children[1];a.isBefore(n(e),"year")?(($=m.value)==null||$.classList.remove(y),(A=f.value)==null||A.classList.remove(y),s==null||s.classList.remove(_),r==null||r.classList.remove(_)):((N=m.value)==null||N.classList.add(y),s==null||s.classList.add(_)),a.isBefore(n(e),"month")?((L=m.value)==null||L.classList.remove(T),(I=f.value)==null||I.classList.remove(T),o==null||o.classList.remove(_),i==null||i.classList.remove(_)):((V=m.value)==null||V.classList.add(T),o==null||o.classList.add(_))},C=()=>{const e=n(new Date).format("YYYY-MM-DD"),a=p.length?p:[e,n(e).add(1,"month").format("YYYY-MM-DD")];t.disableEndDate&&z(a[0])},g=async()=>{await F();const e=document.querySelector(`.${v}`);if(!e)return;const a=e.querySelector(`.${q}`);if(a&&(a.checked=!!t.roundTripSwitch),t.roundTripSwitch){if(t.disabledDatePickerPreEl){const o=e.querySelector(".is-left");o&&(m.value=o.querySelector(".bkc-el-date-range-picker__header"))}const s=e.querySelector(".is-right");s&&(f.value=s.querySelector(".bkc-el-date-range-picker__header")),C();return}t.disabledDatePickerPreEl&&(m.value=e.querySelector(".bkc-el-date-picker__prev-btn")),f.value=e.querySelector(".bkc-el-date-picker__next-btn"),C()},G=()=>{if(!t.showSwitch)return;F();const e=document.querySelector(`.${v}`);if(e&&!e.querySelector(`.${P}`)){const a=document.createElement("div");a.className=P;const s=document.createElement("input");s.type="checkbox",s.id="myCheckbox",s.className=q,s.checked=t.roundTripSwitch;const o=document.createElement("span");o.textContent=d("app.fastQuery.headerQuery.selectDate"),o.className="main-label";const r=document.createElement("span");r.textContent=d("app.fastQuery.headerQuery.Return"),r.className="check-label",a.insertBefore(r,a.firstChild),a.insertBefore(s,a.firstChild),a.insertBefore(o,a.firstChild),e.insertBefore(a,e.firstChild),s.addEventListener("change",async()=>{l("update:roundTripSwitch",!t.roundTripSwitch),l("update:modelValue",t.roundTripSwitch?"":[]),s.checked=!t.roundTripSwitch})}},J=e=>{if(t.roundTripSwitch)p=Y.value;else{const a=n(Y.value).format("YYYY-MM-DD");p=[a,n(a).add(1,"month").format("YYYY-MM-DD")]}G(),!(!e||!t.disableEndDate)&&g()},W=e=>{if(t.roundTripSwitch)p=[n(e[0]).format("YYYY-MM-DD"),n(e[1]).format("YYYY-MM-DD")];else{const a=n(e).format("YYYY-MM-DD");p=[a,n(a).add(1,"month").format("YYYY-MM-DD")]}C()};return te(()=>t.roundTripSwitch,()=>{g()}),(e,a)=>{const s=pe;return ae(),se("div",{id:M.value,class:"custom-date-picker"},[oe(s,ie({modelValue:Y.value,"onUpdate:modelValue":a[0]||(a[0]=o=>Y.value=o),modelModifiers:{trim:!0}},e.$attrs,{"popper-class":U.value,"disabled-date":e.disabledDate,disabled:R.value,clearable:!0,onVisibleChange:J,onPanelChange:W,onBlur:j,onChange:K}),ne({_:2},[re(e.$slots,(o,r)=>({name:r,fn:ce(()=>[le(e.$slots,r,{},void 0,!0)])}))]),1040,["modelValue","popper-class","disabled-date","disabled"])],8,fe)}}});const ye=De(he,[["__scopeId","data-v-d8c88513"]]);export{ye as C,Me as a,Ce as b,ve as c,D as g};
