import{ao as dt,hC as ut,q as W,aE as ia,aF as oa,w as Y,r as S,o as st,x as r,y as K,z as C,G as D,P as e,Q as a,B as i,ai as q,aj as Q,ab as Je,b6 as la,ba as na,iw as ca,as as da,ae as ua,a5 as J,A as n,D as X,b9 as He,a6 as ct,J as R,F as pt,ak as at,E as pa,ah as ga,al as Ye,b3 as We,am as fa,an as ya,s as Ge,aX as Ze,ax as ta,b1 as _a,ix as lt,fb as ha,b2 as va,gU as ot,av as tt,cd as ka,ag as Ke,a$ as ze,af as xa,H as ba}from"./index-18f146fc.js";import{_ as rt}from"./_plugin-vue_export-helper-c27b6911.js";import{_ as ma}from"./empty-8ebd02ca.js";import{E as Ta,a as $a}from"./index-21b1c834.js";import{E as wa}from"./index-2494e7da.js";import{E as ea}from"./index-1c4b8a79.js";import{E as Da,a as Ca}from"./index-c5503643.js";import{E as Na,a as Ra}from"./index-d7d71e18.js";import{E as aa}from"./index-5035a026.js";import{E as La}from"./index-6ea30548.js";import{E as Oa}from"./index-385c3d86.js";import{E as Ia}from"./index-24b42935.js";import{G as nt,q as Ea}from"./ticketOperationApi-fe1536da.js";const Bo=(N,x)=>dt(`${ut}/apiAvSearch/queryRules`,{headers:{gid:x}}).post(N).json(),Fa=(N,x)=>dt(`${ut}/apiAvSearch/queryFareInfoByTicket`,{headers:{gid:x}}).post(N).json(),Aa=(N,x)=>dt(`${ut}/apiAvSearch/queryFareInfoByTicket`,{headers:{gid:x}},{ignoreError:!0,originalValue:!0}).post(N).json(),ja={class:"item-name"},Sa={class:"item-state"},qa={class:"item-container"},Qa={class:"item"},Va={class:"item-text"},Ua={class:"item-name"},Ba={class:"item-state"},Pa={class:"item-container"},Ma={class:"item"},Xa={class:"item-text"},Ha={class:"item-name"},Ka={class:"item-state"},za={class:"item-container"},Ja={class:"item"},Ya={class:"item-text"},Wa={class:"item-name"},Ga={class:"item-state"},Za={class:"item-container"},ts={class:"item"},es={class:"item-text"},as=W({__name:"CollapseInfo",props:{rule:{}},setup(N){const x=ia(),{orderInfo:k,activeTag:O}=oa(x),F=Y(()=>x.getFareType(O.value)),L=Y(()=>k.value.get(O.value).type==="2"),w=Y(()=>k.value.get(O.value).international),t=S("retract"),p=S("retract"),o=S(["change","refund"]),d=v=>{(v??[]).includes("change")?p.value="retract":p.value="unfold",(v??[]).includes("refund")?t.value="retract":t.value="unfold"};return st(()=>{!w.value&&L.value&&F.value==="normalFare"&&(t.value="retract",p.value="retract")}),(v,T)=>{const I=Ta,h=$a;return!w.value&&L.value&&F.value==="normalFare"?(r(),K(h,{key:0,modelValue:o.value,"onUpdate:modelValue":T[0]||(T[0]=f=>o.value=f),onChange:d},{default:C(()=>[D(I,{name:"change"},{title:C(()=>[e("div",ja,[e("span",null,a(v.$t("app.fareQuery.freightate.revalidationRule")),1),e("span",Sa,a(v.$t(`app.fareQuery.freightate.${p.value??"unfold"}`)),1)])]),default:C(()=>[e("div",qa,[e("div",Qa,[(r(!0),i(q,null,Q(v.rule.ruleInfos.change,(f,$)=>(r(),i("div",{key:$},[e("div",Va,a(f.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1}),D(I,{name:"refund"},{title:C(()=>[e("div",Ua,[e("span",null,a(v.$t("app.fareQuery.freightate.refund")),1),e("span",Ba,a(v.$t(`app.fareQuery.freightate.${t.value??"unfold"}`)),1)])]),default:C(()=>[e("div",Pa,[e("div",Ma,[(r(!0),i(q,null,Q(v.rule.ruleInfos.refund,(f,$)=>(r(),i("div",{key:$},[e("span",Xa,a(f.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1})]),_:1},8,["modelValue"])):(r(),K(h,{key:1,modelValue:o.value,"onUpdate:modelValue":T[1]||(T[1]=f=>o.value=f),onChange:d},{default:C(()=>[D(I,{name:"change"},{title:C(()=>[e("div",Ha,[e("span",null,a(v.$t("app.fareQuery.freightate.revalidationRule")),1),e("span",Ka,a(v.$t(`app.fareQuery.freightate.${p.value??"unfold"}`)),1)])]),default:C(()=>[e("div",za,[e("div",Ja,[(r(!0),i(q,null,Q(v.rule.ruleInfos.change,(f,$)=>(r(),i("div",{key:$},[e("div",Ya,a(f.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1}),D(I,{name:"refund"},{title:C(()=>[e("div",Wa,[e("span",null,a(v.$t("app.fareQuery.freightate.refund")),1),e("span",Ga,a(v.$t(`app.fareQuery.freightate.${t.value??"unfold"}`)),1)])]),default:C(()=>[e("div",Za,[e("div",ts,[(r(!0),i(q,null,Q(v.rule.ruleInfos.refund,(f,$)=>(r(),i("div",{key:$},[e("span",es,a(f.replace(/<br>/g,`\r
`)),1)]))),128))])])]),_:1})]),_:1},8,["modelValue"]))}}});const ss=rt(as,[["__scopeId","data-v-360264be"]]),rs=N=>{var M;const{t:x}=Je(),k=S([]),O=S(!1),F=S([]),L=S([]),w=S(),t=S(0),p=S(!1),o=(M=navigator==null?void 0:navigator.userAgent)==null?void 0:M.toLowerCase(),d=Y(()=>o==null?void 0:o.includes("electron/")),v=Y(()=>N.isCnLang),T=S(),I=S(),h=S({serialNumber:"16",serialKey:"",type:"NUM"}),f=[{value:"NUM",label:x("app.fareQuery.freightate.ruleNumber")},{value:"KEY",label:x("app.fareQuery.freightate.ruleKey")}],$=S(!1),j=Y(()=>N.rulesRes.translatedRuleInfo??""),y=la({proChange:[],maxStay:"",minStay:""}),b={serialNumber:[{pattern:na,message:x("app.fareQuery.freightate.inputSplitNum")}],serialKey:[{pattern:ca,message:x("app.fareQuery.freightate.inputSplitNum")}]},_=s=>{k.value=s.map(c=>({...c,contents:c.contents.map(u=>({strList:u.split("<br>")}))}))},A=()=>{var s,c;switch(h.value.type){case"NUM":O.value=!1,(s=T.value)==null||s.validate(u=>{if(u)if(h.value.serialNumber){const m=h.value.serialNumber.split("/"),E=[...new Set(m)].map(Number),U=[];E.forEach(G=>{U.push(...N.rulesRes.fareRuleInfos.filter(g=>G===Number(g.number)))}),_(U)}else _(N.rulesRes.fareRuleInfos)});break;case"KEY":O.value=!1,(c=T.value)==null||c.validate(u=>{if(u)if(h.value.serialKey){const m=N.rulesRes.fareRuleInfos.filter(l=>l.title.indexOf(h.value.serialKey.toUpperCase())!==-1);_(m)}else _(N.rulesRes.fareRuleInfos)});break}},H=()=>{var u,m,l,E;O.value=!0,h.value.serialNumber="";const s=Y(()=>N.rulesRes.fareRuleInfos.map(U=>({number:U.number,title:U.title}))),c=Math.ceil(((u=s.value)==null?void 0:u.length)/2);F.value=(m=s.value)==null?void 0:m.slice(0,c),L.value=(E=s.value)==null?void 0:E.slice(c,(l=s.value)==null?void 0:l.length)},B=s=>{h.value.serialNumber=s,A()},V=()=>{var s;t.value=((s=w.value)==null?void 0:s.offsetWidth)??0,p.value=!(t.value<726)};return st(async()=>{A(),V(),window.addEventListener("resize",V)}),da(()=>{k.value=[],window.removeEventListener("resize",V)}),{interRuleContent:k,priceRules:y,queryRuleInfo:A,QUIT_OR_UPDATE_RULE:b,quitOrUpdateRuleForm:h,quitOrUpdateRuleRef:T,loading:$,scrollbar:I,queryList:H,isList:O,leftColumn:F,rightColumn:L,queryRuleByNumber:B,isChineseShow:v,isCnRulesText:j,isClient:d,infoRuleRef:w,innerWidth:t,QUERY_TYPE_LIST:f,checkLang:p}},is=rs,sa=N=>(fa("data-v-1a8d5082"),N=N(),ya(),N),os={class:"u-cat"},ls=sa(()=>e("div",{class:"u-cat-tit"},[e("span")],-1)),ns={class:"u-cat-cont"},cs={key:0,class:"text-gray-2 text-xs"},ds={key:1,class:"loading-text"},us={class:"rule-query-content w-[180px] p-[6px] rounded-sm border border-gray-6 flex items-center h-[32px] border-solid border-r-0 bg-gray-0"},ps={class:"text-gray-2 text-xs break-keep mr-[6px]"},gs={class:"pasg-name-tag inline"},fs=sa(()=>e("span",{class:"iconfont icon-info-circle-line"},null,-1)),ys={key:0},_s={key:1,class:"loading-text"},hs={key:0},vs=["onClick"],ks=["onClick"],xs={key:1,class:"loading-text"},bs=W({__name:"InterRuleInfo",props:{rulesRes:{},ruleFullHeight:{type:Boolean},openDialog:{type:Boolean}},setup(N){const x=N,{interRuleContent:k,queryRuleInfo:O,quitOrUpdateRuleRef:F,quitOrUpdateRuleForm:L,QUIT_OR_UPDATE_RULE:w,loading:t,scrollbar:p,QUERY_TYPE_LIST:o,queryList:d,isList:v,queryRuleByNumber:T,leftColumn:I,rightColumn:h,isCnRulesText:f,isChineseShow:$,infoRuleRef:j,innerWidth:y,checkLang:b}=is(x);return(_,A)=>{const H=wa,B=ea,V=Da,M=Na,s=Ra,c=aa,u=ga,m=La,l=Ye,E=Ca,U=ua("trimUpper"),G=We;return J((r(),i("div",{ref_key:"infoRuleRef",ref:j,class:X(["rule-table mt-1",_.ruleFullHeight?"h-[668px]":"h-[368px]",n(y)<726&&!_.ruleFullHeight?"block px-1 max-w-[526px] border-none bg-transparent":"flex bg-gray-7"])},[n(y)>726||n(b)&&n(y)<726||_.ruleFullHeight?(r(),i("div",{key:0,class:X(["u-content bg-gray-7",n(y)<726&&!_.ruleFullHeight?"h-[368px]":""])},[J(e("p",null,[D(H,{modelValue:n(b),"onUpdate:modelValue":A[0]||(A[0]=g=>He(b)?b.value=g:null),"inline-prompt":"","inactive-text":_.$t("app.fareQuery.freightate.en"),"active-text":_.$t("app.fareQuery.freightate.cn")},null,8,["modelValue","inactive-text","active-text"])],512),[[ct,n(b)&&n(y)<726&&!_.ruleFullHeight]]),D(B,{class:X([_.ruleFullHeight?"h-[668px]":"h-[368px]"])},{default:C(()=>[e("div",os,[ls,e("div",ns,[n(f)?(r(),i("div",cs,[e("pre",null,a(n(f)),1)])):(r(),i("div",ds,a(_.$t("app.fareQuery.freightate.noDataAvailable")),1))])])]),_:1},8,["class"])],2)):R("",!0),n(y)>726||!n(b)&&n(y)<726||_.ruleFullHeight?(r(),i("div",{key:1,class:X(["w-[526px] flex flex-col crs-new-ui-init-cls border-box en-box bg-gray-7",n(y)<726&&!_.ruleFullHeight?"px-1 h-[368px]":""])},[D(E,{ref_key:"quitOrUpdateRuleRef",ref:F,class:"relative flex items-center",inline:"",model:n(L),rules:n(w),onSubmit:A[5]||(A[5]=pa(()=>{},["prevent"]))},{default:C(()=>[pt(_.$slots,"default",{},void 0,!0),J(D(V,null,{default:C(()=>[D(H,{modelValue:n(b),"onUpdate:modelValue":A[1]||(A[1]=g=>He(b)?b.value=g:null),"inline-prompt":"","inactive-text":_.$t("app.fareQuery.freightate.en"),"active-text":_.$t("app.fareQuery.freightate.cn")},null,8,["modelValue","inactive-text","active-text"])]),_:1},512),[[ct,!n(b)&&n(y)<726&&!_.ruleFullHeight]]),e("div",us,[e("div",ps,[D(s,{modelValue:n(L).type,"onUpdate:modelValue":A[2]||(A[2]=g=>n(L).type=g)},{default:C(()=>[(r(!0),i(q,null,Q(n(o),g=>(r(),K(M,{key:g.value,label:g.label,value:g.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),D(c,{direction:"vertical"}),n(L).type==="NUM"?(r(),K(V,{key:0,class:"query-input",prop:"serialNumber"},{default:C(()=>[J(D(u,{modelValue:n(L).serialNumber,"onUpdate:modelValue":A[3]||(A[3]=g=>n(L).serialNumber=g),clearable:""},null,8,["modelValue"]),[[U]])]),_:1})):(r(),K(V,{key:1,class:"query-input",prop:"serialKey"},{default:C(()=>[J(D(u,{modelValue:n(L).serialKey,"onUpdate:modelValue":A[4]||(A[4]=g=>n(L).serialKey=g),clearable:""},null,8,["modelValue"]),[[U]])]),_:1}))]),e("div",gs,[D(m,{placement:"top"},{content:C(()=>[e("p",null,a(_.$t("app.fareQuery.freightate.ruleNumber"))+": "+a(_.$t("app.fareQuery.freightate.inputSplitNumTip")),1),e("p",null,a(_.$t("app.fareQuery.freightate.ruleKey"))+": "+a(_.$t("app.fareQuery.freightate.inputSplitKeyTip")),1)]),default:C(()=>[fs]),_:1})]),D(l,{type:"primary",size:"small",onClick:n(O)},{default:C(()=>[at(a(_.$t("app.fareQuery.freightate.search")),1)]),_:1},8,["onClick"]),D(l,{type:"primary",link:"",size:"small",class:"ml-1",onClick:n(d)},{default:C(()=>[at(a(_.$t("app.fareQuery.freightate.list")),1)]),_:1},8,["onClick"])]),_:3},8,["model","rules"]),D(B,{ref_key:"scrollbar",ref:p,height:_.ruleFullHeight?"610":"318",class:"mt-2.5 text-xs text-gray-2 grow"},{default:C(()=>[n(v)?R("",!0):(r(!0),i(q,{key:0},Q(n(k),(g,z)=>(r(),i("div",{key:z},[e("pre",null,a(`${g.number}.${g.title}`),1),g.contents.length?(r(),i("div",ys,[(r(!0),i(q,null,Q(g.contents,(Z,it)=>(r(),i("div",{key:it},[(r(!0),i(q,null,Q(Z.strList,et=>(r(),i("pre",{key:et,class:"whitespace-pre-wrap"},a(et),1))),128))]))),128))])):(r(),i("div",_s,a(_.$t("app.fareQuery.freightate.noDataAvailable")),1))]))),128)),n(v)&&!n($)?(r(),i(q,{key:1},[n(I).length>0||n(h).length>0?(r(),i("div",hs,[(r(!0),i(q,null,Q(n(I),(g,z)=>(r(),i("div",{key:z},[n(I).length>0?(r(),i("div",{key:0,class:"cursor-pointer w-[50%]",style:{float:"left"},onClick:Z=>n(T)(`${g==null?void 0:g.number}`)},[e("span",null,a((g==null?void 0:g.number)<10?`0${g==null?void 0:g.number}`:`${g==null?void 0:g.number}`)+" "+a(g==null?void 0:g.title),1)],8,vs)):R("",!0)]))),128)),(r(!0),i(q,null,Q(n(h),(g,z)=>(r(),i("div",{key:z},[n(h).length>0?(r(),i("div",{key:0,class:"cursor-pointer w-[50%]",style:{float:"left"},onClick:Z=>n(T)(`${g==null?void 0:g.number}`)},[e("span",null,a((g==null?void 0:g.number)<10?`0${g==null?void 0:g.number}`:`${g==null?void 0:g.number}`)+" "+a(g==null?void 0:g.title),1)],8,ks)):R("",!0)]))),128))])):(r(),i("div",xs,a(_.$t("app.fareQuery.freightate.noDataAvailable")),1))],64)):R("",!0)]),_:1},8,["height"])],2)):R("",!0)],2)),[[G,n(t)]])}}});const ra=rt(bs,[["__scopeId","data-v-1a8d5082"]]),ms={key:0,class:"w-[1012px]"},Ts={class:"title flex gap-1"},$s={key:0},ws={key:1},Ds={class:"content"},Cs={key:1,class:"empty-info"},Ns=W({__name:"TicketRuleDialog",props:{ruleInfoData:{}},emits:["update:modelValue"],setup(N,{emit:x}){const k=x,O=()=>{k("update:modelValue",!1)},F=Ge([]),L=w=>{const t=F.value.find(p=>p.airportCode===w.toUpperCase());return t?t.airportEnName:w};return st(async()=>{const w=await Ze("searchLocalData");F.value=w?JSON.parse(w==null?void 0:w.localData):[]}),(w,t)=>{const p=ea,o=Oa;return r(),K(o,{title:w.$t("app.fareQuery.freightate.tktRule"),class:"tkt-rules-dialog","align-center":!0,modal:"","close-on-click-modal":!1,width:"1040",onClose:O},{default:C(()=>[D(p,{ref:"scrollbar",height:"688"},{default:C(()=>{var d,v,T;return[((v=(d=w.ruleInfoData)==null?void 0:d.flightRules[0])==null?void 0:v.rules.length)!==0?(r(),i("div",ms,[(r(!0),i(q,null,Q(((T=w.ruleInfoData)==null?void 0:T.flightRules)??[],(I,h)=>(r(),i("div",{key:h,class:"rule-item overflow-y-hidden"},[(r(!0),i(q,null,Q(I.rules,(f,$)=>(r(),i("div",{key:$,class:"min-w-full"},[e("div",Ts,[n(ta)()==="en"?(r(),i("span",$s,a(`${L(f.departureAirport)}(${f.departureAirport}) - ${L(f.arrivalAirport)}(${f.arrivalAirport})`),1)):(r(),i("span",ws,a(`${(f==null?void 0:f.departureAirportCityCh)??""}(${f.departureAirport}) - ${(f==null?void 0:f.arrivalAirportCityCh)??""}(${f.arrivalAirport})`),1))]),e("div",Ds,[D(ra,{"rules-res":f,"rule-full-height":""},{default:C(()=>[pt(w.$slots,"default")]),_:2},1032,["rules-res"])])]))),128))]))),128))])):(r(),i("div",Cs,[e("div",null,a(w.$t("app.fareQuery.freightate.noTktData")),1)]))]}),_:3},512)]),_:3},8,["title"])}}});const Rs={class:"min-h-[50px] mt-[10px]"},Ls={key:0,class:"close-box"},Os={class:"flex gap-1 items-center"},Is={class:"text-brand-2 text-xs font-normal leading-tight"},Es={key:1,class:"empty-page"},Fs=["alt"],As={class:"main-info"},js={key:0,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight mt-1"},Ss={class:"title flex gap-1"},qs={key:0},Qs={key:1},Vs={class:"content"},Us=W({__name:"TicketRule",props:{international:{type:Boolean},ruleInfoData:{},needCloseIcon:{type:Boolean},fullHeight:{type:Boolean},isQueryRuleByTicketNo:{type:Boolean,default:!1},ticketNo:{},ticketIgnoreError:{type:Boolean}},emits:["setLoading"],setup(N,{emit:x}){const k=N,O=x,F=S(!1),L=S(!1),w=Ge([]);let t=k==null?void 0:k.ruleInfoData;const p=()=>{F.value=!0},o=d=>{const v=w.value.find(T=>T.airportCode===d.toUpperCase());return v?v.airportEnName:d};return st(async()=>{var v,T,I,h,f,$,j;const d=await Ze("searchLocalData");if(w.value=d?JSON.parse(d==null?void 0:d.localData):[],((k==null?void 0:k.isQueryRuleByTicketNo)??!1)&&!(t!=null&&t.flightRules)){O("setLoading",!0);try{if(k!=null&&k.ticketIgnoreError){const y=await Aa({ticketNo:(k==null?void 0:k.ticketNo)??""},"02070104");((T=(v=y==null?void 0:y.data)==null?void 0:v.value)==null?void 0:T.code)==="200"&&((f=(h=(I=y==null?void 0:y.data)==null?void 0:I.value)==null?void 0:h.data)!=null&&f.flightRules)?t=(j=($=y==null?void 0:y.data)==null?void 0:$.value)==null?void 0:j.data:L.value=!0}else{const{data:y}=await Fa({ticketNo:(k==null?void 0:k.ticketNo)??""},"02070104");t=y.value}}finally{O("setLoading",!1)}}}),(d,v)=>{var I,h,f;const T=Ia;return r(),i("div",Rs,[e("div",{class:X(["rule-info",[d.fullHeight?"":" border-t border-gray-7 border-solid","mt-2.5 mr-2.5"]])},[d.needCloseIcon?(r(),i("div",Ls,[e("div",Os,[d.needCloseIcon?pt(d.$slots,"default",{key:0},void 0,!0):R("",!0),d.international?(r(),K(T,{key:1,underline:!1,disabled:!((I=n(t))!=null&&I.flightRules),onClick:p},{default:C(()=>[e("span",Is,a(d.$t("app.fareQuery.freightate.fullView")),1)]),_:1},8,["disabled"])):R("",!0)])])):R("",!0),(h=n(t))!=null&&h.flightRules?(r(!0),i(q,{key:2},Q(((f=n(t))==null?void 0:f.flightRules)??[],($,j)=>(r(),i("div",{key:j,class:"rule-item overflow-y-hidden"},[(r(!0),i(q,null,Q($.rules,(y,b)=>(r(),i("div",{key:b,class:"min-w-full"},[e("div",Ss,[n(ta)()==="en"?(r(),i("span",qs,a(`${o(y.departureAirport)}(${y.departureAirport}) - ${o(y.arrivalAirport)}(${y.arrivalAirport})`),1)):(r(),i("span",Qs,a(`${(y==null?void 0:y.departureAirportCityCh)??""}(${y.departureAirport}) - ${(y==null?void 0:y.arrivalAirportCityCh)??""}(${y.arrivalAirport})`),1))]),e("div",Vs,[d.international?(r(),K(ra,{key:0,"rules-res":y,"rule-full-height":d.fullHeight},null,8,["rules-res","rule-full-height"])):(r(),K(ss,{key:1,rule:y},null,8,["rule"]))])]))),128))]))),128)):(r(),i("div",Es,[e("img",{src:ma,alt:d.$t("app.querySearch.airCityCommon.noData")},null,8,Fs),e("p",As,a(d.$t("app.querySearch.airCityCommon.noData")),1),L.value?(r(),i("p",js,a(d.$t("app.ticketOriginal.noFareTip")),1)):R("",!0)]))],2),D(Ns,{modelValue:F.value,"onUpdate:modelValue":v[0]||(v[0]=$=>F.value=$),"rule-info-data":n(t)},null,8,["modelValue","rule-info-data"])])}}});const Bs=rt(Us,[["__scopeId","data-v-8ba03053"]]),Ps={class:"w-[538px] flex-grow overflow-y-auto border border-gray-6 mt-[5px] bg-gray-8 rounded-sm break-words"},Ms={class:"h-[20px] text-gray-1 text-[12px] font-bold ml-[5px] mt-[5px] mb-[-2px]"},Xs={key:0,class:"h-full overflow-auto m-1 font-mono bg-gray-8 rounded-sm"},Hs={class:"grid grid-cols-[70%_0%_0%_30%] auto-rows-[minmax(25px,auto)]"},Ks={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},zs={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px] w-[244px]"},Js={key:0,class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Ys={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},Ws={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},Gs={class:"col-span-2 px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Zs={class:"mb-1"},tr={class:"grid grid-cols-[60%_23%_17%_0%]"},er={class:"ml-1 text-gray-1 text-[12px] font-normal leading-[16px] mr-2"},ar={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},sr={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},rr={class:"flex justify-end"},ir={class:"mr-[2px] text-gray-1 text-[12px] font-normal leading-[16px]"},or={class:"flex justify-end"},lr={class:"ml-1 text-gray-1 text-[12px] font-normal leading-[16px]"},nr={key:0,class:"inline-block text-red-1 mr-1"},cr={class:"w-[102px] inline-block"},dr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},ur={class:"text-gray-1 text-[12px] font-normal leading-[16px] whitespace-pre-wrap"},pr=e("br",null,null,-1),gr={class:"grid auto-rows-[minmax(25px,auto)]"},fr={class:"col-span-8 px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},yr={class:"grid grid-cols-[23%_10%_7%_3%_9%_9%_22%_17%] auto-rows-[minmax(20px,auto)] grid-row"},_r={class:"text-gray-1 text-[12px] font-normal leading-[16px] ml-[10px] mr-[5px] grid-col"},hr={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},vr={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},kr={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},xr={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},br={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},mr={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col mr-[5px]"},Tr={class:"flex text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},$r={class:"flex-1"},wr={class:"flex-1"},Dr={class:"text-right"},Cr={key:0,class:"grid grid-cols-[23%_10%_7%_3%_9%_9%_17%_22%] auto-rows-[minmax(20x,auto)] grid-row"},Nr={class:"text-gray-1 text-[12px] font-normal leading-[16px] ml-[10px] mr-[5px] grid-col"},Rr={class:"text-gray-1 text-[12px] font-normal leading-[16px] grid-col"},Lr={class:"grid grid-cols-[23%_0%_0%_77%] auto-rows-[minmax(25px,auto)]"},Or={class:"col-span-2 flex flex-col justify-start px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Ir={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Er={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Fr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},Ar={class:"text-gray-1 text-[12px] font-normal leading-[16px] w-[395px]"},jr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Sr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},qr=e("div",{class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},[e("div",{class:"text-gray-1 text-[12px] font-normal leading-[16px]"})],-1),Qr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Vr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Ur=e("div",{class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},[e("div",{class:"text-gray-1 text-[12px] font-normal leading-[16px]"})],-1),Br={class:"grid grid-cols-[23%_52%_25%_0%] auto-rows-[minmax(25px,auto)]"},Pr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Mr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Xr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Hr={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},Kr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},zr={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},Jr={class:"grid grid-cols-[23%_27%_25%_25%] auto-rows-[minmax(25px,auto)]"},Yr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-r-0 border-b-0 border-gray-6"},Wr={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},Gr={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},Zr={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},ti={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-r-0 border-gray-6"},ei={class:"ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},ai={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-r-0 border-gray-6"},si={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},ri={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-gray-6 border-r-0 border-b-0"},ii={class:"text-gray-1 text-[12px] font-normal leading-[16px] whitespace-pre-wrap"},oi={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-8 border border-b-0 border-gray-6"},li={class:"text-gray-1 text-[12px] font-normal leading-[16px]"},ni={class:"col-span-4 px-[6px] py-[4px] bg-gray-8 border border-b-1 border-gray-6 flex justify-end items-center"},ci={class:"text-gray-1 text-[12px] font-normal leading-[16px] mr-[5px]"},di={key:0,class:"col-span-4 px-[6px] py-[4px] bg-gray-8 border border-t-0 border-gray-6 flex justify-end items-center"},ui={class:"text-gray-1 text-[12px] font-normal leading-[16px] mr-[5px]"},pi={key:1,class:"col-span-4 flex justify-start items-center px-[6px] py-[4px] border-t-0 bg-gray-8 border border-gray-6"},gi={class:"w-[68px] text-xs"},fi={class:"w-[450px] text-gray-1 text-[12px] font-normal leading-[16px]"},yi={key:2,class:"col-span-4 flex justify-start items-center px-[6px] py-[4px] border-t-0 bg-gray-8 border border-gray-6"},_i={class:"w-[68px] text-xs"},hi={class:"w-[450px] text-gray-1 text-[12px] font-normal leading-[16px]"},vi={key:3,class:"col-span-4 flex justify-start items-center px-[6px] py-[4px] border-t-0 bg-gray-8 border border-gray-6"},ki={class:"w-[68px] text-xs"},xi={class:"w-[450px] ml-[5px] text-gray-1 text-[12px] font-normal leading-[16px]"},bi={key:1,class:"mb-[15px] text-center w-full bg-gray-8"},mi={class:"mt-[15px] text-gray-2 font-normal leading-tight text-xs"},Ti=W({__name:"RtktDetail",props:{rtktDetail:{}},setup(N){const x=t=>{const p=[];return t.forEach(o=>{p.push(`${o.taxCode}: ${o.currencyCode} ${o.taxAmount}`)}),p.length?p.join(" / "):""},k=(t,p)=>{if(!t.length)return"";const o=t.filter(d=>!d.extendedTaxInd);return o[p]&&o[p].newOldRefundTax==="O"?Number(o[p].taxAmount)>0?`PD ${o[p].taxAmount}${o[p].taxCode}`:"PD EXEMPT":o[p]&&o[p].newOldRefundTax==="T"?Number(o[p].taxAmount)>0?`${o[p].taxCode} ${o[p].taxAmount}`:`${o[p].taxCode} EXEMPT`:""},O=t=>{if(!t.length)return"";const p=t.filter(d=>d.taxCode==="XT"&&!d.extendedTaxInd);return t.filter(d=>!d.extendedTaxInd).length===3?k(t,2):p[0]&&p[0].newOldRefundTax==="O"?Number(p[0].taxAmount)>0?`PD ${p[0].taxAmount}${p[0].taxCode}`:"PD EXEMPT":p[0]&&p[0].newOldRefundTax==="T"?Number(p[0].taxAmount)>0?`${p[0].taxCode} ${p[0].taxAmount}`:`${p[0].taxCode} EXEMPT`:""},F=(t,p)=>(p??[]).some(o=>o.taxCode!=="XT"&&o.newOldRefundTax===t),L=t=>{const p=t.filter(o=>o.taxCode!=="XT"&&o.newOldRefundTax==="T");return x(p)},w=t=>{const p=t.filter(d=>d.taxCode!=="XT"&&d.newOldRefundTax==="O"),o=[];return p.forEach(d=>{o.push(`${d.taxCode}: ${d.currencyCode} ${d.taxAmount} PD`)}),o.length?o.join("/"):""};return(t,p)=>{var o,d,v,T,I,h,f,$,j,y,b,_,A,H,B,V,M,s,c,u,m,l,E,U,G,g,z,Z,it,et,gt,ft,yt,_t,ht,vt,kt,xt,bt,mt,Tt,$t,wt,Dt,Ct,Nt,Rt,Lt,Ot,It,Et,Ft,At,jt,St,qt,Qt,Vt,Ut,Bt,Pt,Mt,Xt,Ht,Kt,zt,Jt,Yt,Wt,Gt,Zt,te,ee,ae,se,re,ie,oe,le,ne,ce,de,ue,pe,ge,fe,ye,_e,he,ve,ke,xe,be,me,Te,$e,we,De,Ce,Ne,Re,Le,Oe,Ie,Ee,Fe,Ae,je,Se,qe,Qe,Ve,Ue,Be;return r(),i("div",Ps,[e("div",Ms,a(t.$t("app.queryRtkt.originalTextOfRTKT")),1),(o=t.rtktDetail)!=null&&o.ticket?(r(),i("div",Xs,[e("div",Hs,[e("div",Ks,[e("div",zs,a(((v=(d=t.rtktDetail)==null?void 0:d.ticket)==null?void 0:v.issueAirline)??""),1),(I=(T=t.rtktDetail)==null?void 0:T.ticket)!=null&&I.ticketCount&&((f=(h=t.rtktDetail)==null?void 0:h.ticket)==null?void 0:f.ticketCount)>=2?(r(),i("div",Js," CONJ TKT "+a((j=($=t.rtktDetail)==null?void 0:$.ticket)==null?void 0:j.conjunction)+" OF "+a((b=(y=t.rtktDetail)==null?void 0:y.ticket)==null?void 0:b.ticketCount),1)):R("",!0)]),e("div",Ys,[e("div",Ws,a(((A=(_=t.rtktDetail)==null?void 0:_.ticket)==null?void 0:A.iataCode)??""),1)]),e("div",Gs,[e("div",Zs,[e("div",tr,[e("div",er,a(((B=(H=t.rtktDetail)==null?void 0:H.ticket)==null?void 0:B.ei)??""),1),e("div",ar,a(((M=(V=t.rtktDetail)==null?void 0:V.ticket)==null?void 0:M.recordCreateDateTime)??""),1),e("div",sr,a(((c=(s=t.rtktDetail)==null?void 0:s.ticket)==null?void 0:c.origin)??"")+a(((m=(u=t.rtktDetail)==null?void 0:u.ticket)==null?void 0:m.destination)??""),1)])]),e("div",rr,[e("div",ir,a(((E=(l=t.rtktDetail)==null?void 0:l.ticket)==null?void 0:E.code)??"")+"/"+a(((G=(U=t.rtktDetail)==null?void 0:U.ticket)==null?void 0:G.channelCode)??""),1)]),e("div",or,[e("div",lr,[(z=(g=t.rtktDetail)==null?void 0:g.ticket)!=null&&z.refundVoidTag?(r(),i("div",nr,a(((it=(Z=t.rtktDetail)==null?void 0:Z.ticket)==null?void 0:it.refundVoidTag)??""),1)):R("",!0),e("div",cr,a(((gt=(et=t.rtktDetail)==null?void 0:et.ticket)==null?void 0:gt.originalTicket)??""),1)])])]),e("div",dr,[e("div",ur,[at(a(((yt=(ft=t.rtktDetail)==null?void 0:ft.ticket)==null?void 0:yt.describe)??""),1),pr,at(a(((ht=(_t=t.rtktDetail)==null?void 0:_t.ticket)==null?void 0:ht.accountNumber)??"")+" "+a(((kt=(vt=t.rtktDetail)==null?void 0:vt.ticket)==null?void 0:kt.cipher)??"")+" "+a(((bt=(xt=t.rtktDetail)==null?void 0:xt.ticket)==null?void 0:bt.hourIndicator)??""),1)])])]),e("div",gr,[e("div",fr,[(r(!0),i(q,null,Q((Tt=(mt=t.rtktDetail)==null?void 0:mt.passenger)==null?void 0:Tt.segments,(P,Pe)=>{var Me,Xe;return r(),i("div",{key:Pe},[e("div",yr,[e("div",_r,a(P.departureCityName),1),e("div",hr,a(P.departureCity)+a(P.airline),1),e("div",vr,a(P.flightNoWithAirline),1),e("div",kr,a(P.cabin),1),e("div",xr,a(P.departureDate),1),e("div",br,a(P.departureTime),1),e("div",mr,a(P.reservationStatusCode)+a(P.fareBasis),1),e("div",Tr,[e("div",$r,a(P.notValidBefore),1),e("div",wr,a(P.notValidAfter),1),e("div",Dr,a(P.baggage),1)])]),Pe+1===((Xe=(Me=t.rtktDetail)==null?void 0:Me.passenger)==null?void 0:Xe.segments.length)?(r(),i("div",Cr,[e("div",Nr,a(P.arrivalCityName),1),e("div",Rr,a(P.arrivalCity),1)])):R("",!0)])}),128))])]),e("div",Lr,[e("div",Or,[e("div",Ir,a((wt=($t=t.rtktDetail)==null?void 0:$t.price)==null?void 0:wt.ticketAmountFOrRCode)+" "+a((Ct=(Dt=t.rtktDetail)==null?void 0:Dt.price)==null?void 0:Ct.ticketAmountFOrRAmount),1),e("div",Er,a((Rt=(Nt=t.rtktDetail)==null?void 0:Nt.price)==null?void 0:Rt.ticketAmountECode)+" "+a((Ot=(Lt=t.rtktDetail)==null?void 0:Lt.price)==null?void 0:Ot.ticketAmountE),1)]),e("div",Fr,[e("div",Ar,a((Et=(It=t.rtktDetail)==null?void 0:It.price)==null?void 0:Et.autoFareType)+a((At=(Ft=t.rtktDetail)==null?void 0:Ft.price)==null?void 0:At.fc),1)]),e("div",jr,[e("div",Sr,a(k(((St=(jt=t.rtktDetail)==null?void 0:jt.price)==null?void 0:St.taxes)??[],0)),1)]),qr,e("div",Qr,[e("div",Vr,a(k(((Qt=(qt=t.rtktDetail)==null?void 0:qt.price)==null?void 0:Qt.taxes)??[],1)),1)]),Ur]),e("div",Br,[e("div",Pr,[e("div",Mr,a(O(((Ut=(Vt=t.rtktDetail)==null?void 0:Vt.price)==null?void 0:Ut.taxes)??[])),1)]),e("div",Xr,[e("div",Hr,a((Pt=(Bt=t.rtktDetail)==null?void 0:Bt.price)==null?void 0:Pt.formOfPaymentText),1)]),e("div",Kr,[e("div",zr,a((Xt=(Mt=t.rtktDetail)==null?void 0:Mt.price)==null?void 0:Xt.tc),1)])]),e("div",Jr,[e("div",Yr,[e("div",Wr,a(((Kt=(Ht=t.rtktDetail)==null?void 0:Ht.price)==null?void 0:Kt.fareAmount)===""||((Jt=(zt=t.rtktDetail)==null?void 0:zt.price)==null?void 0:Jt.fareAmount)==="NO ADC"?"":(Wt=(Yt=t.rtktDetail)==null?void 0:Yt.price)==null?void 0:Wt.currency)+" "+a((Zt=(Gt=t.rtktDetail)==null?void 0:Gt.price)==null?void 0:Zt.fareAmount),1)]),e("div",Gr,[e("div",Zr,a((ee=(te=t.rtktDetail)==null?void 0:te.ticket)==null?void 0:ee.originalTicketInfo.ticketNumber)+a((se=(ae=t.rtktDetail)==null?void 0:ae.ticket)==null?void 0:se.originalTicketInfo.cityCode)+a((ie=(re=t.rtktDetail)==null?void 0:re.ticket)==null?void 0:ie.originalTicketInfo.issueDate)+" "+a((le=(oe=t.rtktDetail)==null?void 0:oe.ticket)==null?void 0:le.originalTicketInfo.iataNumber),1)]),e("div",ti,[e("div",ei,a((ce=(ne=t.rtktDetail)==null?void 0:ne.ticket)==null?void 0:ce.ticketNumber),1)]),e("div",ai,[e("div",si,a((ue=(de=t.rtktDetail)==null?void 0:de.price)==null?void 0:ue.scny),1)]),e("div",ri,[e("div",ii,a((ge=(pe=t.rtktDetail)==null?void 0:pe.price)==null?void 0:ge.commissionFareOrRate),1)]),e("div",oi,[e("div",li,a((ye=(fe=t.rtktDetail)==null?void 0:fe.price)==null?void 0:ye.newTaxTotalAmount),1)]),e("div",ni,[e("div",ci,[e("span",null,a((he=(_e=t.rtktDetail)==null?void 0:_e.ticket)!=null&&he.fareBrandInd?"B":"")+" ",1),e("span",null,a((ke=(ve=t.rtktDetail)==null?void 0:ve.ticket)!=null&&ke.ticketType?`${(be=(xe=t.rtktDetail)==null?void 0:xe.ticket)==null?void 0:be.ticketManagementOrganizationCode}-${($e=(Te=(me=t.rtktDetail)==null?void 0:me.ticket)==null?void 0:Te.ticketType)==null?void 0:$e.charAt(0)}`:(De=(we=t.rtktDetail)==null?void 0:we.ticket)==null?void 0:De.ticketManagementOrganizationCode),1)])]),((Ne=(Ce=t.rtktDetail)==null?void 0:Ce.ticket)==null?void 0:Ne.refundVoidTag)==="RFND"?(r(),i("div",di,[e("div",ui,"RFDEV-"+a((Le=(Re=t.rtktDetail)==null?void 0:Re.ticket)==null?void 0:Le.refundPrintNumber),1)])):R("",!0),F("T",(Ie=(Oe=t.rtktDetail)==null?void 0:Oe.price)==null?void 0:Ie.taxes)?(r(),i("div",pi,[e("div",gi,a(t.$t("app.queryRtkt.detailTaxes")),1),e("div",fi,a(L((Fe=(Ee=t.rtktDetail)==null?void 0:Ee.price)==null?void 0:Fe.taxes)),1)])):R("",!0),F("O",(je=(Ae=t.rtktDetail)==null?void 0:Ae.price)==null?void 0:je.taxes)?(r(),i("div",yi,[e("div",_i,a(t.$t("app.queryRtkt.oldDetailTaxes")),1),e("div",hi,a(w((qe=(Se=t.rtktDetail)==null?void 0:Se.price)==null?void 0:qe.taxes)),1)])):R("",!0),(Ve=(Qe=t.rtktDetail)==null?void 0:Qe.price)!=null&&Ve.refundTaxes.length?(r(),i("div",vi,[e("div",ki,a(t.$t("app.queryRtkt.detailsOfTaxRefund")),1),e("div",xi,a(x(((Be=(Ue=t.rtktDetail)==null?void 0:Ue.price)==null?void 0:Be.refundTaxes)??[])),1)])):R("",!0)])])):(r(),i("div",bi,[e("p",mi,a(t.$t("app.querySearch.airCityCommon.noneData")),1)]))])}}}),$i={key:0,class:"w-[538px] p-1.5 bg-gray-7 rounded rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},wi={class:"w-full justify-between items-start gap-2.5 flex"},Di={class:"text-gray-1 text-xs font-bold leading-tight mb-1"},Ci={key:0,class:"text-[12px] justify-start text-gray-1 font-normal leading-4"},Ni={key:0},Ri={class:"font-mono inline-block whitespace-pre-wrap break-words max-w-full"},Li={key:1,class:"font-mono whitespace-pre-wrap break-words max-w-full"},Oi={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},Ii={key:1,class:"w-[538px] p-1.5 bg-gray-8 rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},Ei={class:"justify-start text-gray-1 text-xs font-bold leading-tight mb-1"},Fi={key:0,class:"w-[520px] font-mono text-[12px] justify-start text-gray-1 font-normal leading-4"},Ai={class:"whitespace-pre-wrap break-words max-w-full"},ji={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},Si={key:2,class:"w-[538px] p-1.5 bg-gray-8 rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},qi={class:"justify-start text-gray-1 text-xs font-bold leading-tight mb-1"},Qi={key:0,class:"w-[520px] font-mono text-[12px] justify-start text-gray-1 font-normal leading-4"},Vi={class:"whitespace-pre-wrap break-words max-w-full"},Ui={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},Bi={key:3,class:"w-[538px] p-1.5 bg-gray-8 rounded-sm border border-gray-6 inline-flex flex-col justify-start items-start mt-1.5"},Pi={class:"justify-start text-gray-1 text-xs font-bold leading-tight mb-1"},Mi={key:0,class:"w-[520px] font-mono text-[12px] justify-start text-gray-1 font-normal leading-4"},Xi={class:"whitespace-pre-wrap break-words max-w-full"},Hi={key:1,class:"w-[520px] text-xs text-center text-gray-2 font-normal leading-tight"},Ki=W({__name:"TicketOriginalContent",props:{ticketOriginalItem:{}},emits:["clickOriginalTicketNo"],setup(N,{emit:x}){const{t:k}=Je(),O=x,F=o=>{const d=o.match(lt);return ha((d==null?void 0:d[1])??"")??""},L=o=>{O("clickOriginalTicketNo",o)},{copy:w,isSupported:t}=_a({legacy:!0}),p=o=>{if(!t)return;const d=o==null?void 0:o.join(`
`);w(d),va({message:k("app.batchRefund.copySuccess"),type:"success"})};return(o,d)=>{var T,I,h,f,$;const v=Ye;return r(),i("div",null,[(T=o.ticketOriginalItem.checkedTabList)!=null&&T.includes("D")?(r(),i("div",$i,[e("div",wi,[e("div",Di,a(o.$t("app.ticketOriginal.originalTextTitle")),1),((I=o.ticketOriginalItem.originalTextList)==null?void 0:I.length)>1?(r(),K(v,{key:0,size:"small",class:"copy-all",onClick:d[0]||(d[0]=j=>p(o.ticketOriginalItem.originalTextList))},{default:C(()=>[e("div",null,a(o.$t("app.ticketOriginal.copyAll")),1)]),_:1})):R("",!0)]),o.ticketOriginalItem.originalTextList.length>1?(r(),i("div",Ci,[(r(!0),i(q,null,Q(o.ticketOriginalItem.originalTextList,(j,y)=>(r(),i(q,{key:y},[n(lt).test(j)?(r(),i("span",Ni,[at(" EXCH:   "),e("span",{class:"text-brand-2 font-bold cursor-pointer mr-5 underline",onClick:d[1]||(d[1]=b=>L(o.ticketOriginalItem))},a(F(j)),1),e("pre",Ri,a(j.replace(n(lt),"")),1)])):(r(),i("pre",Li,a(j),1))],64))),128))])):(r(),i("div",Oi,a(o.$t("app.ticketOriginal.noData")),1))])):R("",!0),(h=o.ticketOriginalItem.checkedTabList)!=null&&h.includes("H")?(r(),i("div",Ii,[e("div",Ei,a(o.$t("app.ticketOriginal.historyTitle")),1),o.ticketOriginalItem.historyText?(r(),i("div",Fi,[e("pre",Ai,a(o.ticketOriginalItem.historyText),1)])):(r(),i("div",ji,a(o.$t("app.ticketOriginal.noData")),1))])):R("",!0),(f=o.ticketOriginalItem.checkedTabList)!=null&&f.includes("F")?(r(),i("div",Si,[e("div",qi,a(o.$t("app.ticketOriginal.certificateTitle")),1),o.ticketOriginalItem.certificateText?(r(),i("div",Qi,[e("pre",Vi,a(o.ticketOriginalItem.certificateText),1)])):(r(),i("div",Ui,a(o.$t("app.ticketOriginal.noData")),1))])):R("",!0),($=o.ticketOriginalItem.checkedTabList)!=null&&$.includes("X")?(r(),i("div",Bi,[e("div",Pi,a(o.$t("app.ticketOriginal.taxDetailTitle")),1),o.ticketOriginalItem.taxTextList.length>0?(r(),i("div",Mi,[(r(!0),i(q,null,Q(o.ticketOriginalItem.taxTextList,(j,y)=>(r(),i("div",{key:y},[e("pre",Xi,a(j),1)]))),128))])):(r(),i("div",Hi,a(o.$t("app.ticketOriginal.noData")),1))])):R("",!0)])}}});const zi=rt(Ki,[["__scopeId","data-v-a95595d2"]]),Ji=N=>{const x=S(!1),k=S(!1),O=S(!1),F=S(!1),L=S(1),w=S(""),t=S([]),p=s=>{x.value=s},o=async s=>{var c;try{x.value=!0;const u=T("D"),m=h(s,"D"),l=(c=(await nt(m,u)).data.value)==null?void 0:c.data;t.value[s].checkedTabList.includes("D")||t.value[s].checkedTabList.push("D"),t.value[s].isShowXsFsnTab=(l==null?void 0:l.international)??!1,t.value[s].secondFactor=(l==null?void 0:l.secondFactor)??{},t.value[s].originalTicketNos=(l==null?void 0:l.originalTicketNos)??[],t.value[s].conjunctionTicketNos=(l==null?void 0:l.conjunctionTicketNos)??[],t.value[s].originalTextList=ot((l==null?void 0:l.openSourceText)??"").split(`\r
`)}finally{x.value=!1}},d=async s=>{for(let c=0;c<s.originalTicketNos.length;c++){const u=t.value.findIndex(m=>m.ticketNo===s.originalTicketNos[c]);u===-1?(await t.value.push(B(s.originalTicketNos[c],s.secondFactor)),await o(t.value.length-1)):t.value.splice(u,1)}},v=async()=>{O.value||t.value[0].originalTextList.length!==0||await M(0),O.value=!O.value},T=s=>{switch(s){case"D":return tt("091M0102");case"H":return tt("091M0103");case"F":return tt("091M0104");case"X":return tt("091M0105");case"R":return tt("091M0106");case"N":return tt("091M0107");default:return""}},I=s=>{var m,l,E,U;const c=(l=(m=t.value[s])==null?void 0:m.secondFactor)==null?void 0:l.secondFactorCode,u=(U=(E=t.value[s])==null?void 0:E.secondFactor)==null?void 0:U.secondFactorValue;return c&&u?t.value[s].secondFactor:null},h=(s,c)=>({detrType:c,ticketNo:t.value[s].ticketNo,intl:ka()==="en",secondFactor:I(s)}),f=s=>{if(!s)return[];const c=[],u=(s??"").split(`
`);c.push(u[0]);let m="",l=0;for(const E of u[1].trim())E==="|"&&l++,m=`${m}${E}`,l===5&&(c.push(m),m="",l=0);return m&&c.push(m),c},$=async(s,c)=>{var E,U;const u=T(c),m=h(s,c),l=(E=(await nt(m,u)).data.value)==null?void 0:E.data;switch(c){case"D":t.value[s].isShowXsFsnTab=(l==null?void 0:l.international)??!1,t.value[s].secondFactor=(l==null?void 0:l.secondFactor)??{},t.value[s].originalTicketNos=(l==null?void 0:l.originalTicketNos)??[],t.value[s].conjunctionTicketNos=(l==null?void 0:l.conjunctionTicketNos)??[],t.value[s].originalTextList=ot((l==null?void 0:l.openSourceText)??"").split(`\r
`);break;case"F":t.value[s].certificateText=ot(((U=l==null?void 0:l.credential)==null?void 0:U.certificatesText)??"");break;case"H":t.value[s].historyText=(l==null?void 0:l.tktHistoryText)??"";break;case"X":t.value[s].taxTextList=f((l==null?void 0:l.ticketFareInfoText)??"");break}},j=async(s,c)=>{var E;const u=T(c),m=t.value[s].ticketNo,l=await Ea(m,u,!0);t.value[s].rtktDetail=(E=l.data.value)==null?void 0:E.data},y=async(s,c)=>{try{if(x.value=!0,c==="N")return;c==="R"?await j(s,c):await $(s,c)}finally{x.value=!1}},b=(s,c)=>{var u;(u=t.value[s].checkedTabList)!=null&&u.includes(c)?t.value[s].checkedTabList=t.value[s].checkedTabList.filter(m=>m!==c):(t.value[s].checkedTabList.push(c),y(s,c))},_=async s=>{if(t.value.forEach(c=>c.checkedTabList=["D"]),t.value[s].originalTextList.length===0&&await V(s),t.value[s].originalTicketNos.length>0)for(let c=0;c<t.value[s].originalTicketNos.length;c++){const u=t.value.every(l=>l.ticketNo!==t.value[s].originalTicketNos[c]||l.originalTextList.length===0);t.value.every(l=>l.ticketNo!==t.value[s].originalTicketNos[c])&&await t.value.push(B(t.value[s].originalTicketNos[c],t.value[s].secondFactor)),u&&await _(t.value.length-1)}},A=async s=>{var c;try{if(x.value=!0,t.value.forEach(u=>u.checkedTabList=["R"]),Object.keys(((c=t.value[s])==null?void 0:c.rtktDetail)??{}).length===0&&await j(s,"R"),t.value[s].originalTicketNos.length>0)for(let u=0;u<t.value[s].originalTicketNos.length;u++){const m=t.value.every(E=>E.ticketNo!==t.value[s].originalTicketNos[u]||Object.keys((E==null?void 0:E.rtktDetail)??{}).length===0);t.value.every(E=>E.ticketNo!==t.value[s].originalTicketNos[u])&&await t.value.push(B(t.value[s].originalTicketNos[u],t.value[s].secondFactor)),m&&await A(t.value.length-1)}}finally{x.value=!1}},H=async s=>{k.value=!k.value,w.value=s;for(let c=0;c<t.value.length;c++)s==="D"?await _(c):await A(c)},B=(s,c)=>({ticketNo:s,originalTicketNos:[],conjunctionTicketNos:[],secondFactor:c,originalTextList:[],historyText:"",certificateText:"",taxTextList:[],rtktDetail:{},isShowXsFsnTab:!1,checkedTabList:[],isAutoFare:!1,tktRulesData:{},fsnIssueDate:"",fsnTicketNos:[]}),V=async s=>{var c;try{x.value=!0;const u=T("D"),m=h(s,"D"),l=(c=(await nt(m,u)).data.value)==null?void 0:c.data;s===0&&((l==null?void 0:l.originalTicketNos)??[]).length>0&&(F.value=!0),t.value[s].checkedTabList.includes("D")||t.value[s].checkedTabList.push("D"),t.value[s].isShowXsFsnTab=(l==null?void 0:l.international)??!1,t.value[s].secondFactor=(l==null?void 0:l.secondFactor)??{},t.value[s].originalTicketNos=(l==null?void 0:l.originalTicketNos)??[],t.value[s].conjunctionTicketNos=(l==null?void 0:l.conjunctionTicketNos)??[],t.value[s].originalTextList=ot((l==null?void 0:l.openSourceText)??"").split(`\r
`)}finally{x.value=!1}},M=async s=>{await V(s);const c=t.value[s].conjunctionTicketNos.filter(u=>u!==t.value[s].ticketNo);if(c.length>0)for(let u=0;u<c.length;u++)await t.value.push(B(c[u],t.value[s].secondFactor)),await V(u+1)};return st(async()=>{if(t.value){const s=N.ticketInfo.ticketNo,c=s[3]!=="-"?`${s.slice(0,3)}-${s.slice(3)}`:s;await t.value.push(B(c,N.ticketInfo.secondFactor))}N.isQuery&&(O.value=!0,await M(0),L.value=t.value.length)}),{showLoading:x,isExpandList:k,isExpandTicket:O,isShowTicketContrast:F,ticketAmount:L,contrastType:w,ticketOriginalList:t,handleClickTab:b,handleExpandTicket:v,handleTicketContrast:H,setLoading:p,clickOriginalTicketNo:d}},Yi=Ji,Wi={key:0,class:"w-[550px] h-8 px-2.5 py-1.5 bg-brand-4 rounded-sm border border-brand-3 justify-between items-center inline-flex"},Gi={class:"justify-start items-center flex"},Zi={class:"text-gray-1 text-xs font-bold leading-tight mr-2.5"},to={key:0,class:"text-brand-2 text-xs font-normal leading-tight mr-1"},eo={key:1,class:"relative"},ao={class:"absolute right-[-30px] top-[12px] mt-2 w-[80px] bg-white rounded shadow-lg py-1 z-50 border border-gray-200"},so={class:"flex items-center"},ro={key:0,class:"w-[17px] text-brand-2"},io={key:1,class:"w-[15px]"},oo={class:"flex items-center"},lo={key:0,class:"w-[17px] text-brand-2"},no={key:1,class:"w-[15px]"},co={key:0},uo={key:1},po={key:1,class:"mx-1.5"},go={key:0},fo={class:"w-[530px] text-gray-1 text-xs font-bold leading-tight pt-1"},yo={key:2,class:"w-[550px] h-full p-1.5 flex-col justify-start items-start inline-flex bg-gray-8 rounded-b-sm"},_o={class:"text-xs h-6 justify-start items-start inline-flex cursor-pointer"},ho=["onClick"],vo=["onClick"],ko=["onClick"],xo=["onClick"],bo=["onClick"],mo=["onClick"],To={class:"w-full"},$o={key:1},wo={class:"close-box"},Do={class:"flex gap-1 items-center"},Co={class:"justify-start text-gray-1 text-xs font-bold leading-tight"},No=W({__name:"TicketOriginalItem",props:{ticketInfo:{},isQuery:{type:Boolean}},setup(N){const x=N,{showLoading:k,isExpandList:O,isExpandTicket:F,isShowTicketContrast:L,ticketAmount:w,contrastType:t,ticketOriginalList:p,handleClickTab:o,handleExpandTicket:d,handleTicketContrast:v,clickOriginalTicketNo:T,setLoading:I}=Yi(x);return(h,f)=>{const $=ba,j=aa,y=We;return J((r(),i("div",null,[(r(!0),i(q,null,Q(n(p),(b,_)=>{var A,H,B,V,M,s,c;return r(),i("div",{key:_+b.ticketNo,class:"w-[550px] bg-gray-8"},[_===0?(r(),i("div",Wi,[e("div",Gi,[e("div",Zi,a(h.ticketInfo.ticketNo),1),n(L)?(r(),i("div",to,a(h.$t("app.ticketOriginal.ticketContrast")),1)):R("",!0),n(L)?(r(),i("span",eo,[e("span",{class:"text-[16px] text-brand-2 cursor-pointer",onClick:f[0]||(f[0]=u=>O.value=!n(O))},[D($,null,{default:C(()=>[D(n(Ke))]),_:1})]),J(e("div",ao,[e("div",{class:"px-4 py-2 hover:bg-blue-50 cursor-pointer text-xs",onClick:f[1]||(f[1]=u=>n(v)("D"))},[e("div",so,[n(t)==="D"?(r(),i("div",ro,[D($,null,{default:C(()=>[D(n(ze))]),_:1})])):(r(),i("div",io)),e("span",{class:X({"text-brand-2 font-medium":n(t)==="D"})},a(h.$t("app.ticketOriginal.originalText")),3)])]),e("div",{class:"px-4 py-2 hover:bg-blue-50 cursor-pointer text-xs",onClick:f[2]||(f[2]=u=>n(v)("R"))},[e("div",oo,[n(t)==="R"?(r(),i("div",lo,[D($,null,{default:C(()=>[D(n(ze))]),_:1})])):(r(),i("div",no)),e("span",{class:X({"text-brand-2 font-medium":n(t)==="R"})},a(h.$t("app.ticketOriginal.rtkt")),3)])])],512),[[ct,n(O)]])])):R("",!0)]),e("div",{class:"text-[20px] text-brand-2 cursor-pointer",onClick:f[3]||(f[3]=u=>n(d)())},[n(F)?(r(),i("span",co,[D($,null,{default:C(()=>[D(n(xa))]),_:1})])):(r(),i("span",uo,[D($,null,{default:C(()=>[D(n(Ke))]),_:1})]))])])):R("",!0),_!==0&&n(F)?(r(),i("div",po,[_%n(w)===0?(r(),i("div",go,[D(j,{"border-style":"dashed"})])):R("",!0),e("div",fo,a(b.ticketNo),1)])):R("",!0),n(F)?(r(),i("div",yo,[e("div",_o,[e("div",{class:X(["w-11 px-2 py-0.5 rounded-tl-sm border",[(A=b.checkedTabList)!=null&&A.includes("D")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:u=>n(o)(_,"D")},a(h.$t("app.ticketOriginal.originalText")),11,ho),e("div",{class:X(["w-[99px] px-2 py-0.5 border",[(H=b.checkedTabList)!=null&&H.includes("H")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:u=>n(o)(_,"H")},a(h.$t("app.ticketOriginal.history")),11,vo),e("div",{class:X(["w-[132px] px-2 py-0.5 border",[(B=b.checkedTabList)!=null&&B.includes("F")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:u=>n(o)(_,"F")},a(h.$t("app.ticketOriginal.certificate")),11,ko),e("div",{class:X(["w-[109px] px-2 py-0.5 border",[(V=b.checkedTabList)!=null&&V.includes("X")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:u=>n(o)(_,"X")},a(h.$t("app.ticketOriginal.taxDetail")),11,xo),e("div",{class:X(["w-[50px] px-2 py-0.5 rounded-tl-sm rounded-sm border",[(M=b.checkedTabList)!=null&&M.includes("R")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:u=>n(o)(_,"R")},a(h.$t("app.ticketOriginal.rtkt")),11,bo),b.isShowXsFsnTab?(r(),i("div",{key:0,class:X(["w-[97px] px-2 py-0.5 rounded-tl-sm rounded-sm border",[b.checkedTabList.includes("N")?"bg-brand-7 border-brand-2 text-brand-2":"bg-gray-0 border-gray-6"]]),onClick:u=>n(o)(_,"N")},a(h.$t("app.ticketOriginal.ticketRule")),11,mo)):R("",!0)]),e("div",To,[D(zi,{"ticket-original-item":b,onClickOriginalTicketNo:n(T)},null,8,["ticket-original-item","onClickOriginalTicketNo"]),(s=b.checkedTabList)!=null&&s.includes("R")?(r(),K(Ti,{key:0,"rtkt-detail":b.rtktDetail},null,8,["rtkt-detail"])):R("",!0),(c=b.checkedTabList)!=null&&c.includes("N")?(r(),i("div",$o,[D(Bs,{"ticket-ignore-error":!0,international:b.isShowXsFsnTab,"ticket-no":b.ticketNo,"is-query-rule-by-ticket-no":!0,"need-close-icon":"",onSetLoading:n(I)},{default:C(()=>[e("div",wo,[e("div",Do,[e("span",Co,a(h.$t("app.ticketOriginal.ticketRuleTitle")),1)])])]),_:2},1032,["international","ticket-no","onSetLoading"])])):R("",!0)])])):R("",!0)])}),128))])),[[y,n(k)]])}}});const Po=rt(No,[["__scopeId","data-v-e73d5519"]]);export{Bs as T,Ti as _,zi as a,Po as b,Bo as q};
