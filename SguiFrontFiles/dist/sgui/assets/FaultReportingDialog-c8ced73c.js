import{L as we,M as ge,ez as ke,eB as pe,q as ne,bc as be,ie as Ne,ig as Oe,N as De,v as Le,ih as Ae,r as V,ii as Me,s as Ie,w as T,ac as ve,aI as Ce,o as Fe,x as L,y as ee,G as u,z as h,P as a,D,A as e,C as he,E as Pe,J as N,H as W,a1 as je,B as Y,ai as se,ij as Ye,ik as Be,il as qe,im as Ue,bf as He,io as Ge,ip as Xe,aj as $e,a5 as Ee,a6 as Se,F as ce,T as Je,a7 as Ke,_ as Ve,V as ae,u as le,hy as Qe,K as Te,f5 as We,f4 as Ze,eC as oe,Q as $,X as et,iq as tt,ec as at,hB as lt,p as ot,c3 as ye,c4 as re,ao as ze,ab as st,a9 as nt,aa as rt,aJ as it,b2 as fe,av as Re,d5 as _e,R as xe,bb as ut,ak as ie,ah as ct,al as pt,am as dt,an as ft}from"./index-18f146fc.js";import{m as ue}from"./Tip-a3c59246.js";import{E as mt}from"./index-28cc82cf.js";import{E as gt,a as vt}from"./index-c5503643.js";import{t as me}from"./throttle-39cac876.js";import{i as ht}from"./position-a4b17c53.js";import{E as yt}from"./index-3e6ab209.js";import{E as bt}from"./index-385c3d86.js";import{_ as Rt}from"./_plugin-vue_export-helper-c27b6911.js";const _t=we({urlList:{type:ge(Array),default:()=>ke([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:Boolean,teleported:Boolean,closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7}}),xt={close:()=>!0,switch:s=>pe(s),rotate:s=>pe(s)},wt=["src"],kt=ne({name:"ElImageViewer"}),Dt=ne({...kt,props:_t,emits:xt,setup(s,{expose:y,emit:n}){const b=s,x={CONTAIN:{name:"contain",icon:be(Ne)},ORIGINAL:{name:"original",icon:be(Oe)}},{t:O}=De(),p=Le("image-viewer"),{nextZIndex:H}=Ae(),I=V(),o=V([]),A=Me(),g=V(!0),C=V(b.initialIndex),E=Ie(x.CONTAIN),R=V({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),X=T(()=>{const{urlList:t}=b;return t.length<=1}),B=T(()=>C.value===0),J=T(()=>C.value===b.urlList.length-1),Q=T(()=>b.urlList[C.value]),Z=T(()=>[p.e("btn"),p.e("prev"),p.is("disabled",!b.infinite&&B.value)]),G=T(()=>[p.e("btn"),p.e("next"),p.is("disabled",!b.infinite&&J.value)]),f=T(()=>{const{scale:t,deg:i,offsetX:_,offsetY:z,enableTransition:F}=R.value;let S=_/t,j=z/t;switch(i%360){case 90:case-270:[S,j]=[j,-S];break;case 180:case-180:[S,j]=[-S,-j];break;case 270:case-90:[S,j]=[-j,S];break}const te={transform:`scale(${t}) rotate(${i}deg) translate(${S}px, ${j}px)`,transition:F?"transform .3s":""};return E.value.name===x.CONTAIN.name&&(te.maxWidth=te.maxHeight="100%"),te}),c=T(()=>pe(b.zIndex)?b.zIndex:H());function q(){U(),n("close")}function M(){const t=me(_=>{switch(_.code){case ae.esc:b.closeOnPressEscape&&q();break;case ae.space:w();break;case ae.left:P();break;case ae.up:l("zoomIn");break;case ae.right:K();break;case ae.down:l("zoomOut");break}}),i=me(_=>{const z=_.deltaY||_.deltaX;l(z<0?"zoomIn":"zoomOut",{zoomRate:b.zoomRate,enableTransition:!1})});A.run(()=>{le(document,"keydown",t),le(document,"wheel",i)})}function U(){A.stop()}function r(){g.value=!1}function m(t){g.value=!1,t.target.alt=O("el.image.error")}function d(t){if(g.value||t.button!==0||!I.value)return;R.value.enableTransition=!1;const{offsetX:i,offsetY:_}=R.value,z=t.pageX,F=t.pageY,S=me(te=>{R.value={...R.value,offsetX:i+te.pageX-z,offsetY:_+te.pageY-F}}),j=le(document,"mousemove",S);le(document,"mouseup",()=>{j()}),t.preventDefault()}function v(){R.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function w(){if(g.value)return;const t=Qe(x),i=Object.values(x),_=E.value.name,F=(i.findIndex(S=>S.name===_)+1)%t.length;E.value=x[t[F]],v()}function k(t){const i=b.urlList.length;C.value=(t+i)%i}function P(){B.value&&!b.infinite||k(C.value-1)}function K(){J.value&&!b.infinite||k(C.value+1)}function l(t,i={}){if(g.value)return;const{minScale:_,maxScale:z}=b,{zoomRate:F,rotateDeg:S,enableTransition:j}={zoomRate:b.zoomRate,rotateDeg:90,enableTransition:!0,...i};switch(t){case"zoomOut":R.value.scale>_&&(R.value.scale=Number.parseFloat((R.value.scale/F).toFixed(3)));break;case"zoomIn":R.value.scale<z&&(R.value.scale=Number.parseFloat((R.value.scale*F).toFixed(3)));break;case"clockwise":R.value.deg+=S,n("rotate",R.value.deg);break;case"anticlockwise":R.value.deg-=S,n("rotate",R.value.deg);break}R.value.enableTransition=j}return ve(Q,()=>{Ce(()=>{const t=o.value[0];t!=null&&t.complete||(g.value=!0)})}),ve(C,t=>{v(),n("switch",t)}),Fe(()=>{var t,i;M(),(i=(t=I.value)==null?void 0:t.focus)==null||i.call(t)}),y({setActiveItem:k}),(t,i)=>(L(),ee(Ke,{to:"body",disabled:!t.teleported},[u(Je,{name:"viewer-fade",appear:""},{default:h(()=>[a("div",{ref_key:"wrapper",ref:I,tabindex:-1,class:D(e(p).e("wrapper")),style:he({zIndex:e(c)})},[a("div",{class:D(e(p).e("mask")),onClick:i[0]||(i[0]=Pe(_=>t.hideOnClickModal&&q(),["self"]))},null,2),N(" CLOSE "),a("span",{class:D([e(p).e("btn"),e(p).e("close")]),onClick:q},[u(e(W),null,{default:h(()=>[u(e(je))]),_:1})],2),N(" ARROW "),e(X)?N("v-if",!0):(L(),Y(se,{key:0},[a("span",{class:D(e(Z)),onClick:P},[u(e(W),null,{default:h(()=>[u(e(Ye))]),_:1})],2),a("span",{class:D(e(G)),onClick:K},[u(e(W),null,{default:h(()=>[u(e(Be))]),_:1})],2)],64)),N(" ACTIONS "),a("div",{class:D([e(p).e("btn"),e(p).e("actions")])},[a("div",{class:D(e(p).e("actions__inner"))},[u(e(W),{onClick:i[1]||(i[1]=_=>l("zoomOut"))},{default:h(()=>[u(e(qe))]),_:1}),u(e(W),{onClick:i[2]||(i[2]=_=>l("zoomIn"))},{default:h(()=>[u(e(Ue))]),_:1}),a("i",{class:D(e(p).e("actions__divider"))},null,2),u(e(W),{onClick:w},{default:h(()=>[(L(),ee(He(e(E).icon)))]),_:1}),a("i",{class:D(e(p).e("actions__divider"))},null,2),u(e(W),{onClick:i[3]||(i[3]=_=>l("anticlockwise"))},{default:h(()=>[u(e(Ge))]),_:1}),u(e(W),{onClick:i[4]||(i[4]=_=>l("clockwise"))},{default:h(()=>[u(e(Xe))]),_:1})],2)],2),N(" CANVAS "),a("div",{class:D(e(p).e("canvas"))},[(L(!0),Y(se,null,$e(t.urlList,(_,z)=>Ee((L(),Y("img",{ref_for:!0,ref:F=>o.value[z]=F,key:_,src:_,style:he(e(f)),class:D(e(p).e("img")),onLoad:r,onError:m,onMousedown:d},null,46,wt)),[[Se,z===C.value]])),128))],2),ce(t.$slots,"default")],6)]),_:3})],8,["disabled"]))}});var Lt=Ve(Dt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image-viewer/src/image-viewer.vue"]]);const It=Te(Lt),Ct=we({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:ge([String,Object])},previewSrcList:{type:ge(Array),default:()=>ke([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7}}),Ft={load:s=>s instanceof Event,error:s=>s instanceof Event,switch:s=>pe(s),close:()=>!0,show:()=>!0},$t=["src","loading"],Et={key:0},St=ne({name:"ElImage",inheritAttrs:!1}),Vt=ne({...St,props:Ct,emits:Ft,setup(s,{emit:y}){const n=s;let b="";const{t:x}=De(),O=Le("image"),p=We(),H=Ze(),I=V(),o=V(!1),A=V(!0),g=V(!1),C=V(),E=V(),R=oe&&"loading"in HTMLImageElement.prototype;let X,B;const J=T(()=>[O.e("inner"),G.value&&O.e("preview"),A.value&&O.is("loading")]),Q=T(()=>p.style),Z=T(()=>{const{fit:l}=n;return oe&&l?{objectFit:l}:{}}),G=T(()=>{const{previewSrcList:l}=n;return Array.isArray(l)&&l.length>0}),f=T(()=>{const{previewSrcList:l,initialIndex:t}=n;let i=t;return t>l.length-1&&(i=0),i}),c=T(()=>n.loading==="eager"?!1:!R&&n.loading==="lazy"||n.lazy),q=()=>{oe&&(A.value=!0,o.value=!1,I.value=n.src)};function M(l){A.value=!1,o.value=!1,y("load",l)}function U(l){A.value=!1,o.value=!0,y("error",l)}function r(){ht(C.value,E.value)&&(q(),v())}const m=ot(r,200,!0);async function d(){var l;if(!oe)return;await Ce();const{scrollContainer:t}=n;tt(t)?E.value=t:at(t)&&t!==""?E.value=(l=document.querySelector(t))!=null?l:void 0:C.value&&(E.value=lt(C.value)),E.value&&(X=le(E,"scroll",m),setTimeout(()=>r(),100))}function v(){!oe||!E.value||!m||(X==null||X(),E.value=void 0)}function w(l){if(l.ctrlKey){if(l.deltaY<0)return l.preventDefault(),!1;if(l.deltaY>0)return l.preventDefault(),!1}}function k(){G.value&&(B=le("wheel",w,{passive:!1}),b=document.body.style.overflow,document.body.style.overflow="hidden",g.value=!0,y("show"))}function P(){B==null||B(),document.body.style.overflow=b,g.value=!1,y("close")}function K(l){y("switch",l)}return ve(()=>n.src,()=>{c.value?(A.value=!0,o.value=!1,v(),d()):q()}),Fe(()=>{c.value?d():q()}),(l,t)=>(L(),Y("div",{ref_key:"container",ref:C,class:D([e(O).b(),l.$attrs.class]),style:he(e(Q))},[o.value?ce(l.$slots,"error",{key:0},()=>[a("div",{class:D(e(O).e("error"))},$(e(x)("el.image.error")),3)]):(L(),Y(se,{key:1},[I.value!==void 0?(L(),Y("img",et({key:0},e(H),{src:I.value,loading:l.loading,style:e(Z),class:e(J),onClick:k,onLoad:M,onError:U}),null,16,$t)):N("v-if",!0),A.value?(L(),Y("div",{key:1,class:D(e(O).e("wrapper"))},[ce(l.$slots,"placeholder",{},()=>[a("div",{class:D(e(O).e("placeholder"))},null,2)])],2)):N("v-if",!0)],64)),e(G)?(L(),Y(se,{key:2},[g.value?(L(),ee(e(It),{key:0,"z-index":l.zIndex,"initial-index":e(f),infinite:l.infinite,"zoom-rate":l.zoomRate,"min-scale":l.minScale,"max-scale":l.maxScale,"url-list":l.previewSrcList,"hide-on-click-modal":l.hideOnClickModal,teleported:l.previewTeleported,"close-on-press-escape":l.closeOnPressEscape,onClose:P,onSwitch:K},{default:h(()=>[l.$slots.viewer?(L(),Y("div",Et,[ce(l.$slots,"viewer")])):N("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","zoom-rate","min-scale","max-scale","url-list","hide-on-click-modal","teleported","close-on-press-escape"])):N("v-if",!0)],64)):N("v-if",!0)],6))}});var Tt=Ve(Vt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image/src/image.vue"]]);const zt=Te(Tt),Nt=(s,y)=>ye.post(`${re}/tosp/faultreport/upload`,s,{headers:{"Content-Type":"multipart/form-data",gid:y}}),Ot=(s,y)=>ye.post(`${re}/tosp/faultreport/newFault`,s,{headers:{gid:y}}),Fa=(s,y)=>ze(`${re}/tosp/faultreport/queryFaultReport`,{headers:{gid:y}}).post(s).json(),$a=(s,y)=>ze(`${re}/tosp/faultreport/updatestatus`,{headers:{gid:y}}).post(s).json(),Ea=(s,y)=>ye.post(`${re}/tosp/faultreport/feedback`,s,{headers:{gid:y}}),At=(s,y)=>{var U;const{t:n}=st(),b=nt(),x=V(),O=V(),p=V(""),H=V(!1),I=rt("bugTipsObject",""),o=T(()=>b.state.user),A=()=>o.value.securityLevel===1?o.value.mobile:o.value.securityLevel===4?o.value.email:o.value.mobile,g=V({reportTelphone:((U=o.value)==null?void 0:U.mobile)??"",remark:"",imageListVo:[],faultTime:"",exception:"",description:"",transactionCode:"",reportReason:"",gid:""}),C=V({reportTelphone:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],remark:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:250,message:n("app.faultReport.faultReportingDialog.maximum250Characters"),trigger:"blur"}],imageListVo:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],faultTime:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"}],exception:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:n("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],description:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:n("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],transactionCode:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"},{max:50,message:n("app.faultReport.faultReportingDialog.maximumCharacters"),trigger:"blur"}],reportReason:[{required:!0,message:n("app.faultReport.faultReportingDialog.required"),trigger:"blur"}]}),E=Ie({render(){return it("em",{class:"iconfont icon-calendar"})}}),R=r=>{var k,P;let m=!1,d=!1;const v=["PNG","JPG","JPEG","BMP","IMAGE/JPEG","IMAGE/BMP"],w=((P=(k=r.raw)==null?void 0:k.type)==null?void 0:P.toUpperCase())??"";return v.forEach(K=>{w.includes(K)&&(m=!0)}),r!=null&&r.size&&r.size<=10*1024*1024&&r.size>0&&(d=!0),m&&d},X=(r,m)=>{g.value.imageListVo=m,R(r)?x.value.validateField("imageListVo"):(fe.error(n("app.faultReport.faultReportingDialog.uploadFailed")),B(r.uid))},B=r=>{var d,v;const m=(d=g.value.imageListVo)==null?void 0:d.findIndex(w=>w.uid===r);(v=g.value.imageListVo)==null||v.splice(m,1),x.value.validateField("imageListVo")},J=r=>r.filter(d=>d.success).map(d=>({imageName:d.fileName,imageNewName:d.fileUrl,imageSize:d.fileSize})),Q=()=>{x.value.validate(async r=>{if(!r)return;const m=new FormData;g.value.imageListVo.forEach(w=>{m.append("files",w.raw)});const d=Re("081L0132"),v=await Nt(m,d);if(v.code==="200")if(v.data.every(k=>k.success===!1))await _e("",n("app.faultReport.faultReportingDialog.ImageUploadFailed"),"",v.time);else{const k={...g.value,imageListVo:J(v.data),faultTimeCn:xe().format("YYYY-MM-DD HH:mm:ss"),macAddress:p.value??""},P=Re("081L0124");await Ot(k,P),x.value.resetFields(),x.value.faultTime="",x.value.imageListVo=[],x.value.remark="",x.value.transactionCode="",x.value.exception="",x.value.description="",y("updateFormFaultReportingForm",{...g.value,faultTime:""}),I.value="",y("update:modelValue",!1),y("handleQuery"),fe.success(n("app.faultReport.faultReportingDialog.reportedSuccessfully"))}else await _e("",n("app.faultReport.faultReportingDialog.ImageUploadFailed"),"",v.time)})},Z=r=>{var w;let m=!1;const d=["PNG","JPG","JPEG","BMP","IMAGE/JPEG","IMAGE/BMP"],v=((w=r.type)==null?void 0:w.toUpperCase())??"";return d.forEach(k=>{v.includes(k)&&(m=!0)}),m},G=r=>{if(!H.value)return;const d=r.clipboardData.items,v=d==null?void 0:d[0],w=v==null?void 0:v.getAsFile();if(w&&v.kind==="file"&&Z(v)&&g.value.imageListVo.length<3&&w.size<10*1024*1024){const k={raw:w,url:URL.createObjectURL(w)};g.value.imageListVo.push(k),x.value.validateField("imageListVo")}else fe.error(n("app.faultReport.faultReportingDialog.uploadFailed"))},f=()=>{H.value=!1,I.value="",y("update:modelValue",!1),y("updateFormFaultReportingForm",g.value)},c=async()=>{var r;if(p.value=(r=await M())==null?void 0:r.toUpperCase(),s.faultReportingDialogForm){const m={...s.faultReportingDialogForm,faultTime:s.faultReportingDialogForm.faultTime?s.faultReportingDialogForm.faultTime:xe().format("YYYY-MM-DD HH:mm:ss")};g.value=m}b.dispatch("setFullLoading",!1)},q=()=>{H.value=!0},M=async()=>{var d;const r=(d=navigator==null?void 0:navigator.userAgent)==null?void 0:d.toLowerCase();let m="";return r!=null&&r.includes("electron/")&&(m=await window.electronAPI.getAuthInfo()),m&&ut.decode(m)};return{faultReportingRef:x,isClickFaultReportingDom:H,uploadRef:O,FORM_RULES:C,userInfo:o,datePrefix:E,faultReportingForm:g,bugTipsObjectSession:I,macAddress:p,uploadChange:X,handDelImag:B,handleSubmit:Q,handlePaste:G,handleClose:f,handleFaultReporting:q,handleOpen:c,contactInformation:A}},Mt=At,de=s=>(dt("data-v-9f9cc65e"),s=s(),ft(),s),Pt={class:"justify-start text-gray-1 text-lg font-bold leading-normal"},jt={class:"w-full self-stretch px-2.5 py-1 bg-gray-8 rounded inline-flex flex-col justify-center items-start"},Yt={class:"self-stretch inline-flex justify-start items-center gap-2.5"},Bt={class:"justify-center"},qt={class:"text-gray-4 text-xs font-normal leading-tight"},Ut={class:"text-gray-1 text-xs font-normal leading-tight"},Ht={class:"justify-center"},Gt=de(()=>a("span",{class:"text-gray-4 text-xs font-normal leading-tight"},"OFFICE：",-1)),Xt={class:"text-gray-1 text-xs font-normal leading-tight"},Jt={class:"justify-center flex-wrap flex items-center"},Kt={class:"text-gray-4 text-xs font-normal leading-tight"},Qt={class:"text-gray-1 text-xs font-normal leading-tight truncate w-[78px]"},Wt={class:"justify-center flex-wrap flex items-center"},Zt={class:"text-gray-4 text-xs font-normal leading-tight"},ea={class:"w-[120px] text-gray-1 text-xs font-normal leading-tight max-w-[120px] inline-block align-middle truncate"},ta={class:"self-stretch inline-flex justify-start items-center gap-3.5"},aa={class:"justify-center flex-wrap flex items-center"},la={class:"text-gray-4 text-xs font-normal leading-tight"},oa={class:"text-gray-1 text-xs font-normal leading-tight w-[100px] inline-block align-middle truncate"},sa={class:"justify-center flex-wrap flex items-center"},na={class:"text-gray-4 text-xs font-normal leading-tight"},ra={class:"text-gray-1 text-xs font-normal leading-tight w-[370px] inline-block align-middle truncate"},ia={class:"form-inline"},ua={class:"upload-input-box"},ca={key:0,class:"self-stretch leading-4 justify-start text-gray-5 text-xs font-normal mb-[6px]"},pa=de(()=>a("br",null,null,-1)),da={class:"flex justify-start items-start flex-wrap"},fa=["onClick"],ma=de(()=>a("i",{class:"iconfont icon-delete text-brand-2"},null,-1)),ga=[ma],va=de(()=>a("div",null,[a("div",{class:"w-5 h-5 border-2 border-gray-6 border-solid rounded-sm text-gray-5 flex justify-center items-center"},[a("span",null,"+")])],-1)),ha={class:"w-64 ml-[68px] mb-[10px] justify-start text-red-1 text-xs font-normal leading-tight"},ya={class:"dialog-footer"},ba=ne({__name:"FaultReportingDialog",props:{faultReportingDialogForm:{}},emits:["update: modelValue","updateFormFaultReportingForm","handleQuery"],setup(s,{emit:y}){const n=s,b=y,{faultReportingRef:x,uploadRef:O,isClickFaultReportingDom:p,FORM_RULES:H,userInfo:I,faultReportingForm:o,datePrefix:A,bugTipsObjectSession:g,macAddress:C,uploadChange:E,handDelImag:R,handleSubmit:X,handlePaste:B,handleClose:J,handleFaultReporting:Q,handleOpen:Z,contactInformation:G}=Mt(n,b);return(f,c)=>{const q=mt,M=gt,U=ct,r=zt,m=yt,d=vt,v=pt,w=bt;return L(),ee(w,{width:"680px","custom-class":"fault-reporting-dialog","close-on-click-modal":!1,tabindex:"1",onClose:e(J),onOpen:e(Z),onPaste:e(B)},{header:h(()=>[a("div",Pt,$(f.$t("app.faultReport.faultReportingDialog.faultReporting")),1)]),footer:h(()=>[a("span",ya,[u(v,{type:"primary",class:"w-[80px]",onClick:e(X)},{default:h(()=>[ie($(f.$t("app.faultReport.faultReportingDialog.submit")),1)]),_:1},8,["onClick"]),u(v,{class:"w-[80px]",onClick:e(J)},{default:h(()=>[ie($(f.$t("app.personal.cancelConfigure")),1)]),_:1},8,["onClick"])])]),default:h(()=>{var k,P,K,l;return[a("div",null,[a("div",jt,[a("div",Yt,[a("div",Bt,[a("span",qt,$(f.$t("app.faultReport.faultReportingDialog.jobID"))+"：",1),a("span",Ut,$(((k=e(I))==null?void 0:k.agent)||"-"),1)]),a("div",Ht,[Gt,a("span",Xt,$(((P=e(I))==null?void 0:P.defaultOffice)||"-"),1)]),a("div",Jt,[a("span",Kt,$(f.$t("app.faultReport.faultReportingDialog.workContact"))+"：",1),a("div",Qt,[u(ue,{"show-val":e(G)()},{default:h(()=>[a("span",null,$(e(G)()||"-"),1)]),_:1},8,["show-val"])])]),a("div",Wt,[a("span",Zt,$(f.$t("app.faultReport.faultReportingDialog.MACAddress"))+"：",1),a("div",ea,[u(ue,{"show-val":e(C)},{default:h(()=>[a("span",null,$(e(C)||"-"),1)]),_:1},8,["show-val"])])])]),a("div",ta,[a("div",aa,[a("span",la,$(f.$t("app.faultReport.faultReportingDialog.nameReporter"))+"：",1),a("div",oa,[u(ue,{"show-val":(K=e(I))==null?void 0:K.employeeName},{default:h(()=>{var t;return[a("span",null,$(((t=e(I))==null?void 0:t.employeeName)||"-"),1)]}),_:1},8,["show-val"])])]),a("div",sa,[a("span",na,$(f.$t("app.faultReport.faultReportingDialog.reportingUnit"))+"：",1),a("div",ra,[u(ue,{"show-val":(l=e(I))==null?void 0:l.departmentName},{default:h(()=>{var t;return[a("span",null,$(((t=e(I))==null?void 0:t.departmentName)||"-"),1)]}),_:1},8,["show-val"])])])])]),u(d,{ref_key:"faultReportingRef",ref:x,model:e(o),"label-width":"70px","require-asterisk-position":"right",rules:e(H),inline:"",class:"fault-reporting-form crs-new-ui-init-cls"},{default:h(()=>[a("div",ia,[e(g)?N("",!0):(L(),ee(M,{key:0,label:f.$t("app.faultReport.faultReportingDialog.mtbf"),class:"fault-time",prop:"faultTime"},{default:h(()=>[u(q,{modelValue:e(o).faultTime,"onUpdate:modelValue":c[0]||(c[0]=t=>e(o).faultTime=t),type:"datetime","prefix-icon":e(A),clearable:!1,placeholder:f.$t("app.faultReport.faultReportingDialog.enterDate"),format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",onFocus:c[1]||(c[1]=t=>p.value=!1)},null,8,["modelValue","prefix-icon","placeholder"])]),_:1},8,["label"])),u(M,{label:f.$t("app.faultReport.faultReportingDialog.contactInformation"),prop:"reportTelphone",class:D([e(g)?"report-telphone":""])},{default:h(()=>[u(U,{modelValue:e(o).reportTelphone,"onUpdate:modelValue":c[2]||(c[2]=t=>e(o).reportTelphone=t),placeholder:f.$t("app.faultReport.faultReportingDialog.pleaseContact"),onFocus:c[3]||(c[3]=t=>p.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label","class"]),a("div",ua,[u(M,{label:f.$t("app.faultReport.faultReportingDialog.faultScreenshot"),class:"textarea-input upload-input h-[238px]",prop:"imageListVo"},{default:h(()=>{var t,i,_,z;return[a("div",{class:D(["image-box w-full self-stretch pl-[6px] pt-[6px] pb-0 bg-gray-0 rounded-sm outline outline-1 outline-offset-[-1px] outline-gray-6 gap-1.5 overflow-hidden",(t=e(o).imageListVo)!=null&&t.length?"":"pr-[6px]"]),onClick:c[5]||(c[5]=(...F)=>e(Q)&&e(Q)(...F))},[(i=e(o).imageListVo)!=null&&i.length?N("",!0):(L(),Y("div",ca,[ie($(f.$t("app.faultReport.faultReportingDialog.faultScreenshottipOne")),1),pa,ie($(f.$t("app.faultReport.faultReportingDialog.faultScreenshottipTwo")),1)])),a("div",da,[(L(!0),Y(se,null,$e(e(o).imageListVo,(F,S)=>(L(),Y("div",{key:S,class:D(["border border-gray-6  border-solid mb-[6px] mr-[6px] rounded-sm w-[116px] h-[100px] bg-gray-0 relative image-item",S!==e(o).imageListVo.length-1?"mr-[6px]":""])},[u(r,{class:"w-full h-full",src:F.url,"zoom-rate":1.2,"max-scale":7,"min-scale":.2,"preview-src-list":[F.url],"initial-index":4,fit:"cover"},null,8,["src","preview-src-list"]),a("div",{class:"absolute bottom-0 bg-gray-8 w-full h-[20px] flex justify-center items-center cursor-pointer del-icon",onClick:j=>e(R)(F.uid)},ga,8,fa)],2))),128)),Ee(u(m,{ref_key:"uploadRef",ref:O,"file-list":e(o).imageListVo,"onUpdate:fileList":c[4]||(c[4]=F=>e(o).imageListVo=F),class:D(["add-upload-btn"]),"auto-upload":!1,limit:5,"list-type":"picture-card","show-file-list":!1,"on-change":e(E)},{trigger:h(()=>[va]),_:1},8,["file-list","on-change"]),[[Se,((z=(_=e(o))==null?void 0:_.imageListVo)==null?void 0:z.length)<3]])])],2)]}),_:1},8,["label"]),a("div",ha,$(f.$t("app.faultReport.faultReportingDialog.screenshotsOnce")),1)]),a("div",null,[u(M,{label:f.$t("app.faultReport.faultReportingDialog.faultPhenomenon"),class:"textarea-input",prop:"remark"},{default:h(()=>[u(U,{modelValue:e(o).remark,"onUpdate:modelValue":c[6]||(c[6]=t=>e(o).remark=t),type:"textarea",placeholder:f.$t("app.faultReport.faultReportingDialog.faultPhenomenonTip"),onFocus:c[7]||(c[7]=t=>p.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),e(g)?N("",!0):(L(),ee(M,{key:1,label:f.$t("app.faultReport.faultReportingDialog.errorCode"),prop:"exception"},{default:h(()=>[u(U,{modelValue:e(o).exception,"onUpdate:modelValue":c[8]||(c[8]=t=>e(o).exception=t),placeholder:`${f.$t("app.faultReport.faultReportingDialog.example")}: OM-01-33R09`,onFocus:c[9]||(c[9]=t=>p.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"])),e(g)?N("",!0):(L(),ee(M,{key:2,label:f.$t("app.faultReport.faultReportingDialog.transactionNumber"),prop:"transactionCode"},{default:h(()=>[u(U,{modelValue:e(o).transactionCode,"onUpdate:modelValue":c[10]||(c[10]=t=>e(o).transactionCode=t),placeholder:`${f.$t("app.faultReport.faultReportingDialog.example")}: XXXSAT41862025041319073900733488`,onFocus:c[11]||(c[11]=t=>p.value=!1)},null,8,["modelValue","placeholder"])]),_:1},8,["label"]))])]),_:1},8,["model","rules"])])]}),_:1},8,["onClose","onOpen","onPaste"])}}});const Sa=Rt(ba,[["__scopeId","data-v-9f9cc65e"]]);export{zt as E,Sa as F,Fa as q,Ea as s,$a as u};
