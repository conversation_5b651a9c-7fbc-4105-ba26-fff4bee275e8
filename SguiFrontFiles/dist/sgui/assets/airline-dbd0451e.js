import{c3 as i,c4 as s,ao as c,dP as p}from"./index-18f146fc.js";import{d,s as h}from"./browser-6cfa1fde.js";const A=t=>i.get(`${s}/airportSelector/airports`,{headers:{gid:t}}),y=t=>i.get(`${s}/airportSelector/airportAll`,{headers:{gid:t}}),l=t=>i.get(`${s}/quickInstruction/searchTip`,{headers:{gid:t}}),C=t=>c(`${p}/commonConfiguration/hotCities`,{headers:{gid:t}}).get().json(),f=t=>c(`${s}/city/commonCities `,{headers:{gid:t}}).get().json(),m=t=>{const o="=".repeat((4-t.length%4)%4),a=(t+o).replace(/-/g,"+").replace(/_/g,"/"),e=window.atob(a),n=new Uint8Array(e.length);for(let r=0;r<e.length;++r)n[r]=e.charCodeAt(r);return n},S=t=>{const o=m(t),a=d(o),e=h(a);return JSON.stringify(JSON.parse(e))};export{l as a,f as b,S as d,A as i,C as q,y as s};
