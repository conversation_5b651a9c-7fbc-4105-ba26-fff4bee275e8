import{c as K}from"./_createMathOperation-2069a064.js";import{q as M,r as c,w as R,ac as j,o as H,x as r,B as E,G as h,z as n,y as d,J,ai as L,aj as Q,ak as B,Q as V,P as k,H as T,ah as W,al as X,am as Y,an as Z}from"./index-18f146fc.js";import{E as q,a as ee}from"./index-9b639e2a.js";import{E as le}from"./index-1c4b8a79.js";import{b as oe}from"./index-a197ff1b.js";import{_ as te}from"./_plugin-vue_export-helper-c27b6911.js";var ae=K(function(s,y){return s-y},0);const be=ae,D=s=>(Y("data-v-b25f494f"),s=s(),Z(),s),ne={class:"sales-daily-filter"},se=D(()=>k("em",{class:"iconfont icon-down"},null,-1)),re=D(()=>k("em",{class:"iconfont icon-up"},null,-1)),ue={class:"filter-bottom-sales crs-btn-ui"},ie={name:"FilterSelect"},pe=M({...ie,props:{filters:{},columnKey:{},showFilterInput:{type:Boolean,default:!1},filterInputPlaceholder:{default:""},notDisabled:{type:Boolean}},emits:["handleConfrim"],setup(s,{expose:y,emit:F}){const I=s,S=F,m=c(),i=c(!1),f=R(()=>I.filters),v=c([]),u=c([]),t=c(""),O=e=>{i.value=e},C=e=>{i.value=e,N(e)},$=()=>i.value,N=e=>{e?m.value.handleOpen():m.value.handleClose()},w=()=>{v.value=f.value},g=()=>{i.value=!1,m.value.handleClose(),S("handleConfrim",I.columnKey,u.value,t.value)};j(()=>f.value,()=>{w()},{deep:!0});const x=e=>{if(e.includes(",")){const _=e.split(",").filter(a=>a!==""&&a!=null).map(a=>f.value.filter(p=>p.indexOf(a)!==-1)).reduce((a,p)=>a.concat(p),[]);v.value=Array.from(new Set(_))}else v.value=f.value.filter(l=>l.indexOf(e)!==-1)};H(()=>{w()});const P=()=>{u.value=[],t.value="",w(),g()};return y({showDropDown:C,getVisibleValue:$}),(e,l)=>{const _=T,a=W,p=q,U=le,z=ee,A=X,G=oe;return r(),E("div",ne,[h(G,{ref_key:"dropdown",ref:m,trigger:"contextmenu",placement:"bottom","popper-class":"sale-daily-filter-popper",onVisibleChange:O},{dropdown:n(()=>{var b;return[e.notDisabled&&e.showFilterInput?(r(),d(a,{key:0,modelValue:t.value,"onUpdate:modelValue":l[2]||(l[2]=o=>t.value=o),class:"filter-input",placeholder:e.filterInputPlaceholder,onInput:l[3]||(l[3]=o=>{t.value=t.value.toUpperCase(),x(t.value)})},null,8,["modelValue","placeholder"])):e.showFilterInput?(r(),d(a,{key:1,modelValue:t.value,"onUpdate:modelValue":l[4]||(l[4]=o=>t.value=o),disabled:u.value.length>0,class:"filter-input",placeholder:e.filterInputPlaceholder,onInput:l[5]||(l[5]=o=>x(t.value))},null,8,["modelValue","disabled","placeholder"])):J("",!0),h(z,{modelValue:u.value,"onUpdate:modelValue":l[6]||(l[6]=o=>u.value=o)},{default:n(()=>[h(U,{"max-height":"300px",height:"fitcontent","min-size":"20"},{default:n(()=>[(r(!0),E(L,null,Q(v.value,o=>(r(),d(p,{key:o,label:o,class:"scrollbar-demo-item"},{default:n(()=>[B(V(o),1)]),_:2},1032,["label"]))),128))]),_:1})]),_:1},8,["modelValue"]),k("div",ue,[h(A,{disabled:((b=u.value)==null?void 0:b.length)===0,link:"",type:"primary",size:"small",onClick:g},{default:n(()=>[B(V(e.$t("app.agentReport.confirmBtn")),1)]),_:1},8,["disabled"]),k("div",{onClick:P},V(e.$t("app.agentReport.reset")),1)])]}),default:n(()=>[i.value?(r(),d(_,{key:1,color:"var(--bkc-el-color-primary)",onClick:l[1]||(l[1]=b=>C(!1))},{default:n(()=>[re]),_:1})):(r(),d(_,{key:0,color:"var(--bkc-el-color-primary)",onClick:l[0]||(l[0]=b=>C(!0))},{default:n(()=>[se]),_:1}))]),_:1},512)])}}});const he=te(pe,[["__scopeId","data-v-b25f494f"]]);export{he as F,be as s};
