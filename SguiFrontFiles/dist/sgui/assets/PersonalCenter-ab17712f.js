import{bA as ie,b6 as ue,ab as oe,r as M,a9 as pe,w as j,d4 as Qe,d5 as ke,ax as O,bQ as me,bF as Ae,bJ as ge,bK as he,d6 as Ge,q as ce,ah as q,al as se,H as Pe,aK as We,aH as Ye,d7 as $e,d8 as ze,a4 as ve,x as h,B as w,P as a,a5 as L,a6 as Ue,G as s,z as d,J as N,ai as Y,aj as le,D,Q as r,C as Ke,ak as G,y as x,am as De,an as Re,a8 as xe,bm as Be,d9 as Je,da as He,db as Ze,dc as et,o as Ce,dd as we,ac as be,aJ as re,au as tt,de as at,b2 as Le,bG as st,bM as lt,R as nt,df as Te,bP as Ee,aa as ye,bO as ot,bV as it,b5 as Oe,A as e,b9 as K,dg as rt,dh as Ne,di as ut,dj as Ve,av as Ie,ch as pt,dk as ct,ae as Se,b7 as dt,b3 as ft,bf as mt}from"./index-18f146fc.js";import{U as gt}from"./passwordValid-45ecf3f8.js";import{g as fe}from"./encrypt-4212e77b.js";import{u as ht}from"./usePersonalization-956f86ae.js";import{a as ne,E as Q}from"./index-c5503643.js";import{_ as vt}from"./Personalization.vue_vue_type_script_setup_true_lang-e733350b.js";import{_ as de}from"./_plugin-vue_export-helper-c27b6911.js";import{i as bt,d as yt,b as Ct}from"./airline-dbd0451e.js";import{b as _t}from"./common-9b69fd00.js";import{E as v}from"./index-cb25ab55.js";import{E as U}from"./index-93952dc4.js";import{P as Fe}from"./PrintNoSelect-c8861788.js";import{E as kt}from"./index-2610c77e.js";import{E as $t}from"./index-9b639e2a.js";import{E as wt}from"./index-2494e7da.js";import{E as Tt,a as Et}from"./index-20269f93.js";import{a as Me,E as Xe}from"./index-b0de6559.js";import"./castArray-f685dae0.js";import"./browser-6cfa1fde.js";import"./ticketOperationApi-fe1536da.js";import"./index-d7d71e18.js";import"./index-6ea30548.js";import"./isUndefined-aa0326a0.js";import"./index-1c4b8a79.js";import"./strings-8b86a061.js";import"./isEqual-9c56e106.js";import"./index-c5921abf.js";import"./flatten-1142070a.js";import"./index-0fa663e2.js";function Nt(){const n=document.querySelector("#msg-two-loading")?"#msg-two-loading":"#msg-loading";ie.service({target:"#aside-loading",spinner:!0,customClass:"customLoading"}),ie.service({target:"#tabs-loading",spinner:!0,customClass:"customLoading"}),ie.service({target:n,spinner:!0,customClass:"customLoading"}),ie.service({target:"#server-loading",spinner:!0,customClass:"customLoading"})}const J=ue({oldcertificate:"",certificate:"",recertificate:""}),Vt=()=>({securityTitle:()=>{const _=ue({btn:["简体中文","繁體中文","English"],select:O()==="en"?2:0,language:[0,1,2]});return{title:_,clickActive:l=>{_.select=l}}},securityMessage:()=>{const _=ue({securityInfo:!0,bgColor:"#EBF2FF"});return{showOrClose:_,iconClose:()=>{_.bgColor="#ffffff",_.securityInfo=!1}}}}),It=()=>{const{t:n}=oe(),f=M([]),_=[n("app.personal.oauthTokenRule1"),n("app.personal.oauthTokenRule2"),n("app.personal.oauthTokenRule3"),n("app.personal.oauthTokenRule4"),n("app.personal.oauthTokenRule5")],{validLength:E,validVaried:l,validContinuity:V,validKeyboard:c,validRepeat:T}=gt(),k=$=>{const o=[];return E($)&&o.push(_[0]),l($)&&o.push(_[1]),V($)&&o.push(_[2]),c($)&&o.push(_[3]),T($)&&o.push(_[4]),o},p=($,o,u)=>{const{certificate:g,oldcertificate:b}=J;f.value=k(J.certificate),f.value.length!==0?u(new Error("  ")):g===b&&u(n("app.personal.confirmPasswordOldTip")),u()};return{formInline:J,rules:{oldcertificate:[{required:!0,message:" ",trigger:"blur"}],certificate:[{required:!0,validator:p,trigger:"blur"},{required:!0,validator:p,trigger:"change"}],recertificate:[{required:!0,message:" ",trigger:"blur"},{required:!0,validator:($,o,u)=>{const{certificate:g}=J,{recertificate:b}=J;g===""?u(new Error(" ")):g!==b&&u(n("app.personal.confirmPasswordTip")),u()},trigger:"change"}]},validList:f,validArray:_}},St=()=>{const{t:n}=oe(),f=pe(),_=j(()=>f.state.user.userName),E=M(),l=async T=>{try{await me(T),await Ae("09300123")}finally{const k=ge();localStorage.clear(),he(k)}},V=async(T,k)=>{try{await ke(T,k);const p=await Ge("09300122");if([n("app.tips.networkErrCode")].includes((p==null?void 0:p.code)??""))await V(T,k);else{const S=ge();localStorage.clear(),he(S)}}finally{}};return{userName:_,formRef:E,onSubmit:()=>{E.value.validate(async T=>{if(T){const{oldcertificate:k,certificate:p,recertificate:I}=J,S={oldcertificate:fe(k),certificate:fe(p),recertificate:fe(I)},$=await Qe(S,"09300132"),o=["SGUI-0142-10","SGUI-0143-11","ARCHETYPE-0142-15","ARCHETYPE-0143-16","SGUI-0150-19","SGUI-07-83W11","SGUI-07-83R11"],u=["SGUI-0141-19","ARCHETYPE-0141-14","SGUI-07-83W11"],g=[n("app.tips.networkErrCode")];$.code==="200"?await l(n("app.tips.oauthTokenUpdatedSuccessfully")):o.includes($.code)?await l(n("app.tips.notificationSendingFailure")):u.includes($.code)?await l(n("app.tips.notificationConfigurationIncorrect")):g.includes($.code)?await V(n("app.tips.networkErrCode"),n("app.tips.notificationNetworkErr")):ke($.code,$.msg,$.transactionID,null,null,$.satTransactionID,$.slnDesc)}})}}},Ft=ce({name:"UpdatePassword",components:{ElForm:ne,ElFormItem:Q,ElInput:q,ElButton:se,ElIcon:Pe,Close:We,Personalization:vt},setup(){const n=M(!1),{securityTitle:f,securityMessage:_}=Vt(),{title:E,clickActive:l}=f(),{showOrClose:V,iconClose:c}=_(),{formInline:T,rules:k,validArray:p,validList:I}=It(),{userName:S,formRef:$,onSubmit:o}=St(),{personalizationRules:u}=ht(ze.personalCenter);return Ye(()=>{n.value=T.certificate.length===0&&I.value.length===0}),{showDefault:n,...$e(E),...$e(V),iconClose:c,clickActive:l,validArray:p,formInline:T,rules:k,validList:I,userName:S,formRef:$,onSubmit:o,getLocale:O,personalizationRules:u}}});const W=n=>(De("data-v-abfff5fe"),n=n(),Re(),n),At={key:0,class:"update-password"},Pt={key:0,class:"security-title"},Ut={key:1,class:"security-title"},Dt={key:2,class:"security-title"},Rt=["onClick"],xt={class:"security-content"},Bt={key:0},Lt=W(()=>a("span",null,"由于近期撞库攻击日益猖獗，敬请各位用户在本系统中",-1)),Ot=W(()=>a("span",{style:{color:"red"}},"不要使用与其他网站相同或相近的用户名口令",-1)),Mt=W(()=>a("span",null,"。如因您的故意或疏忽过失，导致您在本系统的用户名口令泄露，对业务造成的影响由您自行承担，对本系统带来的安全危害，",-1)),Xt=W(()=>a("span",null,"系统所有权人中国民航信息网络股份有限公司将保留进一步追究法律责任的权利。——中国民航信息网络股份有限公司",-1)),jt=[Lt,Ot,Mt,Xt],qt={key:1},Qt=W(()=>a("span",null,"由於近期撞庫攻擊日益猖獗，敬請各位用戶在本系統中",-1)),Gt=W(()=>a("span",{style:{color:"red"}},"不要使用與其他網站相同或相近的用戶名口令",-1)),Wt=W(()=>a("span",null,"。如因您的故意或疏忽過失，導致您在本系統的用戶名口令泄露，對業務造成的影響由您自行承擔，對",-1)),Yt=W(()=>a("span",null,"本系統帶來的安全危害，系統所有權人中國民航信息網絡股份有限公司將保留進壹步追究法律責任的權利。——中國民航信息網絡股份有限公司",-1)),zt=[Qt,Gt,Wt,Yt],Kt={key:2},Jt={class:"message-ul"},Ht=W(()=>a("span",null,null,-1));function Zt(n,f,_,E,l,V){const c=ve("close"),T=Pe,k=q,p=Q,I=se,S=ne,$=ve("Personalization");return h(),w("div",null,[n.personalizationRules?(h(),x($,{key:1,"rule-info":n.personalizationRules,"user-name":n.userName},null,8,["rule-info","user-name"])):(h(),w("div",At,[a("div",{class:"security-info",style:Ke({"background-color":n.bgColor})},[L(a("div",null,[s(T,{class:"security-close",onClick:n.iconClose},{default:d(()=>[s(c)]),_:1},8,["onClick"]),a("p",null,[n.language[0]===n.select?(h(),w("span",Pt,"安全声明")):N("",!0),n.language[1]===n.select?(h(),w("span",Ut,"安全聲明")):N("",!0),n.language[2]===n.select?(h(),w("span",Dt,"Security Disclaimer")):N("",!0),(h(!0),w(Y,null,le(n.btn,(o,u)=>(h(),w("span",{key:o,class:D(["title-btn",{"btn-active":u===n.select}]),onClick:g=>n.clickActive(u)},r(o),11,Rt))),128))]),a("div",xt,[n.language[0]===n.select?(h(),w("p",Bt,jt)):N("",!0),n.language[1]===n.select?(h(),w("p",qt,zt)):N("",!0),n.language[2]===n.select?(h(),w("p",Kt," With the ever-increasing amount of Social Engineering Attack, system users should not reuse the same or similar password of this site on any other sites. In case of any leakage of the username and/or password, no matter intentionally of un-intentionally, you will be liable for the business impact caused. The system owner, Travelsky Limited Inc., will reserve the right to take necessary legal actions against the relevant system users. ——Travelsky Limited Inc ")):N("",!0)])],512),[[Ue,n.securityInfo]])],4),a("div",{class:D(["form",n.getLocale()==="en"?"en-form":"cn-form"])},[s(S,{ref:"formRef","hide-required-asterisk":!1,rules:n.rules,inline:!1,model:n.formInline,"label-width":"95px","label-position":"left"},{default:d(()=>[s(p,{label:n.$t("app.personal.oldPassword"),prop:"oldcertificate"},{default:d(()=>[s(k,{modelValue:n.formInline.oldcertificate,"onUpdate:modelValue":f[0]||(f[0]=o=>n.formInline.oldcertificate=o),type:"password",placeholder:n.$t("app.check.inputOldPwd"),autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),s(p,{label:n.$t("app.personal.newPassword"),prop:"certificate",class:"form-item"},{default:d(()=>[s(k,{modelValue:n.formInline.certificate,"onUpdate:modelValue":f[1]||(f[1]=o=>n.formInline.certificate=o),type:"password","show-message":!1,placeholder:n.$t("app.check.inputNewPwd"),class:"form-item",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),s(p,{label:n.$t("app.personal.confirmPassword"),prop:"recertificate",class:"form-item confirm"},{default:d(()=>[s(k,{modelValue:n.formInline.recertificate,"onUpdate:modelValue":f[2]||(f[2]=o=>n.formInline.recertificate=o),type:"password",placeholder:n.$t("app.check.reEnterNewPwd"),class:"form-item",autocomplete:"off","show-password":""},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),s(p,{class:"passWordButton"},{default:d(()=>[s(I,{type:"primary",class:"form-item btn-submit","data-gid":"081W0102",onClick:n.onSubmit},{default:d(()=>[G(r(n.$t("app.button.confirmeTheChange")),1)]),_:1},8,["onClick"])]),_:1})]),_:1},8,["rules","model"])],2),a("div",{class:D(["valid",n.getLocale()==="en"?"en-valid":"cn-valid"])},[a("div",{class:D(["message",n.getLocale()==="en"?"en-message":"cn-message"])},[a("div",{class:D(["message-left",n.getLocale()==="en"?"en-message-left":"cn-message-left"])},null,2),a("div",Jt,[a("ul",null,[(h(!0),w(Y,null,le(n.validArray,(o,u)=>(h(),w("li",{key:u,class:D({error:n.validList.includes(o),default:n.showDefault,"en-li":n.getLocale()==="en"})},[Ht,G(" "+r(o),1)],2))),128))])])],2)],2)]))])}const ea=de(Ft,[["render",Zt],["__scopeId","data-v-abfff5fe"]]),ta=()=>{const{t:n}=oe(),f=pe(),_=xe(),E=M(),l=M();let V="";const c=j(()=>T("defaultSellingGuiIataNum","sellingGuiIataNum")),T=(i,t)=>{var C,X,R;const y=((C=f.state.user)==null?void 0:C[i])??"";return y||(((R=(((X=f.state.user)==null?void 0:X[t])??"").split(";"))==null?void 0:R[0])??"")},k={0:n("app.personal.oauthToken"),1:n("app.personal.oauthTokenMessage"),4:n("app.personal.oauthTokenEmail"),5:n("app.personal.oauthTokenMessageEmail"),6:n("app.personal.oauthTokenWechat"),7:n("app.personal.oauthTokenWechatMessage"),8:n("app.personal.oauthTokenWechatEmail"),9:n("app.personal.oauthTokenWechatMessageEmail")},p=ue({roleNames:"",mgrAirline:"",office:"",belongOffice:"",userName:"",defaultRole:"",departmentName:"",locale:"",employeeId:"",employeeName:"",signature:"",certId:"",mobile:"",email:"",switchAirline:"",defaultOffice:"",defaultSystem:"",agentNo:"",agent:"",aviationAssociation:"",currency:"",airportCode:"",operator:!0,defaultRoleWithPid:!0,roleMap:{},flowLimit:"",overFlowed:"",unlockTimes:"",currentVersion:""}),I={switchAirline:[{required:!0,message:n("app.check.input2Word"),trigger:"blur"},{type:"string",pattern:Be,message:n("app.check.input2WordOrNumber"),trigger:"blur"}]},S={aviationAssociation:[{required:!0,message:n("app.check.inputIata"),trigger:"blur"}],agentNo:[{required:!0,message:n("app.check.enterOnly"),trigger:"blur"},{type:"string",pattern:Je,message:n("app.check.agentRule"),trigger:"blur"}],defaultOffice:[{required:!0,message:n("app.check.officeRule"),trigger:"blur"},{type:"string",pattern:He,message:n("app.check.officeRule"),trigger:"blur"}],currency:[{required:!0,message:n("app.check.upTo10SeperateBySemicolon"),trigger:"blur"},{type:"string",pattern:Ze,message:n("app.check.inputCorrectCurrency"),trigger:"blur"}],airportCode:[{required:!0,message:n("app.check.airportRule"),trigger:"blur"},{type:"string",pattern:et,message:n("app.check.airportRule"),trigger:"blur"}]},$=async i=>{if(p.userName=i.userName,p.defaultRole=i.defaultRole,O()==="en"){const t=await f.getters.userStrus,y=t==null?void 0:t.filter(C=>C.struName===i.departmentName);p.departmentName=y&&y.length>0?y[0].struEnName:i.departmentName}else p.departmentName=i.departmentName;p.locale=i.locale==="en"?n("app.personal.english"):n("app.personal.chinese"),p.employeeId=i.employeeId,p.employeeName=i.employeeName,p.signature=k[i.securityLevel],p.certId=i.certId||"-",p.mobile=i.mobile,p.email=i.email,p.switchAirline=i.switchAirline,p.defaultOffice=i.office,p.defaultSystem=i.defaultSystem,p.agent=i.agent,p.agentNo=i.agentNo,p.aviationAssociation=i.sellingGuiIataNum,p.currency=i.tssellingguicurrency,p.airportCode=i.tssellingguiairportcode,p.operator=i.operator,p.mgrAirline=i.mgrAirline,p.defaultRoleWithPid=i.defaultRoleWithPid,p.flowLimit=i.flowLimit?i.flowLimit:"-",p.overFlowed=i.overFlowed?n("app.personal.exceedFlowLimit"):n("app.personal.nonExceedFlowLimit"),p.unlockTimes=i.unlockTimes?i.unlockTimes:"-",p.expireTime="",p.epidNumber="",p.agentManageStatus=i.agentManageStatus},o=()=>{const i=[re("div",{class:"info-tip-title text-[18px] text-gray-1"},n("app.domesticRepresentatives.unbindConfirm",{epidNumber:p.epidNumber})),re("div",{class:"info-tip-title text-[18px] mr-[10px] text-gray-1"},n("app.domesticRepresentatives.unbindConfirmTip"))];tt.confirm(re("div",{class:"unbind-title"},i),{icon:re("em",{class:"iconfont icon-info-circle-line text-brand-2 text-[32px]"}),customClass:"alert-message-common crs-btn-ui unbind-epid-message-box min-w-[500px]",confirmButtonText:n("app.domesticRepresentatives.unbind"),cancelButtonText:n("app.ticketStatus.cancelBtn"),closeOnClickModal:!1,showClose:!1}).then(async()=>{const t=_.query.authValue,y=await _t(t);await at(y,p.epidNumber??"","09300126"),Le({type:"success",message:n("app.domesticRepresentatives.unbind")}),Ae("09300123");const C=ge();st(),he(C)})},u=async()=>{try{const t=(await lt("09300127")).data;if(t){const{expireTime:y,epidNumber:C}=t;p.expireTime=nt(y).format("YYYY-MM-DD"),p.epidNumber=C}}catch{}},g=async()=>{const i=await f.getters.user;if(await $(i),!(i!=null&&i.defaultOfficeInternational)&&(i!=null&&i.crsSystem)&&await u(),p.operator)try{await E.value.validate()}catch{}},b=async i=>{var _e;const t=f.state.user.office,y=ye("currnetHasEtermInfoSessionession",""),C=JSON.parse(y.value?y.value:"{}");if(i===t)return;const X=((_e=(i||"").split(";"))==null?void 0:_e[0])??"",R=f.state.user.defaultRole,je=f.state.user.defaultUserGroup;if(X){const qe={office:X,roleName:R,userGroup:je,currentSellingGuiIataNum:"",system:p.defaultSystem,etermId:(C==null?void 0:C.etermId)??"",etermPwd:(C==null?void 0:C.etermPwd)??"",etermServerAddress:(C==null?void 0:C.etermServerAddress)??""};await ot(qe,"09400105")}},F=async i=>{var C;const t=f.state.user.sellingGuiIataNum;if(i===t)return;const y=((C=(i||"").split(";"))==null?void 0:C[0])??"";await it({sellingGuiIataNum:y},"09400114")},A=async()=>{if(V.trim()!==p.switchAirline.trim()){const i=await bt("09400117"),t=new Date().getTime();await Oe("diLocalData",yt(i.data),t)}V=p.switchAirline.trim()},P=async()=>{const i={switchAirline:p.switchAirline,office:p.defaultOffice,agentNo:p.agentNo,sellingGuiIataNum:p.aviationAssociation,tssellingguicurrency:p.currency,tssellingguiairportcode:p.airportCode};await Promise.all([l.value.validate(),E.value.validate()]),await Te(i,"091V0843"),await A(),await me(n("app.tips.success")),await b(p.defaultOffice),await F(p.aviationAssociation);const t=await Ee("09400110"),y=ye("needCallEtermInfoDialog","");t.data.manualInputEtermInfo&&(y.value="need"),f.dispatch("addUser",t.data)},z=()=>{l.value.validate(async i=>{if(!i)return;await Te({switchAirline:p.switchAirline},"091V0843"),await A(),await me(n("app.tips.success"));const t=await Ee("09400111");f.dispatch("addUser",t.data)})},H=async()=>{!E.value&&!l.value||(p.defaultRoleWithPid?z():await P())},Z=j({get:()=>{var i;return(i=p.switchAirline)==null?void 0:i.toUpperCase()},set:i=>{p.switchAirline=i==null?void 0:i.toUpperCase()}}),ee=j({get:()=>{var i;return(i=p.agentNo)==null?void 0:i.toUpperCase()},set:i=>{p.agentNo=i==null?void 0:i.toUpperCase()}}),B=j({get:()=>{var i;return(i=p.currency)==null?void 0:i.toUpperCase()},set:i=>{p.currency=i==null?void 0:i.toUpperCase()}}),te=j({get:()=>{var i;return(i=p.aviationAssociation)==null?void 0:i.toUpperCase()},set:i=>{p.aviationAssociation=i==null?void 0:i.toUpperCase()}}),ae=j({get:()=>{var i;return(i=p.airportCode)==null?void 0:i.toUpperCase()},set:i=>{p.airportCode=i==null?void 0:i.toUpperCase()}}),m=j({get:()=>{var i;return(i=p.defaultOffice)==null?void 0:i.toUpperCase()},set:i=>{p.defaultOffice=i==null?void 0:i.toUpperCase()}});return Ce(()=>{p.currentVersion=`SGUI_V_${we.slice(0,we.lastIndexOf("."))}`,g().then()}),be(()=>f.getters.user,async(i,t)=>{const y=await t,C=await i;y.defaultRoleWithPid!==C.defaultRoleWithPid&&g()}),{form:p,rules:S,formRef:E,getCurrentUserInfo:g,onSave:H,switchAirline:Z,agentNo:ee,currency:B,aviationAssociation:te,airportCode:ae,defaultOffice:m,airlineFormRef:l,unbindEpidClick:o,airlineFormRules:I,defaultSellingGuiIataNum:c}},aa=ta,sa=n=>(De("data-v-228b4d71"),n=n(),Re(),n),la={class:"base-info"},na={class:"base-info-title"},oa={class:"base-info-container"},ia={class:"base-info-value"},ra={class:"base-info-label"},ua={class:"base-info-value"},pa={class:"base-info-label"},ca={class:"base-info-value"},da={class:"base-info-value"},fa={class:"base-info-label"},ma={class:"base-info-value"},ga={class:"base-info-label"},ha={class:"base-info-value"},va={key:0,class:"inline-flex bg-green-3 rounded-sm px-[4px] text-green-1 ml-1 text-sm leading-[24px]"},ba={key:1,class:"inline-flex bg-yellow-2 rounded-sm px-[4px] text-yellow-1 ml-1 text-sm leading-[24px]"},ya={key:2,class:"inline-flex bg-red-3 rounded-sm px-[4px] text-red-1 ml-1 text-sm leading-[24px]"},Ca={class:"base-info-value"},_a={class:"base-info-value"},ka={class:"base-info-label"},$a={class:"base-info-value"},wa={class:"base-info-label"},Ta={class:"base-info-value"},Ea={class:"base-info-container"},Na={class:"base-info-value"},Va={class:"base-info-label"},Ia={class:"base-info-value"},Sa={class:"base-info-container"},Fa={class:"base-info-value"},Aa={class:"base-info-label"},Pa={class:"base-info-value"},Ua={class:"base-info-container"},Da={class:"base-info-label"},Ra={class:"base-info-value"},xa={key:0,class:"base-info-container"},Ba={class:"base-info-label"},La={class:"base-info-value base-info-epid-value"},Oa={class:"base-info-label"},Ma={class:"base-info-value"},Xa={class:"base-info-container"},ja={class:"base-info-label"},qa={class:"base-info-value"},Qa={key:0,class:"sell-setting"},Ga={class:"base-info-title"},Wa={class:"base-info-container"},Ya={class:"base-info-label"},za={class:"base-info-value"},Ka=sa(()=>a("label",{class:"base-info-label"},"Office",-1)),Ja={class:"base-info-value"},Ha={class:"base-info-label base-info-labelw100"},Za={class:"base-info-value showAgentTop"},es={class:"base-info-label"},ts={class:"base-info-value"},as={class:"base-info-label"},ss={class:"base-info-value"},ls={class:"save-btn"},ns=ce({__name:"Information",setup(n){const{form:f,rules:_,formRef:E,onSave:l,switchAirline:V,agentNo:c,currency:T,aviationAssociation:k,airportCode:p,defaultOffice:I,airlineFormRef:S,airlineFormRules:$,unbindEpidClick:o,defaultSellingGuiIataNum:u}=aa();return(g,b)=>(h(),w("div",la,[a("div",null,[a("p",na,r(g.$t("app.personal.settingsProfile")),1),a("div",oa,[s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",{class:D(["base-info-label",e(O)()==="en"?"base-info-label-en":""])},r(g.$t("app.personal.userName")),3),a("span",ia,r(e(f).userName),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",ra,r(g.$t("app.personal.organization")),1),a("span",ua,r(e(f).departmentName),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",pa,r(g.$t("app.personal.language")),1),a("span",ca,r(e(f).locale),1)]),_:1})]),_:1}),s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",{class:D(["base-info-label",e(O)()==="en"?"base-info-label-en":""])},r(g.$t("app.personal.employeeNumber")),3),a("span",da,r(e(f).employeeId),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",fa,r(g.$t("app.personal.name")),1),a("span",ma,r(e(f).employeeName),1)]),_:1}),e(f).agent?(h(),x(e(v),{key:0,span:8},{default:d(()=>[a("label",ga,r(g.$t("app.personal.agent")),1),a("div",ha,[a("span",null,r(e(f).agent),1),e(f).agentManageStatus===2?(h(),w("span",va,r(g.$t("app.personal.normalAgent")),1)):N("",!0),e(f).agentManageStatus===3?(h(),w("span",ba,r(g.$t("app.personal.limitAgent")),1)):N("",!0),e(f).agentManageStatus===4?(h(),w("span",ya,r(g.$t("app.personal.restrictAgent")),1)):N("",!0)])]),_:1})):(h(),x(e(v),{key:1,span:8}))]),_:1}),s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",{class:D(["base-info-label",e(O)()==="en"?"base-info-label-en":""])},r(g.$t("app.personal.settlementNo")),3),a("span",Ca,r(e(u)),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",{class:D(["base-info-label",e(O)()==="en"?"base-info-label-en":""])},r(g.$t("app.personal.flowLimit")),3),a("span",_a,r(e(f).flowLimit),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",ka,r(g.$t("app.personal.overFlowed")),1),a("span",$a,r(e(f).overFlowed),1)]),_:1})]),_:1}),s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",wa,r(g.$t("app.personal.unlockTimes")),1),a("span",Ta,r(e(f).unlockTimes),1)]),_:1})]),_:1})]),a("div",Ea,[s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",{class:D(["base-info-label",e(O)()==="en"?"base-info-label-en":""])},r(g.$t("app.personal.certificationType")),3),a("span",Na,r(e(f).signature),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",Va,r(g.$t("app.personal.certificateID")),1),a("span",Ia,r(e(f).certId),1)]),_:1}),s(e(v),{span:8})]),_:1})]),a("div",Sa,[s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",{class:D(["base-info-label",e(O)()==="en"?"base-info-label-en":""])},r(g.$t("app.personal.cellphone")),3),a("span",Fa,r(e(f).mobile),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",Aa,r(g.$t("app.personal.email")),1),a("span",Pa,r(e(f).email),1)]),_:1}),s(e(v),{span:8})]),_:1})]),a("div",Ua,[s(e(U),null,{default:d(()=>[e(f).operator?(h(),x(e(ne),{key:1,ref_key:"airlineFormRef",ref:S,model:e(f),rules:e($)},{default:d(()=>[s(e(Q),{label:g.$t("app.personal.airline"),prop:"switchAirline",class:"airline"},{default:d(()=>[s(e(q),{modelValue:e(V),"onUpdate:modelValue":b[0]||(b[0]=F=>K(V)?V.value=F:null),class:"hasVal",placeholder:g.$t("app.check.input2Word")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1},8,["model","rules"])):(h(),x(e(v),{key:0,span:8},{default:d(()=>[a("label",Da,r(g.$t("app.personal.airline")),1),a("span",Ra,r(e(f).switchAirline),1)]),_:1})),s(e(v),{span:8}),s(e(v),{span:8})]),_:1})]),e(f).epidNumber?(h(),w("div",xa,[s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",{class:D(["font-bold text-[16px] text-gray-1",e(O)()==="en"?"base-info-label-en":""])},r(g.$t("app.domesticRepresentatives.connectionChannel")),3)]),_:1}),s(e(v),{span:8})]),_:1}),s(e(U),null,{default:d(()=>[s(e(v),{class:"flex items-center"},{default:d(()=>[a("label",Ba,r(g.$t("app.domesticRepresentatives.ChannelNumber")),1),a("span",La,r(e(f).epidNumber),1),s(e(se),{type:"primary",size:"small",onClick:e(o)},{default:d(()=>[G(r(g.$t("app.domesticRepresentatives.unbind")),1)]),_:1},8,["onClick"])]),_:1}),s(e(v),{span:16})]),_:1}),s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",Oa,r(g.$t("app.domesticRepresentatives.expiryDate")),1),a("span",Ma,r(e(f).expireTime),1)]),_:1}),s(e(v),{span:16})]),_:1})])):N("",!0),a("div",Xa,[s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",{class:D(["base-info-label",e(O)()==="en"?"base-info-label-en":""])},r(g.$t("app.personal.versionInfo")),3)]),_:1}),s(e(v),{span:8})]),_:1}),s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",ja,r(g.$t("app.personal.currentVersion")),1),a("span",qa,r(e(f).currentVersion),1)]),_:1}),s(e(v),{span:16})]),_:1})])]),e(f).defaultRoleWithPid?N("",!0):(h(),w("div",Qa,[a("p",Ga,r(g.$t("app.personal.salesAccountConfiguration")),1),a("div",Wa,[e(f).operator?(h(),x(e(ne),{key:1,ref_key:"formRef",ref:E,"label-width":"74px","label-position":"left",model:e(f),rules:e(_)},{default:d(()=>[s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[s(e(Q),{label:g.$t("app.personal.iata"),class:"aviationNum",prop:"aviationAssociation"},{default:d(()=>[s(e(q),{modelValue:e(k),"onUpdate:modelValue":b[1]||(b[1]=F=>K(k)?k.value=F:null),class:"hasVal",placeholder:g.$t("app.check.enterOnly")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),s(e(v),{span:8},{default:d(()=>[s(e(Q),{label:"Office",prop:"defaultOffice"},{default:d(()=>[s(e(q),{modelValue:e(I),"onUpdate:modelValue":b[2]||(b[2]=F=>K(I)?I.value=F:null),placeholder:g.$t("app.check.upTo5SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1})]),_:1}),s(e(v),{span:8},{default:d(()=>[s(e(Q),{label:g.$t("app.personal.extraServiceAgent"),class:"agent",prop:"agentNo",style:{"align-items":"flex-start"},"label-width":"125px"},{default:d(()=>[s(e(q),{modelValue:e(c),"onUpdate:modelValue":b[3]||(b[3]=F=>K(c)?c.value=F:null),class:"hasVal",placeholder:g.$t("app.check.enterOnly")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1})]),_:1}),s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[s(e(Q),{label:g.$t("app.personal.currency"),prop:"currency"},{default:d(()=>[s(e(q),{modelValue:e(T),"onUpdate:modelValue":b[4]||(b[4]=F=>K(T)?T.value=F:null),placeholder:g.$t("app.check.upTo10SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),s(e(v),{span:8},{default:d(()=>[s(e(Q),{label:g.$t("app.personal.airport"),prop:"airportCode"},{default:d(()=>[s(e(q),{modelValue:e(p),"onUpdate:modelValue":b[5]||(b[5]=F=>K(p)?p.value=F:null),placeholder:g.$t("app.check.upTo5SeperateBySemicolon")},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),_:1}),s(e(v),{span:8})]),_:1})]),_:1},8,["model","rules"])):(h(),w(Y,{key:0},[s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",Ya,r(g.$t("app.personal.iata")),1),a("span",za,r(e(f).aviationAssociation),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[Ka,a("span",Ja,r(e(f).defaultOffice),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",Ha,r(g.$t("app.personal.extraServiceAgent")),1),a("span",Za,r(e(f).agentNo),1)]),_:1})]),_:1}),s(e(U),null,{default:d(()=>[s(e(v),{span:8},{default:d(()=>[a("label",es,r(g.$t("app.personal.currency")),1),a("span",ts,r(e(f).currency),1)]),_:1}),s(e(v),{span:8},{default:d(()=>[a("label",as,r(g.$t("app.personal.airport")),1),a("span",ss,r(e(f).airportCode),1)]),_:1}),s(e(v),{span:8})]),_:1})],64))])])),a("div",ls,[e(f).operator?(h(),x(e(se),{key:0,type:"primary","data-gid":"081W0101",onClick:e(l)},{default:d(()=>[G(r(g.$t("app.button.save")),1)]),_:1},8,["onClick"])):N("",!0)])]))}});const os=de(ns,[["__scopeId","data-v-228b4d71"]]),is=n=>{const{t:f}=oe(),_=xe(),E=M(!1),l=M(),V=pe(),c=M({ct:"",ctct:"",ctce:"",airlineSettings:!1,airlinesCTCT:[{airline:"",ctct:""}],unshared:!0,nonstop:!1,autoSearch:"1",autoSelectCabinClass:!1,useQtb:!1,manualQuery:!1,domesticPrinterno:"",internationalPrinterno:"",backfieldEnName:!0,checkTrip:{airlineNameCheckSwitch:!1,cityName:!1,airportName:!1,passengerInformation:!1,idNumber:!1,contact:!1,priceIncludingTax:!1,cabinSpace:!1,flightTime:!1,week:!1,airCode:!0},defaultRemark:!1,remarkList:[""],defaultQmail:!1,defaultNatAndIssCountry:!1,autoOccupy:!1,passengerInfo:{passengerNationality:"",visaIssueCountry:""},place:"BOTTOM",followPnr:!1,domesticCities:"",internationalCities:""}),T=j(()=>{var t;return((t=V.state.user)==null?void 0:t.defaultOfficeInternational)??!1}),k=(t,y,C)=>{y||C(new Error(f("app.pnrManagement.validate.required"))),c.value.airlinesCTCT.filter(R=>R.airline.toLocaleUpperCase()===(y==null?void 0:y.toLocaleUpperCase())).length>1?C(new Error(f("app.pnrManagement.validate.contactor"))):C()},p=(t,y,C)=>{y?C():C(new Error(f("app.pnrManagement.validate.required")))},I={ct:[{pattern:rt,message:f("app.pnrManagement.validate.formatErr"),trigger:"blur"}],ctct:[{pattern:Ne,message:f("app.pnrManagement.validate.formatErr"),trigger:"blur"}],airlinesCtct:[{pattern:Ne,message:f("app.pnrManagement.validate.formatErr"),trigger:"blur"},{validator:p,trigger:"blur"}],ctce:[{required:!1,pattern:ut,message:f("app.pnrManagement.validate.enterValidEmail"),trigger:["blur","change"]}],airline:[{pattern:Be,trigger:["blur","change"],message:f("app.pnrManagement.validate.characterCode")},{validator:k,trigger:["blur","change"]}],"passengerInfo.visaIssueCountry":[{pattern:/^[a-zA-Z]{2,3}$/,trigger:["change","blur"],message:f("app.intlPassengerForm.enterCharacterCode")}],"passengerInfo.passengerNationality":[{pattern:/^[a-zA-Z]{2,3}$/,trigger:["change","blur"],message:f("app.intlPassengerForm.enterCharacterCode")}],internationalCities:[{pattern:Ve,message:f("app.pnrManagement.validate.cityFormatErr"),trigger:"blur"}],domesticCities:[{pattern:Ve,message:f("app.pnrManagement.validate.cityFormatErr"),trigger:"blur"}]},S=()=>{c.value.airlinesCTCT.push({airline:"",ctct:""})},$=t=>{c.value.airlinesCTCT.splice(t,1)},o=()=>{c.value.remarkList.push("")},u=t=>{c.value.remarkList.splice(t,1)},g=async()=>{l.value.validate(async t=>{if(t)try{c.value.checkTrip.priceIncludingTax=c.value.checkTrip.passengerInformation,E.value=!0;const y={autoOccupy:c.value.autoOccupy,ct:c.value.ct,ctct:c.value.ctct,ctce:c.value.ctce,unshared:c.value.unshared,nonstop:c.value.nonstop,autoSearch:c.value.autoSearch,autoSelectCabinClass:c.value.autoSelectCabinClass,useQtb:c.value.useQtb,manualQuery:c.value.manualQuery,backfieldEnName:c.value.backfieldEnName,checkTrip:{airlineNameCheckSwitch:c.value.checkTrip.airlineNameCheckSwitch,cityName:c.value.checkTrip.cityName,airportName:c.value.checkTrip.airportName,passengerInformation:c.value.checkTrip.passengerInformation,idNumber:c.value.checkTrip.idNumber,contact:c.value.checkTrip.contact,priceIncludingTax:c.value.checkTrip.priceIncludingTax,cabinSpace:c.value.checkTrip.cabinSpace,flightTime:c.value.checkTrip.flightTime,week:c.value.checkTrip.week,airCode:c.value.checkTrip.airCode},airlinesCTCT:c.value.airlineSettings?c.value.airlinesCTCT:[],internationalPrinterno:c.value.internationalPrinterno,domesticPrinterno:c.value.domesticPrinterno,remarkList:c.value.defaultRemark?c.value.remarkList:[""],passengerInfo:{visaIssueCountry:c.value.defaultNatAndIssCountry?c.value.passengerInfo.visaIssueCountry:"",passengerNationality:c.value.defaultNatAndIssCountry?c.value.passengerInfo.passengerNationality:""},place:c.value.place,followPnr:c.value.followPnr,domesticCities:c.value.domesticCities,internationalCities:c.value.internationalCities,defaultQmail:c.value.defaultQmail},C=Ie("081W0103"),{data:X}=await ct(y,C);X.value&&(V.dispatch("updateUserPreferences",y),Le({message:f("app.tips.success"),type:"success"}));const{data:R}=await Ct("081W0103");R!=null&&R.value&&await Oe("COMMON_CITIES",JSON.stringify(R.value))}finally{E.value=!1}})},b=t=>{(t.keyCode===13||t.keyCode===100)&&g()};be(()=>n.activeTab,t=>{t!==f("app.personal.preferences")?window.removeEventListener("keydown",b,!1):window.addEventListener("keydown",b)}),be(()=>_.fullPath,t=>{t!=="/personalCenter"?window.removeEventListener("keydown",b,!1):n.activeTab===f("app.personal.preferences")&&window.addEventListener("keydown",b)},{immediate:!0,deep:!0});const F=t=>{c.value.ct=(t==null?void 0:t.ct)??"",c.value.ctct=(t==null?void 0:t.ctct)??"",c.value.ctce=(t==null?void 0:t.ctce)??"",c.value.airlinesCTCT=(t==null?void 0:t.airlinesCTCT)??[{airline:"",ctct:""}],c.value.airlineSettings=!!(t!=null&&t.airlinesCTCT)},A=t=>{c.value.unshared=(t==null?void 0:t.unshared)??!0,c.value.nonstop=(t==null?void 0:t.nonstop)??!1,c.value.autoSearch=(t==null?void 0:t.autoSearch)??"1",c.value.autoSelectCabinClass=(t==null?void 0:t.autoSelectCabinClass)??!1,c.value.autoOccupy=(t==null?void 0:t.autoOccupy)??!1,c.value.domesticCities=(t==null?void 0:t.domesticCities)??"",c.value.internationalCities=(t==null?void 0:t.internationalCities)??""},P=t=>{c.value.useQtb=(t==null?void 0:t.useQtb)??!1,c.value.manualQuery=(t==null?void 0:t.manualQuery)??!1},z=t=>{c.value.backfieldEnName=(t==null?void 0:t.backfieldEnName)??!0},H=t=>{c.value.followPnr=(t==null?void 0:t.followPnr)??!1},Z=t=>{c.value.domesticPrinterno=(t==null?void 0:t.domesticPrinterno)??"",c.value.internationalPrinterno=(t==null?void 0:t.internationalPrinterno)??""},ee=t=>{if(t!=null&&t.checkTrip){const y=t.checkTrip.priceIncludingTax||t.checkTrip.passengerInformation;t.checkTrip.priceIncludingTax=y,t.checkTrip.passengerInformation=y}c.value.checkTrip=(t==null?void 0:t.checkTrip)??{cityName:!1,airportName:!1,cabinSpace:!1,flightTime:!1,passengerInformation:!1,idNumber:!1,contact:!1,priceIncludingTax:!1,airlineNameCheckSwitch:!1,week:!1,airCode:!0}},B=t=>{c.value.defaultRemark=!!((t==null?void 0:t.remarkList)??[]).length,c.value.remarkList=((t==null?void 0:t.remarkList)??[]).length?(t==null?void 0:t.remarkList)??[""]:[""]},te=t=>{c.value.defaultQmail=(t==null?void 0:t.defaultQmail)??!1},ae=t=>{var y,C,X,R;c.value.defaultNatAndIssCountry=!!((y=t==null?void 0:t.passengerInfo)!=null&&y.visaIssueCountry||(C=t==null?void 0:t.passengerInfo)!=null&&C.passengerNationality),c.value.passengerInfo.visaIssueCountry=((X=t==null?void 0:t.passengerInfo)==null?void 0:X.visaIssueCountry)??"",c.value.passengerInfo.passengerNationality=((R=t==null?void 0:t.passengerInfo)==null?void 0:R.passengerNationality)??""},m=t=>{c.value.place=(t==null?void 0:t.place)??"BOTTOM"};function i(t){F(t),A(t),P(t),z(t),Z(t),ee(t),B(t),ae(t),m(t),H(t),te(t)}return Ce(async()=>{try{E.value=!0;const t=Ie("081W0104"),{data:y}=await pt(t);i(y.value)}finally{E.value=!1}}),{FORM_RULES:I,formRef:l,formParams:c,isIntlCrs:T,onSave:g,loading:E,addAirline:S,delAirline:$,keyDown:b,deleteRemark:u,addReamrk:o}},rs=is,us={class:"preference-conf mb-5"},ps={class:"mb-2.5 text-gray-1 text-base font-bold"},cs={class:"my-[5px]"},ds={class:"text-gray-2 text-xs font-normal leading-tight"},fs=["onClick"],ms={class:"flex-col justify-start items-start gap-3.5 flex mb-7"},gs={class:"self-stretch justify-between items-start inline-flex"},hs={class:"text-gray-1 text-base font-bold leading-normal"},vs={class:"justify-start items-center gap-2 inline-flex"},bs={class:"self-stretch justify-start items-center gap-1.5 flex"},ys={class:"text-gray-3 text-xs font-normal leading-tight"},Cs={class:"self-stretch justify-start items-center gap-2 inline-flex"},_s={class:"text-gray-3 text-xs font-normal leading-tight"},ks={class:"self-stretch justify-start items-center gap-2 inline-flex"},$s={class:"text-gray-3 text-xs font-normal leading-tight"},ws={class:"self-stretch justify-start items-center gap-2 inline-flex"},Ts={class:"text-gray-3 text-xs font-normal leading-tight"},Es={class:"self-stretch justify-start items-center gap-2 inline-flex"},Ns={class:"text-gray-3 text-xs font-normal leading-tight"},Vs={class:"h-[54px] flex-col justify-start items-start gap-2.5 flex mb-7"},Is={class:"self-stretch justify-between items-start inline-flex"},Ss={class:"text-gray-1 text-base font-bold leading-normal"},Fs={class:"justify-start items-center gap-2 inline-flex"},As={class:"self-stretch justify-start items-center gap-1.5 flex"},Ps={class:"text-gray-3 text-xs font-normal leading-tight"},Us={class:"h-[114px] flex-col justify-start items-start gap-2.5 inline-flex"},Ds={class:"self-stretch justify-between items-start inline-flex"},Rs={class:"text-gray-1 text-base font-bold leading-normal"},xs={class:"justify-start items-center gap-2 inline-flex"},Bs={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},Ls={class:"text-gray-3 text-xs font-normal leading-tight"},Os={class:"self-stretch h-5 justify-start items-center gap-1.5 inline-flex"},Ms={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},Xs={class:"text-gray-3 text-xs font-normal leading-tight"},js={class:"flex-col justify-start items-start gap-2.5 flex mb-3"},qs={class:"self-stretch justify-between items-start inline-flex"},Qs={class:"text-gray-1 text-base font-bold leading-normal"},Gs={class:"justify-start items-center gap-2 inline-flex"},Ws={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},Ys={class:"text-gray-3 text-xs font-normal leading-tight"},zs={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},Ks={class:"text-gray-3 text-xs font-normal leading-tight mb-2.5"},Js={class:"mr-2.5"},Hs={class:"mr-[6px]"},Zs={class:"mr-[6px]"},el={class:"flex-col justify-start items-start gap-2.5 flex mb-3"},tl={class:"self-stretch justify-between items-start inline-flex"},al={class:"text-gray-1 text-base font-bold leading-normal"},sl={class:"justify-start items-center gap-2 inline-flex"},ll={class:"w-70 self-stretch justify-start items-center gap-1.5 flex"},nl={class:"text-gray-3 text-xs font-normal leading-tight"},ol={class:"flex-col justify-start items-start gap-2.5 inline-flex mb-3"},il={class:"self-stretch justify-between items-start inline-flex"},rl={class:"text-gray-1 text-base font-bold leading-normal"},ul={class:"flex items-center"},pl={class:"text-gray-3 text-xs font-normal leading-tight mr-[20px]"},cl={class:"text-gray-3 text-xs font-normal leading-tight flex"},dl={class:"mr-1 py-2"},fl={class:"bg-gray-7 px-1 py-2 rounded-sm flex-col justify-center items-start inline-flex gap-2"},ml={key:0},gl={class:"flex-col inline-flex gap-2"},hl={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},vl={class:"w-70 mb-[5px] self-stretch justify-start items-center gap-1.5 flex"},bl={class:"text-gray-3 text-xs font-normal leading-tight"},yl=["onClick"],Cl={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},_l={class:"w-70 mb-[5px] self-stretch justify-start items-center gap-1.5 flex"},kl={class:"text-gray-3 text-xs font-normal leading-tight"},$l={key:2},wl={class:"text-gray-1 text-base font-bold leading-normal mb-2.5"},Tl={class:"w-[100%] h-8 justify-end items-center gap-3.5 inline-flex"},El=ce({__name:"Preferences",props:{activeTab:{}},setup(n){const f=n,{FORM_RULES:_,formRef:E,formParams:l,isIntlCrs:V,onSave:c,loading:T,addAirline:k,delAirline:p,keyDown:I,deleteRemark:S,addReamrk:$}=rs(f);return(o,u)=>{const g=q,b=Q,F=kt,A=$t,P=wt,z=Tt,H=Et,Z=ne,ee=se,B=Se("trimUpper"),te=Se("permission"),ae=ft;return L((h(),w("div",us,[a("div",ps,r(o.$t("app.personal.setSefaultCTAndCTCT")),1),s(Z,{ref_key:"formRef",ref:E,"require-asterisk-position":"right",model:e(l),inline:!0,rules:e(_),onKeyup:dt(e(I),["enter"])},{default:d(()=>[a("div",null,[s(b,{label:"CT",prop:"ct"},{default:d(()=>[L(s(g,{modelValue:e(l).ct,"onUpdate:modelValue":u[0]||(u[0]=m=>e(l).ct=m)},null,8,["modelValue"]),[[B]])]),_:1}),s(b,{label:"CTCT",prop:"ctct"},{default:d(()=>[L(s(g,{modelValue:e(l).ctct,"onUpdate:modelValue":u[1]||(u[1]=m=>e(l).ctct=m)},null,8,["modelValue"]),[[B]])]),_:1}),s(b,{label:"CTCE",prop:"ctce"},{default:d(()=>[L(s(g,{modelValue:e(l).ctce,"onUpdate:modelValue":u[2]||(u[2]=m=>e(l).ctce=m)},null,8,["modelValue"]),[[B]])]),_:1}),s(F,{size:"large",type:"info"},{default:d(()=>[G(r(o.$t("app.personal.defaultValue")),1)]),_:1})]),a("div",cs,[s(b,{prop:"airlineSettings"},{default:d(()=>[s(A,{modelValue:e(l).airlineSettings,"onUpdate:modelValue":u[3]||(u[3]=m=>e(l).airlineSettings=m),type:"default"},{default:d(()=>[a("span",ds,r(o.$t("app.personal.airlineSettings")),1)]),_:1},8,["modelValue"])]),_:1})]),e(l).airlineSettings?(h(!0),w(Y,{key:0},le(e(l).airlinesCTCT,(m,i)=>(h(),w("div",{key:i,class:"mb-[14px]"},[s(b,{label:o.$t("app.personal.airlineCompany"),prop:"airlinesCTCT."+i+".airline",class:"w-[100px]",rules:e(_).airline},{default:d(()=>[L(s(g,{modelValue:m.airline,"onUpdate:modelValue":t=>m.airline=t},null,8,["modelValue","onUpdate:modelValue"]),[[B]])]),_:2},1032,["label","prop","rules"]),s(b,{label:"CTCT",prop:"airlinesCTCT."+i+".ctct",rules:e(_).airlinesCtct},{default:d(()=>[L(s(g,{modelValue:m.ctct,"onUpdate:modelValue":t=>m.ctct=t},null,8,["modelValue","onUpdate:modelValue"]),[[B]])]),_:2},1032,["prop","rules"]),i===0&&e(l).airlinesCTCT.length!==1||i>0?(h(),w("em",{key:0,class:"iconfont icon-minus-square text-[20px] relative text-undefined text-brand-2 mr-[5px] cursor-pointer",onClick:t=>e(p)(i)},null,8,fs)):N("",!0),i===e(l).airlinesCTCT.length-1?(h(),w("em",{key:1,class:"iconfont icon-plus-square text-[20px] relative text-undefined text-brand-2 cursor-pointer",onClick:u[4]||(u[4]=(...t)=>e(k)&&e(k)(...t))})):N("",!0)]))),128)):N("",!0),a("div",ms,[a("div",gs,[a("div",hs,r(o.$t("app.personal.flightQueryConditions")),1)]),a("div",vs,[a("div",bs,[a("div",ys,r(o.$t("app.personal.defaultNonShared")),1),s(P,{modelValue:e(l).unshared,"onUpdate:modelValue":u[5]||(u[5]=m=>e(l).unshared=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),a("div",Cs,[a("div",_s,r(o.$t("app.personal.defaultDirectFlightOnly")),1),s(P,{modelValue:e(l).nonstop,"onUpdate:modelValue":u[6]||(u[6]=m=>e(l).nonstop=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),a("div",ks,[a("div",$s,r(o.$t("app.personal.autoCollapseSearch")),1),s(P,{modelValue:e(l).autoSearch,"onUpdate:modelValue":u[7]||(u[7]=m=>e(l).autoSearch=m),"inline-prompt":"","active-value":"1","inactive-value":"0","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),a("div",ws,[a("div",Ts,r(o.$t("app.personal.autoSelectCabinClass")),1),s(P,{modelValue:e(l).autoSelectCabinClass,"onUpdate:modelValue":u[8]||(u[8]=m=>e(l).autoSelectCabinClass=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),a("div",Es,[a("div",Ns,r(o.$t("app.personal.autoPlaceholder")),1),s(P,{modelValue:e(l).autoOccupy,"onUpdate:modelValue":u[9]||(u[9]=m=>e(l).autoOccupy=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),s(b,{label:o.$t("app.personal.domesticCities"),prop:"domesticCities",class:"w-[914px]"},{default:d(()=>[s(g,{modelValue:e(l).domesticCities,"onUpdate:modelValue":u[10]||(u[10]=m=>e(l).domesticCities=m),placeholder:o.$t("app.personal.cityPlaceholder"),onInput:u[11]||(u[11]=m=>e(l).domesticCities=e(l).domesticCities.toUpperCase().trim())},null,8,["modelValue","placeholder"])]),_:1},8,["label"]),s(b,{label:o.$t("app.personal.internationalCities"),prop:"internationalCities",class:"w-[914px]"},{default:d(()=>[s(g,{modelValue:e(l).internationalCities,"onUpdate:modelValue":u[12]||(u[12]=m=>e(l).internationalCities=m),placeholder:o.$t("app.personal.cityPlaceholder"),onInput:u[13]||(u[13]=m=>e(l).internationalCities=e(l).internationalCities.toUpperCase().trim())},null,8,["modelValue","placeholder"])]),_:1},8,["label"])]),a("div",Vs,[a("div",Is,[a("div",Ss,r(o.$t("app.personal.passengerInfo")),1)]),a("div",Fs,[a("div",As,[a("div",Ps,r(o.$t("app.personal.defaultPassengerDocuments")),1),s(P,{modelValue:e(l).defaultNatAndIssCountry,"onUpdate:modelValue":u[14]||(u[14]=m=>e(l).defaultNatAndIssCountry=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])]),e(l).defaultNatAndIssCountry?(h(),w(Y,{key:0},[s(b,{label:o.$t("app.personal.passengerNationality"),prop:"passengerInfo.passengerNationality"},{default:d(()=>[L(s(g,{modelValue:e(l).passengerInfo.passengerNationality,"onUpdate:modelValue":u[15]||(u[15]=m=>e(l).passengerInfo.passengerNationality=m)},null,8,["modelValue"]),[[B]])]),_:1},8,["label"]),s(b,{label:o.$t("app.personal.visaIssueCountry"),prop:"passengerInfo.visaIssueCountry"},{default:d(()=>[L(s(g,{modelValue:e(l).passengerInfo.visaIssueCountry,"onUpdate:modelValue":u[16]||(u[16]=m=>e(l).passengerInfo.visaIssueCountry=m)},null,8,["modelValue"]),[[B]])]),_:1},8,["label"])],64)):N("",!0)])]),a("div",Us,[a("div",Ds,[a("div",Rs,r(o.$t("app.personal.fareQueryConditions")),1)]),a("div",xs,[a("div",Bs,[a("div",Ls,r(o.$t("app.personal.defaultUseQTB")),1),s(P,{modelValue:e(l).useQtb,"onUpdate:modelValue":u[17]||(u[17]=m=>e(l).useQtb=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),a("div",Os,[a("div",Ms,[a("div",Xs,r(o.$t("app.personal.manualRateQuery")),1),s(P,{modelValue:e(l).manualQuery,"onUpdate:modelValue":u[18]||(u[18]=m=>e(l).manualQuery=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),a("div",js,[a("div",qs,[a("div",Qs,r(o.$t("app.personal.followPnr")),1)]),a("div",Gs,[a("div",Ws,[a("div",Ys,r(o.$t("app.personal.followPnrTip")),1),s(P,{modelValue:e(l).followPnr,"onUpdate:modelValue":u[19]||(u[19]=m=>e(l).followPnr=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),a("div",zs,r(o.$t("app.personal.ticketMachine")),1),a("div",Ks,[a("span",Js,r(o.$t("app.personal.defaultTicketMachine")),1),a("span",Hs,r(o.$t("app.personal.domestic")),1),s(Fe,{modelValue:e(l).domesticPrinterno,"onUpdate:modelValue":u[20]||(u[20]=m=>e(l).domesticPrinterno=m),"need-distinguish":!0,"select-class":"w-[100px] mr-2.5"},null,8,["modelValue"]),a("span",Zs,r(o.$t("app.personal.international")),1),s(Fe,{modelValue:e(l).internationalPrinterno,"onUpdate:modelValue":u[21]||(u[21]=m=>e(l).internationalPrinterno=m),"is-inter":!0,"need-distinguish":!0,"select-class":"w-[100px]"},null,8,["modelValue"])]),a("div",el,[a("div",tl,[a("div",al,r(o.$t("app.personal.ticketRefund")),1)]),a("div",sl,[a("div",ll,[a("div",nl,r(o.$t("app.personal.manualRefundName")),1),s(P,{modelValue:e(l).backfieldEnName,"onUpdate:modelValue":u[22]||(u[22]=m=>e(l).backfieldEnName=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])])]),a("div",ol,[a("div",il,[a("div",rl,r(o.$t("app.personal.checkTrip")),1)]),a("div",ul,[a("div",pl,r(o.$t("app.personal.verifyItinerary")),1),s(A,{modelValue:e(l).checkTrip.airlineNameCheckSwitch,"onUpdate:modelValue":u[23]||(u[23]=m=>e(l).checkTrip.airlineNameCheckSwitch=m),label:o.$t("app.personal.airlineName")},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.cityName,"onUpdate:modelValue":u[24]||(u[24]=m=>e(l).checkTrip.cityName=m),label:o.$t("app.personal.cityName"),class:"ml-[-10px]"},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.airportName,"onUpdate:modelValue":u[25]||(u[25]=m=>e(l).checkTrip.airportName=m),label:o.$t("app.personal.airportName"),class:"ml-[-10px]"},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.week,"onUpdate:modelValue":u[26]||(u[26]=m=>e(l).checkTrip.week=m),label:o.$t("app.personal.weekday"),class:"ml-[-10px]"},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.passengerInformation,"onUpdate:modelValue":u[27]||(u[27]=m=>e(l).checkTrip.passengerInformation=m),label:o.$t("app.personal.pasgInfoAndPriceTax"),class:"ml-[-10px]"},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.idNumber,"onUpdate:modelValue":u[28]||(u[28]=m=>e(l).checkTrip.idNumber=m),label:o.$t("app.personal.idNumber"),class:"ml-[-10px]"},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.contact,"onUpdate:modelValue":u[29]||(u[29]=m=>e(l).checkTrip.contact=m),label:o.$t("app.personal.contact"),class:"ml-[-10px]"},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.cabinSpace,"onUpdate:modelValue":u[30]||(u[30]=m=>e(l).checkTrip.cabinSpace=m),label:o.$t("app.personal.cabinCode"),class:"ml-[-10px]"},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.flightTime,"onUpdate:modelValue":u[31]||(u[31]=m=>e(l).checkTrip.flightTime=m),label:o.$t("app.personal.flightTime"),class:"ml-[-10px]"},null,8,["modelValue","label"]),s(A,{modelValue:e(l).checkTrip.airCode,"onUpdate:modelValue":u[32]||(u[32]=m=>e(l).checkTrip.airCode=m),label:o.$t("app.personal.airportCode"),class:"ml-[-10px]"},null,8,["modelValue","label"])]),a("div",cl,[a("div",dl,r(o.$t("app.personal.example"))+" :",1),a("div",fl,[a("div",null,[e(l).checkTrip.airlineNameCheckSwitch?(h(),w("span",ml,r(o.$t("app.personal.AirChina")),1)):N("",!0),a("span",null," CA1519 "+r(e(l).checkTrip.cabinSpace?o.$t("app.personal.cabinY"):" ")+" 2024-10-23 "+r(e(l).checkTrip.week?"("+o.$t("app.pnrManagement.flight.Days3")+")":"")+" "+r(e(l).checkTrip.cityName?o.$t("app.personal.BeiJing"):" ")+r(e(l).checkTrip.airCode?"PEK":"")+r(e(l).checkTrip.airportName?o.$t("app.personal.CapitalAirport"):" ")+"T3 ",1),G(" — "+r(e(l).checkTrip.cityName?o.$t("app.personal.Shanghai"):" ")+r(e(l).checkTrip.airCode?"SHA":"")+r(e(l).checkTrip.airportName?o.$t("app.personal.HongqiaoAirport"):" ")+"T2  09:30  11:55 "+r(e(l).checkTrip.flightTime?o.$t("app.personal.flightTimeExample"):""),1)]),L(a("div",gl,[a("div",null,r(`${o.$t("app.personal.passenger")+"1"}${e(l).checkTrip.passengerInformation||e(l).checkTrip.idNumber||e(l).checkTrip.contact?" 张三 ":""} ${e(l).checkTrip.idNumber?"110106XXXXXXXXXXXXX":""}
                 ${e(l).checkTrip.contact?"150113XXXXX":""} ${e(l).checkTrip.passengerInformation?o.$t("app.personal.taxPrice")+"3000.00":""}`),1),a("div",null,r(`${o.$t("app.personal.passenger")+"2"}${e(l).checkTrip.passengerInformation||e(l).checkTrip.idNumber||e(l).checkTrip.contact?" 李四 ":""} ${e(l).checkTrip.idNumber?"110106XXXXXXXXXXXXX":""}
                 ${e(l).checkTrip.contact?"150113XXXXX":""} ${e(l).checkTrip.passengerInformation?o.$t("app.personal.taxPrice")+"3000.00":""}`),1)],512),[[Ue,e(l).checkTrip.passengerInformation||e(l).checkTrip.contact||e(l).checkTrip.idNumber]])])])]),a("div",hl,r(o.$t("app.personal.remark")),1),s(b,null,{default:d(()=>[a("div",vl,[a("div",bl,r(o.$t("app.personal.defaultRemark")),1),s(P,{modelValue:e(l).defaultRemark,"onUpdate:modelValue":u[33]||(u[33]=m=>e(l).defaultRemark=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),_:1}),e(l).defaultRemark?(h(!0),w(Y,{key:1},le(e(l).remarkList,(m,i)=>(h(),w("div",{key:i,class:"mb-2.5 flex items-center"},[s(b,{class:"w-[600px]",label:"RMK"},{default:d(()=>[L(s(g,{modelValue:e(l).remarkList[i],"onUpdate:modelValue":t=>e(l).remarkList[i]=t},null,8,["modelValue","onUpdate:modelValue"]),[[B]])]),_:2},1024),i===0&&e(l).remarkList.length!==1||i>0?(h(),w("em",{key:0,class:"iconfont icon-minus-square text-[20px] relative text-undefined text-brand-2 mr-[5px] cursor-pointer",onClick:t=>e(S)(i)},null,8,yl)):N("",!0),i===e(l).remarkList.length-1&&i<2?(h(),w("em",{key:1,class:"iconfont icon-plus-square text-[20px] relative text-undefined text-brand-2 cursor-pointer",onClick:u[34]||(u[34]=(...t)=>e($)&&e($)(...t))})):N("",!0)]))),128)):N("",!0),a("div",Cl,r(o.$t("app.personal.qMailName")),1),s(b,null,{default:d(()=>[a("div",_l,[a("div",kl,r(o.$t("app.personal.defaultQmail")),1),s(P,{modelValue:e(l).defaultQmail,"onUpdate:modelValue":u[35]||(u[35]=m=>e(l).defaultQmail=m),"inline-prompt":"","active-text":o.$t("app.avSearch.openYes"),"inactive-text":o.$t("app.avSearch.openNo")},null,8,["modelValue","active-text","inactive-text"])])]),_:1}),e(V)?N("",!0):L((h(),w("div",$l,[a("div",wl,r(o.$t("app.personal.frequentlyAskedQuestionsEntrance")),1),s(H,{modelValue:e(l).place,"onUpdate:modelValue":u[36]||(u[36]=m=>e(l).place=m),class:"frequently-asked-questions"},{default:d(()=>[s(z,{label:"BOTTOM"},{default:d(()=>[G(r(o.$t("app.personal.suspendedDisplay")),1)]),_:1}),s(z,{label:"TOP"},{default:d(()=>[G(r(o.$t("app.personal.topFixedDisplay")),1)]),_:1})]),_:1},8,["modelValue"])])),[[te,"frequently-asked-questions-show"]])]),_:1},8,["model","rules","onKeyup"]),a("div",Tl,[s(ee,{size:"default",type:"primary","data-gid":"081W0103",onClick:e(c)},{default:d(()=>[G(r(o.$t("app.personal.save")),1)]),_:1},8,["onClick"])])])),[[ae,e(T)]])}}});const Nl=de(El,[["__scopeId","data-v-61f9779f"]]),Vl=ce({name:"PersonalCenter",components:{Information:os,UpdatePassword:ea,Preferences:Nl,ElTabs:Me,ElTabPane:Xe},setup(){const{t:n}=oe(),f=pe(),{user:_}=f.state,E=ye("isMini",!1),l=M(!1),V=async()=>(l.value=_.longTimeNoChangePwd,_&&(_.lastLoginTime===null||l.value||_.twoPwdMismatch)),c=M(!1),T=[{component:"",label:n("app.personal.accountMapping"),auth:"setting-personCenter-accountMapping"},{component:"",label:n("app.personal.OnTheFlight"),auth:"setting-personCenter-followFlight"},{component:"Preferences",label:n("app.personal.preferences"),auth:"setting-personCenter-preferences"}],k=M([]),p=M(""),I=async()=>{var o;const $=await f.getters.roleResource;k.value.push({component:"Information",label:n("app.personal.basicInformation"),auth:"setting-personalCenter-baseInfo"}),k.value.push({component:"UpdatePassword",label:n("app.personal.updatePwd"),auth:"setting-personCenter-updatePassword"}),T.forEach(u=>{$.includes(u.auth)&&k.value.push(u)}),p.value=((o=k.value[0])==null?void 0:o.label)??""},S=()=>O()==="en"?p.value===n("app.personal.preferences")?"tabs_short_en":"tabs_en":"";return Ce(async()=>{const o=(await f.getters.user).twoPwdMismatch;await V()||o?(c.value=!0,E.value||Nt()):c.value=!1,I()}),{onlyUpdatePassword:c,activeTab:p,centerComponents:k,getTabENClass:S,longTimeNoChangePwd:l}}});const Il={class:"user-center"};function Sl(n,f,_,E,l,V){const c=ve("UpdatePassword"),T=Xe,k=Me;return h(),w("div",Il,[n.onlyUpdatePassword?(h(),x(k,{key:0},{default:d(()=>[s(T,{label:n.$t("app.personal.updatePwd")},{default:d(()=>[s(c)]),_:1},8,["label"]),n.longTimeNoChangePwd?(h(),x(T,{key:0,disabled:"",label:n.$t("app.personal.longTimeChange")},null,8,["label"])):(h(),x(T,{key:1,disabled:"",label:n.$t("app.personal.updateFirst")},null,8,["label"]))]),_:1})):N("",!0),!n.onlyUpdatePassword&&n.centerComponents.length>0?(h(),x(k,{key:1,modelValue:n.activeTab,"onUpdate:modelValue":f[0]||(f[0]=p=>n.activeTab=p),class:D(n.getTabENClass())},{default:d(()=>[(h(!0),w(Y,null,le(n.centerComponents,p=>(h(),x(T,{key:p.component,label:p.label,name:p.label},{default:d(()=>[p.component.length>0?(h(),x(mt(p.component),{key:0,"active-tab":n.activeTab},null,8,["active-tab"])):N("",!0)]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue","class"])):N("",!0)])}const on=de(Vl,[["render",Sl],["__scopeId","data-v-b6f9bf98"]]);export{on as default};
