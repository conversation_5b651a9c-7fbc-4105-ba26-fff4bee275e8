import{ao as p,ap as l,q as ve,w as ke,x as g,B as y,P as t,Q as r,D as me,ai as _e,aj as De,ak as Ie,by as Re,am as $e,an as je}from"./index-18f146fc.js";import{_ as Te}from"./_plugin-vue_export-helper-c27b6911.js";import{u as qe}from"./TicketOriginalPopover.vue_vue_type_script_setup_true_lang-98b79173.js";const Ya=(s,o)=>p(`${l}/apiRefundTicket/findRefundTicket`,{headers:{gid:o}},{originalValue:!0}).post(s).json(),Za=(s,o)=>p(`${l}/apiRefundTicket/manualRefundTicket`,{headers:{gid:o}},{originalValue:!0}).post(s).json(),ts=(s,o)=>p(`${l}/apiRefundTicket/batchManualRefundTicket`,{headers:{gid:o}},{originalValue:!0}).post(s).json(),es=(s,o)=>p(`${l}/apiRefundTicket/previewRefundTicket`,{headers:{gid:o}}).post(s).json(),rs=(s,o)=>p(`${l}/crs/involuntary/queryPnrMessage`,{headers:{gid:o}},{ignoreError:!0}).post(s).json(),as=(s,o)=>p(`${l}/pnrManager/deletePnrAndDeleteInfantInfo`,{headers:{gid:o}}).post(s).json(),ss=(s,o)=>p(`${l}/apiRefundTicket/queryRtktDetail`,{headers:{gid:o}},{ignoreError:!0}).post(s).json(),os=(s,o)=>p(`${l}/apiRefundTicket/batchFindRefundFee`,{headers:{gid:o}}).post(s).json(),is=(s,o)=>p(`${l}/apiRefundTicket/checkPassengerInPnr`,{headers:{gid:o}}).post(s).json(),b=s=>($e("data-v-a1972225"),s=s(),je(),s),Ae={class:"w-full h-full overflow-auto"},Ce={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},Ne={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},we={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Fe={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Se={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Pe={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Oe={key:0,class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ve={key:1,class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Be={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Ee={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Me={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Qe={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},We={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ke={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Le={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},ze=b(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"OFFICE：",-1)),Je={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ue={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Ge={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},He={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Xe={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Ye={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ze={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},tr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},er={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},rr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},ar={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},sr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},or=Re('<div class="flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2" data-v-a1972225><div class="text-gray-2 text-[12px] font-bold leading-[20px]" data-v-a1972225></div><div class="text-gray-2 text-[12px] font-normal leading-[20px]" data-v-a1972225></div></div><div class="flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2" data-v-a1972225><div class="text-gray-2 text-[12px] font-bold leading-[20px]" data-v-a1972225></div><div class="text-gray-2 text-[12px] font-normal leading-[20px]" data-v-a1972225></div></div>',2),ir={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},dr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},nr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},pr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},lr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},xr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},cr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},gr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},yr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},fr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},br={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ur={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},kr={class:"col-span-4 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},_r={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},vr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},hr={class:"grid grid-cols-[28%_22%_28%_22%] auto-rows-[minmax(32px,auto)]"},mr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},Dr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ir={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Rr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},$r={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},jr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Tr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-b-0 border-gray-2"},qr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ar={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Cr={class:"flex justify-start items-center flex-wrap row-span-2 bg-gray-0 border border-b-0 border-gray-2"},Nr={class:"flex justify-start items-center h-[32px] px-[6px] py-[4px] w-full"},wr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Fr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Sr={class:"flex justify-start items-center h-[32px] px-[6px] py-[4px]"},Pr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Or={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Vr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Br={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Er={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Mr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Qr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Wr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Kr={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Lr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},zr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Jr={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},Ur={class:"col-span-1 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Gr={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Hr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Xr={class:"col-span-3 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},Yr={class:"text-gray-2 text-[12px] font-bold leading-[20px] break-keep"},Zr={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ta={class:"grid grid-cols-[22%_28%_28%_22%] auto-rows-[minmax(32px,auto)]"},ea={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},ra={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},aa={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},sa={class:"col-span-3 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},oa={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ia={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},da={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},na={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},pa={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},la={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},xa={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ca={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ga={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},ya={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},fa={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ba={class:"flex justify-start items-center p-1.5 py-2.5 bg-gray-0 border border-b-0 border-gray-2"},ua={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ka={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},_a={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},va={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},ha={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},ma={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-r-0 border-gray-2"},Da={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ia={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ra={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0 border-b-0"},$a={class:"text-gray-2 text-[12px] font-bold leading-[20px] whitespace-pre-wrap"},ja={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ta={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-b-0 border-gray-2"},qa={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Aa={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ca={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0"},Na=b(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"FP：",-1)),wa={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Fa={class:"flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2 border-r-0"},Sa=b(()=>t("div",{class:"text-gray-2 text-[12px] font-bold leading-[20px]"},"TC：",-1)),Pa={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Oa={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-gray-2"},Va={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Ba={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ea={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-r-0 border-t-0 border-gray-2"},Ma={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},Qa={key:0,class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Wa={key:1,class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ka={class:"col-span-2 flex justify-start items-center px-[6px] py-[4px] bg-gray-0 border border-t-0 border-gray-2"},La={class:"text-gray-2 text-[12px] font-bold leading-[20px]"},za={class:"text-gray-2 text-[12px] font-normal leading-[20px]"},Ja=ve({__name:"RTKTTable",props:{rtktDetailedInfo:{}},setup(s){const o=s,x=ke(()=>{var c,d;const e=((d=(c=o.rtktDetailedInfo)==null?void 0:c.price)==null?void 0:d.taxes)||[];if(e.length){const i=e.filter(n=>n.newOldRefundTax==="R");return i.length?i.map(n=>`${n.taxCode}:${n.currencyCode} ${n.taxAmount}`).join(" / "):"-"}return"-"}),f=ke(()=>{var c,d;const e=((d=(c=o.rtktDetailedInfo)==null?void 0:c.ticket)==null?void 0:d.ticketState)||"";return e==="void"||e==="refund"});return(e,c)=>{var d,i,n,u,k,_,v,h,m,D,I,R,$,j,T,q,A,C,N,w,F,S,P,O,V,B,E,M,Q,W,K,L,z,J,U,G,H,X,Y,Z,tt,et,rt,at,st,ot,it,dt,nt,pt,lt,xt,ct,gt,yt,ft,bt,ut,kt,_t,vt,ht,mt,Dt,It,Rt,$t,jt,Tt,qt,At,Ct,Nt,wt,Ft,St,Pt,Ot,Vt,Bt,Et,Mt,Qt,Wt,Kt,Lt,zt,Jt,Ut,Gt,Ht,Xt,Yt,Zt,te,ee,re,ae,se,oe,ie,de,ne,pe,le,xe,ce,ge,ye,fe,be,ue;return g(),y("div",Ae,[t("div",Ce,[t("div",Ne,[t("div",we,r(e.$t("app.queryRtkt.issueAirline")),1),t("div",Fe,r(((i=(d=e.rtktDetailedInfo)==null?void 0:d.ticket)==null?void 0:i.issueAirline)||"-"),1)]),t("div",Se,[t("div",Pe,r(e.$t("app.queryRtkt.jointTicket")),1),((u=(n=e.rtktDetailedInfo)==null?void 0:n.ticket)==null?void 0:u.ticketCount)>1?(g(),y("div",Oe,"CONJ TKT "+r(((_=(k=e.rtktDetailedInfo)==null?void 0:k.ticket)==null?void 0:_.conjunction)||"-")+" OF "+r(((h=(v=e.rtktDetailedInfo)==null?void 0:v.ticket)==null?void 0:h.ticketCount)||"-"),1)):(g(),y("div",Ve,"-"))]),t("div",Be,[t("div",Ee,r(e.$t("app.queryRtkt.issueMethod")),1),t("div",Me,r(((D=(m=e.rtktDetailedInfo)==null?void 0:m.ticket)==null?void 0:D.issueType)||"-"),1)]),t("div",Qe,[t("div",We,r(e.$t("app.queryRtkt.asa")),1),t("div",Ke,r(((R=(I=e.rtktDetailedInfo)==null?void 0:I.ticket)==null?void 0:R.iataCode)||"-"),1)]),t("div",Le,[ze,t("div",Je,r(((j=($=e.rtktDetailedInfo)==null?void 0:$.ticket)==null?void 0:j.office)||"-"),1)]),t("div",Ue,[t("div",Ge,r(e.$t("app.queryRtkt.code")),1),t("div",He,r(((q=(T=e.rtktDetailedInfo)==null?void 0:T.ticket)==null?void 0:q.code)||"-")+"/"+r(((C=(A=e.rtktDetailedInfo)==null?void 0:A.ticket)==null?void 0:C.channelCode)||"-"),1)]),t("div",Xe,[t("div",Ye,r(e.$t("app.queryRtkt.ticketState")),1),t("div",{class:me(["text-gray-2 text-[12px] font-normal leading-[20px]",f.value?"text-red-1":""])},r((w=(N=e.rtktDetailedInfo)==null?void 0:N.ticket)!=null&&w.ticketState?e.$t(`app.queryRtkt.${(S=(F=e.rtktDetailedInfo)==null?void 0:F.ticket)==null?void 0:S.ticketState}`):"-"),3)]),t("div",Ze,[t("div",tr,r(e.$t("app.queryRtkt.account")),1),t("div",er,r(((O=(P=e.rtktDetailedInfo)==null?void 0:P.ticket)==null?void 0:O.accountNumber)||"-")+" "+r(((B=(V=e.rtktDetailedInfo)==null?void 0:V.ticket)==null?void 0:B.cipher)||"-")+" "+r(((M=(E=e.rtktDetailedInfo)==null?void 0:E.ticket)==null?void 0:M.hourIndicator)||"-"),1)]),t("div",rr,[t("div",ar,r(e.$t("app.queryRtkt.issueDate")),1),t("div",sr,r(((W=(Q=e.rtktDetailedInfo)==null?void 0:Q.ticket)==null?void 0:W.issueDate)||"-"),1)]),or,t("div",ir,[t("div",dr,r(e.$t("app.queryRtkt.ticketNumber")),1),t("div",nr,r(((L=(K=e.rtktDetailedInfo)==null?void 0:K.ticket)==null?void 0:L.ticketNumber)||"-"),1)]),t("div",pr,[t("div",lr,r(e.$t("app.queryRtkt.deviceNumber")),1),t("div",xr,r((J=(z=e.rtktDetailedInfo)==null?void 0:z.ticket)!=null&&J.printNumber?`DEV-${(G=(U=e.rtktDetailedInfo)==null?void 0:U.ticket)==null?void 0:G.printNumber}`:"-"),1)]),t("div",cr,[t("div",gr,r(e.$t("app.queryRtkt.refundDeviceNumber")),1),t("div",yr,r((X=(H=e.rtktDetailedInfo)==null?void 0:H.ticket)!=null&&X.refundPrintNumber?`DEV-${(Z=(Y=e.rtktDetailedInfo)==null?void 0:Y.ticket)==null?void 0:Z.refundPrintNumber}`:"-"),1)]),t("div",fr,[t("div",br,r(e.$t("app.queryRtkt.trip")),1),t("div",ur,r(((et=(tt=e.rtktDetailedInfo)==null?void 0:tt.ticket)==null?void 0:et.origin)||"-")+r(((at=(rt=e.rtktDetailedInfo)==null?void 0:rt.ticket)==null?void 0:at.destination)||"-"),1)]),t("div",kr,[t("div",_r,r(e.$t("app.queryRtkt.eiItem")),1),t("div",vr,r(((ot=(st=e.rtktDetailedInfo)==null?void 0:st.ticket)==null?void 0:ot.ei)||"-"),1)])]),t("div",hr,[(g(!0),y(_e,null,De((dt=(it=e.rtktDetailedInfo)==null?void 0:it.passenger)==null?void 0:dt.segments,(a,he)=>(g(),y(_e,{key:he},[t("div",mr,[t("div",Dr,r(e.$t("app.queryRtkt.originAndDestination")),1),t("div",Ir,r(a.departureCityName)+"("+r(a.departureCity)+")-"+r(a.arrivalCityName)+"("+r(a.arrivalCity)+") ",1)]),t("div",Rr,[t("div",$r,r(e.$t("app.queryRtkt.flightNumber")),1),t("div",jr,r(a.flightNo?a.flightNo:"-"),1)]),t("div",Tr,[t("div",qr,r(e.$t("app.queryRtkt.departureDate")),1),t("div",Ar,r(a.departureDateTime?a.departureDateTime:"-"),1)]),t("div",Cr,[t("div",Nr,[t("div",wr,r(e.$t("app.queryRtkt.effectiveDate")),1),t("div",Fr,r(a.notValidBefore?a.notValidBefore:"-"),1)]),t("div",Sr,[t("div",Pr,r(e.$t("app.queryRtkt.expiryDate")),1),t("div",Or,r(a.notValidAfter?a.notValidAfter:"-"),1)])]),t("div",Vr,[t("div",Br,r(e.$t("app.queryRtkt.cabin")),1),t("div",Er,r(a!=null&&a.cabin?a==null?void 0:a.cabin:"-"),1)]),t("div",Mr,[t("div",Qr,r(e.$t("app.queryRtkt.fareBasis")),1),t("div",Wr,r(a!=null&&a.fareBasis?a==null?void 0:a.fareBasis:"-"),1)]),t("div",Kr,[t("div",Lr,r(e.$t("app.queryRtkt.baggage")),1),t("div",zr,r(a!=null&&a.baggage?a==null?void 0:a.baggage:"-"),1)])],64))),128))]),t("div",Jr,[t("div",Ur,[t("div",Gr,r(e.$t("app.queryRtkt.fareFlag")),1),t("div",Hr,r((pt=(nt=e.rtktDetailedInfo)==null?void 0:nt.price)!=null&&pt.autoFareType?e.$t("app.queryRtkt.auto"):e.$t("app.queryRtkt.manual")),1)]),t("div",Xr,[t("div",Yr,r(e.$t("app.queryRtkt.fcItem")),1),t("div",Zr,r(((xt=(lt=e.rtktDetailedInfo)==null?void 0:lt.price)==null?void 0:xt.fc)||"-"),1)])]),t("div",ta,[t("div",ea,[t("div",ra,r(e.$t("app.queryRtkt.totalTax")),1),t("div",aa,r(((gt=(ct=e.rtktDetailedInfo)==null?void 0:ct.price)==null?void 0:gt.taxAmount)||"-"),1)]),t("div",sa,[t("div",oa,r(e.$t("app.queryRtkt.taxDetails")),1),t("div",ia,r(((ft=(yt=e.rtktDetailedInfo)==null?void 0:yt.price)==null?void 0:ft.taxDetail)||"-"),1)]),t("div",da,[t("div",na,r(e.$t("app.queryRtkt.ticketAmountFR")),1),t("div",pa,r(((ut=(bt=e.rtktDetailedInfo)==null?void 0:bt.price)==null?void 0:ut.ticketAmountFOrRAmount)||"-"),1)]),t("div",la,[t("div",xa,r(e.$t("app.queryRtkt.ticketAmountE")),1),t("div",ca,r(((_t=(kt=e.rtktDetailedInfo)==null?void 0:kt.price)==null?void 0:_t.ticketAmount)||"-"),1)]),t("div",ga,[t("div",ya,r(e.$t("app.queryRtkt.commissionFare")),1),t("div",fa,r(((ht=(vt=e.rtktDetailedInfo)==null?void 0:vt.ticket)==null?void 0:ht.internationalIndicator)==="D"?(Dt=(mt=e.rtktDetailedInfo)==null?void 0:mt.price)==null?void 0:Dt.commissionFare:"-"),1)]),t("div",ba,[t("div",ua,r(e.$t("app.queryRtkt.commissionRate")),1),t("div",ka,r(((Rt=(It=e.rtktDetailedInfo)==null?void 0:It.ticket)==null?void 0:Rt.internationalIndicator)==="I"?(jt=($t=e.rtktDetailedInfo)==null?void 0:$t.price)==null?void 0:jt.commissionRate:"-"),1)]),t("div",_a,[t("div",va,r(e.$t("app.queryRtkt.scny"))+"：",1),t("div",ha,r(((qt=(Tt=e.rtktDetailedInfo)==null?void 0:Tt.price)==null?void 0:qt.scny)||"-"),1)]),t("div",ma,[t("div",Da,r(e.$t("app.queryRtkt.fareAmount")),1),t("div",Ia,r(((Ct=(At=e.rtktDetailedInfo)==null?void 0:At.price)==null?void 0:Ct.fareAmount)||"-"),1)]),t("div",Ra,[t("div",$a,[Ie(r(e.$t("app.queryRtkt.issueAirlineCode"))+" ",1),t("span",ja,r(((wt=(Nt=e.rtktDetailedInfo)==null?void 0:Nt.ticket)==null?void 0:wt.issueAirlineCode)||"-"),1)])]),t("div",Ta,[t("div",qa,r(e.$t("app.queryRtkt.gpSign")),1),t("div",Aa,r(((St=(Ft=e.rtktDetailedInfo)==null?void 0:Ft.ticket)==null?void 0:St.ticketManagementOrganizationCode)??"-")+" "+r(((Ot=(Pt=e.rtktDetailedInfo)==null?void 0:Pt.ticket)==null?void 0:Ot.ticketType)!=="NONE"?"-":"")+" "+r((((Bt=(Vt=e.rtktDetailedInfo)==null?void 0:Vt.ticket)==null?void 0:Bt.ticketType)==="NONE"?"":(Mt=(Et=e.rtktDetailedInfo)==null?void 0:Et.ticket)==null?void 0:Mt.ticketType)??""),1)]),t("div",Ca,[Na,t("div",wa,r(((Wt=(Qt=e.rtktDetailedInfo)==null?void 0:Qt.price)==null?void 0:Wt.formOfPaymentText)||"-"),1)]),t("div",Fa,[Sa,t("div",Pa,r(((Lt=(Kt=e.rtktDetailedInfo)==null?void 0:Kt.price)==null?void 0:Lt.tc)||"-"),1)]),t("div",Oa,[t("div",Va,r(e.$t("app.queryRtkt.creditCard")),1),t("div",Ba,r(((Jt=(zt=e.rtktDetailedInfo)==null?void 0:zt.price)==null?void 0:Jt.creditCardDetail)||"-"),1)]),t("div",Ea,[t("div",Ma,r(e.$t("app.queryRtkt.originalTicket")),1),!((Ht=(Gt=(Ut=e.rtktDetailedInfo)==null?void 0:Ut.ticket)==null?void 0:Gt.originalTicketInfo)!=null&&Ht.ticketNumber)&&!((Zt=(Yt=(Xt=e.rtktDetailedInfo)==null?void 0:Xt.ticket)==null?void 0:Yt.originalTicketInfo)!=null&&Zt.cityCode)&&!((re=(ee=(te=e.rtktDetailedInfo)==null?void 0:te.ticket)==null?void 0:ee.originalTicketInfo)!=null&&re.issueDate)&&!((oe=(se=(ae=e.rtktDetailedInfo)==null?void 0:ae.ticket)==null?void 0:se.originalTicketInfo)!=null&&oe.iataNumber)?(g(),y("div",Qa," - ")):(g(),y("div",Wa,r(((ne=(de=(ie=e.rtktDetailedInfo)==null?void 0:ie.ticket)==null?void 0:de.originalTicketInfo)==null?void 0:ne.ticketNumber)||"-")+" "+r(((xe=(le=(pe=e.rtktDetailedInfo)==null?void 0:pe.ticket)==null?void 0:le.originalTicketInfo)==null?void 0:xe.cityCode)||"-")+" "+r(((ye=(ge=(ce=e.rtktDetailedInfo)==null?void 0:ce.ticket)==null?void 0:ge.originalTicketInfo)==null?void 0:ye.issueDate)||"-")+" "+r(((ue=(be=(fe=e.rtktDetailedInfo)==null?void 0:fe.ticket)==null?void 0:be.originalTicketInfo)==null?void 0:ue.iataNumber)||"-"),1))]),t("div",Ka,[t("div",La,r(e.$t("app.queryRtkt.detailsOfTaxRefund")),1),t("div",za,r(x.value),1)])])])}}});const ds=Te(Ja,[["__scopeId","data-v-a1972225"]]),Ua=t("i",{class:"iconfont icon-windowed mr-[2px]"},null,-1),ns=ve({__name:"RtktWindoing",props:{rtktDetailedInfo:{}},emits:["openRtktDetailWindow"],setup(s,{emit:o}){const x=s,f=o,e=qe(),c=async()=>{f("openRtktDetailWindow");const d=x.rtktDetailedInfo.ticket.ticketNumber.indexOf(x.rtktDetailedInfo.ticket.issueAirlineCode);let i="";d===0&&(i=x.rtktDetailedInfo.ticket.ticketNumber.substring(d+x.rtktDetailedInfo.ticket.issueAirlineCode.length)),i=`${x.rtktDetailedInfo.ticket.issueAirlineCode}-${i}`,await e.delRtktDetailInfoWindowsList(i),e.setRtktDetailInfoWindowsList({...x.rtktDetailedInfo,id:i}),e.closeFastQuery()};return(d,i)=>(g(),y("div",{class:"open-detail-dialog flex items-center text-brand-2 text-[12px] cursor-pointer",onClick:i[0]||(i[0]=n=>c())},[Ua,t("div",null,r(d.$t("app.fastQuery.windowing")),1)]))}});export{ds as R,ns as _,rs as a,os as b,ts as c,is as d,es as e,Za as m,Ya as o,as as p,ss as q};
