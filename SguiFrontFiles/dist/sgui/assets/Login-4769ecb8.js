import{b6 as J,r as y,ab as ee,o as te,jV as Re,jW as Nt,jX as Mt,q as G,al as le,ah as ve,w as ne,x as R,B as D,G as w,z as _,a5 as we,a6 as Ve,P as m,Q as $,am as ce,an as ue,av as He,jY as At,A as p,H as tt,jZ as ze,D as Oe,ax as ot,ai as it,aj as St,ak as X,aJ as j,ed as lt,ac as Ge,J as H,y as B,j_ as Dt,b3 as ct,cv as at,a8 as Me,az as Ke,cd as Ft,as as Tt,au as Qe,aX as st,ca as nt,j$ as Ut,bJ as Bt,b5 as Vt,b2 as zt,k0 as Ot,k1 as Qt,k2 as xt,b7 as Ie,b9 as Te,E as xe,C as qt,k3 as Ht,k4 as Gt,k5 as Kt,by as Wt,k6 as Yt,k7 as Xt,k8 as jt,a4 as ge}from"./index-18f146fc.js";import{u as ut,a as Jt,l as rt,b as dt,i as qe,c as $e,e as Zt}from"./common-9b69fd00.js";import{a as Ce,E as re}from"./index-c5503643.js";import{E as ye}from"./index-93952dc4.js";import{E as _e}from"./index-cb25ab55.js";import{_ as de}from"./_plugin-vue_export-helper-c27b6911.js";import{g as Le}from"./encrypt-4212e77b.js";import{U as eo}from"./passwordValid-45ecf3f8.js";import{e as to}from"./browserEvent-e8caf045.js";import{I as oo}from"./I18n-f51b7284.js";import{V as ao}from"./index.min-11305384.js";import{E as so}from"./index-385c3d86.js";import{E as no}from"./index-9b639e2a.js";import{ac as ro,ad as io}from"./regular-crs-4d4d60ea.js";import"./castArray-f685dae0.js";import"./index-a197ff1b.js";import"./index-6ea30548.js";import"./isUndefined-aa0326a0.js";import"./index-1c4b8a79.js";import"./dropdown-bbbdd88c.js";import"./refs-649593ac.js";import"./isEqual-9c56e106.js";import"./flatten-1142070a.js";const Ue={userNameInput:"",checkCodeInput:""},Ee=J({userName:"",verificationCode:"",captcha:""}),pt=y(`${Re}?rdm=${Math.random()}`),Be=()=>{pt.value=`${Re}?rdm=${Math.random()}`},ft=a=>{const{t:r}=ee(),d=y(!1),c=y(!1),u=J({findPasswordContent:r("app.retrievePassword.getVerifyCode"),resetLoading:!1,nextLoading:!1,nextCanClick:!0,findPasswordCanClick:!0}),{checkCodeInput:f,startCount:n,isDisabled:e}=ut("{0}s",u.findPasswordContent),t=y(),o=async h=>{u.resetLoading=!0;try{c.value=!1;const P=window.location.pathname.includes("crs")?"C":"";await Mt({username:Ee.userName,verificationCode:h,system:P},"09300120"),n(),d.value=!0,a.emit("changeBtn",!1),setTimeout(()=>{d.value=!1,a.emit("changeBtn",!0)},3e3),u.resetLoading=!1,u.nextCanClick=!1}catch{Be(),c.value=!0}},s=h=>{a.emit("reset"),a.emit("chagepagestatus",h)},i=h=>{t.value&&t.value.validateField(["userName","verificationCode"],P=>{P?(o(h),c.value=!0):u.nextLoading=!1})},l=async h=>{if(!u.nextLoading){Ue.userNameInput=Ee.userName,Ue.checkCodeInput=Ee.captcha,u.nextLoading=!0;try{await Nt(Ue,"09300121"),u.nextLoading=!1,a.emit("chagepagestatus",h)}catch{u.nextLoading=!1}}},g=h=>{t.value&&t.value.validate(P=>{P?l(h):u.nextLoading=!1})};return te(()=>{Be()}),{findPassword:Ee,findPasswordPageInfo:u,goBack:s,checkCodeInput:f,isDisabled:e,countDownFindPassword:i,nextFormFindPass:g,nextFormFindPassHandle:l,findPasswordForm:t,showMessage:d,captchaUrl:pt,captchaChange:Be,isError:c}},lo=()=>{const{t:a}=ee(),r=a("app.retrievePassword.pleaseInputUserName"),d=a("app.retrievePassword.usernameLength"),c=a("app.retrievePassword.pleaseInputVerifyCode"),u=a("app.retrievePassword.verifyFormat");return{findPasswordRules:J({userName:[{required:!0,message:r,trigger:"blur"},{max:50,message:d,trigger:"blur"}],captcha:[{required:!0,message:a("app.retrievePassword.pleaseInputVerifyMegCode"),trigger:"blur"},{pattern:/^[0-9a-zA-Z]{6}$/,message:u,trigger:"blur"}],verificationCode:[{required:!0,message:c,trigger:"blur"},{min:4,max:4,message:a("app.retrievePassword.length"),trigger:"blur"}]})}},co=G({name:"PasswordRetrieval",components:{ElButton:le,ElForm:Ce,ElFormItem:re,ElRow:ye,ElCol:_e,ElInput:ve},emits:["changeBtn","chagepagestatus"],setup(a,r){const d=J({username:y(""),certificate:y("")}),c=ne(()=>window.location.pathname.includes("crs")??!1),{findPassword:u,findPasswordPageInfo:f,goBack:n,checkCodeInput:e,isDisabled:t,countDownFindPassword:o,nextFormFindPass:s,findPasswordForm:i,showMessage:l,captchaUrl:g,captchaChange:h,isError:P}=ft(r),{findPasswordRules:C}=lo();return{formInline:d,findPassword:u,findPasswordPageInfo:f,goBack:n,countDownFindPassword:o,nextFormFindPass:s,findPasswordRules:C,findPasswordForm:i,checkCodeInput:e,isDisabled:t,showMessage:l,captchaUrl:g,captchaChange:h,isError:P,isCrs:c}}});const Ae=a=>(ce("data-v-32001fe3"),a=a(),ue(),a),uo={class:"retrieve-password"},po={class:"showTip"},fo=Ae(()=>m("em",{class:"iconfont icon-info-circle-fill"},null,-1)),go={class:"tip"},mo={class:"anchor-link"},vo={class:"titleCapcha"},ho=Ae(()=>m("em",{class:"iconfont icon-loginuser"},null,-1)),wo={class:"flex-row"},_o=Ae(()=>m("em",{class:"iconfont icon-loginverify"},null,-1)),Co=["src"],yo={class:"flexStyle"},Po=Ae(()=>m("em",{class:"iconfont icon-loginverify"},null,-1)),bo={key:0},ko={key:1},Eo={key:0},Ro={key:1};function Io(a,r,d,c,u,f){const n=_e,e=ye,t=ve,o=re,s=le,i=Ce;return R(),D("div",uo,[w(i,{key:"formFindPassword",ref:"findPasswordForm",class:"form",model:a.findPassword,rules:a.findPasswordRules},{default:_(()=>[we(m("div",po,[fo,m("span",go,$(a.$t("app.retrievePassword.sentCode")),1)],512),[[Ve,a.showMessage]]),m("div",mo,[w(e,null,{default:_(()=>[w(n,{span:9},{default:_(()=>[m("div",{class:"linkBack",onClick:r[0]||(r[0]=l=>a.goBack("pageLogin"))},"< "+$(a.$t("app.retrievePassword.back")),1)]),_:1}),w(n,{span:15},{default:_(()=>[m("span",vo,$(a.$t("app.retrievePassword.findPassword")),1)]),_:1})]),_:1})]),w(o,{prop:"userName"},{default:_(()=>[w(t,{modelValue:a.findPassword.userName,"onUpdate:modelValue":r[1]||(r[1]=l=>a.findPassword.userName=l),size:"large",type:"text",clearable:"",placeholder:a.isCrs?a.$t("app.retrievePassword.userNameCRS"):a.$t("app.retrievePassword.userName")},{prefix:_(()=>[ho]),_:1},8,["modelValue","placeholder"])]),_:1}),w(o,{prop:"verificationCode"},{default:_(()=>[m("div",wo,[w(t,{modelValue:a.findPassword.verificationCode,"onUpdate:modelValue":r[2]||(r[2]=l=>a.findPassword.verificationCode=l),clearable:"",placeholder:a.$t("app.login.verifyCode"),size:"large",class:"findPasswordCaptcha"},{prefix:_(()=>[_o]),_:1},8,["modelValue","placeholder"]),m("img",{class:"captcha-img",src:a.captchaUrl,alt:"",onClick:r[3]||(r[3]=l=>a.captchaChange())},null,8,Co)])]),_:1}),w(o,{prop:"captcha"},{default:_(()=>[m("div",yo,[w(t,{modelValue:a.findPassword.captcha,"onUpdate:modelValue":r[4]||(r[4]=l=>a.findPassword.captcha=l),type:"text",size:"large",class:"findPasswordCaptcha",placeholder:a.$t("app.retrievePassword.verification")},{prefix:_(()=>[Po]),_:1},8,["modelValue","placeholder"]),w(s,{type:"primary",class:"btn-verify",disabled:a.isDisabled,"data-gid":"081V0103",onClick:r[5]||(r[5]=l=>a.countDownFindPassword(a.findPassword.verificationCode))},{default:_(()=>[a.isDisabled?(R(),D("span",ko,$(a.checkCodeInput),1)):(R(),D("span",bo,$(a.$t("app.retrievePassword.getVerifyCode")),1))]),_:1},8,["disabled"])])]),_:1}),w(o,null,{default:_(()=>[w(s,{class:"next-btn",type:"primary",size:"large",style:{width:"100%",height:"40px","font-size":"14px"},disabled:a.findPasswordPageInfo.nextCanClick,onClick:r[6]||(r[6]=l=>a.nextFormFindPass("pageResetPassword"))},{default:_(()=>[a.findPasswordPageInfo.nextLoading?(R(),D("span",Ro,"Loading...")):(R(),D("span",Eo,$(a.$t("app.retrievePassword.next")),1))]),_:1},8,["disabled"])]),_:1})]),_:1},8,["model","rules"])])}const $o=de(co,[["render",Io],["__scopeId","data-v-32001fe3"]]),Lo=a=>{const{t:r}=ee(),{validVaried:d,validContinuity:c,validKeyboard:u,validRepeat:f}=eo(),n=J({certificate:"",recertificate:""}),e=J({resetConfLoading:!1}),t=async C=>{if(!e.resetConfLoading){e.resetConfLoading=!0;try{const v=He("09300119");await At({certificate:Le(n.certificate),recertificate:Le(n.recertificate)},v),e.resetConfLoading=!1,a("chagepagestatus",C)}catch(v){e.resetConfLoading=!1,["SGUI-0137-14","SGUI-0138-15","SGUI-07-83W11"].includes(v.code)&&a("chagepagestatus",C)}}},o=(C,v,E)=>{(d(v)||c(v)||u(v)||f(v))&&E(new Error(r("app.ResetPassword.strongPwd"))),E()},s=(C,v,E)=>{n.certificate!==n.recertificate?E(new Error(r("app.ResetPassword.notSame"))):E()},i=J({certificate:[{required:!0,message:r("app.ResetPassword.newPwdTip"),trigger:"blur"},{min:8,max:16,message:r("app.ResetPassword.strongPwd"),trigger:"blur"},{validator:o,trigger:"blur"}],recertificate:[{required:!0,message:r("app.ResetPassword.confirmNewPwd"),trigger:"blur"},{min:8,max:16,message:r("app.ResetPassword.confirmNewPwdIsStrong"),trigger:"blur"},{validator:s,trigger:"blur"},{validator:o,trigger:"blur"}]}),l=y(),g=[r("app.personal.oauthTokenRule1"),r("app.personal.oauthTokenRule2"),r("app.personal.oauthTokenRule3"),r("app.personal.oauthTokenRule4"),r("app.personal.oauthTokenRule5")];return{resetPasswordRules:i,resetPasswordReq:n,resetPasswordInfo:e,goBack:C=>{a("reset"),a("chagepagestatus",C)},resetPasswordSubmit:C=>{l.value&&l.value.validate(v=>{v&&t(C)})},resetPasswordForm:l,validArray:g}},gt=Lo,mt=a=>(ce("data-v-fe1fa4d2"),a=a(),ue(),a),No={class:"retrieve-password"},Mo={class:"anchor-link"},Ao={class:"titleCapcha"},So=mt(()=>m("div",{class:"message-left"},null,-1)),Do={class:"message-ul"},Fo=mt(()=>m("span",null,null,-1)),To={key:0},Uo={key:1},Bo={name:"ResetPassword"},Vo=G({...Bo,emits:["chagepagestatus","reset"],setup(a,{emit:r}){const d=r,{resetPasswordRules:c,resetPasswordReq:u,resetPasswordInfo:f,goBack:n,resetPasswordSubmit:e,resetPasswordForm:t,validArray:o}=gt(d);return(s,i)=>(R(),D("div",No,[w(p(Ce),{key:"formResetPassword",ref_key:"resetPasswordForm",ref:t,class:"form",rules:p(c),model:p(u)},{default:_(()=>[m("div",Mo,[w(p(ye),null,{default:_(()=>[w(p(_e),{span:9},{default:_(()=>[m("div",{class:"linkBack",onClick:i[0]||(i[0]=l=>p(n)("pagePasswordRetrieval"))},"< "+$(s.$t("app.ResetPassword.back")),1)]),_:1}),w(p(_e),{span:15},{default:_(()=>[m("span",Ao,$(s.$t("app.ResetPassword.findPassword")),1)]),_:1})]),_:1})]),w(p(re),{prop:"certificate",class:"certificate-item"},{default:_(()=>[w(p(ve),{modelValue:p(u).certificate,"onUpdate:modelValue":i[1]||(i[1]=l=>p(u).certificate=l),size:"large",type:"password",clearable:"",placeholder:s.$t("app.ResetPassword.newPassword"),"show-password":""},{default:_(()=>[w(p(tt),null,{default:_(()=>[w(p(ze))]),_:1})]),_:1},8,["modelValue","placeholder"])]),_:1}),w(p(re),{prop:"recertificate"},{default:_(()=>[w(p(ve),{modelValue:p(u).recertificate,"onUpdate:modelValue":i[2]||(i[2]=l=>p(u).recertificate=l),size:"large",type:"password",placeholder:s.$t("app.ResetPassword.newPasswordAgain"),"show-password":""},{default:_(()=>[w(p(tt),null,{default:_(()=>[w(p(ze))]),_:1})]),_:1},8,["modelValue","placeholder"])]),_:1}),w(p(re),null,{default:_(()=>[m("div",{class:Oe(["valid",p(ot)()==="en"?"en-valid-h":"valid-h"])},[m("div",{class:Oe(["message",p(ot)()==="en"?"en-message":""])},[So,m("div",Do,[m("ul",null,[(R(!0),D(it,null,St(p(o),(l,g)=>(R(),D("li",{key:g,class:"default"},[Fo,X(" "+$(l),1)]))),128))])])],2)],2),w(p(le),{type:"primary",size:"large",style:{width:"100%",height:"40px","font-size":"14px"},onClick:i[3]||(i[3]=l=>p(e)("pageLogin"))},{default:_(()=>[p(f).resetConfLoading?(R(),D("span",Uo,"Loading...")):(R(),D("span",To,$(s.$t("app.ResetPassword.sure")),1))]),_:1})]),_:1})]),_:1},8,["rules","model"])]))}});const zo=de(Vo,[["__scopeId","data-v-fe1fa4d2"]]);/*!
 * qrcode.vue v3.4.1
 * A Vue.js component to generate QRCode.
 * © 2017-2023 @scopewu(https://github.com/scopewu)
 * MIT License.
 */var Ne=function(){return Ne=Object.assign||function(r){for(var d,c=1,u=arguments.length;c<u;c++){d=arguments[c];for(var f in d)Object.prototype.hasOwnProperty.call(d,f)&&(r[f]=d[f])}return r},Ne.apply(this,arguments)};var ie;(function(a){var r=function(){function n(e,t,o,s){if(this.version=e,this.errorCorrectionLevel=t,this.modules=[],this.isFunction=[],e<n.MIN_VERSION||e>n.MAX_VERSION)throw new RangeError("Version value out of range");if(s<-1||s>7)throw new RangeError("Mask value out of range");this.size=e*4+17;for(var i=[],l=0;l<this.size;l++)i.push(!1);for(var l=0;l<this.size;l++)this.modules.push(i.slice()),this.isFunction.push(i.slice());this.drawFunctionPatterns();var g=this.addEccAndInterleave(o);if(this.drawCodewords(g),s==-1)for(var h=1e9,l=0;l<8;l++){this.applyMask(l),this.drawFormatBits(l);var P=this.getPenaltyScore();P<h&&(s=l,h=P),this.applyMask(l)}u(0<=s&&s<=7),this.mask=s,this.applyMask(s),this.drawFormatBits(s),this.isFunction=[]}return n.encodeText=function(e,t){var o=a.QrSegment.makeSegments(e);return n.encodeSegments(o,t)},n.encodeBinary=function(e,t){var o=a.QrSegment.makeBytes(e);return n.encodeSegments([o],t)},n.encodeSegments=function(e,t,o,s,i,l){if(o===void 0&&(o=1),s===void 0&&(s=40),i===void 0&&(i=-1),l===void 0&&(l=!0),!(n.MIN_VERSION<=o&&o<=s&&s<=n.MAX_VERSION)||i<-1||i>7)throw new RangeError("Invalid value");var g,h;for(g=o;;g++){var P=n.getNumDataCodewords(g,t)*8,C=f.getTotalBits(e,g);if(C<=P){h=C;break}if(g>=s)throw new RangeError("Data too long")}for(var v=0,E=[n.Ecc.MEDIUM,n.Ecc.QUARTILE,n.Ecc.HIGH];v<E.length;v++){var A=E[v];l&&h<=n.getNumDataCodewords(g,A)*8&&(t=A)}for(var L=[],M=0,F=e;M<F.length;M++){var z=F[M];d(z.mode.modeBits,4,L),d(z.numChars,z.mode.numCharCountBits(g),L);for(var V=0,x=z.getData();V<x.length;V++){var pe=x[V];L.push(pe)}}u(L.length==h);var q=n.getNumDataCodewords(g,t)*8;u(L.length<=q),d(0,Math.min(4,q-L.length),L),d(0,(8-L.length%8)%8,L),u(L.length%8==0);for(var K=236;L.length<q;K^=253)d(K,8,L);for(var W=[];W.length*8<L.length;)W.push(0);return L.forEach(function(Y,U){return W[U>>>3]|=Y<<7-(U&7)}),new n(g,t,W,i)},n.prototype.getModule=function(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]},n.prototype.getModules=function(){return this.modules},n.prototype.drawFunctionPatterns=function(){for(var e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);for(var t=this.getAlignmentPatternPositions(),o=t.length,e=0;e<o;e++)for(var s=0;s<o;s++)e==0&&s==0||e==0&&s==o-1||e==o-1&&s==0||this.drawAlignmentPattern(t[e],t[s]);this.drawFormatBits(0),this.drawVersion()},n.prototype.drawFormatBits=function(e){for(var t=this.errorCorrectionLevel.formatBits<<3|e,o=t,s=0;s<10;s++)o=o<<1^(o>>>9)*1335;var i=(t<<10|o)^21522;u(i>>>15==0);for(var s=0;s<=5;s++)this.setFunctionModule(8,s,c(i,s));this.setFunctionModule(8,7,c(i,6)),this.setFunctionModule(8,8,c(i,7)),this.setFunctionModule(7,8,c(i,8));for(var s=9;s<15;s++)this.setFunctionModule(14-s,8,c(i,s));for(var s=0;s<8;s++)this.setFunctionModule(this.size-1-s,8,c(i,s));for(var s=8;s<15;s++)this.setFunctionModule(8,this.size-15+s,c(i,s));this.setFunctionModule(8,this.size-8,!0)},n.prototype.drawVersion=function(){if(!(this.version<7)){for(var e=this.version,t=0;t<12;t++)e=e<<1^(e>>>11)*7973;var o=this.version<<12|e;u(o>>>18==0);for(var t=0;t<18;t++){var s=c(o,t),i=this.size-11+t%3,l=Math.floor(t/3);this.setFunctionModule(i,l,s),this.setFunctionModule(l,i,s)}}},n.prototype.drawFinderPattern=function(e,t){for(var o=-4;o<=4;o++)for(var s=-4;s<=4;s++){var i=Math.max(Math.abs(s),Math.abs(o)),l=e+s,g=t+o;0<=l&&l<this.size&&0<=g&&g<this.size&&this.setFunctionModule(l,g,i!=2&&i!=4)}},n.prototype.drawAlignmentPattern=function(e,t){for(var o=-2;o<=2;o++)for(var s=-2;s<=2;s++)this.setFunctionModule(e+s,t+o,Math.max(Math.abs(s),Math.abs(o))!=1)},n.prototype.setFunctionModule=function(e,t,o){this.modules[t][e]=o,this.isFunction[t][e]=!0},n.prototype.addEccAndInterleave=function(e){var t=this.version,o=this.errorCorrectionLevel;if(e.length!=n.getNumDataCodewords(t,o))throw new RangeError("Invalid argument");for(var s=n.NUM_ERROR_CORRECTION_BLOCKS[o.ordinal][t],i=n.ECC_CODEWORDS_PER_BLOCK[o.ordinal][t],l=Math.floor(n.getNumRawDataModules(t)/8),g=s-l%s,h=Math.floor(l/s),P=[],C=n.reedSolomonComputeDivisor(i),v=0,E=0;v<s;v++){var A=e.slice(E,E+h-i+(v<g?0:1));E+=A.length;var L=n.reedSolomonComputeRemainder(A,C);v<g&&A.push(0),P.push(A.concat(L))}for(var M=[],F=function(z){P.forEach(function(V,x){(z!=h-i||x>=g)&&M.push(V[z])})},v=0;v<P[0].length;v++)F(v);return u(M.length==l),M},n.prototype.drawCodewords=function(e){if(e.length!=Math.floor(n.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");for(var t=0,o=this.size-1;o>=1;o-=2){o==6&&(o=5);for(var s=0;s<this.size;s++)for(var i=0;i<2;i++){var l=o-i,g=(o+1&2)==0,h=g?this.size-1-s:s;!this.isFunction[h][l]&&t<e.length*8&&(this.modules[h][l]=c(e[t>>>3],7-(t&7)),t++)}}u(t==e.length*8)},n.prototype.applyMask=function(e){if(e<0||e>7)throw new RangeError("Mask value out of range");for(var t=0;t<this.size;t++)for(var o=0;o<this.size;o++){var s=void 0;switch(e){case 0:s=(o+t)%2==0;break;case 1:s=t%2==0;break;case 2:s=o%3==0;break;case 3:s=(o+t)%3==0;break;case 4:s=(Math.floor(o/3)+Math.floor(t/2))%2==0;break;case 5:s=o*t%2+o*t%3==0;break;case 6:s=(o*t%2+o*t%3)%2==0;break;case 7:s=((o+t)%2+o*t%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[t][o]&&s&&(this.modules[t][o]=!this.modules[t][o])}},n.prototype.getPenaltyScore=function(){for(var e=0,t=0;t<this.size;t++){for(var o=!1,s=0,i=[0,0,0,0,0,0,0],l=0;l<this.size;l++)this.modules[t][l]==o?(s++,s==5?e+=n.PENALTY_N1:s>5&&e++):(this.finderPenaltyAddHistory(s,i),o||(e+=this.finderPenaltyCountPatterns(i)*n.PENALTY_N3),o=this.modules[t][l],s=1);e+=this.finderPenaltyTerminateAndCount(o,s,i)*n.PENALTY_N3}for(var l=0;l<this.size;l++){for(var o=!1,g=0,i=[0,0,0,0,0,0,0],t=0;t<this.size;t++)this.modules[t][l]==o?(g++,g==5?e+=n.PENALTY_N1:g>5&&e++):(this.finderPenaltyAddHistory(g,i),o||(e+=this.finderPenaltyCountPatterns(i)*n.PENALTY_N3),o=this.modules[t][l],g=1);e+=this.finderPenaltyTerminateAndCount(o,g,i)*n.PENALTY_N3}for(var t=0;t<this.size-1;t++)for(var l=0;l<this.size-1;l++){var h=this.modules[t][l];h==this.modules[t][l+1]&&h==this.modules[t+1][l]&&h==this.modules[t+1][l+1]&&(e+=n.PENALTY_N2)}for(var P=0,C=0,v=this.modules;C<v.length;C++){var E=v[C];P=E.reduce(function(M,F){return M+(F?1:0)},P)}var A=this.size*this.size,L=Math.ceil(Math.abs(P*20-A*10)/A)-1;return u(0<=L&&L<=9),e+=L*n.PENALTY_N4,u(0<=e&&e<=2568888),e},n.prototype.getAlignmentPatternPositions=function(){if(this.version==1)return[];for(var e=Math.floor(this.version/7)+2,t=this.version==32?26:Math.ceil((this.version*4+4)/(e*2-2))*2,o=[6],s=this.size-7;o.length<e;s-=t)o.splice(1,0,s);return o},n.getNumRawDataModules=function(e){if(e<n.MIN_VERSION||e>n.MAX_VERSION)throw new RangeError("Version number out of range");var t=(16*e+128)*e+64;if(e>=2){var o=Math.floor(e/7)+2;t-=(25*o-10)*o-55,e>=7&&(t-=36)}return u(208<=t&&t<=29648),t},n.getNumDataCodewords=function(e,t){return Math.floor(n.getNumRawDataModules(e)/8)-n.ECC_CODEWORDS_PER_BLOCK[t.ordinal][e]*n.NUM_ERROR_CORRECTION_BLOCKS[t.ordinal][e]},n.reedSolomonComputeDivisor=function(e){if(e<1||e>255)throw new RangeError("Degree out of range");for(var t=[],o=0;o<e-1;o++)t.push(0);t.push(1);for(var s=1,o=0;o<e;o++){for(var i=0;i<t.length;i++)t[i]=n.reedSolomonMultiply(t[i],s),i+1<t.length&&(t[i]^=t[i+1]);s=n.reedSolomonMultiply(s,2)}return t},n.reedSolomonComputeRemainder=function(e,t){for(var o=t.map(function(h){return 0}),s=function(h){var P=h^o.shift();o.push(0),t.forEach(function(C,v){return o[v]^=n.reedSolomonMultiply(C,P)})},i=0,l=e;i<l.length;i++){var g=l[i];s(g)}return o},n.reedSolomonMultiply=function(e,t){if(e>>>8||t>>>8)throw new RangeError("Byte out of range");for(var o=0,s=7;s>=0;s--)o=o<<1^(o>>>7)*285,o^=(t>>>s&1)*e;return u(o>>>8==0),o},n.prototype.finderPenaltyCountPatterns=function(e){var t=e[1];u(t<=this.size*3);var o=t>0&&e[2]==t&&e[3]==t*3&&e[4]==t&&e[5]==t;return(o&&e[0]>=t*4&&e[6]>=t?1:0)+(o&&e[6]>=t*4&&e[0]>=t?1:0)},n.prototype.finderPenaltyTerminateAndCount=function(e,t,o){return e&&(this.finderPenaltyAddHistory(t,o),t=0),t+=this.size,this.finderPenaltyAddHistory(t,o),this.finderPenaltyCountPatterns(o)},n.prototype.finderPenaltyAddHistory=function(e,t){t[0]==0&&(e+=this.size),t.pop(),t.unshift(e)},n.MIN_VERSION=1,n.MAX_VERSION=40,n.PENALTY_N1=3,n.PENALTY_N2=3,n.PENALTY_N3=40,n.PENALTY_N4=10,n.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],n.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],n}();a.QrCode=r;function d(n,e,t){if(e<0||e>31||n>>>e)throw new RangeError("Value out of range");for(var o=e-1;o>=0;o--)t.push(n>>>o&1)}function c(n,e){return(n>>>e&1)!=0}function u(n){if(!n)throw new Error("Assertion error")}var f=function(){function n(e,t,o){if(this.mode=e,this.numChars=t,this.bitData=o,t<0)throw new RangeError("Invalid argument");this.bitData=o.slice()}return n.makeBytes=function(e){for(var t=[],o=0,s=e;o<s.length;o++){var i=s[o];d(i,8,t)}return new n(n.Mode.BYTE,e.length,t)},n.makeNumeric=function(e){if(!n.isNumeric(e))throw new RangeError("String contains non-numeric characters");for(var t=[],o=0;o<e.length;){var s=Math.min(e.length-o,3);d(parseInt(e.substring(o,o+s),10),s*3+1,t),o+=s}return new n(n.Mode.NUMERIC,e.length,t)},n.makeAlphanumeric=function(e){if(!n.isAlphanumeric(e))throw new RangeError("String contains unencodable characters in alphanumeric mode");var t=[],o;for(o=0;o+2<=e.length;o+=2){var s=n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(o))*45;s+=n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(o+1)),d(s,11,t)}return o<e.length&&d(n.ALPHANUMERIC_CHARSET.indexOf(e.charAt(o)),6,t),new n(n.Mode.ALPHANUMERIC,e.length,t)},n.makeSegments=function(e){return e==""?[]:n.isNumeric(e)?[n.makeNumeric(e)]:n.isAlphanumeric(e)?[n.makeAlphanumeric(e)]:[n.makeBytes(n.toUtf8ByteArray(e))]},n.makeEci=function(e){var t=[];if(e<0)throw new RangeError("ECI assignment value out of range");if(e<128)d(e,8,t);else if(e<16384)d(2,2,t),d(e,14,t);else if(e<1e6)d(6,3,t),d(e,21,t);else throw new RangeError("ECI assignment value out of range");return new n(n.Mode.ECI,0,t)},n.isNumeric=function(e){return n.NUMERIC_REGEX.test(e)},n.isAlphanumeric=function(e){return n.ALPHANUMERIC_REGEX.test(e)},n.prototype.getData=function(){return this.bitData.slice()},n.getTotalBits=function(e,t){for(var o=0,s=0,i=e;s<i.length;s++){var l=i[s],g=l.mode.numCharCountBits(t);if(l.numChars>=1<<g)return 1/0;o+=4+g+l.bitData.length}return o},n.toUtf8ByteArray=function(e){e=encodeURI(e);for(var t=[],o=0;o<e.length;o++)e.charAt(o)!="%"?t.push(e.charCodeAt(o)):(t.push(parseInt(e.substring(o+1,o+3),16)),o+=2);return t},n.NUMERIC_REGEX=/^[0-9]*$/,n.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,n.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",n}();a.QrSegment=f})(ie||(ie={}));(function(a){(function(r){var d=function(){function c(u,f){this.ordinal=u,this.formatBits=f}return c.LOW=new c(0,1),c.MEDIUM=new c(1,0),c.QUARTILE=new c(2,3),c.HIGH=new c(3,2),c}();r.Ecc=d})(a.QrCode||(a.QrCode={}))})(ie||(ie={}));(function(a){(function(r){var d=function(){function c(u,f){this.modeBits=u,this.numBitsCharCount=f}return c.prototype.numCharCountBits=function(u){return this.numBitsCharCount[Math.floor((u+7)/17)]},c.NUMERIC=new c(1,[10,12,14]),c.ALPHANUMERIC=new c(2,[9,11,13]),c.BYTE=new c(4,[8,16,16]),c.KANJI=new c(8,[8,10,12]),c.ECI=new c(7,[0,0,0]),c}();r.Mode=d})(a.QrSegment||(a.QrSegment={}))})(ie||(ie={}));var me=ie,vt="H",We={L:me.QrCode.Ecc.LOW,M:me.QrCode.Ecc.MEDIUM,Q:me.QrCode.Ecc.QUARTILE,H:me.QrCode.Ecc.HIGH},Oo=function(){try{new Path2D().addPath(new Path2D)}catch{return!1}return!0}();function ht(a){return a in We}function wt(a,r){r===void 0&&(r=0);var d=[];return a.forEach(function(c,u){var f=null;c.forEach(function(n,e){if(!n&&f!==null){d.push("M".concat(f+r," ").concat(u+r,"h").concat(e-f,"v1H").concat(f+r,"z")),f=null;return}if(e===c.length-1){if(!n)return;f===null?d.push("M".concat(e+r,",").concat(u+r," h1v1H").concat(e+r,"z")):d.push("M".concat(f+r,",").concat(u+r," h").concat(e+1-f,"v1H").concat(f+r,"z"));return}n&&f===null&&(f=e)})}),d.join("")}var Ye={value:{type:String,required:!0,default:""},size:{type:Number,default:100},level:{type:String,default:vt,validator:function(a){return ht(a)}},background:{type:String,default:"#fff"},foreground:{type:String,default:"#000"},margin:{type:Number,required:!1,default:0}},Qo=Ne(Ne({},Ye),{renderAs:{type:String,required:!1,default:"canvas",validator:function(a){return["canvas","svg"].indexOf(a)>-1}}}),xo=G({name:"QRCodeSvg",props:Ye,setup:function(a){var r=y(0),d=y(""),c=function(){var u=a.value,f=a.level,n=a.margin,e=me.QrCode.encodeText(u,We[f]).getModules();r.value=e.length+n*2,d.value=wt(e,n)};return c(),lt(c),function(){return j("svg",{width:a.size,height:a.size,"shape-rendering":"crispEdges",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(r.value," ").concat(r.value)},[j("path",{fill:a.background,d:"M0,0 h".concat(r.value,"v").concat(r.value,"H0z")}),j("path",{fill:a.foreground,d:d.value})])}}}),qo=G({name:"QRCodeCanvas",props:Ye,setup:function(a){var r=y(null),d=function(){var c=a.value,u=a.level,f=a.size,n=a.margin,e=a.background,t=a.foreground,o=r.value;if(o){var s=o.getContext("2d");if(s){var i=me.QrCode.encodeText(c,We[u]).getModules(),l=i.length+n*2,g=window.devicePixelRatio||1,h=f/l*g;o.height=o.width=f*g,s.scale(h,h),s.fillStyle=e,s.fillRect(0,0,l,l),s.fillStyle=t,Oo?s.fill(new Path2D(wt(i,n))):i.forEach(function(P,C){P.forEach(function(v,E){v&&s.fillRect(E+n,C+n,1,1)})})}}};return te(d),lt(d),function(){return j("canvas",{ref:r,style:{width:"".concat(a.size,"px"),height:"".concat(a.size,"px")}})}}}),Ho=G({name:"Qrcode",render:function(){var a=this.$props,r=a.renderAs,d=a.value,c=a.size,u=a.margin,f=a.level,n=a.background,e=a.foreground,t=c>>>0,o=u>>>0,s=ht(f)?f:vt;return j(r==="svg"?xo:qo,{value:d,size:t,margin:o,level:s,background:n,foreground:e})},props:Qo});const Go={key:0},Ko=G({__name:"QrCodeComponent",props:{identifyQrCode:{},customSize:{default:100},customLevel:{default:"M"},customFg:{default:"#000000"},customBg:{default:"#FFFFFF"}},setup(a){const r=a,d=y(!1),c=y(""),u=()=>{r.identifyQrCode&&r.identifyQrCode.length>0&&(d.value=!0,c.value=r.identifyQrCode)};return te(()=>{u()}),Ge(()=>r.identifyQrCode,()=>{u()}),(f,n)=>d.value?(R(),D("div",Go,[w(Ho,{value:c.value,size:f.customSize,level:f.customLevel,foreground:f.customFg,background:f.customBg},null,8,["value","size","level","foreground","background"])])):H("",!0)}}),Wo=a=>(ce("data-v-2a268acf"),a=a(),ue(),a),Yo={class:"h-[524px] min-h-[400px] protocol-box"},Xo={class:"states"},jo=Wo(()=>m("div",{id:"protocolMdId",class:"markdown-license"},null,-1)),Jo={class:"footer"},Zo=G({__name:"ProtocolDialog",emits:["update:modelValue","changeState"],setup(a,{emit:r}){const d=r,{t:c}=ee(),u=y(!1),f=o=>{n(),d("changeState",o)},n=()=>{d("update:modelValue",!1)},e=o=>{ao.preview(document.getElementById("protocolMdId"),o,{mode:"light",anchor:0,theme:{current:"light",path:`${at}/dist/css/content-theme`},cdn:at})},t=async()=>{u.value=!0;try{const s=await(await fetch(`${Dt}/SguiProtocol.md`)).text();!s||s!=null&&s.startsWith("<!DOCTYPE html>")?e(c("app.emptyTip")):e(s)}catch{return}finally{u.value=!1}};return te(async()=>{await t()}),(o,s)=>{const i=le,l=so,g=ct;return R(),B(l,{width:"1040","close-on-click-modal":!1,"show-close":!1,"close-on-press-escape":!1,class:"protocol-view-dialog",onClose:n},{default:_(()=>[m("div",Yo,[we((R(),D("div",Xo,[jo,m("div",Jo,[w(i,{type:"primary",onClick:s[0]||(s[0]=h=>f(!0))},{default:_(()=>[X($(o.$t("app.login.agree")),1)]),_:1}),w(i,{plain:"",onClick:s[1]||(s[1]=h=>f(!1))},{default:_(()=>[X($(o.$t("app.login.cancel")),1)]),_:1})])])),[[g,u.value]])])]),_:1})}}});const ea=de(Zo,[["__scopeId","data-v-2a268acf"]]),ta=(a,r)=>{const d=Me(),c=Ke(),u=d.query.authValue;let f=!1;const n=y(0),e=y(!1),t=y(""),o=["IAM-7101-15","IAM-7102-16","IAM-7103-17","IAM-7104-18","IAM-7106-10","IAM-7199-78","IAM-71-ADR32","IAM-71-ADU11","SGUI-71-ADU11"];let s=null;const i=y(),l=y(""),g=y(!1),h=y(!1),{startCountdown:P,timeOut:C,countdownText:v,stopCountDownInterval:E}=Jt(),A=y(""),L=ne(()=>A.value?"#D9D9D9":"#000000"),M=J({username:"",certificate:"",verificationCode:""}),F=ne(()=>{var k;return window.location.hostname.includes("intl")||((k=window.location.pathname)==null?void 0:k.includes("intl"))}),z=y(Ft()),V=y(!1),x=y(!1),pe=ne(()=>M.certificate!==l.value),q=y(null),K=y(!1),W=y(),Y=y(),{t:U}=ee(),De=U("app.login.pleaseInputUserName"),T=U("app.login.pleaseInputWorkNo"),b=U("app.login.pleaseInputPwd"),oe=U("app.login.pleaseInputVerifyCode"),O=U("app.login.length"),be={username:[{required:!0,message:De,trigger:"blur"}],workNo:[{required:!0,message:T,trigger:"blur"}],certificate:[{required:!0,message:b,trigger:"blur"}],verificationCode:[{required:!0,message:oe,trigger:"blur"},{min:4,max:4,message:O,trigger:"blur"}]},ae=y(`${Re}?rdm=${Math.random()}`);let fe=!0;const he=()=>{ae.value=`${Re}?rdm=${Math.random()}`},se=380,I=y(0),_t=ne(()=>({transform:`translate3d(${-I.value}px, 0px, 0px)`})),Ct=async()=>{const k=pe.value?Le(M.certificate):M.certificate,S=Bt(),N={certificate:k,username:M.username,verificationCode:M.verificationCode};return S.includes("icrspsssell")&&(N.username=M.workNo??"",N.system="C"),N},Xe=()=>{s!==null&&(clearTimeout(s),s=null)},ke=()=>{Xe(),E()},je=async()=>{if(C.value)return;const k=He("09300117"),{data:S}=await Ot(t.value,k),N=S.value;if(!C.value)if(N.code==="200"){const Z=await qe(f,N.data.token);if(ke(),Z){r("update:currentToken",N.data.token),r("update:agentDomesticEpid",!0);return}await rt(N.data.token,c)}else if(o.includes(N.code))ke(),N.code==="IAM-7103-17"||N.code==="IAM-71-ADR32"?A.value=U("app.login.bioQrCodeTimeout"):A.value=U("app.login.bioQrCodeInvalid");else{if(I.value===0||C.value)return;s=setTimeout(je,i.value)}},yt=(k,S)=>{A.value="",i.value=S*1e3,P(k*60),je()},Je=()=>nt.config({driver:nt.INDEXEDDB,name:"index_db_util",storeName:"airport"}),Pt=async k=>{if(!h.value)return;await Je();const S={username:k.username,certificate:k.certificate,operationTime:new Date().getTime()},N=await st("LOGIN_INFO_ACCOUNT"),Z=N?JSON.parse(N.localData??""):[];Z.length=0,Z.push(S),await Vt("LOGIN_INFO_ACCOUNT",JSON.stringify(Z),new Date().getTime())},bt=k=>navigator.userAgent.toLowerCase().includes("electron/")&&(k??"")===""?(zt.error(U("app.login.macNull")),!1):!0,Fe=async(k,S)=>{try{fe=!1,t.value="";const N=await Ct(),Z=await dt(u);if(!bt(Z))return;const Q=(await Ut(N,Z)).data;if(r("update:currentToken",Q.token),n.value=Q.securityLevel,f=!Q.agentAbroad,Pt(N),Q.bioExpireTime&&Q.bioExpireTime>0&&Q.iamToken){r("update:showLanguage",!1),I.value+=se,t.value=Q.iamToken,yt(Q.bioExpireTime,Q.bioIntervalTime??5);return}if(n.value===0){if(await qe(f,Q.token)){r("update:agentDomesticEpid",!0);return}await $e(Q.token,c);return}r("update:showLanguage",!1),I.value+=se,a.value=Q.iamToken,S(),k.resetFields()}catch{he()}finally{fe=!0}},kt=(k,S)=>{Y.value.validate(N=>{N&&fe&&Fe(k,S)})},Et=()=>{q.value&&(Qe.close(),q.value=null),et(!0)},Rt=(k,S)=>{Y.value&&Y.value.validate(N=>{if(N&&fe){if(W.value={form:k,submitMultCaptcha:S},!F.value&&!V.value){q.value=Qe.confirm(j("div",{class:"protocol-info-tip-box"},[j("div",{class:"text-gray-1 text-lg font-normal leading-normal"},U("app.login.pleaseReadAndAgree")),j("div",{class:"pl-1 text-brand-2 text-lg font-normal leading-normal cursor-pointer",onClick:Et},U("app.login.sguiProtocol"))]),{icon:j("em",{class:"iconfont icon-info-circle-line"}),customClass:"protocol-msg crs-btn-ui",dangerouslyUseHTMLString:!0,closeOnClickModal:!1,showClose:!1,cancelButtonClass:z.value==="en"?"ptotocol-cancel-en":"ptotocol-cancel",confirmButtonClass:z.value==="en"?"ptotocol-sure-en":"ptotocol-sure",confirmButtonText:U("app.login.agree"),cancelButtonText:U("app.login.cancel")}).then(()=>{V.value=!0,Fe(k,S)});return}Fe(k,S)}})},It=()=>{r("update:showLanguage",!0),ke(),I.value-=se,n.value=0,he(),Y.value&&Y.value.resetFields(),Ze()},$t=async()=>{const{token:k,iamToken:S}=d.query;k?await rt(k,c):S&&(I.value=se)},Ze=async()=>{if(g.value){await Je();const k=await st("LOGIN_INFO_ACCOUNT"),S=k?JSON.parse(k.localData??""):[];if(!k||S.length===0)return;h.value=!0;const N=S[0];M.workNo=N.username,M.username=N.username,l.value=N.certificate,M.certificate=N.certificate}},Lt=k=>{var S,N;k&&(V.value=k),k&&K.value&&kt((S=W.value)==null?void 0:S.form,(N=W.value)==null?void 0:N.submitMultCaptcha)},et=k=>{K.value=k,x.value=!0};return Ge(()=>C.value,k=>{k&&(Xe(),A.value=U("app.login.bioQrCodeTimeout"))}),te(async()=>{$t();const k=c.currentRoute.value.fullPath;e.value=k.includes("icrspsssell"),navigator.userAgent.toLowerCase().includes("electron/")&&(g.value=!0),await Ze()}),Tt(()=>{ke()}),{submitLogin:Rt,loginUser:M,captchaChange:he,loginRules:be,captchaUrl:ae,loginForm:Y,backLoginForm:It,trackStyles:_t,securityLevel:n,isCrsLogin:e,identifyQrCode:t,countdownText:v,qrCodeCustomFg:L,qrErrorTip:A,isChangeAutoPwd:pe,isClientAndCheck:h,isClient:g,isAgreementConsent:V,showProtocol:x,changeState:Lt,viewProtocol:et,isIntl:F}},oa=ta,aa=a=>{const r=Me(),d=Ke(),{t:c}=ee(),u=J({captcha:""}),f={captcha:[{required:!0,message:c("app.login.pleaseInputVerifyCode"),trigger:"blur"},{min:6,max:6,message:c("app.login.sixLength"),trigger:"blur"}]},{checkCodeInput:n,startCount:e,isDisabled:t}=ut(c("app.login.verifyCodeSending"),c("app.login.resendVerifycode"));let o=!0;const s=y(""),i=async()=>{e(),await Qt(s.value,"09300125")},l=async()=>{try{o=!1;const C=(await xt(u.captcha,s.value,"09300124")).data;if(a("update:currentToken",C.token),await qe(!C.agentAbroad,C.token)){a("update:agentDomesticEpid",!0);return}await $e(C.token,d)}finally{o=!0}},g=y(),h=()=>{g.value&&g.value.validate(P=>{P&&o&&l()})};return te(()=>{const{iamToken:P}=r.query;P&&(s.value=P)}),{multUser:u,submitMultLogin:h,submitMultCaptcha:i,multLoginRules:f,multLoginForm:g,checkCodeInput:n,isDisabled:t,currentIamToken:s}},sa=aa,Se=a=>(ce("data-v-0d1aae07"),a=a(),ue(),a),na=Se(()=>m("em",{class:"iconfont icon-loginuser"},null,-1)),ra=Se(()=>m("em",{class:"iconfont icon-loginuser"},null,-1)),ia=Se(()=>m("em",{class:"iconfont icon-loginpassword"},null,-1)),la={class:"flex-row"},ca=Se(()=>m("em",{class:"iconfont icon-loginverify"},null,-1)),ua=["src"],da={class:"flex h-[22px] items-center protocol-box"},pa={class:"flex items-center"},fa={class:"leading-[22px] rounded-sm flex-col justify-start items-start inline-flex overflow-hidden"},ga={class:"tips"},ma={key:0,class:"form"},va={class:"mult-top bio-identify-title"},ha={class:"mult-title"},wa={class:"flex items-center flex-col"},_a={class:"text-[14px] text-gray-4 mt-[10px] text-center"},Ca={key:0},ya={key:1},Pa={class:"mult-top"},ba={class:"mult-title"},ka=G({__name:"LoginForm",emits:["chagepagestatus","update:currentToken","update:agentDomesticEpid","update:showLanguage"],setup(a,{emit:r}){const d=r,{multUser:c,submitMultLogin:u,multLoginRules:f,multLoginForm:n,checkCodeInput:e,submitMultCaptcha:t,isDisabled:o,currentIamToken:s}=sa(d),{loginUser:i,captchaChange:l,loginRules:g,captchaUrl:h,loginForm:P,backLoginForm:C,trackStyles:v,submitLogin:E,securityLevel:A,isCrsLogin:L,identifyQrCode:M,countdownText:F,qrCodeCustomFg:z,qrErrorTip:V,isClientAndCheck:x,isClient:pe,isAgreementConsent:q,showProtocol:K,changeState:W,viewProtocol:Y,isIntl:U}=oa(s,d),De=T=>{d("chagepagestatus",T)};return(T,b)=>{const oe=ve,O=re,be=no,ae=le,fe=_e,he=ye,se=Ce;return R(),D(it,null,[m("div",{class:"login-form-wipper",style:qt(p(v))},[w(se,{ref_key:"loginForm",ref:P,rules:p(g),model:p(i),class:"form",onSubmit:b[11]||(b[11]=xe(()=>{},["prevent"]))},{default:_(()=>[p(L)?(R(),B(O,{key:0,prop:"workNo"},{default:_(()=>[w(oe,{modelValue:p(i).workNo,"onUpdate:modelValue":b[0]||(b[0]=I=>p(i).workNo=I),placeholder:T.$t("app.login.workNo"),clearable:"",size:"large"},{prefix:_(()=>[na]),_:1},8,["modelValue","placeholder"])]),_:1})):(R(),B(O,{key:1,prop:"username"},{default:_(()=>[w(oe,{modelValue:p(i).username,"onUpdate:modelValue":b[1]||(b[1]=I=>p(i).username=I),placeholder:T.$t("app.login.userName"),clearable:"",size:"large"},{prefix:_(()=>[ra]),_:1},8,["modelValue","placeholder"])]),_:1})),w(O,{prop:"certificate"},{default:_(()=>[w(oe,{modelValue:p(i).certificate,"onUpdate:modelValue":b[2]||(b[2]=I=>p(i).certificate=I),type:"password",clearable:"",placeholder:T.$t("app.login.oauth"),size:"large","show-password":""},{prefix:_(()=>[ia]),_:1},8,["modelValue","placeholder"])]),_:1}),w(O,{prop:"verificationCode"},{default:_(()=>[m("div",la,[w(oe,{modelValue:p(i).verificationCode,"onUpdate:modelValue":b[3]||(b[3]=I=>p(i).verificationCode=I),clearable:"",placeholder:T.$t("app.login.verifyCode"),size:"large",onKeyup:b[4]||(b[4]=Ie(I=>p(E)(p(n),p(t)),["enter"]))},{prefix:_(()=>[ca]),_:1},8,["modelValue","placeholder"]),m("img",{class:"captcha-img z-10",src:p(h),alt:"","data-gid":"081V0102",onClick:b[5]||(b[5]=I=>p(l)())},null,8,ua)])]),_:1}),p(pe)?(R(),B(O,{key:2,class:"flight-time","label-width":"56spx"},{default:_(()=>[w(be,{modelValue:p(x),"onUpdate:modelValue":b[6]||(b[6]=I=>Te(x)?x.value=I:null),label:T.$t("app.personal.saveLoginInfoTip")},null,8,["modelValue","label"])]),_:1})):H("",!0),p(U)?H("",!0):(R(),B(O,{key:3},{default:_(()=>[m("div",da,[m("div",pa,[w(be,{modelValue:p(q),"onUpdate:modelValue":b[7]||(b[7]=I=>Te(q)?q.value=I:null),size:"large"},{default:_(()=>[X($(T.$t("app.login.readAndAgree")),1)]),_:1},8,["modelValue"])]),m("div",fa,[w(ae,{type:"primary",link:"",class:"pro-btn",onClick:b[8]||(b[8]=I=>p(Y)(!1))},{default:_(()=>[X($(T.$t("app.login.sguiProtocol")),1)]),_:1})])])]),_:1})),w(O,null,{default:_(()=>[w(ae,{type:"primary",class:"submit-button","data-gid":"081V0104",onClick:b[9]||(b[9]=I=>p(E)(p(n),p(t)))},{default:_(()=>[X($(T.$t("app.login.loginText")),1)]),_:1})]),_:1}),w(he,{class:"retrieve-password retrieve-password-center"},{default:_(()=>[w(fe,null,{default:_(()=>[m("span",ga,$(T.$t("app.login.tipsText")),1),m("a",{class:"linkPwd",onClick:b[10]||(b[10]=I=>De("pagePasswordRetrieval"))},$(T.$t("app.login.retrievePassword")),1)]),_:1})]),_:1})]),_:1},8,["rules","model"]),p(M)?(R(),D("div",ma,[m("div",va,[m("a",{href:"#",class:"linkBack",onClick:b[12]||(b[12]=I=>p(C)())},$(T.$t("app.login.back")),1),m("span",ha,$(T.$t("app.login.verifyCode")),1)]),m("div",wa,[w(Ko,{"identify-qr-code":p(M),"custom-size":220,"custom-fg":p(z)},null,8,["identify-qr-code","custom-fg"]),m("div",_a,[p(V)?(R(),D("span",Ca,$(p(V)),1)):(R(),D("span",ya,$(T.$t("app.login.bioIdentificationTip",{countdown:p(F)})),1))])])])):p(A)!==0?(R(),B(se,{key:1,ref_key:"multLoginForm",ref:n,rules:p(f),model:p(c),class:"form",onSubmit:b[17]||(b[17]=xe(()=>{},["prevent"]))},{default:_(()=>[m("div",Pa,[m("a",{href:"#",class:"linkBack",onClick:b[13]||(b[13]=I=>p(C)())},$(T.$t("app.login.back")),1),m("span",ba,$(T.$t("app.login.verifyCode")),1)]),w(O,{prop:"captcha"},{default:_(()=>[w(oe,{modelValue:p(c).captcha,"onUpdate:modelValue":b[14]||(b[14]=I=>p(c).captcha=I),placeholder:T.$t("app.login.verifyCode"),clearable:"","prefix-icon":p(ze),size:"large",onKeyup:b[15]||(b[15]=Ie(I=>p(u)(),["enter"]))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1}),w(O,null,{default:_(()=>[w(ae,{type:"primary",class:"submit-button",onClick:b[16]||(b[16]=I=>p(u)())},{default:_(()=>[X($(T.$t("app.login.sure")),1)]),_:1})]),_:1}),w(O,null,{default:_(()=>[w(ae,{type:"primary",class:"submit-button",disabled:p(o),onClick:p(t)},{default:_(()=>[X($(p(e)),1)]),_:1},8,["disabled","onClick"])]),_:1})]),_:1},8,["rules","model"])):H("",!0)],4),p(K)?(R(),B(ea,{key:0,modelValue:p(K),"onUpdate:modelValue":b[18]||(b[18]=I=>Te(K)?K.value=I:null),class:"max-h-[560px]",onChangeState:p(W)},null,8,["modelValue","onChangeState"])):H("",!0)],64)}}});const Ea=de(ka,[["__scopeId","data-v-0d1aae07"]]),Ra="/sgui/assets/epid-a0f6d60a.png",Ia=a=>(ce("data-v-12149dff"),a=a(),ue(),a),$a={key:0,class:"epid-form"},La={class:"form-header"},Na=Ia(()=>m("img",{class:"logo",src:Ra},null,-1)),Ma=G({__name:"DomesticRepresentatives",props:{currentToken:{}},emits:["update:agentDomesticEpid"],setup(a,{emit:r}){const d=a,c=r,{t:u}=ee(),f=Ke(),n=Me();let e="";const t=y(),o=y({epidNumber:"",activationCode:""}),s=y(!0),i={epidNumber:[{required:!0,message:u("app.domesticRepresentatives.pleaseInputChannelNumber"),trigger:"blur"},{pattern:ro,message:u("app.domesticRepresentatives.illeigalAccess"),trigger:"blur"}],activationCode:[{required:!0,message:u("app.domesticRepresentatives.pleaseInputActiveCode"),trigger:"blur"},{pattern:io,message:u("app.domesticRepresentatives.illeigalAccess"),trigger:"blur"}]},l=C=>{const v=u("app.domesticRepresentatives.expiryDateNotSufficient",{num:C});Qe.confirm(v,{icon:j("em",{class:"iconfont icon-info-circle-line text-brand-2 text-[32px]"}),customClass:"alert-message-common crs-btn-ui unbind-epid-expire-time-box",showClose:!1,showCancelButton:!1,closeOnClickModal:!1}).then(async()=>{await $e(d.currentToken,f)})},g=async(C,v)=>{try{await Kt(C,e,"09300128");const E=Zt(v);if(E){await l(E);return}await $e(d.currentToken,f)}catch{c("update:agentDomesticEpid",!1)}},h=async()=>{var C;s.value=!1;try{const v=await Ht(e,"09300130"),E=(C=v==null?void 0:v.data)==null?void 0:C[0];if(E&&E.activationStatus===0){const{epidNumber:A,v:L,nextOccupyStatus:M,expireTime:F}=E;await g({epidNumber:A,version:L,occupyStatus:M},F);return}s.value=!0}catch{c("update:agentDomesticEpid",!1)}},P=()=>{t.value.validate(async C=>{if(C){const v=await Gt({epidNumber:o.value.epidNumber,activationCode:Le(o.value.activationCode)},e,"09300129"),E=v==null?void 0:v.data;if(!E)return;const{epidNumber:A,v:L,occupyStatus:M,expireTime:F}=E;await g({epidNumber:A,version:L,occupyStatus:M},F)}})};return te(async()=>{const C=n.query.authValue;e=await dt(C),h()}),(C,v)=>{const E=ve,A=re,L=le,M=Ce;return s.value?(R(),D("div",$a,[w(M,{ref_key:"representativesFormRef",ref:t,rules:i,model:o.value,onSubmit:v[4]||(v[4]=xe(()=>{},["prevent"]))},{default:_(()=>[m("div",La,[m("div",null,$(C.$t("app.domesticRepresentatives.connectionChannelActivation")),1),Na]),w(A,{prop:"epidNumber",label:C.$t("app.domesticRepresentatives.ChannelNumber")},{default:_(()=>[w(E,{modelValue:o.value.epidNumber,"onUpdate:modelValue":v[0]||(v[0]=F=>o.value.epidNumber=F),modelModifiers:{trim:!0},clearable:"",onKeyup:Ie(P,["enter"]),onInput:v[1]||(v[1]=F=>o.value.epidNumber=o.value.epidNumber.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label"]),w(A,{prop:"activationCode",label:C.$t("app.domesticRepresentatives.activeCode")},{default:_(()=>[w(E,{modelValue:o.value.activationCode,"onUpdate:modelValue":v[2]||(v[2]=F=>o.value.activationCode=F),modelModifiers:{trim:!0},clearable:"",onKeyup:Ie(P,["enter"]),onInput:v[3]||(v[3]=F=>o.value.activationCode=o.value.activationCode.toUpperCase())},null,8,["modelValue"])]),_:1},8,["label"]),w(A,null,{default:_(()=>[w(L,{type:"primary",class:"submit-button",onClick:P},{default:_(()=>[X($(C.$t("app.domesticRepresentatives.active")),1)]),_:1})]),_:1})]),_:1},8,["model"])])):H("",!0)}}});const Aa=de(Ma,[["__scopeId","data-v-12149dff"]]),Sa=a=>{const r=y("pageLogin"),{resetPasswordReq:d,resetPasswordInfo:c}=gt(a),{findPassword:u,findPasswordPageInfo:f}=ft(a),n=()=>{u.userName="",u.captcha="",u.verificationCode="",f.resetLoading=!1,f.nextLoading=!1,f.nextCanClick=!0,f.findPasswordCanClick=!0,d.certificate="",d.recertificate="",c.resetConfLoading=!1};return{pageName:r,changePageStatus:t=>{n(),r.value=t},reset:n}},Da=Sa,Fa="/sgui/assets/forbidLogo-17609a7c.png",Ta="/sgui/assets/Vector-0248234d.png",Ua="/sgui/assets/forbidDown-94867865.png",Ba=a=>(ce("data-v-92c5a629"),a=a(),ue(),a),Va={class:"w-full h-[100vh] bg-gradient-to-b from-undefined to-undefined flex-col justify-center items-center gap-[170px] inline-flex bg-color"},za={class:"w-[900px] justify-between items-center inline-flex"},Oa={class:"flex-col justify-start items-start gap-20 inline-flex"},Qa=Wt('<div class="justify-start items-center gap-6 inline-flex" data-v-92c5a629><div class="pt-1.5 bg-undefined bg-opacity-60 rounded-[128px] shadow shadow-inner justify-end items-center flex" data-v-92c5a629><img class="w-[76px]" src="'+Fa+'" data-v-92c5a629></div><div class="flex-col justify-start items-start inline-flex" data-v-92c5a629><img class="w-[226px]" src="'+Ta+'" data-v-92c5a629></div></div>',1),xa={class:"justify-start items-center gap-10 inline-flex"},qa={class:"text-justify text-brand-1 text-2xl font-normal"},Ha={class:"h-[46px] py-[11px] bg-undefined rounded justify-center items-center gap-2.5 flex"},Ga=Ba(()=>m("img",{class:"w-[216px] h-[260px] relative",src:Ua},null,-1)),Ka=G({__name:"ForbidCrs",setup(a){const r=()=>{window.open("https://eterm.travelsky.cn/tosp/#/SGUI")};return(d,c)=>{const u=le;return R(),D("div",Va,[m("div",za,[m("div",Oa,[Qa,m("div",xa,[m("div",qa,$(d.$t("app.login.clientLogin")),1),m("div",Ha,[w(u,{type:"primary",class:"download-button",size:"large",onClick:r},{default:_(()=>[X($(d.$t("app.login.downLoad")),1)]),_:1})])])]),Ga])])}}});const Wa=de(Ka,[["__scopeId","data-v-92c5a629"]]),Ya=a=>a.length===3?{airline:a.slice(1,3),isIntranet:"0"}:a.length===2?{airline:a,isIntranet:"1"}:{airline:"travelsky",isIntranet:a==="i"?"0":"1"},Xa=()=>{const a=y("");localStorage.removeItem("belongOffice");const r=y(!1),d=y(""),c=ne(()=>window.location.pathname.includes("crs")??!1),u=ne(()=>window.location.href.includes("uatsgui")??!1),f=Me(),n=y(!0),e=y(!1),t=async()=>{const l=f.path.match(Yt)??["sgui"];Xt(f.path);const g=Ya(l[0]);d.value=g.airline},o=async()=>{try{if(e.value=!0,navigator.userAgent.toLowerCase().includes("electron/"))n.value=!1;else{const l=He("09300115"),{data:g}=await jt(l);g.value!==null&&(n.value=g.value)}}finally{e.value=!1}},s=()=>{window.open("https://uatsgui.travelsky.com.cn:38443/client-app/index.html")};return te(async()=>{t(),c.value&&await o()}),{airlineRefer:d,isCrs:c,isUat:u,goUATClientDownload:s,currentToken:a,agentDomesticEpid:r,isForbidCrs:n,loading:e}},ja=Xa,Ja=G({name:"Login",components:{PasswordRetrieval:$o,ResetPassword:zo,I18n:oo,DomesticRepresentatives:Aa,LoginForm:Ea,ForbidCrs:Wa},setup(a,r){const d=y(!0),c=y(1),{locale:u}=ee({useScope:"global"}),f=E=>{d.value=E},n=y(!0);to(),sessionStorage.removeItem("bugTipsPermission"),Ge(u,()=>{c.value+=1});const{pageName:e,changePageStatus:t,reset:o}=Da(r),{airlineRefer:s,isCrs:i,isUat:l,goUATClientDownload:g,currentToken:h,agentDomesticEpid:P,isForbidCrs:C,loading:v}=ja();return{pageName:e,changePageStatus:t,reset:o,languageNumber:c,airlineRefer:s,showLineAndInfo:d,changeBtn:f,currentToken:h,agentDomesticEpid:P,isCrs:i,isUat:l,goUATClientDownload:g,isForbidCrs:C,loading:v,showLanguage:n}}}),Za="/sgui/assets/logo-crs-earth-4561e049.png",es="/sgui/assets/logo-ics-earth-4e132c76.png";const Pe=a=>(ce("data-v-4b97ebb2"),a=a(),ue(),a),ts={key:0,class:"icon svg-icon","aria-hidden":"true"},os=["xlink:href"],as={class:"wipper-container"},ss={key:0,class:"logo logo-title"},ns=Pe(()=>m("img",{class:"m-auto h-[48px]",src:Za},null,-1)),rs=[ns],is={key:1,class:"logo logo-title"},ls=Pe(()=>m("img",{class:"m-auto h-[50px]",src:es},null,-1)),cs=[ls],us={class:"top"},ds=Pe(()=>m("span",{class:"vector-left vector"},null,-1)),ps=Pe(()=>m("span",{class:"ellipse"},null,-1)),fs=Pe(()=>m("span",{class:"vector-right vector"},null,-1)),gs=[ds,ps,fs],ms={class:"copyright-span text-gray-4 text-xs font-normal leading-tight"};function vs(a,r,d,c,u,f){const n=ge("i18n"),e=ge("ForbidCrs"),t=ge("DomesticRepresentatives"),o=ge("login-form"),s=ge("PasswordRetrieval"),i=ge("ResetPassword"),l=ye,g=ct;return we((R(),D("div",null,[a.showLanguage?(R(),B(n,{key:0})):H("",!0),a.isCrs&&a.isForbidCrs?(R(),B(e,{key:1})):(R(),B(l,{key:a.languageNumber,class:Oe(["login",a.isCrs?"login-new":""])},{default:_(()=>[a.airlineRefer?(R(),D("svg",ts,[m("use",{"xlink:href":"#icon-"+a.airlineRefer+"-login"},null,8,os)])):H("",!0),a.agentDomesticEpid?(R(),B(t,{key:1,"agent-domestic-epid":a.agentDomesticEpid,"onUpdate:agentDomesticEpid":r[0]||(r[0]=h=>a.agentDomesticEpid=h),class:"wipper-container","current-token":a.currentToken},null,8,["agent-domestic-epid","current-token"])):H("",!0),we(m("div",as,[a.isCrs?(R(),D("div",ss,rs)):(R(),D("div",is,cs)),we(m("div",us,gs,512),[[Ve,a.showLineAndInfo]]),a.pageName==="pageLogin"?(R(),B(o,{key:2,"current-token":a.currentToken,"onUpdate:currentToken":r[1]||(r[1]=h=>a.currentToken=h),"agent-domestic-epid":a.agentDomesticEpid,"onUpdate:agentDomesticEpid":r[2]||(r[2]=h=>a.agentDomesticEpid=h),"show-language":a.showLanguage,"onUpdate:showLanguage":r[3]||(r[3]=h=>a.showLanguage=h),onChagepagestatus:a.changePageStatus},null,8,["current-token","agent-domestic-epid","show-language","onChagepagestatus"])):a.pageName==="pagePasswordRetrieval"?(R(),B(s,{key:3,onChangeBtn:a.changeBtn,onChagepagestatus:a.changePageStatus,onReset:a.reset},null,8,["onChangeBtn","onChagepagestatus","onReset"])):a.pageName==="pageResetPassword"?(R(),B(i,{key:4,onChagepagestatus:a.changePageStatus,onReset:a.reset},null,8,["onChagepagestatus","onReset"])):H("",!0)],512),[[Ve,!a.agentDomesticEpid]]),m("span",ms,$(a.$t("app.login.companyName")),1),a.isUat?(R(),D("span",{key:2,class:"text-[14px] text-gray-4 cursor-pointer absolute right-[30px] bottom-[16px]",onClick:r[4]||(r[4]=(...h)=>a.goUATClientDownload&&a.goUATClientDownload(...h))},$(a.$t("app.login.downloadClient")),1)):H("",!0)]),_:1},8,["class"]))])),[[g,a.loading]])}const Qs=de(Ja,[["render",vs],["__scopeId","data-v-4b97ebb2"]]);export{Qs as default};
