import{L as h,M as o,ez as u,q as m,O as g,w as c,v as _,eB as p,e5 as $,x as N,y as x,z as C,F as j,D as v,A as f,C as O,bf as E,_ as w,K as k}from"./index-18f146fc.js";import{r as B}from"./index-93952dc4.js";const K=h({tag:{type:String,default:"div"},span:{type:Number,default:24},offset:{type:Number,default:0},pull:{type:Number,default:0},push:{type:Number,default:0},xs:{type:o([Number,Object]),default:()=>u({})},sm:{type:o([Number,Object]),default:()=>u({})},md:{type:o([Number,Object]),default:()=>u({})},lg:{type:o([Number,Object]),default:()=>u({})},xl:{type:o([Number,Object]),default:()=>u({})}}),S=m({name:"ElCol"}),D=m({...S,props:K,setup(b){const t=b,{gutter:n}=g(B,{gutter:c(()=>0)}),a=_("col"),d=c(()=>{const e={};return n.value&&(e.paddingLeft=e.paddingRight=`${n.value/2}px`),e}),i=c(()=>{const e=[];return["span","offset","pull","push"].forEach(s=>{const l=t[s];p(l)&&(s==="span"?e.push(a.b(`${t[s]}`)):l>0&&e.push(a.b(`${s}-${t[s]}`)))}),["xs","sm","md","lg","xl"].forEach(s=>{p(t[s])?e.push(a.b(`${s}-${t[s]}`)):$(t[s])&&Object.entries(t[s]).forEach(([l,r])=>{e.push(l!=="span"?a.b(`${s}-${l}-${r}`):a.b(`${s}-${r}`))})}),n.value&&e.push(a.is("guttered")),[a.b(),e]});return(e,y)=>(N(),x(E(e.tag),{class:v(f(i)),style:O(f(d))},{default:C(()=>[j(e.$slots,"default")]),_:3},8,["class","style"]))}});var L=w(D,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/col/src/col.vue"]]);const F=k(L);export{F as E};
