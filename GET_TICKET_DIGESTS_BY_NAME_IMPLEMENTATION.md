# 按姓名查询客票接口实现文档

## 接口信息
- **路径**: `/sgui-tc/v2/crs/ticket/getTicketDigestsByName`
- **方法**: POST
- **功能**: 通过姓名查询客票信息

## 请求参数
```json
{
  "certCode": "NM",
  "certNo": "5byg5LiJ"
}
```

### 参数说明
- `certCode`: 证件类型代码，固定为"NM"
- `certNo`: Base64编码的旅客姓名

## 响应结果
```json
{
  "code": "200",
  "msg": null,
  "data": [
    {
      "etNumber": "781-355****681",
      "passengerName": "张三",
      "passengerType": "ADT",
      "airSeg": [
        {
          "airlineCode": "MU",
          "depAirportCode": "PKX",
          "arrAirportCode": "SHA",
          "depDate": "2025-09-24",
          "fltNo": "6865",
          "status": "OPEN FOR USE",
          "operateAirline": null
        }
      ]
    }
  ]
}
```

## 实现文件

### 1. DTO类
- **文件**: `sgui/src/main/java/com/swcares/obj/dto/GetTicketDigestsByNameDto.java`
- **说明**: 定义请求参数结构

### 2. VO类
- **文件**: `sgui/src/main/java/com/swcares/obj/vo/GetTicketDigestsByNameVo.java`
- **说明**: 定义响应数据结构

### 3. 服务接口
- **文件**: `sgui/src/main/java/com/swcares/service/tc/ITcV2Service.java`
- **方法**: `List<GetTicketDigestsByNameVo> getTicketDigestsByName(GetTicketDigestsByNameDto dto)`

### 4. 服务实现
- **文件**: `sgui/src/main/java/com/swcares/service/tc/impl/TcV2ServiceImpl.java`
- **方法**: `getTicketDigestsByName(GetTicketDigestsByNameDto dto)`

### 5. 控制器
- **文件**: `sgui/src/main/java/com/swcares/controller/tc/TcV2Controller.java`
- **端点**: `@PostMapping("/crs/ticket/getTicketDigestsByName")`

## 业务逻辑

### 主要步骤：
1. **参数校验**: 验证姓名信息不为空
2. **Base64解码**: 解码获取旅客姓名
3. **获取Office信息**: 获取当前登录用户的Office信息
4. **时间范围计算**: 计算当天及过去30天的时间范围
5. **查询旅客信息**: 根据姓名查询mnjx_pnr_nm表
6. **查询PNR信息**: 根据pnrId查询mnjx_pnr表
7. **查询出票信息**: 根据pnrNmId查询mnjx_pnr_nm_tn表
8. **筛选时间范围**: 筛选符合时间条件的出票记录
9. **查询票务信息**: 根据tnId查询mnjx_pnr_nm_ticket表
10. **构建返回数据**: 遍历数据构建响应结果

### 涉及的数据库表：
- `mnjx_pnr_nm`: 旅客姓名表
- `mnjx_pnr`: PNR主表
- `mnjx_pnr_nm_tn`: 出票记录表
- `mnjx_pnr_nm_ticket`: 票务信息表
- `mnjx_ticket_price`: 票价信息表
- `mnjx_ssr`: SSR特殊服务请求表（用于判断儿童旅客）
- `mnjx_nm_rmk`: 姓名组备注表（用于判断革命军人家属）

### 关键业务规则：
1. **时间筛选**: 只查询当天及过去30天的出票记录
2. **Office筛选**: 只查询当前OFFICE所出的票
3. **姓名匹配**: 旅客姓名要完全精确匹配
4. **票号脱敏**: etNumber中间4-7位以*号代替
5. **旅客类型**: 默认为ADT，通过查询SSR和RMK表确定具体类型
   - 查询mnjx_ssr表，ssrType为CHLD时设置为CHD
   - 查询mnjx_nm_rmk表，rmkName为GMJC时设置为GMJC
6. **航段状态**: 根据航段序号奇偶性获取对应的票面状态
7. **特殊处理**: EXCHANGED/REFUNDED状态或PNR状态为DEL时的特殊处理

### 航段信息构建：
- 解析mnjx_ticket_price表的seg_info和issue_info字段
- 根据issue_info确定涉及的航段序号
- 解析seg_info构建航段详细信息
- 处理缺口航段（SA段）的特殊情况
- 处理特殊状态下的航班号和日期显示

## 异常处理
- 姓名信息为空时抛出异常
- Base64解码失败时抛出异常
- 获取Office信息失败时抛出异常
- 其他数据处理异常统一捕获并返回友好错误信息

## 返回值说明
- 成功时返回客票信息列表
- 无数据时返回空列表
- 失败时抛出SguiResultException异常

## 测试用例

### Base64编码示例
```
"张三" -> "5byg5LiJ"
"李四" -> "5Lit5Zub"
```

### 票号脱敏示例
```
原始票号: "7813551234681"
脱敏后: "781-355****681"
```

### 航段序号解析示例
```
"1" -> [1]
"1,3" -> [1, 3]
"1-3" -> [1, 2, 3]
```
