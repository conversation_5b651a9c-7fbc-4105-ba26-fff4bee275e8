package com.swcares.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.*;
import com.swcares.mapper.PnrCommandMapper;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.AvVo;
import com.swcares.obj.vo.EtdzVo;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * pnr操作的相关实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PnrCommandServicePartEtdz {
    private static final Pattern REG_ETDZ_ACTION_CODE = Pattern.compile("^.+((NN|DK)(\\d+)).+$");
    @Resource
    private PnrCommandMapper pnrCommandMapper;
    @Resource
    private ITicketRecordService iTicketRecordService;
    @Resource
    private IMnjxPnrService iMnjxPnrService;
    @Resource
    private IMnjxAirlineService iMnjxAirlineService;
    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;
    @Resource
    private IMnjxPrinterService iMnjxPrinterService;
    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;
    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;
    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;
    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;
    @Resource
    private IMnjxOfficeService iMnjxOfficeService;
    @Resource
    private PnrCommandServicePartAv pnrCommandServicePartAv;
    @Resource
    private PnrCommandServicePartXe pnrCommandServicePartXe;
    @Resource
    private PnrCommandServicePartAt pnrCommandServicePartAt;
    @Resource
    private IPnrOperationService iPnrOperationService;
    @Resource
    private IMnjxTicketPriceService iMnjxTicketPriceService;
    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    EtdzVo etdz(MemoryDataPnr memoryDataPnr, EtdzDto etdzDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        // 检查验证参数是否满足出票的条件
        this.checkParams(memoryDataPnr, etdzDto, mnjxOffice, mnjxSi);
        // 出票
        EtdzVo etdzVo = this.issueTicket(memoryDataPnr, etdzDto, mnjxOffice, mnjxSi);
        // 判断是否已经完全出票完成  完全出票完成之后清空内存PNR
        this.isClearPnr(memoryDataPnr, etdzDto.getOption());
        return etdzVo;
    }

    /**
     * 检查验证参数是否满足出票的条件
     *
     * @param memoryDataPnr pnr的内存大对象
     * @param etdzDto       参数对象
     * @throws UnifiedResultException 统一异常处理
     */
    private void checkParams(MemoryDataPnr memoryDataPnr, EtdzDto etdzDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        // 检查pnr中是否有修改或者删除的姓名
        long count = memoryDataPnr.getPnrNmDtos().stream()
                .filter(p -> CharSequenceUtil.isNotEmpty(p.getUpdateMark()) || CharSequenceUtil.isNotEmpty(p.getMnjxPnrNm().getChangeType()))
                .count();
        if (count > 0) {
            throw new UnifiedResultException(Constant.NEED_EOT);
        }
        //数据校验
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        //PNR校验
        if (ObjectUtil.isEmpty(mnjxPnr)) {
            throw new UnifiedResultException(Constant.NO_PNR);
        }
        // 当继续出票时，检查当前pnr的航班组是否已修改过，和已出票的航段组是否符合
        this.checkTicketedSeg(memoryDataPnr, etdzDto);
        //检查如果是团队旅客时，是否有输入姓名的旅客，如果没有就报错names
        this.checkTeamNames(memoryDataPnr);
        //检查当前PNR是否已经出完票
        this.checkPnrNmTn(memoryDataPnr);
        //出票权限校验 检查当前PNR中所订航段的航班信息，与当前OFFICE的出票授权
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        this.checkPnrSegAndAgent(etdzDto, pnrSegDtos, mnjxOffice);
        //检查航段是否已经起飞
        this.checkPnrSegTime(pnrSegDtos);
        //打票机校验
        this.checkPrinter(etdzDto.getPrintNo(), memoryDataPnr, mnjxOffice, mnjxSi);
        // 检查SSR FOID
        this.checkSsrFoid(memoryDataPnr);
        //检查婴儿行动代码是否是HK
        this.checkActionCode(memoryDataPnr);
        //检查xn婴儿项
        this.checkPnrXn(memoryDataPnr);
        //FN项完整性校验、Fc项完整性校验、fp项完整性校验,FN/IN、FC/IN、FP/IN项反查XN项校验
        this.checkPnrFnAndFcAndFp(memoryDataPnr, etdzDto);
        //出票时限校验,团体票出票时限校验
        this.checkIssueTime(memoryDataPnr);
        //检查当前PNR有没有未提交封口的修改项，如果有，则后台自动发起一次封口。如果封口报错则报错中断出票
        this.checkPnrChangedAndAt(memoryDataPnr, mnjxOffice, mnjxSi);
    }

    private void checkTeamNames(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        if (CollUtil.isNotEmpty(pnrGnDtos) && CollUtil.isEmpty(pnrNmDtos)) {
            throw new UnifiedResultException(Constant.NAMES);
        }
    }

    /**
     * Title: checkTicketedSeg
     * Description: 进行后续出票时，如果航段组发生变化，和已出票的部分不同，报错（改签除外）<br>
     *
     * <AUTHOR>
     * @date 2023/10/11 16:12
     */
    private void checkTicketedSeg(MemoryDataPnr memoryDataPnr, EtdzDto etdzDto) throws UnifiedResultException {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        List<String> segInfoList = pnrSegDtos.stream()
                .filter(p -> !p.isXe() && !"SA".equals(p.getMnjxPnrSeg().getPnrSegType()))
                .map(p -> CharSequenceUtil.format("{} {} {} {} {} {} {}", p.getMnjxPnrSeg().getFlightNo(), p.getMnjxPnrSeg().getOrg(), p.getMnjxPnrSeg().getDst(), p.getMnjxPnrSeg().getFlightDate(), p.getMnjxPnrSeg().getSellCabin(), p.getMnjxPnrSeg().getEstimateOff(), p.getMnjxPnrSeg().getEstimateArr()))
                .collect(Collectors.toList());
        segInfoList.addAll(pnrSegDtos.stream()
                .filter(p -> !p.isXe() && "SA".equals(p.getMnjxPnrSeg().getPnrSegType()))
                .map(p -> CharSequenceUtil.format("SA {} {}", p.getMnjxPnrSeg().getOrg(), p.getMnjxPnrSeg().getDst()))
                .collect(Collectors.toList()));
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream()
                .filter(p -> !p.isXe())
                .collect(Collectors.toList());
        List<String> issuedTicketNoList = pnrNmDtos.stream()
                .flatMap(p -> p.getMnjxPnrNmTickets().stream())
                .map(MnjxPnrNmTicket::getTicketNo)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(issuedTicketNoList)) {
            List<MnjxTicketPrice> ticketPriceList = iMnjxTicketPriceService.lambdaQuery()
                    .in(MnjxTicketPrice::getTicketNo, issuedTicketNoList)
                    .list();
            String segInfo = ticketPriceList.get(0).getSegInfo();
            List<String> issuedTicketSegList = Arrays.asList(segInfo.split("/"));
            // 1.出票时，判断当前出票的航段组和已出票的航段组是否匹配，不匹配报错SEGMENT ERROR
            // 2.多个成人的pnr，包含已出票和未出票的旅客，当已出票旅客进行免费改签后，不允许剩余旅客单独出票
            // 3.多个成人的pnr，包含已出票和未出票的旅客，当已出票旅客进行付费改签但未重新出票时，不允许未出票的旅客单独出票，不允许所有旅客同时出票
            // 判断是否做了改签
            boolean isTri = ObjectUtil.isNotEmpty(memoryDataPnr.getTriVo());
            List<PnrOiDto> oiDtoList = pnrNmDtos.stream()
                    .flatMap(p -> p.getPnrOiDtos().stream())
                    .collect(Collectors.toList());
            if (!isTri && CollUtil.isEmpty(oiDtoList)) {
                if (!CollUtil.isEqualList(segInfoList.stream().sorted().collect(Collectors.toList()), issuedTicketSegList.stream().sorted().collect(Collectors.toList()))) {
                    throw new UnifiedResultException("SEGMENT ERROR \r\n航段组发生了变化!");
                }
            } else {
                // 执行的免费改签，还未封口，此时不允许进行出票操作
                if (isTri && CollUtil.isEmpty(oiDtoList)) {
                    throw new UnifiedResultException(Constant.SEGMENT_ERROR);
                }
                // 执行付费改签，还未封口，此时仅允许指定改签的旅客进行出票
                else if (isTri && CollUtil.isNotEmpty(oiDtoList)) {
                    if (StrUtil.isNotEmpty(etdzDto.getPsgNoStart())) {
                        for (PnrNmDto pnrNmDto : pnrNmDtos) {
                            if (CollUtil.isNotEmpty(pnrNmDto.getPnrOiDtos())) {
                                int pnrIndex = pnrNmDto.getPnrIndex();
                                if (StrUtil.isEmpty(etdzDto.getPsgNoEnd())) {
                                    if (pnrIndex != Integer.parseInt(etdzDto.getPsgNoStart().replace("P", ""))) {
                                        throw new UnifiedResultException(Constant.SEGMENT_ERROR);
                                    }
                                } else {
                                    if (pnrIndex < Integer.parseInt(etdzDto.getPsgNoStart().replace("P", "")) || pnrIndex > Integer.parseInt(etdzDto.getPsgNoEnd().replace("P", ""))) {
                                        throw new UnifiedResultException(Constant.SEGMENT_ERROR);
                                    }
                                }
                            }
                        }
                    }
                    // 所有一起出票，需要验证是不是所有旅客（包含婴儿）都执行了TRI产生了OI项
                    else {
                        int oiSize = pnrNmDtos.stream()
                                .mapToInt(p -> p.getPnrOiDtos().size())
                                .sum();
                        int xnSize = pnrNmDtos.stream()
                                .mapToInt(p -> p.getPnrXnDtos().size())
                                .sum();
                        if (pnrNmDtos.size() + xnSize != oiSize) {
                            throw new UnifiedResultException(Constant.SEGMENT_ERROR);
                        }
                    }
                }
                // 已执行过付费改签，且已封过口
                else if (!isTri && CollUtil.isNotEmpty(oiDtoList)) {
                    // 判断有oi项的旅客票号票价表里航段和当前航段是否一致
                    for (PnrNmDto pnrNmDto : pnrNmDtos) {
                        if (pnrNmDto.getPnrOiDtos().stream().anyMatch(p -> !p.isXe())) {
                            List<String> nmTicketNoList = pnrNmDto.getMnjxPnrNmTickets().stream()
                                    .map(MnjxPnrNmTicket::getTicketNo)
                                    .collect(Collectors.toList());
                            MnjxTicketPrice ticketPrice = ticketPriceList.stream()
                                    .filter(t -> nmTicketNoList.contains(t.getTicketNo()))
                                    .collect(Collectors.toList())
                                    .get(0);
                            List<String> list = Arrays.asList(ticketPrice.getSegInfo().split("/"));
                            // 航段不一致，付费改签后还没有出票，不允许其他出票
                            if (!CollUtil.isEqualList(segInfoList.stream().sorted().collect(Collectors.toList()), list.stream().sorted().collect(Collectors.toList()))) {
                                if (StrUtil.isNotEmpty(etdzDto.getPsgNoStart())) {
                                    if (CollUtil.isNotEmpty(pnrNmDto.getPnrOiDtos())) {
                                        int pnrIndex = pnrNmDto.getPnrIndex();
                                        if (StrUtil.isEmpty(etdzDto.getPsgNoEnd())) {
                                            if (pnrIndex != Integer.parseInt(etdzDto.getPsgNoStart().replace("P", ""))) {
                                                throw new UnifiedResultException(Constant.SEGMENT_ERROR);
                                            }
                                        } else {
                                            if (pnrIndex < Integer.parseInt(etdzDto.getPsgNoStart().replace("P", "")) || pnrIndex > Integer.parseInt(etdzDto.getPsgNoEnd().replace("P", ""))) {
                                                throw new UnifiedResultException(Constant.SEGMENT_ERROR);
                                            }
                                        }
                                    }
                                }
                                // 所有一起出票，需要验证是不是所有旅客都执行了TRI产生了OI项
                                else {
                                    int oiSize = pnrNmDtos.stream()
                                            .mapToInt(p -> p.getPnrOiDtos().size())
                                            .sum();
                                    int xnSize = pnrNmDtos.stream()
                                            .mapToInt(p -> p.getPnrXnDtos().size())
                                            .sum();
                                    if (pnrNmDtos.size() + xnSize != oiSize) {
                                        throw new UnifiedResultException(Constant.SEGMENT_ERROR);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 检查航段是否已经起飞
     */
    private void checkPnrSegTime(List<PnrSegDto> pnrSegDtos) throws UnifiedResultException {
        boolean anyMatch = pnrSegDtos.stream()
                .filter(s -> !"SA".equals(s.getMnjxPnrSeg().getPnrSegType()))
                .anyMatch(s -> DateUtils.compare(
                        DateUtils.ymdhms2Date(StrUtil.format("{} {}", s.getMnjxPnrSeg().getFlightDate(), DateUtils.comHm2hmis(s.getMnjxPnrSeg().getEstimateOff()))), DateUtils.date()) < 0);
        if (anyMatch) {
            throw new UnifiedResultException(Constant.SEG_EXPIRED);
        }
    }

    /**
     * 检查当前PNR是否已经出完票
     *
     * @param memoryDataPnr 内存大对象
     * @throws UnifiedResultException 统一异常
     */
    private void checkPnrNmTn(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos().stream()
                .filter(p -> !p.isXe())
                .collect(Collectors.toList());
        List<String> nmAndXnIds = this.filterNmAndXnId(pnrNmDtos);
        //统计已经出票的旅客，是否已完全出票
        if (nmAndXnIds.size() == pnrNmTnDtos.size()) {
            throw new UnifiedResultException(Constant.PNR_TICKETED);
        }
    }

    /**
     * 出票权限校验
     */
    private void checkPnrSegAndAgent(EtdzDto etdzDto, List<PnrSegDto> pnrSegDtos, MnjxOffice mnjxOffice) throws UnifiedResultException {
        if (StrUtil.isNotEmpty(etdzDto.getAirlineCode())) {
            MnjxAirline mnjxAirline = iMnjxAirlineService.lambdaQuery().eq(MnjxAirline::getAirlineCode, etdzDto.getAirlineCode()).one();
            if (ObjectUtil.isEmpty(mnjxAirline)) {
                throw new UnifiedResultException(Constant.AIRLINE);
            }
            //查询筛选出某个工作号下授权的所有航空公司信息
            List<MnjxAgentAirline> agentAirlineList = iMnjxAgentAirlineService.lambdaQuery().eq(MnjxAgentAirline::getAgentId, mnjxOffice.getOrgId()).eq(MnjxAgentAirline::getAirlineId, mnjxAirline.getAirlineId()).list();
            if (CollUtil.isEmpty(agentAirlineList)) {
                throw new UnifiedResultException(Constant.AUTHORITY);
            }
        } else {
            List<String> airlineCodes = new ArrayList<>();
            //获取航空公司二字码
            pnrSegDtos.forEach(p -> {
                String airlineCode = StrUtil.subPre(p.getMnjxPnrSeg().getFlightNo(), 2);
                airlineCodes.add(airlineCode);
            });
            //一个pnr下的航段组信息关联到的航空公司信息
            List<String> airlineIds = iMnjxAirlineService.lambdaQuery().in(MnjxAirline::getAirlineCode, airlineCodes).list().stream().map(MnjxAirline::getAirlineId).collect(Collectors.toList());
            //查询筛选出某个部门号所属的机构下授权的所有航空公司信息
            List<String> agentAirlineIds = iMnjxAgentAirlineService.lambdaQuery().eq(MnjxAgentAirline::getAgentId, mnjxOffice.getOrgId()).list().stream().map(MnjxAgentAirline::getAirlineId).collect(Collectors.toList());
            List<String> collect = airlineIds.stream().filter(airlineId -> !agentAirlineIds.contains(airlineId)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                throw new UnifiedResultException(Constant.AUTHORITY);
            }
        }
    }

    /**
     * 打票机校验
     */
    private void checkPrinter(String printNo, MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        MnjxPrinter mnjxPrinter = this.retrievePrinter(printNo, mnjxOffice.getOfficeId());
        //4、打票机序号校验 如果输入的打票机序号在当前OFFICE所属的打票机范围中查找不到，
        if (ObjectUtil.isEmpty(mnjxPrinter)) {
            throw new UnifiedResultException(Constant.DEVICE);
        }
        //5、打票机类型校验，如果打票机找到，但是打票机类型不是4，报错：DEVICE TYPE
        if (!Constant.STR_FOUR.equals(mnjxPrinter.getPrinterType())) {
            throw new UnifiedResultException(Constant.DEVICE_TYPE);
        }
        // 6、打票机未建控或者不是由当前登录工作号所建控，如果否，报错：INACTIVE
        if (StrUtil.isEmpty(mnjxPrinter.getSiId()) || !mnjxSi.getSiId().equals(mnjxPrinter.getSiId())) {
            throw new UnifiedResultException(Constant.INACTIVE);
        }
        //7、打票机状态（STATUS）是否是”UP“，如果否，报错：DEVICE STATUS
        if (!Constant.UP.equals(mnjxPrinter.getPrinterStatus())) {
            throw new UnifiedResultException(Constant.DEVICE_STATUS);
        }
        //  8、打票机属性（ATTRIBUTE)是否是”TAT/ET“，如果否，报错：DEVICE ATTRIBUTE
        if (!Constant.TAT_ET.equals(mnjxPrinter.getPrintAttribute())) {
            throw new UnifiedResultException(Constant.DEVICE_ATTRIBUTE);
        }
        //9、打票机输入状态（INPUT）是否是ACTIVE，如果否，报错：INPUT INACTIVE
        if (!Constant.ACTIVE.equals(mnjxPrinter.getInputStatus())) {
            throw new UnifiedResultException(Constant.INPUT_INACTIVE);
        }
        //检查打票机如果未上票，则报错：STOCK，如果已上票，则判断LAST TKT是否为空，
        if (ObjectUtils.isNull(mnjxPrinter.getTicketStart()) && ObjectUtils.isNull(mnjxPrinter.getTicketEnd())) {
            throw new UnifiedResultException(Constant.STOCK);
        } else {
            long filterPnrSegDtosCount = pnrSegDtos.stream().filter(pnrSegDto -> !pnrSegDto.isXe()).count();
            //每个旅客总票数
            int psgNumToTal = NumberUtils.ceilDiv(Long.valueOf(filterPnrSegDtosCount).intValue(), Constant.TWO);
            //总票数(为什么要改造，提供精准计算)
            int totalTicket = Double.valueOf(NumberUtils.mul(Integer.valueOf(pnrNmDtos.size()).doubleValue(), Integer.valueOf(psgNumToTal).doubleValue())).intValue();
            //  如果不为空，则判断所上票的截止票号与LAST TKT所代表票号之间剩余的票够不够出当前指定的票数，如果不够，报错：STOCK
            //  如果为空，  则判断所上票的截止票号与起始票号之间剩余的票够不够出当前指定的票数，如果不够，报错：STOCK
            BigInteger ticketNum = (mnjxPrinter.getLastTicket() != null) ? mnjxPrinter.getTicketEnd().subtract(mnjxPrinter.getLastTicket()) : mnjxPrinter.getTicketEnd().subtract(mnjxPrinter.getTicketStart());
            if (ticketNum.compareTo(BigInteger.valueOf(totalTicket)) < Constant.ZERO) {
                throw new UnifiedResultException(Constant.STOCK);
            }
        }
    }

    /**
     * 检查当前PNR有没有未提交封口的修改项
     */
    private void checkPnrChangedAndAt(MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        boolean pnrChanged = memoryDataPnr.isPnrChanged();
        //如果有未提交封口的修改项，则后台自动发起一次封口
        if (pnrChanged) {
            try {
                log.debug("在出票的时候{}封口", DateUtils.now());
                pnrCommandServicePartAt.at(memoryDataPnr, mnjxOffice, mnjxSi);
                // 由于是etdz调用的封口，不会重新提取PNR，如果从订座开始到etdz中间没有执行过一次封口，这里需要把item的行动代码修改
                List<String> existHistoryPnrItem = memoryDataPnr.getExistHistoryPnrItem();
                Iterator<String> iterator = existHistoryPnrItem.iterator();
                List<String> newItem = new ArrayList<>();
                while (iterator.hasNext()) {
                    String e = iterator.next();
                    if (ReUtil.isMatch(REG_ETDZ_ACTION_CODE, e)) {
                        List<String> allGroups = ReUtil.getAllGroups(REG_ETDZ_ACTION_CODE, e);
                        String actionCodeSeatNum = allGroups.get(1);
                        String seatNum = allGroups.get(3);
                        if (e.startsWith("SSR:SSR INFT")) {
                            e = e.replace(actionCodeSeatNum, StrUtil.format("KK{}", seatNum));
                        } else {
                            e = e.replace(actionCodeSeatNum, StrUtil.format("HK{}", seatNum));
                        }
                        newItem.add(e);
                        iterator.remove();
                    }
                }
                if (CollUtil.isNotEmpty(newItem)) {
                    existHistoryPnrItem.addAll(newItem);
                }
            } catch (Exception e) {
                this.resetMemoryDataPnr(memoryDataPnr);
                throw e;
            }
        }
    }

    /**
     * 检查SSR FOID
     */
    private void checkSsrFoid(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        if (CollUtil.isEmpty(pnrGnDtos)) {
            List<String> nmIds = new ArrayList<>();
            List<String> ssrNmIds = new ArrayList<>();
            for (PnrNmDto p : pnrNmDtos) {
                if (!p.isXe()) {
                    List<PnrSsrDto> pnrSsrDtos = p.getPnrSsrDtos();
                    for (PnrSsrDto pnrSsrDto : pnrSsrDtos) {
                        if (!pnrSsrDto.isXe() && !Constant.ACTION_CODE_XX.equals(pnrSsrDto.getMnjxNmSsr().getActionCode()) && Constant.SSR_TYPE_FOID.equals(pnrSsrDto.getMnjxNmSsr().getSsrType())) {
                            ssrNmIds.add(pnrSsrDto.getMnjxNmSsr().getPnrNmId());
                        }
                    }
                    nmIds.add(p.getMnjxPnrNm().getPnrNmId());
                }
            }
            nmIds = CollUtil.distinct(nmIds);
            ssrNmIds = CollUtil.distinct(ssrNmIds);
            if (nmIds.size() != ssrNmIds.size()) {
                throw new UnifiedResultException(Constant.NO_VALID_FOID);
            }
        }
    }

    /**
     * 遍历检查每一项行动代码
     */
    private void checkActionCode(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            List<PnrXnDto> pnrXnDtos = pnrNmDto.getPnrXnDtos();
            if (CollUtil.isNotEmpty(pnrXnDtos)) {
                //遍历每一行SSR INFT信息，如果订座状态不是HK，报错：ACTION CODE
                List<PnrSsrDto> pnrSsrDtos = pnrNmDto.getPnrSsrDtos().stream()
                        .filter(pnrSsrDto -> Constant.SSR_TYPE_INFT.equals(pnrSsrDto.getMnjxNmSsr().getSsrType()))
                        .filter(pnrSsrDto -> Constant.HK.equals(pnrSsrDto.getMnjxNmSsr().getActionCode()))
                        .collect(Collectors.toList());
                if (CollUtil.isEmpty(pnrSsrDtos)) {
                    throw new UnifiedResultException(Constant.ACTION_CODE);
                }
            }
        }
    }

    /**
     * 检查xn婴儿项 如果有xn项
     * 每个婴儿项对应每个航段，有一条SSR INFT信息。例如一个成人带一个婴儿，订了两个航段的座，那么，应该有两条SSR INFT信息
     */
    private void checkPnrXn(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        long segNum = memoryDataPnr.getPnrSegDtos()
                .stream()
                .filter(s -> !"SA".equals(s.getMnjxPnrSeg().getPnrSegType())).count();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            List<PnrXnDto> pnrXnDtos = pnrNmDto.getPnrXnDtos();
            List<PnrSsrDto> ssrInfts = pnrNmDto.getPnrSsrDtos().stream().filter(s -> Constant.INFT.equals(s.getMnjxNmSsr().getSsrType())).collect(Collectors.toList());
            //检查PNR有没有XN婴儿项，如果有，检查每一个婴儿项是否有相应的SSR INFT项，要求是每个婴儿项对应每个航段，有一条SSR INFT信息。例如一个成人带一个婴儿，订了两个航段的座，那么，应该有两条SSR INFT信息。如果信息不足，
            // 检查INFT的行动代码是不是HK
            //报错：PLEASE INPUT SSR INFT AND NEED AIRLINE CONFIRM IT
            if (CollUtil.isNotEmpty(pnrXnDtos)) {
                long ssrInftNum = ssrInfts.size();
                if (segNum != ssrInftNum || ssrInfts.stream().anyMatch(s -> !Constant.HK.equals(s.getMnjxNmSsr().getActionCode()))) {
                    throw new UnifiedResultException(Constant.XN_INFT_ERROR);
                }
            }
        }
    }

    /**
     * FN项完整性校验、Fc项完整性校验、FP项完整性校验
     */
    private void checkPnrFnAndFcAndFp(MemoryDataPnr memoryDataPnr, EtdzDto etdzDto) throws UnifiedResultException {
        String option = etdzDto.getOption();
        //出票组旅客
        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos().stream().filter(t -> !t.isXe()).collect(Collectors.toList());
        List<PnrNmDto> pnrNmDtos = filterNmOrXnTn(memoryDataPnr.getPnrNmDtos(), StrUtil.EMPTY, pnrNmTnDtos);

        //FN------------PNR上整体fn项
        //成人
        List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxPnrFn().getIsBaby().equals(0)).collect(Collectors.toList());
        //婴儿
        List<PnrFnDto> pnrXnFnDtos = memoryDataPnr.getPnrFnDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxPnrFn().getIsBaby().equals(1)).collect(Collectors.toList());
        List<PnrNmFnDto> nmFnList = new ArrayList<>();
        List<PnrNmFnDto> nmXnFnList = new ArrayList<>();

        //FC------------PNR上整体fc项
        //成人
        List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxPnrFc().getIsBaby().equals(0)).collect(Collectors.toList());
        //婴儿
        List<PnrFcDto> pnrXnFcDtos = memoryDataPnr.getPnrFcDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxPnrFc().getIsBaby().equals(1)).collect(Collectors.toList());
        List<PnrNmFcDto> nmFcList = new ArrayList<>();
        List<PnrNmFcDto> nmXnFcList = new ArrayList<>();

        //FP------------PNR上整体fp项
        //成人
        List<PnrFpDto> pnrFpDtos = memoryDataPnr.getPnrFpDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxPnrFp().getIsBaby().equals(0)).collect(Collectors.toList());
        //婴儿
        List<PnrFpDto> pnrXnFpDtos = memoryDataPnr.getPnrFpDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxPnrFp().getIsBaby().equals(1)).collect(Collectors.toList());
        List<PnrNmFpDto> nmFpList = new ArrayList<>();
        List<PnrNmFpDto> nmXnFpList = new ArrayList<>();

        List<PnrXnDto> pnrXnList = new ArrayList<>();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            //出票组的婴儿
            List<PnrXnDto> xnDtos = pnrNmDto.getPnrXnDtos().stream().filter(xn -> !xn.isXe()).collect(Collectors.toList());
            pnrXnList.addAll(xnDtos);

            //FN------------挂在姓名组fn(旅客自身的fn项)
            List<PnrNmFnDto> nmFnDtos = pnrNmDto.getPnrNmFnDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxNmFn().getIsBaby().equals(0)).collect(Collectors.toList());
            nmFnList.addAll(nmFnDtos);
            //挂在姓名组的婴儿fn(自身fn)
            List<PnrNmFnDto> nmXnFnDtos = pnrNmDto.getPnrNmFnDtos().stream().filter(fn -> !fn.isXe()).filter(k -> k.getMnjxNmFn().getIsBaby().equals(1)).collect(Collectors.toList());
            nmXnFnList.addAll(nmXnFnDtos);

            //FC------------挂在姓名组fc(旅客自身的fc项)
            List<PnrNmFcDto> nmFcDtos = pnrNmDto.getPnrNmFcDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxNmFc().getIsBaby().equals(0)).collect(Collectors.toList());
            nmFcList.addAll(nmFcDtos);
            //挂在姓名组的婴儿fc(自身fc)
            List<PnrNmFcDto> nmXnFcDtos = pnrNmDto.getPnrNmFcDtos().stream().filter(fn -> !fn.isXe()).filter(k -> k.getMnjxNmFc().getIsBaby().equals(1)).collect(Collectors.toList());
            nmXnFcList.addAll(nmXnFcDtos);

            //FP------------挂在姓名组fp(旅客自身的fp项)
            List<PnrNmFpDto> nmFpDtos = pnrNmDto.getPnrNmFpDtos().stream().filter(f -> !f.isXe()).filter(f -> f.getMnjxNmFp().getIsBaby().equals(0)).collect(Collectors.toList());
            nmFpList.addAll(nmFpDtos);
            //挂在姓名组的婴儿fc(自身fc)
            List<PnrNmFpDto> nmXnFpDtos = pnrNmDto.getPnrNmFpDtos().stream().filter(fn -> !fn.isXe()).filter(k -> k.getMnjxNmFp().getIsBaby().equals(1)).collect(Collectors.toList());
            nmXnFpList.addAll(nmXnFpDtos);

            //FN/IN、FC/IN、FP/IN项反查XN项校验
            //带旅客编号的情况，查看当前PNR中的XN/IN记录中，是否存在一个XN/IN记录的旅客编号与当前旅客编号相同
            if (CollUtil.isNotEmpty(nmXnFnDtos) || CollUtil.isNotEmpty(nmXnFcDtos) || CollUtil.isNotEmpty(nmXnFpDtos)) {
                if (CollUtil.isEmpty(xnDtos)) {
                    MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();
                    Integer pnrIndex = mnjxPnrNm.getPnrIndex();
                    throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/XN/P{}", pnrIndex));
                }
            }
        }
        //FN/IN、FC/IN、FP/IN项反查XN项校验
        //没有带旅客编号的情况,查看当前PNR中的XN/IN记录数如果为零
        if (CollUtil.isNotEmpty(pnrXnFnDtos) || CollUtil.isNotEmpty(pnrXnFcDtos) || CollUtil.isNotEmpty(pnrXnFpDtos)) {
            if (CollUtil.isEmpty(pnrXnList)) {
                throw new UnifiedResultException(Constant.INCOMPLETE_PNR_XN);
            }
        }
        //FN-------- 判断当前PNR是否存在关联于PNR上的整体FN项(成人或者儿童)，如果不存在，遍历所有成人旅客,检查是否存在旅客无直接关联于旅客自身的FN项
        if (!Constant.INF.equals(option)) {
            if (CollUtil.isEmpty(pnrFnDtos)) {
                if (CollUtil.isEmpty(nmFnList)) {
                    throw new UnifiedResultException(Constant.INCOMPLETE_PNR_FN);
                } else {
                    if (nmFnList.size() != pnrNmDtos.size()) {
                        //缺少fn
                        for (PnrNmDto nmDto : pnrNmDtos) {
                            if (StrUtil.isNotEmpty(etdzDto.getPsgNoStart())) {
                                List<PnrNmFnDto> nmFns = nmFnList.stream()
                                        .filter(nfn -> nmDto.getMnjxPnrNm().getPnrNmId().equals(nfn.getMnjxNmFn().getPnrNmId()))
                                        .collect(Collectors.toList());
                                Integer pnrIndex = null;
                                if (CollUtil.isEmpty(nmFns)) {
                                    MnjxPnrNm mnjxPnrNm = nmDto.getMnjxPnrNm();
                                    pnrIndex = mnjxPnrNm.getPnrIndex();
                                }
                                int start = Integer.parseInt(etdzDto.getPsgNoStart().replace("P", ""));
                                if (StrUtil.isNotEmpty(etdzDto.getPsgNoEnd())) {
                                    int end = Integer.parseInt(etdzDto.getPsgNoEnd().replace("P", ""));
                                    if (ObjectUtil.isNotEmpty(pnrIndex) && pnrIndex > start && pnrIndex < end) {
                                        throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FN/P{}", pnrIndex));
                                    }
                                } else if (ObjectUtil.isNotEmpty(pnrIndex) && start == pnrIndex) {
                                    throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FN/P{}", pnrIndex));
                                }
                            } else {
                                List<PnrNmFnDto> nmFns = nmFnList.stream()
                                        .filter(nfn -> nmDto.getMnjxPnrNm().getPnrNmId().equals(nfn.getMnjxNmFn().getPnrNmId()))
                                        .collect(Collectors.toList());
                                if (CollUtil.isEmpty(nmFns)) {
                                    MnjxPnrNm mnjxPnrNm = nmDto.getMnjxPnrNm();
                                    Integer pnrIndex = mnjxPnrNm.getPnrIndex();
                                    throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FN/P{}", pnrIndex));
                                }
                            }
                        }
                    }
                }
            }
        }

        //判断是否有婴儿
        if (CollUtil.isNotEmpty(pnrXnList) && CollUtil.isEmpty(pnrXnFnDtos) && CollUtil.isEmpty(nmXnFnList)) {
            //判断当前PNR是否存在关联于PNR上的整体婴儿类型的FN项（FN/IN）(婴儿)
            throw new UnifiedResultException(Constant.INCOMPLETE_PNR_FN_IN);
        }

        //1、FC-------- 判断当前PNR是否存在关联于PNR上的整体Fc项(成人或者儿童)，如果不存在，遍历所有成人旅客,检查是否存在旅客无直接关联于旅客自身的Fc项
        //2、如果是只出婴儿票，成人fc 不进行校验
        if (!Constant.INF.equals(option)) {
            if (CollUtil.isEmpty(pnrFcDtos)) {
                if (CollUtil.isEmpty(nmFcList)) {
                    throw new UnifiedResultException(Constant.INCOMPLETE_PNR_FC);
                } else {
                    if (nmFcList.size() != pnrNmDtos.size()) {
                        //缺少fc
                        for (PnrNmDto nmDto : pnrNmDtos) {
                            if (StrUtil.isNotEmpty(etdzDto.getPsgNoStart())) {
                                List<PnrNmFcDto> nmFcs = nmFcList.stream()
                                        .filter(nfc -> nmDto.getMnjxPnrNm().getPnrNmId().equals(nfc.getMnjxNmFc().getPnrNmId()))
                                        .collect(Collectors.toList());
                                Integer pnrIndex = null;
                                List<PnrNmTnDto> nmTns = pnrNmTnDtos.stream().filter(n -> nmDto.getMnjxPnrNm().getPnrNmId().equals(n.getMnjxPnrNmTn().getPnrNmId())).collect(Collectors.toList());
                                if (CollUtil.isEmpty(nmFcs) && CollUtil.isEmpty(nmTns)) {
                                    MnjxPnrNm mnjxPnrNm = nmDto.getMnjxPnrNm();
                                    pnrIndex = mnjxPnrNm.getPnrIndex();
                                }
                                int start = Integer.parseInt(etdzDto.getPsgNoStart().replace("P", ""));
                                if (StrUtil.isNotEmpty(etdzDto.getPsgNoEnd())) {
                                    int end = Integer.parseInt(etdzDto.getPsgNoEnd().replace("P", ""));
                                    if (ObjectUtil.isNotEmpty(pnrIndex) && pnrIndex > start && pnrIndex < end) {
                                        throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FC/P{}", pnrIndex));
                                    }
                                } else if (ObjectUtil.isNotEmpty(pnrIndex) && start == pnrIndex) {
                                    throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FC/P{}", pnrIndex));
                                }
                            } else {
                                List<PnrNmFcDto> nmFcs = nmFcList.stream()
                                        .filter(nfc -> nmDto.getMnjxPnrNm().getPnrNmId().equals(nfc.getMnjxNmFc().getPnrNmId()))
                                        .collect(Collectors.toList());
                                List<PnrNmTnDto> nmTns = pnrNmTnDtos.stream().filter(n -> nmDto.getMnjxPnrNm().getPnrNmId().equals(n.getMnjxPnrNmTn().getPnrNmId())).collect(Collectors.toList());
                                if (CollUtil.isEmpty(nmFcs) && CollUtil.isEmpty(nmTns)) {
                                    MnjxPnrNm mnjxPnrNm = nmDto.getMnjxPnrNm();
                                    Integer pnrIndex = mnjxPnrNm.getPnrIndex();
                                    throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FC/P{}", pnrIndex));
                                }
                            }
                        }
                    }
                }
            }
        }
        //判断是否有婴儿
        if (CollUtil.isNotEmpty(pnrXnList) && CollUtil.isEmpty(pnrXnFcDtos) && CollUtil.isEmpty(nmXnFcList)) {
            //判断当前PNR是否存在关联于PNR上的整体婴儿类型的FC项（FC/IN）(婴儿)
            throw new UnifiedResultException(Constant.INCOMPLETE_PNR_FC_IN);
        }
        // 验证当前的运价组和航段是否匹配（可能存在先加了运价后又进行过航段添加或ES）
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        if (CollUtil.isNotEmpty(pnrFcDtos)) {
            this.validatePnrFcAndSeg(pnrFcDtos, pnrSegDtos);
        }
        if (CollUtil.isNotEmpty(pnrXnFcDtos)) {
            this.validatePnrFcAndSeg(pnrXnFcDtos, pnrSegDtos);
        }
        if (CollUtil.isNotEmpty(nmFcList)) {
            this.validateNmFcAndSeg(nmFcList, pnrSegDtos);
        }
        if (CollUtil.isNotEmpty(nmXnFcList)) {
            this.validateNmFcAndSeg(nmXnFcList, pnrSegDtos);
        }

        //FP--------判断当前PNR是否存在关联于PNR上的整体FP项(成人或者儿童)，如果不存在，遍历所有成人旅客,检查是否存在旅客无直接关联于旅客自身的FP项
        if (!Constant.INF.equals(option)) {
            if (CollUtil.isEmpty(pnrFpDtos)) {
                if (CollUtil.isEmpty(nmFpList)) {
                    throw new UnifiedResultException(Constant.INCOMPLETE_PNR_FP);
                } else {
                    if (nmFpList.size() != pnrNmDtos.size()) {
                        //缺少fn
                        for (PnrNmDto nmDto : pnrNmDtos) {
                            if (StrUtil.isNotEmpty(etdzDto.getPsgNoStart())) {
                                List<PnrNmFpDto> nmFps = nmFpList.stream()
                                        .filter(nfp -> nmDto.getMnjxPnrNm().getPnrNmId().equals(nfp.getMnjxNmFp().getPnrNmId()))
                                        .collect(Collectors.toList());
                                Integer pnrIndex = null;
                                if (CollUtil.isEmpty(nmFps)) {
                                    MnjxPnrNm mnjxPnrNm = nmDto.getMnjxPnrNm();
                                    pnrIndex = mnjxPnrNm.getPnrIndex();
                                }
                                int start = Integer.parseInt(etdzDto.getPsgNoStart().replace("P", ""));
                                if (StrUtil.isNotEmpty(etdzDto.getPsgNoEnd())) {
                                    int end = Integer.parseInt(etdzDto.getPsgNoEnd().replace("P", ""));
                                    if (ObjectUtil.isNotEmpty(pnrIndex) && pnrIndex > start && pnrIndex < end) {
                                        throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FP/P{}", pnrIndex));
                                    }
                                } else if (ObjectUtil.isNotEmpty(pnrIndex) && start == pnrIndex) {
                                    throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FP/P{}", pnrIndex));
                                }
                            } else {
                                List<PnrNmFpDto> nmFps = nmFpList.stream()
                                        .filter(nfp -> nmDto.getMnjxPnrNm().getPnrNmId().equals(nfp.getMnjxNmFp().getPnrNmId()))
                                        .collect(Collectors.toList());
                                if (CollUtil.isEmpty(nmFps)) {
                                    MnjxPnrNm mnjxPnrNm = nmDto.getMnjxPnrNm();
                                    Integer pnrIndex = mnjxPnrNm.getPnrIndex();
                                    throw new UnifiedResultException(StrUtil.format("INCOMPLETE PNR/FP/P{}", pnrIndex));
                                }
                            }
                        }
                    }
                }
            }
        }

        //判断是否有婴儿
        if (CollUtil.isNotEmpty(pnrXnList) && CollUtil.isEmpty(pnrXnFpDtos) && CollUtil.isEmpty(nmXnFpList)) {
            //判断当前PNR是否存在关联于PNR上的整体婴儿类型的FP项（FP/IN）(婴儿)
            throw new UnifiedResultException(Constant.INCOMPLETE_PNR_FP_IN);
        }

    }

    private void validatePnrFcAndSeg(List<PnrFcDto> fcDtos, List<PnrSegDto> segDtos) throws UnifiedResultException {
        segDtos = segDtos.stream()
                .filter(s -> !s.isXe() && !Constant.SA.equals(s.getMnjxPnrSeg().getPnrSegType()))
                .collect(Collectors.toList());
        Pattern pattern = Pattern.compile("[A-Z0-9]{2}\\s[A-Z]{3}\\s\\d+\\.\\d+[A-Z]");
        for (PnrFcDto fcDto : fcDtos) {
            List<String> segPriceList = new ArrayList<>();
            String fcValue = fcDto.getMnjxPnrFc().getInputValue();
            Matcher matcher = pattern.matcher(fcValue);
            while (matcher.find()) {
                String segPrice = matcher.group();
                segPriceList.add(segPrice);
            }
            if (segPriceList.size() != segDtos.size()) {
                throw new UnifiedResultException(Constant.RE_QUOTE);
            }
            for (int i = 0; i < segDtos.size(); i++) {
                MnjxPnrSeg pnrSeg = segDtos.get(i).getMnjxPnrSeg();
                String segPrice = segPriceList.get(i);
                String[] split = segPrice.split(Constant.ONE_SPACE);
                String airlineCode = split[0];
                String dst = split[1];
                String priceSellCabin = split[2];
                if (!airlineCode.equals(pnrSeg.getFlightNo().substring(0, 2)) || !dst.equals(pnrSeg.getDst()) || !priceSellCabin.substring(priceSellCabin.length() - 1).equals(pnrSeg.getSellCabin())) {
                    throw new UnifiedResultException(Constant.RE_QUOTE);
                }
            }
        }
    }

    private void validateNmFcAndSeg(List<PnrNmFcDto> nmFcDtos, List<PnrSegDto> segDtos) throws UnifiedResultException {
        segDtos = segDtos.stream()
                .filter(s -> !s.isXe() && !Constant.SA.equals(s.getMnjxPnrSeg().getPnrSegType()))
                .collect(Collectors.toList());
        Pattern pattern = Pattern.compile("[A-Z0-9]{2}\\s[A-Z]{3}\\s\\d+\\.\\d+[A-Z]");
        for (PnrNmFcDto nmFcDto : nmFcDtos) {
            List<String> segPriceList = new ArrayList<>();
            String nmFcValue = nmFcDto.getMnjxNmFc().getInputValue();
            Matcher matcher = pattern.matcher(nmFcValue);
            while (matcher.find()) {
                String segPrice = matcher.group();
                segPriceList.add(segPrice);
            }
            if (segPriceList.size() != segDtos.size()) {
                throw new UnifiedResultException(Constant.RE_QUOTE);
            }
            for (int i = 0; i < segDtos.size(); i++) {
                MnjxPnrSeg pnrSeg = segDtos.get(i).getMnjxPnrSeg();
                String segPrice = segPriceList.get(i);
                String[] split = segPrice.split(Constant.ONE_SPACE);
                String airlineCode = split[0];
                String dst = split[1];
                String priceSellCabin = split[2];
                if (!airlineCode.equals(pnrSeg.getFlightNo().substring(0, 2)) || !dst.equals(pnrSeg.getDst()) || !priceSellCabin.substring(priceSellCabin.length() - 1).equals(pnrSeg.getSellCabin())) {
                    throw new UnifiedResultException(Constant.RE_QUOTE);
                }
            }
        }
    }

    /**
     * 检查出票时限
     * 出票时限校验，拿当前时间与PNR中首段航班的出发时间比较，如果已经过了出发时间，则报错：航段过期
     * 团体票出票时限校验(团体票必须在航班出发前72小时之前出票），如果当前PNR是团体旅客，则查看当前时间是否已大于首航段航班出发时间-72小时，如果是，则报错：TKT TIME RESTRICTION   DZ NOT ALLOWED
     */
    private void checkIssueTime(MemoryDataPnr memoryDataPnr) throws UnifiedResultException {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream().filter(seg -> !seg.isXe()).collect(Collectors.toList());
        //当前时间与PNR中首段航班的出发时间比较
        MnjxPnrSeg mnjxPnrSeg = pnrSegDtos.get(0).getMnjxPnrSeg();
        String segFlightDate = mnjxPnrSeg.getFlightDate();
        List<MnjxPlanSection> planSections = pnrCommandMapper.retrievePlanSection(mnjxPnrSeg.getFlightNo(), segFlightDate);
        if (CollUtil.isEmpty(planSections)) {
            planSections = pnrCommandMapper.retrievePlanSection(mnjxPnrSeg.getCarrierFlight(), segFlightDate);
        }
        if (CollUtil.isEmpty(planSections)) {
            throw new UnifiedResultException("没有航节数据");
        }
        String estimateOff = planSections.get(0).getEstimateOff();
        //首航段航班出发时间
        String fltDateTime = StrUtils.format("{} {}", segFlightDate, DateUtils.comHm2hmis(estimateOff));
        int compare = DateUtil.compare(DateUtil.date(), DateUtils.ymdhms2Date(fltDateTime));
        if (compare > 0) {
            throw new UnifiedResultException(Constant.SEG_EXPIRED);
        }
        //团体票出票时限校验
        List<PnrGnDto> pnrGnDtos = memoryDataPnr.getPnrGnDtos();
        //暂时关闭，便于跑数据
        boolean hasGn = CollUtil.isNotEmpty(pnrGnDtos) && pnrGnDtos.stream().anyMatch(pnrGnDto -> !pnrGnDto.isXe());
//        if (hasGn) {
//            int gnCompare = DateUtil.compare(DateUtil.date(), DateUtil.offsetDay(DateUtil.parseDate(fltDateTime), Constant.MINUS_THREE));
//            if (gnCompare > 0) {
//                throw new UnifiedResultException(Constant.GN_ISSUE_TIME);
//            }
//        }
    }

    /**
     * 出票的业务处理
     *
     * @param etdzDto    出票参数对象
     * @param mnjxOffice 当前部门
     * @param mnjxSi     当前工作号
     * @return 回显
     * @throws UnifiedResultException 统一异常
     */
    private EtdzVo issueTicket(MemoryDataPnr memoryDataPnr, EtdzDto etdzDto, MnjxOffice mnjxOffice, MnjxSi mnjxSi) throws UnifiedResultException {
        //获取需要出票的旅客
        List<PnrNmDto> pnrNmDtos = this.getPnrNmDtoList(memoryDataPnr, etdzDto);
        //计算票号 生成tn,nmTicket,ssr记录
        EtdzVo etdzVo = this.structureTicketNo(etdzDto, pnrNmDtos, memoryDataPnr, mnjxOffice);
        // 新增/修改票号票价对应表
        this.structureTicketPrice(pnrNmDtos, memoryDataPnr);
        //当前出票旅客的TK/TL、FC、EI记录做删除操作,移到历史部分
        this.deleteTkAndFcAndEi(memoryDataPnr, pnrNmDtos, etdzDto);
        //生成TK/T记录行
        this.structureTk(memoryDataPnr);
        //生成RMK TJ 记录行,生成RMK TR记录行
        this.structureRmk(memoryDataPnr);
        //入库 新增mnjxPsgcki数据 mnjxPsgSeat数据
        this.structurePsgCkiAndSeat(pnrNmDtos, memoryDataPnr);
        //新增客票操作记录
        this.structureOperateRecord(pnrNmDtos, mnjxSi, memoryDataPnr);
        memoryDataPnr.setPnrChanged(true);
        // 调一次recall，因为有新增的TN和TK T，以及删除了TK TL、FC，对PNR序号重新排序，同时操作记录的生成逻辑也在recall里面
        iPnrOperationService.recall(memoryDataPnr);
        memoryDataPnr.setEtdz(true);
        //封口
        this.checkPnrChangedAndAt(memoryDataPnr, mnjxOffice, mnjxSi);
        // 当前操作的是分离出来的PNR时，需要将之前的PNR也进行一次封口处理
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        if (ObjectUtil.isNotEmpty(memoryData) && ObjectUtil.isNotEmpty(memoryData.getTmpMemoryDataPnr().getMnjxPnr()) && StrUtil.isNotEmpty(memoryData.getTmpMemoryDataPnr().getMnjxPnr().getPnrId())) {
            MemoryDataPnr oldPnr = memoryData.getMemoryDataPnr();
            this.checkPnrChangedAndAt(oldPnr, mnjxOffice, mnjxSi);
            oldPnr.clearPnr();
        }
        // 客票变更，修改电子客票状态
        this.reviseOldTicket(memoryDataPnr, mnjxSi);
        //回显
        return etdzVo;
    }

    private void structureTicketPrice(List<PnrNmDto> pnrNmDtos, MemoryDataPnr memoryDataPnr) {
        List<MnjxTicketPrice> ticketPriceList = new ArrayList<>();

        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream()
                .filter(p -> !p.isXe())
                .collect(Collectors.toList());
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            List<String> xnTicketNoList = pnrNmDto.getPnrSsrDtos().stream()
                    .filter(s -> Constant.TKNE.equals(s.getMnjxNmSsr().getSsrType()) && s.getMnjxNmSsr().getInputValue().contains("INF"))
                    .map(s -> s.getMnjxNmSsr().getInputValue())
                    .collect(Collectors.toList());
            List<MnjxPnrNmTicket> mnjxPnrNmTickets = pnrNmDto.getMnjxPnrNmTickets();
            List<String> ticketNoList = mnjxPnrNmTickets.stream()
                    .filter(m -> ObjectUtil.isEmpty(iMnjxPnrNmTicketService.getById(m.getNmTicketId())))
                    .map(MnjxPnrNmTicket::getTicketNo)
                    .distinct()
                    .collect(Collectors.toList());
            for (String ticketNo : ticketNoList) {
                boolean isXnTicket = xnTicketNoList.stream().anyMatch(t -> t.contains(ticketNo));
                MnjxTicketPrice ticketPrice = new MnjxTicketPrice();
                String fcInfo;
                String fnInfo;
                String payType;
                StringBuilder segInfoBuilder = new StringBuilder();
                List<PnrNmFcDto> nmFcDtos = pnrNmDto.getPnrNmFcDtos().stream()
                        .filter(f -> isXnTicket ? f.getMnjxNmFc().getIsBaby() == 1 : f.getMnjxNmFc().getIsBaby() == 0)
                        .filter(p -> !p.isXe())
                        .collect(Collectors.toList());
                List<PnrNmFnDto> nmFnDtos = pnrNmDto.getPnrNmFnDtos().stream()
                        .filter(f -> isXnTicket ? f.getMnjxNmFn().getIsBaby() == 1 : f.getMnjxNmFn().getIsBaby() == 0)
                        .filter(p -> !p.isXe())
                        .collect(Collectors.toList());
                List<PnrNmFpDto> nmFpDtos = pnrNmDto.getPnrNmFpDtos().stream()
                        .filter(f -> isXnTicket ? f.getMnjxNmFp().getIsBaby() == 1 : f.getMnjxNmFp().getIsBaby() == 0)
                        .filter(p -> !p.isXe())
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(nmFcDtos)) {
                    fcInfo = nmFcDtos.get(0).getMnjxNmFc().getInputValue();
                } else {
                    List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos().stream()
                            .filter(p -> !p.isXe())
                            .collect(Collectors.toList());
                    fcInfo = pnrFcDtos.stream()
                            .filter(f -> isXnTicket ? f.getMnjxPnrFc().getIsBaby() == 1 : f.getMnjxPnrFc().getIsBaby() == 0)
                            .collect(Collectors.toList())
                            .get(0).getMnjxPnrFc().getInputValue();
                }
                if (CollUtil.isNotEmpty(nmFnDtos)) {
                    fnInfo = nmFnDtos.get(0).getMnjxNmFn().getInputValue();
                } else {
                    List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos().stream()
                            .filter(p -> !p.isXe())
                            .collect(Collectors.toList());
                    fnInfo = pnrFnDtos.stream()
                            .filter(f -> isXnTicket ? f.getMnjxPnrFn().getIsBaby() == 1 : f.getMnjxPnrFn().getIsBaby() == 0)
                            .collect(Collectors.toList())
                            .get(0).getMnjxPnrFn().getInputValue();
                }
                if (CollUtil.isNotEmpty(nmFpDtos)) {
                    payType = nmFpDtos.get(0).getMnjxNmFp().getPayType();
                } else {
                    List<PnrFpDto> pnrFpDtos = memoryDataPnr.getPnrFpDtos().stream()
                            .filter(p -> !p.isXe())
                            .collect(Collectors.toList());
                    payType = pnrFpDtos.stream()
                            .filter(f -> isXnTicket ? f.getMnjxPnrFp().getIsBaby() == 1 : f.getMnjxPnrFp().getIsBaby() == 0)
                            .collect(Collectors.toList())
                            .get(0).getMnjxPnrFp().getPayType();
                }
                ticketPrice.setFcInfo(fcInfo);
                ticketPrice.setFnInfo(fnInfo);
                ticketPrice.setPayType(payType);
                ticketPrice.setTicketNo(ticketNo);
                for (int i = 0; i < pnrSegDtos.size(); i++) {
                    PnrSegDto pnrSegDto = pnrSegDtos.get(i);
                    if (StrUtil.isNotEmpty(pnrSegDto.getMnjxPnrSeg().getFlightNo())) {
                        segInfoBuilder.append(StrUtil.format("{} {} {} {} {} {} {}", pnrSegDto.getMnjxPnrSeg().getFlightNo(), pnrSegDto.getMnjxPnrSeg().getOrg(), pnrSegDto.getMnjxPnrSeg().getDst(), pnrSegDto.getMnjxPnrSeg().getFlightDate(), pnrSegDto.getMnjxPnrSeg().getSellCabin(), pnrSegDto.getMnjxPnrSeg().getEstimateOff(), pnrSegDto.getMnjxPnrSeg().getEstimateArr()));
                    } else {
                        segInfoBuilder.append(StrUtil.format("SA {} {}", pnrSegDto.getMnjxPnrSeg().getOrg(), pnrSegDto.getMnjxPnrSeg().getDst()));
                    }
                    if (i < pnrSegDtos.size() - 1) {
                        segInfoBuilder.append("/");
                    }
                }
                ticketPrice.setSegInfo(segInfoBuilder.toString());
                MnjxPnrNmTn tn = memoryDataPnr.getPnrNmTnDtos().get(0).getMnjxPnrNmTn();
                MnjxPnrNmTicket pnrNmTicket = mnjxPnrNmTickets.stream()
                        .filter(t -> t.getTicketNo().equals(ticketNo))
                        .collect(Collectors.toList())
                        .get(0);
                String issueSegNo;
                if (StrUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
                    Integer pnrSegNo = pnrSegDtos.stream()
                            .filter(s -> pnrNmTicket.getS1Id().equals(s.getMnjxPnrSeg().getPnrSegId()))
                            .collect(Collectors.toList())
                            .get(0)
                            .getMnjxPnrSeg().getPnrSegNo();
                    issueSegNo = pnrSegNo.toString();
                    if (pnrSegDtos.stream().anyMatch(s -> s.getMnjxPnrSeg().getPnrSegNo() > pnrSegNo)) {
                        issueSegNo = issueSegNo + "-" + (pnrSegNo + 1);
                    }
                } else {
                    Integer pnrSegNo = pnrSegDtos.stream()
                            .filter(s -> pnrNmTicket.getS2Id().equals(s.getMnjxPnrSeg().getPnrSegId()))
                            .collect(Collectors.toList())
                            .get(0)
                            .getMnjxPnrSeg().getPnrSegNo();
                    issueSegNo = (pnrSegNo - 1) + "-" + pnrSegNo;
                }
                ticketPrice.setIssueInfo(StrUtil.format("{} {} {} {} {}", tn.getIssuedSiId(), tn.getIssuedAirline(), tn.getIssuedTime(), tn.getPrinterId(), issueSegNo));
                ticketPriceList.add(ticketPrice);
            }
        }
        iMnjxTicketPriceService.saveBatch(ticketPriceList);
    }


    /**
     * 当前出票旅客的TK/TL、FC、EI记录做删除操作,移到历史部分
     */
    private void deleteTkAndFcAndEi(MemoryDataPnr memoryDataPnr, List<PnrNmDto> pnrNmDtos, EtdzDto etdzDto) throws UnifiedResultException {
        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
        List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
        List<PnrEiDto> pnrEiDtos = memoryDataPnr.getPnrEiDtos();
        List<Integer> xeIndexs = new ArrayList<>();

        //pnr上的所有旅客
        List<String> nmAndXnIds = this.filterNmAndXnId(memoryDataPnr.getPnrNmDtos());
        //已经出过票的旅客
        List<PnrNmTnDto> mdPnrTnDtos = memoryDataPnr.getPnrNmTnDtos();
        EtdzRecallDto etdzRecallDto = memoryDataPnr.getEtdzRecallDto();

        //全部出票的情况
        if (nmAndXnIds.size() == mdPnrTnDtos.size()) {
            //pnr整体上的tk的pnr序号
            pnrTkDtos.stream()
                    .filter(p -> !p.isXe())
                    .forEach(tk -> {
                        xeIndexs.add(tk.getPnrIndex());
                        etdzRecallDto.getTkIds().add(tk.getMnjxPnrTk().getPnrTkId());
                    });
            //pnr整体上的fc的pnr序号
            pnrFcDtos.stream()
                    .filter(p -> !p.isXe())
                    .forEach(fc -> {
                        xeIndexs.add(fc.getPnrIndex());
                        etdzRecallDto.getFcIds().add(fc.getMnjxPnrFc().getPnrFcId());
                    });
            //pnr整体上的ei的pnr序号
            pnrEiDtos.stream()
                    .filter(p -> !p.isXe())
                    .forEach(ei -> {
                        xeIndexs.add(ei.getPnrIndex());
                        etdzRecallDto.getEiIds().add(ei.getMnjxPnrEi().getPnrEiId());
                    });
        }
        //部分出票+全部出票
        //挂在旅客上的EI fc
        this.filterXeFcEiId(xeIndexs, pnrNmDtos, StrUtils.toString(etdzDto.getOption()), etdzRecallDto);
        XeDto xeDto = new XeDto();
        List<Integer> distinctXeIndexs = xeIndexs.stream()
                .distinct()
                .collect(Collectors.toList());
        xeDto.setXeIndexs(distinctXeIndexs);
        pnrCommandServicePartXe.xe(memoryDataPnr, xeDto);
    }

    /**
     * 构建tn
     */
    private MnjxPnrNmTn structureTn(String airlineCode, MnjxPrinter mnjxPrinter, String nmId, String option) {
        MnjxPnrNmTn pnrNmTn = new MnjxPnrNmTn();
        pnrNmTn.setTnId(IdUtil.getSnowflake(1, 1).nextIdStr());
        if (CharSequenceUtil.equals(option, Constant.ADL)) {
            pnrNmTn.setPnrNmId(nmId);
        } else {
            pnrNmTn.setNmXnId(nmId);
        }
        pnrNmTn.setPrinterId(mnjxPrinter.getPrinterId());
        pnrNmTn.setIssuedTime(DateUtil.now());
        pnrNmTn.setIssuedSiId(mnjxPrinter.getSiId());
        pnrNmTn.setIssuedAirline(airlineCode);
        return pnrNmTn;
    }

    /**
     * 构建nmTicket ssr
     */
    private BigInteger structureNmTicket(List<PnrNmTnDto> pnrNmTnDtos, PnrNmDto pnrNmDto, List<PnrSegDto> pnrSegDtos, MnjxPnrNmTn pnrNmTn, BigInteger ticketStart, BigInteger issueTicket, String settlementCode, String option, String isEt, EtdzRecallDto etdzRecallDto) {
        PnrNmTnDto pnrNmTnDto = new PnrNmTnDto();
        BigInteger ticketNoEnd;
        //出票起始票号
        BigInteger ticketNoStart = null;
        //存储客票
        List<MnjxPnrNmTicket> mnjxPnrNmTickets = pnrNmDto.getMnjxPnrNmTickets();
        MnjxPnrNmTicket pnrNmTicket = new MnjxPnrNmTicket();
        //存储ssr
        List<PnrSsrDto> pnrSsrDtos = pnrNmDto.getPnrSsrDtos();

        for (int i = 0; i < pnrSegDtos.size(); i++) {
            MnjxPnrSeg pnrSeg = pnrSegDtos.get(i).getMnjxPnrSeg();
            PnrSsrDto pnrSsrDto = new PnrSsrDto();
            if (Constant.SA.equals(pnrSeg.getPnrSegType())) {
                if (i % 2 != 0) {
                    mnjxPnrNmTickets.add(pnrNmTicket);
                } else {
                    if (i == pnrSegDtos.size() - 1) {
                        break;
                    }
                    if (issueTicket != null) {
                        issueTicket = issueTicket.add(BigInteger.valueOf(1));
                    } else {
                        issueTicket = ticketStart;
                    }
                    if (ticketNoStart == null) {
                        ticketNoStart = issueTicket;
                    }
                    //构建nmTicket
                    pnrNmTicket = new MnjxPnrNmTicket();
                    pnrNmTicket.setNmTicketId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    etdzRecallDto.getNmTicketIds().add(pnrNmTicket.getNmTicketId());
                    pnrNmTicket.setPnrNmTnId(pnrNmTn.getTnId());
                    pnrNmTicket.setTicketNo(StrUtil.format("{}{}", settlementCode, issueTicket));
                    pnrNmTicket.setReceiptPrint(Constant.STR_ZERO);
                    pnrNmTicket.setS1Id(pnrSeg.getPnrSegId());
                    pnrNmTicket.setIsEt(StrUtil.isEmpty(isEt) ? Constant.STR_ONE : isEt);
                }
                continue;
            }
            if (i % 2 == 0) {
                if (issueTicket != null) {
                    issueTicket = issueTicket.add(BigInteger.valueOf(1));
                } else {
                    issueTicket = ticketStart;
                }
                if (ticketNoStart == null) {
                    ticketNoStart = issueTicket;
                }
                //构建nmTicket
                pnrNmTicket = new MnjxPnrNmTicket();
                pnrNmTicket.setNmTicketId(IdUtil.getSnowflake(1, 1).nextIdStr());
                etdzRecallDto.getNmTicketIds().add(pnrNmTicket.getNmTicketId());
                pnrNmTicket.setPnrNmTnId(pnrNmTn.getTnId());
                pnrNmTicket.setTicketNo(StrUtil.format("{}{}", settlementCode, issueTicket));
                pnrNmTicket.setTicketStatus1(Constant.TICKET_STATUS_OPEN_FOR_USE);
                pnrNmTicket.setReceiptPrint(Constant.STR_ZERO);
                pnrNmTicket.setS1Id(pnrSeg.getPnrSegId());
                pnrNmTicket.setIsEt(StrUtil.isEmpty(isEt) ? Constant.STR_ONE : isEt);
                if (i == pnrSegDtos.size() - 1) {
                    mnjxPnrNmTickets.add(pnrNmTicket);
                }
                //构建ssr
                MnjxNmSsr nmSsr = structureSsr(pnrNmDto, pnrNmTicket, pnrSeg, etdzRecallDto, option);
                pnrSsrDto.setMnjxNmSsr(nmSsr);
            } else {
                pnrNmTicket.setS2Id(pnrSeg.getPnrSegId());
                pnrNmTicket.setTicketStatus2(Constant.TICKET_STATUS_OPEN_FOR_USE);
                mnjxPnrNmTickets.add(pnrNmTicket);
                //构建ssr
                MnjxNmSsr nmSsr = structureSsr(pnrNmDto, pnrNmTicket, pnrSeg, etdzRecallDto, option);
                pnrSsrDto.setMnjxNmSsr(nmSsr);
            }
            pnrSsrDtos.add(pnrSsrDto);
        }
        ticketNoEnd = issueTicket;
        String inputValue = StrUtil.EMPTY;
        switch (option) {
            case Constant.ADL:
                if (ticketNoEnd.subtract(ticketNoStart).equals(BigInteger.ZERO)) {
                    inputValue = StrUtil.format("TN/{}-{}/P{}", settlementCode, ticketNoEnd, pnrNmDto.getMnjxPnrNm().getPsgIndex());
                } else {
                    inputValue = StrUtil.format("TN/{}-{}-{}/P{}", settlementCode, ticketNoStart, StrUtil.subSuf(StrUtil.toString(ticketNoEnd), 8), pnrNmDto.getMnjxPnrNm().getPsgIndex());
                }
                break;
            case Constant.INF:
                if (ticketNoEnd.subtract(ticketNoStart).equals(BigInteger.ZERO)) {
                    inputValue = StrUtil.format("TN/IN/{}-{}/P{}", settlementCode, ticketNoEnd, pnrNmDto.getMnjxPnrNm().getPsgIndex());
                } else {
                    inputValue = StrUtil.format("TN/IN/{}-{}-{}/P{}", settlementCode, ticketNoStart, StrUtil.subSuf(StrUtil.toString(ticketNoEnd), 8), pnrNmDto.getMnjxPnrNm().getPsgIndex());
                }
                break;
            default:
                break;
        }

        pnrNmTn.setInputValue(inputValue);
        pnrNmTnDto.setMnjxPnrNmTn(pnrNmTn);
        pnrNmTnDtos.add(pnrNmTnDto);
        return ticketNoEnd;
    }

    /**
     * 构建RMK项
     *
     * @param memoryDataPnr 内存大对象
     */
    private void structureRmk(MemoryDataPnr memoryDataPnr) {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
        EtdzRecallDto etdzRecallDto = memoryDataPnr.getEtdzRecallDto();

        int i = 0;
        // 这个地方只是单纯的添加这个(可以被修改)
        MnjxOffice mnjxOffice = iMnjxOfficeService.lambdaQuery().eq(MnjxOffice::getOfficeNo, memoryDataPnr.getMnjxPnr().getCreateOfficeNo()).one();
        String info = StrUtil.format("RMK TJ {}", mnjxOffice.getOfficeNo());
        do {
            PnrRmkDto pnrRmkDto = new PnrRmkDto();
            MnjxPnrRmk mnjxPnrRmk = new MnjxPnrRmk();
            mnjxPnrRmk.setPnrRmkId(IdUtils.getSnowflake(1, 1).nextIdStr());

            etdzRecallDto.getRmkIds().add(mnjxPnrRmk.getPnrRmkId());

            mnjxPnrRmk.setPnrId(mnjxPnr.getPnrId());
            mnjxPnrRmk.setRmkInfo(info);
            mnjxPnrRmk.setInputValue(info);
            pnrRmkDto.setMnjxPnrRmk(mnjxPnrRmk);
            pnrRmkDtos.add(pnrRmkDto);
            i++;
            info = StrUtil.format("RMK TR {}", mnjxOffice.getOfficeNo());
        } while (i <= 1);
    }

    /**
     * 构建TK/T 项
     *
     * @param memoryDataPnr pnr的大对象
     */
    private void structureTk(MemoryDataPnr memoryDataPnr) {
        MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
        EtdzRecallDto etdzRecallDto = memoryDataPnr.getEtdzRecallDto();

        List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos().stream().filter(tk -> !tk.isXe()).collect(Collectors.toList());
        boolean pnrTk = pnrTkDtos.stream().anyMatch(tk -> tk.getMnjxPnrTk().getPnrTkType().equals(Constant.T));
        if (!pnrTk) {
            PnrTkDto pnrTkDto = new PnrTkDto();
            MnjxPnrTk mnjxPnrTk = new MnjxPnrTk();

            mnjxPnrTk.setPnrTkId(IdUtils.getSnowflake(1, 1).nextIdStr());
            etdzRecallDto.setTkTid(mnjxPnrTk.getPnrTkId());

            mnjxPnrTk.setPnrId(mnjxPnr.getPnrId());
            mnjxPnrTk.setPnrTkType(Constant.T);
            mnjxPnrTk.setPlanEtdzDate(DateUtil.today());
            mnjxPnrTk.setInputValue("T");
            pnrTkDto.setMnjxPnrTk(mnjxPnrTk);
            memoryDataPnr.getPnrTkDtos().add(pnrTkDto);
        }
    }


    /**
     * 计算票号 生成tn,nmTicket，ssr记录
     *
     * @param etdzDto       出票对象
     * @param pnrNmDtos     姓名项
     * @param memoryDataPnr 内存项
     * @return 计算票号 生成tn,nmTicket记录
     */
    private EtdzVo structureTicketNo(EtdzDto etdzDto, List<PnrNmDto> pnrNmDtos, MemoryDataPnr memoryDataPnr, MnjxOffice mnjxOffice) {
        EtdzRecallDto etdzRecallDto = memoryDataPnr.getEtdzRecallDto();
        EtdzVo etdzVo;

        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos().stream().filter(seg -> !seg.isXe()).collect(Collectors.toList());
        //出票航司二字码，如果当前ETDZ指令中未明确指定出票航司，则取第一个航段的航司为出票航司
        MnjxPnrSeg mnjxPnrSeg = pnrSegDtos.get(0).getMnjxPnrSeg();
        String airlineCode = StrUtil.isNotEmpty(etdzDto.getAirlineCode()) ? etdzDto.getAirlineCode() : StrUtil.subPre(mnjxPnrSeg.getFlightNo(), 2);
        MnjxAirline mnjxAirline = iMnjxAirlineService.lambdaQuery().eq(MnjxAirline::getAirlineCode, airlineCode).one();
        //获取航司结算码
        String settlementCode = mnjxAirline.getAirlineSettlementCode();

        List<String> nmAndXnIds = this.filterNmAndXnId(pnrNmDtos);
        //成人出票之后生成的tn数据(用于后面过滤掉部分出票的成人或者婴儿，只出成人或者只出婴儿，避免重复出票)
        List<MnjxPnrNmTn> nmTns = iMnjxPnrNmTnService.lambdaQuery().in(MnjxPnrNmTn::getPnrNmId, nmAndXnIds).list();
        List<MnjxPnrNmTn> xnTns = iMnjxPnrNmTnService.lambdaQuery().in(MnjxPnrNmTn::getNmXnId, nmAndXnIds).list();

        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
        List<PnrFnDto> pnrFnDtos = memoryDataPnr.getPnrFnDtos().stream().filter(fn -> !fn.isXe()).collect(Collectors.toList());

        synchronized (this) {
            //记录已出票结束票号
            BigInteger ticketNoEnd;
            BigDecimal fnApriceToTal = BigDecimal.ZERO;
            MnjxPrinter mnjxPrinter = this.retrievePrinter(etdzDto.getPrintNo(), mnjxOffice.getOfficeId());

            String currentThreadName = Thread.currentThread().getName();
            log.debug("当前线程 {} 获取的票号：{}", currentThreadName, mnjxPrinter.getLastTicket());
            etdzRecallDto.setLastTicket(mnjxPrinter.getLastTicket());
            etdzRecallDto.setPrinterId(mnjxPrinter.getPrinterId());

            //获取起始票号
            BigInteger ticketStart = mnjxPrinter.getTicketStart();
            //记录每次循环结束的截止票号
            BigInteger issueTicket = mnjxPrinter.getLastTicket();
            String option = StrUtils.toString(etdzDto.getOption());
            switch (option) {
                case Constant.ADL:
                    for (PnrNmDto pnrNmDto : pnrNmDtos) {
                        MnjxPnrNm pnrNm = pnrNmDto.getMnjxPnrNm();
                        List<MnjxPnrNmTn> ntns = null;
                        if (CollUtils.isNotEmpty(nmTns)) {
                            ntns = nmTns.stream().filter(nm -> nm.getPnrNmId().equals(pnrNm.getPnrNmId())).collect(Collectors.toList());
                        }
                        if (CollUtils.isEmpty(ntns)) {
                            //构建tn
                            MnjxPnrNmTn pnrNmTn = this.structureTn(airlineCode, mnjxPrinter, pnrNm.getPnrNmId(), etdzDto.getOption());
                            etdzRecallDto.getTnIds().add(pnrNmTn.getTnId());

                            //构建nmTicket ssr
                            issueTicket = this.structureNmTicket(pnrNmTnDtos, pnrNmDto, pnrSegDtos, pnrNmTn, ticketStart, issueTicket, settlementCode, etdzDto.getOption(), etdzDto.getIsEt(), etdzRecallDto);
                            fnApriceToTal = fnApriceToTal.add(sumFnAncy(pnrNmDto, pnrFnDtos, etdzDto.getOption()));
                        }
                    }
                    ticketNoEnd = issueTicket;
                    break;
                case Constant.INF:
                    for (PnrNmDto pnrNmDto : pnrNmDtos) {
                        List<PnrXnDto> pnrXnDtos = pnrNmDto.getPnrXnDtos().stream().filter(xn -> !xn.isXe()).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(pnrXnDtos)) {
                            MnjxNmXn pnrXn = pnrXnDtos.get(0).getMnjxNmXn();
                            List<MnjxPnrNmTn> xtns = null;
                            if (CollUtils.isNotEmpty(xnTns)) {
                                xtns = xnTns.stream().filter(nm -> nm.getNmXnId().equals(pnrXn.getNmXnId())).collect(Collectors.toList());
                            }
                            if (CollUtils.isEmpty(xtns)) {
                                //构建tn
                                MnjxPnrNmTn pnrNmTn = this.structureTn(airlineCode, mnjxPrinter, pnrXn.getNmXnId(), etdzDto.getOption());
                                etdzRecallDto.getTnIds().add(pnrNmTn.getTnId());
                                //构建nmTicket ssr
                                issueTicket = this.structureNmTicket(pnrNmTnDtos, pnrNmDto, pnrSegDtos, pnrNmTn, ticketStart, issueTicket, settlementCode, etdzDto.getOption(), etdzDto.getIsEt(), etdzRecallDto);
                                fnApriceToTal = fnApriceToTal.add(sumFnAncy(pnrNmDto, pnrFnDtos, etdzDto.getOption()));
                            }
                        }
                    }
                    ticketNoEnd = issueTicket;
                    break;
                default:
                    for (PnrNmDto pnrNmDto : pnrNmDtos) {
                        MnjxPnrNm pnrNm = pnrNmDto.getMnjxPnrNm();
                        List<MnjxPnrNmTn> ntns = null;
                        if (CollUtils.isNotEmpty(nmTns)) {
                            ntns = nmTns.stream().filter(nm -> nm.getPnrNmId().equals(pnrNm.getPnrNmId())).collect(Collectors.toList());
                        }
                        //构建tn
                        MnjxPnrNmTn pnrNmTn;
                        if (CollUtils.isEmpty(ntns)) {
                            pnrNmTn = this.structureTn(airlineCode, mnjxPrinter, pnrNm.getPnrNmId(), Constant.ADL);
                            etdzRecallDto.getTnIds().add(pnrNmTn.getTnId());
                            //构建nmTicket
                            issueTicket = this.structureNmTicket(pnrNmTnDtos, pnrNmDto, pnrSegDtos, pnrNmTn, ticketStart, issueTicket, settlementCode, Constant.ADL, etdzDto.getIsEt(), etdzRecallDto);
                            fnApriceToTal = fnApriceToTal.add(sumFnAncy(pnrNmDto, pnrFnDtos, "ADL"));
                        }

                        //婴儿出票
                        List<PnrXnDto> pnrXnDtos = pnrNmDto.getPnrXnDtos().stream().filter(xn -> !xn.isXe()).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(pnrXnDtos)) {
                            MnjxNmXn pnrXn = pnrXnDtos.get(0).getMnjxNmXn();
                            List<MnjxPnrNmTn> xtns = null;
                            if (CollUtils.isNotEmpty(xnTns)) {
                                xtns = xnTns.stream().filter(nm -> nm.getNmXnId().equals(pnrXn.getNmXnId())).collect(Collectors.toList());
                            }
                            if (CollUtils.isEmpty(xtns)) {
                                pnrNmTn = this.structureTn(airlineCode, mnjxPrinter, pnrXn.getNmXnId(), Constant.INF);
                                etdzRecallDto.getTnIds().add(pnrNmTn.getTnId());
                                //构建nmTicket ssr
                                issueTicket = this.structureNmTicket(pnrNmTnDtos, pnrNmDto, pnrSegDtos, pnrNmTn, ticketStart, issueTicket, settlementCode, Constant.INF, etdzDto.getIsEt(), etdzRecallDto);
                                fnApriceToTal = fnApriceToTal.add(sumFnAncy(pnrNmDto, pnrFnDtos, "INF"));
                            }
                        }
                    }
                    ticketNoEnd = issueTicket;
                    break;
            }
            //修改保存最后的票号
            this.updateMnjxPrinter(mnjxPrinter.getPrinterId(), ticketNoEnd);
            BigInteger ticketNoStart = mnjxPrinter.getLastTicket() == null ? mnjxPrinter.getTicketStart() : mnjxPrinter.getLastTicket().add(BigInteger.valueOf(1));
            // 回显数据封装
            etdzVo = this.getEtdzDto(memoryDataPnr, settlementCode, ticketNoStart, ticketNoEnd, fnApriceToTal, etdzDto.getCmd());
        }
        // 生成旅客编号，更新mnjxPnrNmTicket表
        // 查询这个航班某天的数据（如果是多航段就是所有航班)
        List<MnjxPnrSeg> mnjxPnrSegs = iPnrOperationService.retrieveMnjxSegByFltNoAndDate(memoryDataPnr.getPnrSegDtos());
        this.structurePsgNum(pnrNmDtos, mnjxPnrSegs);
        // 生成离港团名
        this.structureGn(memoryDataPnr);
        return etdzVo;
    }

    /**
     * 构建ssr
     */
    private MnjxNmSsr structureSsr(PnrNmDto pnrNmDto, MnjxPnrNmTicket pnrNmTicket, MnjxPnrSeg pnrSeg, EtdzRecallDto etdzRecallDto, String option) {
        MnjxNmSsr nmSsr = new MnjxNmSsr();
        nmSsr.setNmSsrId(IdUtil.getSnowflake(1, 1).nextIdStr());
        nmSsr.setPnrNmId(pnrNmDto.getMnjxPnrNm().getPnrNmId());
        nmSsr.setPnrSegNo(pnrSeg.getPnrSegNo());
        nmSsr.setSsrType(Constant.TKNE);
        nmSsr.setActionCode(pnrSeg.getActionCode());
        nmSsr.setOrgDst(StrUtil.format("{}{}", pnrSeg.getOrg(), pnrSeg.getDst()));
        nmSsr.setAirlineCode(StrUtil.subPre(pnrSeg.getFlightNo(), 2));
        nmSsr.setFltDate(pnrSeg.getFlightDate());
        if (Constant.INF.equals(option)) {
            nmSsr.setSsrInfo(StrUtil.format("SSR {} {} {}1 {} {} {}{} INF{}/{}/P{}",
                    nmSsr.getSsrType(), nmSsr.getAirlineCode(), nmSsr.getActionCode(), nmSsr.getOrgDst(),
                    StrUtil.subSuf(pnrSeg.getFlightNo(), 2), pnrSeg.getSellCabin(), DateUtils.ymd2PreCom(nmSsr.getFltDate(), 5), pnrNmTicket.getTicketNo(),
                    nmSsr.getPnrSegNo(), pnrNmDto.getMnjxPnrNm().getPsgIndex()));
        } else {
            nmSsr.setSsrInfo(StrUtil.format("SSR {} {} {}1 {} {} {}{} {}/{}/P{}",
                    nmSsr.getSsrType(), nmSsr.getAirlineCode(), nmSsr.getActionCode(), nmSsr.getOrgDst(),
                    StrUtil.subSuf(pnrSeg.getFlightNo(), 2), pnrSeg.getSellCabin(), DateUtils.ymd2PreCom(nmSsr.getFltDate(), 5), pnrNmTicket.getTicketNo(),
                    nmSsr.getPnrSegNo(), pnrNmDto.getMnjxPnrNm().getPsgIndex()));
        }

        nmSsr.setInputValue(nmSsr.getSsrInfo());
        etdzRecallDto.getSsrTkneIds().add(nmSsr.getNmSsrId());
        return nmSsr;
    }

    /**
     * 构建mnjxPsgCki
     */
    private void structurePsgCkiAndSeat(List<PnrNmDto> pnrNmDtos, MemoryDataPnr memoryDataPnr) {
        List<PnrSegDto> pnrSegDtos = memoryDataPnr.getPnrSegDtos();
        //存储需要还原的对象
        EtdzRecallDto etdzRecallDto = memoryDataPnr.getEtdzRecallDto();
        List<MnjxPsgCki> psgCkis = new ArrayList<>();
        List<MnjxPsgSeat> psgSeats = new ArrayList<>();
        List<String> nmIds = pnrNmDtos.stream().map(n -> n.getMnjxPnrNm().getPnrNmId()).collect(Collectors.toList());
        List<MnjxPsgCki> nmCkiList = iMnjxPsgCkiService.lambdaQuery().in(MnjxPsgCki::getPnrNmId, nmIds).list();
        if (CollUtil.isNotEmpty(nmCkiList)) {
            //筛选出已经存在的cki 记录的旅客
            List<String> nmIdCkis = nmCkiList.stream().map(MnjxPsgCki::getPnrNmId).collect(Collectors.toList());
            pnrNmDtos = pnrNmDtos.stream().filter(n -> !nmIdCkis.contains(n.getMnjxPnrNm().getPnrNmId())).collect(Collectors.toList());
        }
        //psgCki数据
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            pnrSegDtos.forEach(seg -> {
                MnjxPnrSeg pnrSeg = seg.getMnjxPnrSeg();
                MnjxPsgCki mnjxPsgCki = new MnjxPsgCki();
                MnjxPnrNm mnjxPnrNm = pnrNmDto.getMnjxPnrNm();
                mnjxPsgCki.setPsgCkiId(IdUtils.getSnowflake(1, 1).nextIdStr());
                etdzRecallDto.getPsgCkiIds().add(mnjxPsgCki.getPsgCkiId());

                mnjxPsgCki.setPnrNmId(mnjxPnrNm.getPnrNmId());
                mnjxPsgCki.setCkiStatus(Constant.NACC);
                mnjxPsgCki.setAbdStatusInfi(Constant.STR_ZERO);
                mnjxPsgCki.setPnrSegNo(StrUtil.toString(pnrSeg.getPnrSegNo()));
                mnjxPsgCki.setCabinClass(pnrSeg.getCabinClass());
                mnjxPsgCki.setSellCabin(pnrSeg.getSellCabin());
                psgCkis.add(mnjxPsgCki);

                //psgSeat数据
                MnjxPsgSeat mnjxPsgSeat = new MnjxPsgSeat();
                mnjxPsgSeat.setPsgSeatId(IdUtils.getSnowflake(1, 1).nextIdStr());
                etdzRecallDto.getPsgSeatIds().add(mnjxPsgSeat.getPsgSeatId());

                mnjxPsgSeat.setPsgCkiId(mnjxPsgCki.getPsgCkiId());
                psgSeats.add(mnjxPsgSeat);
            });
        }
        if (CollUtil.isNotEmpty(psgCkis)) {
            iMnjxPsgCkiService.saveBatch(psgCkis);
            iMnjxPsgSeatService.saveBatch(psgSeats);
        }
    }

    /**
     * 新增客票操作记录
     */
    private void structureOperateRecord(List<PnrNmDto> pnrNmDtos, MnjxSi mnjxSi, MemoryDataPnr memoryDataPnr) {
        //存储需要还原的对象
        List<MnjxPnrNmTicket> tickets = new ArrayList<>();
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            List<MnjxPnrNmTicket> mnjxPnrNmTickets = pnrNmDto.getMnjxPnrNmTickets();
            tickets.addAll(mnjxPnrNmTickets);
        }
        //排序
        //按照票号排序-升序
        tickets = tickets.stream().sorted(Comparator.comparing(MnjxPnrNmTicket::getTicketNo)).collect(Collectors.toList());
        List<String> ticketNos = tickets.stream().map(MnjxPnrNmTicket::getTicketNo).collect(Collectors.toList());
        List<MnjxTicketOperateRecord> ticketOperateRecords = iMnjxTicketOperateRecordService.lambdaQuery().in(MnjxTicketOperateRecord::getTicketNo, ticketNos).list();
        if (CollUtil.isNotEmpty(ticketOperateRecords)) {
            //过滤掉已经存在的客票记录
            List<String> ticketsRecord = ticketOperateRecords.stream().map(MnjxTicketOperateRecord::getTicketNo).collect(Collectors.toList());
            tickets = tickets.stream().filter(t -> !ticketsRecord.contains(t.getTicketNo())).collect(Collectors.toList());
        }
        iTicketRecordService.recordTicketOperateStatus(tickets, mnjxSi, memoryDataPnr);
    }

    /**
     * 生成离港团名
     * 如果PNR中有多个旅客并且非团队PNR，自动设置一个团队名，只是一个标识，用于白屏显示，并不是真正的团队
     * （离港团名是在离港侧会引用的一个团队名称，除了本身就以团体GROUP形式订PNR出票的这种团体外，凡PNR中旅客数量大于1的，也会被视为团体旅客，赋予一个团体名）
     */
    private void structureGn(MemoryDataPnr mdPnr) {
        MnjxPnr mnjxPnr = mdPnr.getMnjxPnr();
        EtdzRecallDto etdzRecallDto = mdPnr.getEtdzRecallDto();

        MnjxPnr pnrById = iMnjxPnrService.getById(mnjxPnr.getPnrId());
        List<PnrNmDto> pnrNmDtos = mdPnr.getPnrNmDtos();
        //帅选出成人和婴儿，（1成人带1婴儿，则是2名旅客了）则需要生成团名
        List<String> nmAndXnId = filterNmAndXnId(pnrNmDtos);
        if (StrUtil.isEmpty(pnrById.getDefaultGroupName()) && nmAndXnId.size() > 1) {
            synchronized (this) {
                String groupName = iPnrOperationService.getGroupName(mdPnr);
                mnjxPnr.setDefaultGroupName(groupName);
                etdzRecallDto.setDefaultGroupName(groupName);
            }
        }
    }

    /**
     * 查询航班是已存在的旅客编号
     *
     * @param pnrNmDtos 已筛选过需要出票的旅客列表
     */

    private void structurePsgNum(List<PnrNmDto> pnrNmDtos, List<MnjxPnrSeg> mnjxPnrSegs) {
        log.debug("{}{}线程进入设置旅客序号", DateUtils.now(), Thread.currentThread().getName());

        //所有航班里各航段
        List<String> s1IdList = mnjxPnrSegs.stream().filter(v -> v.getPnrSegNo() % 2 != 0).map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList());
        List<String> s2IdList = mnjxPnrSegs.stream().filter(v -> v.getPnrSegNo() % 2 == 0).map(MnjxPnrSeg::getPnrSegId).collect(Collectors.toList());
        synchronized (this) {
            //所有航班按航段分开的
            List<MnjxPnrNmTicket> s1IdNmTicketList = CollUtil.isEmpty(s1IdList) ? new ArrayList<>() : iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getS1Id, s1IdList).list();
            if (CollUtil.isNotEmpty(s1IdNmTicketList)) {
                s1IdNmTicketList = s1IdNmTicketList.stream()
                        .filter(t -> StrUtil.isNotEmpty(t.getHbnb1()))
                        .collect(Collectors.toList());
            }

            List<MnjxPnrNmTicket> s2IdNmTicketList = CollUtil.isEmpty(s2IdList) ? new ArrayList<>() : iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getS2Id, s2IdList).list();
            if (CollUtil.isNotEmpty(s2IdNmTicketList)) {
                s2IdNmTicketList = s2IdNmTicketList.stream()
                        .filter(t -> StrUtil.isNotEmpty(t.getHbnb2()))
                        .collect(Collectors.toList());
            }

            //按航班分组
            Map<String, List<MnjxPnrSeg>> fltMap = mnjxPnrSegs.stream()
                    .collect(Collectors.groupingBy(v -> {
                        if (StrUtils.isNotEmpty(v.getCarrierFlight())) {
                            return v.getFlightDate() + v.getCarrierFlight();
                        } else {
                            return v.getFlightDate() + v.getFlightNo();
                        }
                    }));

            //根据航班遍历
            List<MnjxPnrNmTicket> finalS1IdNmTicketList = s1IdNmTicketList;
            List<MnjxPnrNmTicket> finalS2IdNmTicketList = s2IdNmTicketList;
            fltMap.forEach((k, v) -> {
                int max = 0;
                //该航班所有segId
                List<String> segIds = v.stream()
                        .map(MnjxPnrSeg::getPnrSegId)
                        .collect(Collectors.toList());
                //该航班航段1的
                List<MnjxPnrNmTicket> s1List = finalS1IdNmTicketList.stream()
                        .filter(t -> segIds.contains(t.getS1Id()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(s1List)) {
                    OptionalInt s1Max = s1List.stream()
                            .mapToInt(s -> Integer.parseInt(s.getHbnb1()))
                            .max();
                    max = s1Max.isPresent() ? s1Max.getAsInt() : 0;
                }
                //该航班航段2的
                List<MnjxPnrNmTicket> s2List = finalS2IdNmTicketList.stream()
                        .filter(t -> segIds.contains(t.getS2Id()))
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(s2List)) {
                    OptionalInt s2Max = s2List.stream()
                            .mapToInt(s -> Integer.parseInt(s.getHbnb2()))
                            .max();
                    int tempMax = s2Max.isPresent() ? s2Max.getAsInt() : 0;
                    max = Math.max(tempMax, max);
                }

                max += 1;
                for (PnrNmDto pnrNmDto : pnrNmDtos) {
                    List<MnjxPnrNmTicket> mnjxPnrNmTickets = pnrNmDto.getMnjxPnrNmTickets();
                    for (MnjxPnrNmTicket mnjxPnrNmTicket : mnjxPnrNmTickets) {
                        //航段1,2 id
                        String s1Id = mnjxPnrNmTicket.getS1Id();
                        String s2Id = mnjxPnrNmTicket.getS2Id();
                        String hbnb = StrUtil.fill(StrUtil.toString(max), '0', 4, true);
                        if (segIds.contains(s1Id) && StrUtil.isEmpty(mnjxPnrNmTicket.getHbnb1())) {
                            mnjxPnrNmTicket.setHbnb1(hbnb);
                            max++;
                        } else if (segIds.contains(s2Id) && StrUtil.isEmpty(mnjxPnrNmTicket.getHbnb2())) {
                            mnjxPnrNmTicket.setHbnb2(hbnb);
                            max++;
                        }
                    }
                }
            });
        }
    }

    /**
     * 保存 出完票的最后一张的票号
     */
    private void updateMnjxPrinter(String printerId, BigInteger ticketNoEnd) {
        iMnjxPrinterService.lambdaUpdate().eq(MnjxPrinter::getPrinterId, printerId).set(MnjxPrinter::getLastTicket, ticketNoEnd).update();
    }

    /**
     * 筛选成人id,婴儿ID 并合并为一个集合
     *
     * @param pnrNmDtos 旅客列表
     * @return id集合
     */
    private List<String> filterNmAndXnId(List<PnrNmDto> pnrNmDtos) {
        //pnr下所有旅客姓名id列表
        List<String> pnrNmIds = pnrNmDtos.stream().map(pnrNmDto -> pnrNmDto.getMnjxPnrNm().getPnrNmId()).collect(Collectors.toList());
        //pnr下所有婴儿Id
        List<String> pnrXnIds = pnrNmDtos.stream().flatMap(pnrNmDto -> pnrNmDto.getPnrXnDtos().stream().map(xn -> xn.getMnjxNmXn().getNmXnId())).collect(Collectors.toList());
        return Stream.of(pnrNmIds, pnrXnIds).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 筛选出没出票的旅客 成人+婴儿
     */
    private List<PnrNmDto> filterNmOrXnTn(List<PnrNmDto> pnrNmDtos, String option, List<PnrNmTnDto> pnrNmTnDtos) {
        // 已出票的(成人、婴儿)pnrNmId、xnNmId
        List<String> issueNmList = new ArrayList<>();
        List<String> issueXnList = new ArrayList<>();
        if (CollUtil.isNotEmpty(pnrNmTnDtos)) {
            // 成人
            List<String> nmList = pnrNmTnDtos.stream().filter(k -> ObjectUtil.isNotEmpty(k.getMnjxPnrNmTn().getPnrNmId())).map(k -> k.getMnjxPnrNmTn().getPnrNmId()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(nmList)) {
                issueNmList.addAll(nmList);
            }
            // 婴儿
            List<String> xnList = pnrNmTnDtos.stream().filter(k -> ObjectUtil.isNotEmpty(k.getMnjxPnrNmTn().getNmXnId())).map(k -> k.getMnjxPnrNmTn().getNmXnId()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(xnList)) {
                issueXnList.addAll(xnList);
            }
        }
        List<PnrNmDto> xnPnrNmDtos = new ArrayList<>();
        List<PnrNmDto> nmDtoList = new ArrayList<>();
        if (Constant.ADL.equals(option)) {
            // 去除已出票的nm
            pnrNmDtos = pnrNmDtos.stream().filter(k -> !issueNmList.contains(k.getMnjxPnrNm().getPnrNmId())).collect(Collectors.toList());
            nmDtoList.addAll(pnrNmDtos);
        } else if (Constant.INF.equals(option)) {
            // 取出所有未出票的婴儿
            // 循环过滤调已出票得婴儿信息
            pnrNmDtos.stream().filter(pnrNmDto -> CollUtil.isNotEmpty(pnrNmDto.getPnrXnDtos())).forEach(k -> k.getPnrXnDtos().forEach(x -> {
                if (!issueXnList.contains(x.getMnjxNmXn().getNmXnId())) {
                    xnPnrNmDtos.add(k);
                }
            }));
            nmDtoList.addAll(xnPnrNmDtos);
        } else {
            // 去除已出票的nm
            List<PnrNmDto> nmDtos = pnrNmDtos.stream().filter(k -> !issueNmList.contains(k.getMnjxPnrNm().getPnrNmId())).collect(Collectors.toList());
            // 取出所有未出票的婴儿
            // 循环过滤调已出票得婴儿信息
            pnrNmDtos.stream().filter(k -> CollUtil.isNotEmpty(k.getPnrXnDtos())).forEach(k -> k.getPnrXnDtos().forEach(x -> {
                if (!issueXnList.contains(x.getMnjxNmXn().getNmXnId())) {
                    xnPnrNmDtos.add(k);
                }
            }));
            nmDtoList.addAll(Stream.of(nmDtos, xnPnrNmDtos).flatMap(Collection::stream).distinct().collect(Collectors.toList()));
        }
        return nmDtoList;
    }

    /**
     * 筛选需要xe掉的FC EI项 挂在旅客上的FC、EI记录
     */
    private void filterXeFcEiId(List<Integer> xeIndexList, List<PnrNmDto> pnrNmDtos, String option, EtdzRecallDto etdzRecallDto) {
        for (PnrNmDto pnrNmDto : pnrNmDtos) {
            List<PnrNmFcDto> adlPnrNmFcDtos = pnrNmDto.getPnrNmFcDtos().stream().filter(pnrNmFcDto -> pnrNmFcDto.getMnjxNmFc().getIsBaby().equals(0)).collect(Collectors.toList());
            List<PnrNmFcDto> infPnrNmFcDtos = pnrNmDto.getPnrNmFcDtos().stream().filter(pnrNmFcDto -> pnrNmFcDto.getMnjxNmFc().getIsBaby().equals(1)).collect(Collectors.toList());
            List<PnrNmEiDto> pnrNmEiDtos = pnrNmDto.getPnrNmEiDtos();
            pnrNmEiDtos.forEach(pnrNmEiDto -> {
                xeIndexList.add(pnrNmEiDto.getPnrIndex());
                etdzRecallDto.getEiIds().add(pnrNmEiDto.getMnjxNmEi().getNmEiId());
            });
            switch (option) {
                case Constant.ADL:
                    adlPnrNmFcDtos.forEach(nmFc -> {
                        xeIndexList.add(nmFc.getPnrIndex());
                        etdzRecallDto.getFcIds().add(nmFc.getMnjxNmFc().getNmFcId());
                    });
                    break;
                case Constant.INF:
                    infPnrNmFcDtos.forEach(nmFc -> {
                        xeIndexList.add(nmFc.getPnrIndex());
                        etdzRecallDto.getFcIds().add(nmFc.getMnjxNmFc().getNmFcId());
                    });
                    break;
                default:
                    adlPnrNmFcDtos.forEach(nmFc -> {
                        xeIndexList.add(nmFc.getPnrIndex());
                        etdzRecallDto.getFcIds().add(nmFc.getMnjxNmFc().getNmFcId());
                    });
                    infPnrNmFcDtos.forEach(nmFc -> {
                        xeIndexList.add(nmFc.getPnrIndex());
                        etdzRecallDto.getFcIds().add(nmFc.getMnjxNmFc().getNmFcId());
                    });
                    break;
            }
        }
    }

    /**
     * 查询部门下打票机
     */
    private MnjxPrinter retrievePrinter(String printNo, String officeId) {
        return iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getOfficeId, officeId)
                .eq(MnjxPrinter::getPrinterNo, printNo)
                .one();
    }

    /**
     * 获取需要出票的旅客
     *
     * @param memoryDataPnr PNR内存大对象
     * @param etdzDto       出票对象
     * @return 旅客列表
     * @throws UnifiedResultException 统一移除
     */
    private List<PnrNmDto> getPnrNmDtoList(MemoryDataPnr memoryDataPnr, EtdzDto etdzDto) throws UnifiedResultException {
        List<PnrNmDto> pnrNmDtos;
        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos().stream().filter(t -> !t.isXe()).collect(Collectors.toList());
        List<Integer> psgNos = new ArrayList<>();
        List<PnrNmDto> pnrNmDtoList;
        //P1
        if (StrUtil.isNotEmpty(etdzDto.getPsgNoStart())) {
            int psgNo1 = Integer.parseInt(etdzDto.getPsgNoStart().replaceAll("P", ""));
            //P1-P2
            if (StrUtil.isNotEmpty(etdzDto.getPsgNoEnd())) {
                int psgNo2 = Integer.parseInt(etdzDto.getPsgNoEnd().replaceAll("P", ""));
                IntStream.range(psgNo1, psgNo2 + 1).forEach(psgNos::add);
            } else {
                psgNos.add(psgNo1);
            }
            pnrNmDtos = memoryDataPnr.getPnrNmDtos().stream().filter(k -> psgNos.contains(k.getMnjxPnrNm().getPsgIndex())).sorted(Comparator.comparing(k -> k.getMnjxPnrNm().getPsgIndex())).collect(Collectors.toList());
        } else {
            //不带旅客标识
            pnrNmDtos = memoryDataPnr.getPnrNmDtos();
            //筛选没出票的pnrNmDtos
        }
        pnrNmDtoList = this.filterNmOrXnTn(pnrNmDtos, etdzDto.getOption(), pnrNmTnDtos);
        if (CollUtil.isEmpty(pnrNmDtoList)) {
            throw new UnifiedResultException(Constant.PNR_TICKETED);
        }
        //按照旅客序号排序-升序
        pnrNmDtoList = pnrNmDtoList.stream().sorted(Comparator.comparing(p -> p.getMnjxPnrNm().getPsgIndex())).collect(Collectors.toList());
        return pnrNmDtoList;
    }

    /**
     * 回显数据封装
     */
    private EtdzVo getEtdzDto(MemoryDataPnr mdPnr, String settlementCode, BigInteger ticketNoStart, BigInteger ticketNoEnd, BigDecimal fnApriceToTal, String cmd) {
        EtdzVo etdzVo = new EtdzVo();
        etdzVo.setPnrNo(mdPnr.getMnjxPnr().getPnrCrs());
        etdzVo.setTicketNoStart(StrUtil.format("{}-{}", settlementCode, StrUtil.toString(ticketNoStart)));
        if (!ticketNoStart.equals(ticketNoEnd)) {
            etdzVo.setTicketNoEnd(StrUtil.format("{}", StrUtil.subSuf(StrUtil.toString(ticketNoEnd), 8)));
        }
        etdzVo.setAcny(StrUtil.format("CNY{}", StrUtil.toString(fnApriceToTal)));
        etdzVo.setCmd(cmd);
        return etdzVo;
    }

    /**
     * 计算票的价格
     */
    private BigDecimal sumFnAncy(PnrNmDto pnrNmDto, List<PnrFnDto> pnrFnDtos, String option) {
        //pnr上的fn
        List<PnrFnDto> pnrFnAdl = pnrFnDtos.stream().filter(pnrFn -> pnrFn.getMnjxPnrFn().getIsBaby().equals(0)).collect(Collectors.toList());
        List<PnrFnDto> pnrFnInf = pnrFnDtos.stream().filter(pnrFn -> pnrFn.getMnjxPnrFn().getIsBaby().equals(1)).collect(Collectors.toList());
        //旅客身上的fn
        List<PnrNmFnDto> nmFnAdl = pnrNmDto.getPnrNmFnDtos().stream().filter(nmFn -> nmFn.getMnjxNmFn().getIsBaby().equals(0)).collect(Collectors.toList());
        List<PnrNmFnDto> nmFnInf = pnrNmDto.getPnrNmFnDtos().stream().filter(nmFn -> nmFn.getMnjxNmFn().getIsBaby().equals(1)).collect(Collectors.toList());
        List<PnrXnDto> pnrXnDtos = pnrNmDto.getPnrXnDtos();

        BigDecimal aPrice = BigDecimal.ZERO;
        //先判断是否有挂在旅客上的fn,优先取旅客上的fn
        switch (option) {
            case Constant.ADL:
                if (CollUtil.isNotEmpty(nmFnAdl)) {
                    MnjxNmFn mnjxNmFn = nmFnAdl.get(0).getMnjxNmFn();
                    aPrice = mnjxNmFn.getAPrice();
                } else {
                    MnjxPnrFn mnjxPnrFn = pnrFnAdl.get(0).getMnjxPnrFn();
                    aPrice = mnjxPnrFn.getAPrice();
                }
                break;
            case Constant.INF:
                if (CollUtil.isNotEmpty(pnrXnDtos)) {
                    if (CollUtil.isNotEmpty(nmFnInf)) {
                        MnjxNmFn mnjxNmFn = nmFnInf.get(0).getMnjxNmFn();
                        aPrice = mnjxNmFn.getAPrice();
                    } else {
                        MnjxPnrFn mnjxPnrFn = pnrFnInf.get(0).getMnjxPnrFn();
                        aPrice = mnjxPnrFn.getAPrice();
                    }
                }
                break;
            default:
                //成人
                if (CollUtil.isNotEmpty(nmFnAdl)) {
                    MnjxNmFn mnjxNmFn = nmFnAdl.get(0).getMnjxNmFn();
                    aPrice = mnjxNmFn.getAPrice();
                } else {
                    MnjxPnrFn mnjxPnrFn = pnrFnAdl.get(0).getMnjxPnrFn();
                    aPrice = mnjxPnrFn.getAPrice();
                }
                //婴儿
                if (CollUtil.isNotEmpty(pnrXnDtos)) {
                    if (CollUtil.isNotEmpty(nmFnInf)) {
                        MnjxNmFn mnjxNmFn = nmFnInf.get(0).getMnjxNmFn();
                        aPrice = aPrice.add(mnjxNmFn.getAPrice());
                    } else {
                        MnjxPnrFn mnjxPnrFn = pnrFnInf.get(0).getMnjxPnrFn();
                        aPrice = aPrice.add(mnjxPnrFn.getAPrice());
                    }
                }
        }
        return aPrice;
    }

    /**
     * 判断是否需要清除pnr
     */
    private void isClearPnr(MemoryDataPnr memoryDataPnr, String option) {
        List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        List<String> nmAndXnIds = this.filterNmAndXnId(pnrNmDtos);
        //如果已经完全出票，就可以清除掉内存中的pnr,就可以直接进行下一次的订座操作
        if (nmAndXnIds.size() == pnrNmTnDtos.size() || Constant.INF.equals(option)) {
            memoryDataPnr.clearPnr();
        }
    }

    /**
     * etdz出票之后调用封口指令发生异常时，pnr进行还原到etdz操作之前的状态
     *
     * @param memoryDataPnr pnr的大对象
     */
    private void resetMemoryDataPnr(MemoryDataPnr memoryDataPnr) {
        EtdzRecallDto etdzRecallDto = memoryDataPnr.getEtdzRecallDto();
        String printerId = etdzRecallDto.getPrinterId();
        BigInteger lastTicket = etdzRecallDto.getLastTicket();
        List<String> tnIds = etdzRecallDto.getTnIds();
        List<String> tkIds = etdzRecallDto.getTkIds();
        String tkTid = etdzRecallDto.getTkTid();
        List<String> fcIds = etdzRecallDto.getFcIds();
        List<String> eiIds = etdzRecallDto.getEiIds();
        List<String> rmkIds = etdzRecallDto.getRmkIds();
        String defaultGroupName = etdzRecallDto.getDefaultGroupName();
        List<String> psgCkiIds = etdzRecallDto.getPsgCkiIds();
        List<String> psgSeatIds = etdzRecallDto.getPsgSeatIds();
        List<String> nmTicketIds = etdzRecallDto.getNmTicketIds();
        List<String> optRecordIds = etdzRecallDto.getTicketOptRecordIds();
        if (memoryDataPnr.isEtdz()) {
            //还原票号
            this.updateMnjxPrinter(printerId, lastTicket);
            //还原tn项，移除添加的tn项
            List<PnrNmTnDto> pnrNmTnDtos = memoryDataPnr.getPnrNmTnDtos();
            pnrNmTnDtos.removeIf(tn -> tnIds.contains(tn.getMnjxPnrNmTn().getTnId()));

            //移除添加的TK/T 项
            List<PnrTkDto> pnrTkDtos = memoryDataPnr.getPnrTkDtos();
            pnrTkDtos.removeIf(tk -> tk.getMnjxPnrTk().getPnrTkId().equals(tkTid));
            //还原TK/TL项
            pnrTkDtos.stream().filter(tk -> tkIds.contains(tk.getMnjxPnrTk().getPnrTkId())).forEach(tk -> tk.setXe(false));
            //还原nmTicket数据
            List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
            for (PnrNmDto pnrNmDto : pnrNmDtos) {
                List<MnjxPnrNmTicket> nmTickets = pnrNmDto.getMnjxPnrNmTickets();
                nmTickets.removeIf(ticket -> tnIds.contains(ticket.getPnrNmTnId()));
                List<PnrNmFcDto> pnrNmFcDtos = pnrNmDto.getPnrNmFcDtos();
                List<PnrNmEiDto> pnrNmEiDtos = pnrNmDto.getPnrNmEiDtos();
                //还原FC EI
                pnrNmFcDtos.stream().filter(nmFc -> fcIds.contains(nmFc.getMnjxNmFc().getNmFcId())).forEach(tk -> tk.setXe(false));
                pnrNmEiDtos.stream().filter(nmEi -> eiIds.contains(nmEi.getMnjxNmEi().getNmEiId())).forEach(tk -> tk.setXe(false));
            }
            //还原FC EI
            List<PnrFcDto> pnrFcDtos = memoryDataPnr.getPnrFcDtos();
            List<PnrEiDto> pnrEiDtos = memoryDataPnr.getPnrEiDtos();
            pnrFcDtos.stream().filter(fc -> fcIds.contains(fc.getMnjxPnrFc().getPnrFcId())).forEach(tk -> tk.setXe(false));
            pnrEiDtos.stream().filter(ei -> eiIds.contains(ei.getMnjxPnrEi().getPnrEiId())).forEach(tk -> tk.setXe(false));
            //移除添加的rmk
            List<PnrRmkDto> pnrRmkDtos = memoryDataPnr.getPnrRmkDtos();
            pnrRmkDtos.removeIf(rmk -> rmkIds.contains(rmk.getMnjxPnrRmk().getPnrRmkId()));
            //还原生成的离港团名
            MnjxPnr mnjxPnr = memoryDataPnr.getMnjxPnr();
            mnjxPnr.setDefaultGroupName(defaultGroupName);
            //移除mnjxPsgSeat的数据
            iMnjxPsgSeatService.removeByIds(psgSeatIds);
            //移除mnjxPsgCki的数据
            iMnjxPsgCkiService.removeByIds(psgCkiIds);
            //移除nmTIcket
            iMnjxPnrNmTicketService.removeByIds(nmTicketIds);
            iMnjxTicketOperateRecordService.removeByIds(optRecordIds);
        }
    }

    public List<AvVo> av(MemoryDataPnr memoryDataPnr, AvDto avDto) throws UnifiedResultException {
        return pnrCommandServicePartAv.av(memoryDataPnr, avDto);
    }

    /**
     * 修改旧票信息
     */
    private void reviseOldTicket(MemoryDataPnr memoryDataPnr, MnjxSi mnjxSi) {
        // 收费改期、升舱的票号信息
        List<PnrNmDto> pnrNmDtos = memoryDataPnr.getPnrNmDtos();
        if (CollUtil.isNotEmpty(pnrNmDtos)) {
            // 旧票号
            Set<String> oldTicketList = new HashSet<>();
            pnrNmDtos.forEach(nm -> {
                List<PnrOiDto> pnrOiDtos = nm.getPnrOiDtos();
                if (CollUtil.isNotEmpty(pnrOiDtos)) {
                    log.info("客票变更：[{}]", pnrOiDtos);
                    oldTicketList.addAll(pnrOiDtos.stream().map(PnrOiDto::getMnjxNmOi).map(k -> k.getOldTicketNo().replace("-", "")).collect(Collectors.toList()));
                }
            });
            // 修改电子客票状态
            if (CollUtil.isNotEmpty(oldTicketList)) {
                log.info("修改的电子客票：[{}]", oldTicketList);
                List<MnjxPnrNmTicket> pnrNmTickets = iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getTicketNo, oldTicketList).list();
                // 如果是多航段，通过OI中的票信息取得对应的电子客票
                List<String> nmTnIdList = pnrNmTickets.stream().map(MnjxPnrNmTicket::getPnrNmTnId).collect(Collectors.toList());
                List<MnjxPnrNmTicket> mnjxPnrNmTickets = iMnjxPnrNmTicketService.lambdaQuery().in(MnjxPnrNmTicket::getPnrNmTnId, nmTnIdList).list();
                mnjxPnrNmTickets.forEach(k -> {
                    k.setTicketStatus1(Constant.TICKET_STATUS_EXCHANGED);
                    if (StrUtil.isNotEmpty(k.getTicketStatus2())) {
                        k.setTicketStatus2(Constant.TICKET_STATUS_EXCHANGED);
                    }
                });
                iMnjxPnrNmTicketService.updateBatchById(mnjxPnrNmTickets);
                // 记录电子客票状态修改
//                iTicketRecordService.recordTicketOperateStatus(mnjxPnrNmTickets, mnjxSi, memoryDataPnr);
            }
        }
    }
}
