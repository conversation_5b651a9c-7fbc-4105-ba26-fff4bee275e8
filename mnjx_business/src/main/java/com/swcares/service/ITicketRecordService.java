package com.swcares.service;

import com.swcares.core.cache.MemoryDataPnr;
import com.swcares.entity.MnjxPnrNmTicket;
import com.swcares.entity.MnjxSi;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/28
 */
public interface ITicketRecordService {

    /**
     * recordTicketOperateStatus
     *
     * @param pnrNmTickets  pnrNmTickets
     * @param mnjxSi        mnjxSi
     * @param memoryDataPnr memoryDataPnr
     */
    void recordTicketOperateStatus(List<MnjxPnrNmTicket> pnrNmTickets, MnjxSi mnjxSi, MemoryDataPnr memoryDataPnr);
}
