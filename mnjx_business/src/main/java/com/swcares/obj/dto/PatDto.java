package com.swcares.obj.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PatDto {

    /**
     * 运价模式参数
     */
    private String pat;

    /**
     * 运价类型参数
     */
    private String patType;

    /**
     * 旅客编号
     */
    private String pNo;

    /**
     * 回显结果(这个地方没懂，为什么要设置这样一个值，现在先不考虑，等后面后时间了来考虑吧)
     */
    private String result;
}
