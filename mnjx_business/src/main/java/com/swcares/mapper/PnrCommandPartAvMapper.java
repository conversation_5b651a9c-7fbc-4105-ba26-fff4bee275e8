package com.swcares.mapper;

import com.swcares.entity.MnjxTcard;
import org.apache.ibatis.annotations.Param;

import com.swcares.entity.MnjxCnd;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PnrCommandPartAvMapper {
    /**
     * Description: 通过城市对查询tcard id
     *
     * @param orgIds 出发城市id
     * @param dstIds 目的城市id
     * @return 通过城市对查询tcard id
     */
    List<MnjxTcard> retrieveTcardIdsByCityPair(@Param("orgIds") List<String> orgIds, @Param("dstIds") List<String> dstIds);
    
    
    /**
     * Title：queryCndByPlaneSecitonId <br>
     * description：根据planSectionI查询cnd<br>
     * @param planSectionId planSectionId
     * @return 根据planSectionI查询cnd<br>
     * author：zhaokan <br>
     * date：2022/11/25 <br>
     */
    MnjxCnd queryCndByPlanSecitonId(String planSectionId);

    /**
     * retrieveCnd
     * @return  retrieveCnd
     */
    List<MnjxCnd> retrieveCnd();
}
