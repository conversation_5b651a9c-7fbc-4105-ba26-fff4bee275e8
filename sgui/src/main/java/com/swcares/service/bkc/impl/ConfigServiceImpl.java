package com.swcares.service.bkc.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.core.unified.PageDto;
import com.swcares.core.unified.PageVo;
import com.swcares.entity.SguiData;
import com.swcares.obj.dto.ContentDto;
import com.swcares.obj.dto.EntryDto;
import com.swcares.obj.vo.EntryVo;
import com.swcares.obj.vo.TypeTreeVo;
import com.swcares.service.ISguiDataService;
import com.swcares.service.bkc.IConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18 14:42
 */
@Service
public class ConfigServiceImpl implements IConfigService {

    @Resource
    private ISguiDataService iSguiDataService;

    @Override
    public TypeTreeVo queryTypeTree() {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "type_tree")
                .one();
        return JSONUtil.parseObj(data.getValue()).toBean(TypeTreeVo.class);
    }

    @Override
    public PageVo<EntryVo> queryEntryList(PageDto<EntryDto> dto) {
        Page<EntryVo> page = new Page<>(dto.getPageNumber(), dto.getPageSize());
        List<EntryVo> list = new ArrayList<>();
        EntryVo entryVo = new EntryVo();
        entryVo.setId(222897L);
        entryVo.setTypeId(102584L);
        entryVo.setTypeCode("SYSTEM");
        entryVo.setCode("EXSYSTEM.SAT.EPID.UNUSE");
        entryVo.setContent("ALL");
        entryVo.setLastDate("2025-05-28 05:41:41");
        entryVo.setSeq(1);
        entryVo.setLastOpe("");
        entryVo.setOperatorId("");
        entryVo.setStatus("1");
        entryVo.setIsSys("0");
        entryVo.setDataType("1");
        entryVo.setName("不使用外部通道的office配置");

        list.add(entryVo);
        page.setRecords(list);
        return PageVo.of(page);
    }

    @Override
    public String getContentByCode(ContentDto dto) {
        if ("BUSINESS.LIMIT.HIDEID.ENABLE".equals(dto.getCode())) {
            return "0";
        } else {
            return null;
        }
    }
}
