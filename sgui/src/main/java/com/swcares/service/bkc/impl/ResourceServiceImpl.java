package com.swcares.service.bkc.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.entity.SguiData;
import com.swcares.obj.vo.ResourceVo;
import com.swcares.service.ISguiDataService;
import com.swcares.service.bkc.IResourceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:27
 */
@Service
public class ResourceServiceImpl implements IResourceService {

    @Resource
    private ISguiDataService iSguiDataService;

    @Override
    public List<ResourceVo> queryRoleResource() {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "resource_vo")
                .one();
        String jsonValue = data.getValue();
        return JSONUtil.toList(JSONUtil.parseArray(jsonValue), ResourceVo.class);
    }
}
