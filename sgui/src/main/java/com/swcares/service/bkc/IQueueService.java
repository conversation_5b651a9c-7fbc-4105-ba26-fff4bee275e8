package com.swcares.service.bkc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.QueuePnrVo;
import com.swcares.obj.vo.SubQueueCrsPnrDetailVo;
import com.swcares.obj.vo.SubQueueCrsPnrResVo;
import com.swcares.obj.vo.SubQueueCrsPnrVo;

/**
 * 队列服务接口
 *
 * <AUTHOR>
 * @date 2025/08/04 17:30
 */
public interface IQueueService {

    /**
     * 查询关注PNR列表
     *
     * @param dto 查询参数
     * @return 关注PNR列表
     * @throws SguiResultException 异常
     */
    SubQueueCrsPnrVo queryQueueList(SubQueueCrsPnrDto dto) throws SguiResultException;

    /**
     * 关注PNR
     *
     * @param dto 关注PNR请求参数
     * @return 关注结果
     * @throws SguiResultException 异常
     */
    SubQueueCrsPnrResVo subQueueCrsPnr(SubQueueCrsPnrReqDto dto) throws SguiResultException;

    /**
     * 取消关注PNR
     *
     * @param dto 取消关注PNR请求参数
     * @return 取消关注结果
     * @throws SguiResultException 异常
     */
    Boolean unsubscribeCrsQueue(UnsubscribeCrsQueueDto dto) throws SguiResultException;

    /**
     * 关注PNR详情
     *
     * @param dto 关注PNR详情请求参数
     * @return 关注PNR详情结果
     * @throws SguiResultException 异常
     */
    SubQueueCrsPnrDetailVo subQueueCrsPnrDetail(SubQueueCrsPnrDetailDto dto) throws SguiResultException;

    /**
     * 刷新关注PNR
     *
     * @param dto 刷新关注PNR请求参数
     * @return 刷新关注PNR结果
     * @throws SguiResultException 异常
     */
    QueuePnrVo queuePnr(QueuePnrDto dto) throws SguiResultException;
}
