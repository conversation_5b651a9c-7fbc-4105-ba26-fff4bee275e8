package com.swcares.service.bkc.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.cache.UserPreferenceCachePreloader;
import com.swcares.core.constants.CacheConstants;
import com.swcares.core.security.custom.CustomUserDetails;
import com.swcares.entity.*;
import com.swcares.obj.dto.UserPreferenceFullDto;
import com.swcares.obj.vo.UserPreferenceFullVo;
import com.swcares.service.*;
import com.swcares.service.bkc.IUserPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 用户偏好设置服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
@Slf4j
@Service
public class UserPreferenceServiceImpl implements IUserPreferenceService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserPreferenceCachePreloader cachePreloader;

    @Resource
    private ISguiUserPreferenceService iSguiUserPreferenceService;

    @Resource
    private ISguiUserPreferenceAirlineCtctService iSguiUserPreferenceAirlineCtctService;

    @Resource
    private ISguiUserPreferenceCheckTipService iSguiUserPreferenceCheckTipService;

    @Resource
    private ISguiUserPreferencePassengerInfoService iSguiUserPreferencePassengerInfoService;

    @Resource
    private ISguiUserPreferenceRemarkService iSguiUserPreferenceRemarkService;

    /**
     * 获取当前用户的偏好设置
     *
     * @return 用户偏好设置DTO
     */
    @Override
    public UserPreferenceFullVo getCurrentUserPreference() {
        String userId = this.getCurrentUserId();

        // 从缓存中获取
        String preferenceKey = CacheConstants.USER_PREFERENCE_PREFIX + userId;
        String preferenceJson = stringRedisTemplate.opsForValue().get(preferenceKey);

        if (CharSequenceUtil.isNotBlank(preferenceJson)) {
            // 如果是空值占位符，返回默认设置
            if (CacheConstants.EMPTY_VALUE.equals(preferenceJson)) {
                return this.createDefaultPreference();
            }

            // 缓存命中，解析数据
            SguiUserPreference preference = JSONUtil.toBean(preferenceJson, SguiUserPreference.class);

            // 转换为VO
            UserPreferenceFullVo vo = new UserPreferenceFullVo();
            BeanUtil.copyProperties(preference, vo);

            // 获取航司CTCT设置
            String airlineCtctKey = CacheConstants.USER_PREFERENCE_AIRLINE_CTCT_PREFIX + preference.getPreferenceId();
            String airlineCtctJson = stringRedisTemplate.opsForValue().get(airlineCtctKey);
            if (CharSequenceUtil.isNotBlank(airlineCtctJson)) {
                List<SguiUserPreferenceAirlineCtct> airlineCtcts = JSONUtil.toList(JSONUtil.parseArray(airlineCtctJson), SguiUserPreferenceAirlineCtct.class);
                vo.setAirlinesCTCT(this.convertToAirlineCtctVos(airlineCtcts));
            }

            // 获取check tip设置
            String checkTipKey = CacheConstants.USER_PREFERENCE_CHECK_TIP_PREFIX + preference.getPreferenceId();
            String checkTipJson = stringRedisTemplate.opsForValue().get(checkTipKey);
            if (CharSequenceUtil.isNotBlank(checkTipJson)) {
                SguiUserPreferenceCheckTip checkTip = JSONUtil.parseObj(checkTipJson).toBean(SguiUserPreferenceCheckTip.class);
                UserPreferenceFullVo.CheckTipVo checkTipVo = new UserPreferenceFullVo.CheckTipVo();
                BeanUtil.copyProperties(checkTip, checkTipVo);
                vo.setCheckTrip(checkTipVo);
            }

            // 获取passenger info设置
            String passengerInfoKey = CacheConstants.USER_PREFERENCE_PASSENGER_INFO_PREFIX + preference.getPreferenceId();
            String passengerInfoJson = stringRedisTemplate.opsForValue().get(passengerInfoKey);
            if (CharSequenceUtil.isNotBlank(passengerInfoJson)) {
                SguiUserPreferencePassengerInfo passengerInfo = JSONUtil.parseObj(passengerInfoJson).toBean(SguiUserPreferencePassengerInfo.class);
                UserPreferenceFullVo.PassengerInfoVo passengerInfoVo = new UserPreferenceFullVo.PassengerInfoVo();
                BeanUtil.copyProperties(passengerInfo, passengerInfoVo);
                vo.setPassengerInfo(passengerInfoVo);
            }

            // 获取remark设置
            String remarkKey = CacheConstants.USER_PREFERENCE_REMARK_PREFIX + preference.getPreferenceId();
            String remarkJson = stringRedisTemplate.opsForValue().get(remarkKey);
            if (CharSequenceUtil.isNotBlank(remarkJson)) {
                List<SguiUserPreferenceRemark> remarkList = JSONUtil.toList(JSONUtil.parseArray(remarkJson), SguiUserPreferenceRemark.class);
                for (SguiUserPreferenceRemark remark : remarkList) {
                    vo.getRemarkList().add(remark.getRemarkContent());
                }
            }
            return vo;
        } else {
            // 缓存未命中，从数据库查询
            SguiUserPreference preference = iSguiUserPreferenceService.lambdaQuery()
                    .eq(SguiUserPreference::getSiId, userId)
                    .one();

            // 如果没有设置，缓存空值并返回默认设置
            if (preference == null) {
                // 缓存空值，防止缓存穿透
                stringRedisTemplate.opsForValue().set(
                        preferenceKey,
                        CacheConstants.EMPTY_VALUE,
                        CacheConstants.EMPTY_VALUE_EXPIRE_MINUTES,
                        TimeUnit.MINUTES
                );
                return this.createDefaultPreference();
            }

            // 查询航司CTCT设置
            List<SguiUserPreferenceAirlineCtct> airlineCtcts = iSguiUserPreferenceAirlineCtctService.lambdaQuery()
                    .eq(SguiUserPreferenceAirlineCtct::getPreferenceId, preference.getPreferenceId())
                    .list();
            // 更新缓存
            if (CollUtil.isNotEmpty(airlineCtcts)) {
                String airlineCtctKey = CacheConstants.USER_PREFERENCE_AIRLINE_CTCT_PREFIX + preference.getPreferenceId();
                stringRedisTemplate.opsForValue().set(
                        airlineCtctKey,
                        JSONUtil.toJsonStr(airlineCtcts),
                        CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                        TimeUnit.HOURS
                );
            }

            // 查询check tip设置
            SguiUserPreferenceCheckTip checkTip = iSguiUserPreferenceCheckTipService.lambdaQuery()
                    .eq(SguiUserPreferenceCheckTip::getPreferenceId, preference.getPreferenceId())
                    .one();
            // 更新缓存
            String checkTipKey = CacheConstants.USER_PREFERENCE_CHECK_TIP_PREFIX + preference.getPreferenceId();
            stringRedisTemplate.opsForValue().set(
                    checkTipKey,
                    JSONUtil.toJsonStr(checkTip),
                    CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                    TimeUnit.HOURS
            );

            // 查询passenger info设置
            SguiUserPreferencePassengerInfo passengerInfo = iSguiUserPreferencePassengerInfoService.lambdaQuery()
                    .eq(SguiUserPreferencePassengerInfo::getPreferenceId, preference.getPreferenceId())
                    .one();
            // 更新缓存
            String passengerInfoKey = CacheConstants.USER_PREFERENCE_PASSENGER_INFO_PREFIX + preference.getPreferenceId();
            stringRedisTemplate.opsForValue().set(
                    passengerInfoKey,
                    JSONUtil.toJsonStr(passengerInfo),
                    CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                    TimeUnit.HOURS
            );

            // 查询remark
            List<SguiUserPreferenceRemark> remarkList = iSguiUserPreferenceRemarkService.lambdaQuery()
                    .eq(SguiUserPreferenceRemark::getPreferenceId, preference.getPreferenceId())
                    .list();
            // 更新缓存
            String remarkKey = CacheConstants.USER_PREFERENCE_REMARK_PREFIX + preference.getPreferenceId();
            stringRedisTemplate.opsForValue().set(
                    remarkKey,
                    JSONUtil.toJsonStr(remarkList),
                    CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                    TimeUnit.HOURS
            );

            // 转换为VO
            UserPreferenceFullVo vo = new UserPreferenceFullVo();
            BeanUtil.copyProperties(preference, vo);
            if (CollUtil.isNotEmpty(airlineCtcts)) {
                vo.setAirlinesCTCT(this.convertToAirlineCtctVos(airlineCtcts));
            }
            vo.setPassengerInfo(BeanUtil.toBean(passengerInfo, UserPreferenceFullVo.PassengerInfoVo.class));
            vo.setCheckTrip(BeanUtil.toBean(checkTip, UserPreferenceFullVo.CheckTipVo.class));
            for (SguiUserPreferenceRemark remark : remarkList) {
                vo.getRemarkList().add(remark.getRemarkContent());
            }

            return vo;
        }
    }

    /**
     * 更新当前用户的偏好设置
     *
     * @param dto 用户偏好设置VO
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCurrentUserPreference(UserPreferenceFullDto dto) {
        String userId = this.getCurrentUserId();
        SguiUserPreference preference = iSguiUserPreferenceService.lambdaQuery()
                .eq(SguiUserPreference::getSiId, userId)
                .one();
        // 存在偏好设置
        if (ObjectUtil.isNotEmpty(preference)) {
            String preferenceId = preference.getPreferenceId();
            // 拷贝preference新属性
            BeanUtil.copyProperties(dto, preference);

            // 清除之前的airline_ctct、check_tip、passenger_info、remark数据
            iSguiUserPreferenceAirlineCtctService.lambdaUpdate()
                    .eq(SguiUserPreferenceAirlineCtct::getPreferenceId, preferenceId)
                    .remove();
            iSguiUserPreferenceCheckTipService.lambdaUpdate()
                    .eq(SguiUserPreferenceCheckTip::getPreferenceId, preferenceId)
                    .remove();
            iSguiUserPreferencePassengerInfoService.lambdaUpdate()
                    .eq(SguiUserPreferencePassengerInfo::getPreferenceId, preferenceId)
                    .remove();
            iSguiUserPreferenceRemarkService.lambdaUpdate()
                    .eq(SguiUserPreferenceRemark::getPreferenceId, preferenceId)
                    .remove();

            // 创建新的airline_ctct数据
            List<UserPreferenceFullDto.AirlineCtctDto> airlinesCTCT = dto.getAirlinesCTCT();
            List<SguiUserPreferenceAirlineCtct> ctctList = new ArrayList<>();
            for (UserPreferenceFullDto.AirlineCtctDto airlineCtctDto : airlinesCTCT) {
                SguiUserPreferenceAirlineCtct airlineCtct = new SguiUserPreferenceAirlineCtct();
                BeanUtil.copyProperties(airlineCtctDto, airlineCtct);
                airlineCtct.setPreferenceId(preferenceId);
                ctctList.add(airlineCtct);
            }
            if (CollUtil.isNotEmpty(ctctList)) {
                iSguiUserPreferenceAirlineCtctService.saveBatch(ctctList);
            }

            // 创建新的check_tip数据
            if (ObjectUtil.isNotEmpty(dto.getCheckTrip())) {
                SguiUserPreferenceCheckTip checkTip = new SguiUserPreferenceCheckTip();
                BeanUtil.copyProperties(dto.getCheckTrip(), checkTip);
                checkTip.setPreferenceId(preferenceId);
                iSguiUserPreferenceCheckTipService.save(checkTip);
            }

            // 创建新的passenger_info数据
            if (ObjectUtil.isNotEmpty(dto.getPassengerInfo())) {
                SguiUserPreferencePassengerInfo passengerInfo = new SguiUserPreferencePassengerInfo();
                BeanUtil.copyProperties(dto.getPassengerInfo(), passengerInfo);
                passengerInfo.setPreferenceId(preferenceId);
                iSguiUserPreferencePassengerInfoService.save(passengerInfo);
            }

            // 创建新的remark数据
            List<String> remarkDtoList = dto.getRemarkList();
            List<SguiUserPreferenceRemark> remarkList = new ArrayList<>();
            for (String remarkDto : remarkDtoList) {
                SguiUserPreferenceRemark remark = new SguiUserPreferenceRemark();
                remark.setPreferenceId(preferenceId);
                remark.setRemarkContent(remarkDto);
                remarkList.add(remark);
            }
            if (CollUtil.isNotEmpty(remarkList)) {
                iSguiUserPreferenceRemarkService.saveBatch(remarkList);
            }

            // 更新preference
            iSguiUserPreferenceService.updateById(preference);

            // 刷新偏好设置的缓存
            cachePreloader.preloadCache(userId);
        }
        // 新账号没有偏好设置，新建一个偏好设置存储
        else {
            SguiUserPreference newPreference = new SguiUserPreference();
            // 拷贝preference属性
            BeanUtil.copyProperties(dto, newPreference);
            newPreference.setSiId(userId);
            iSguiUserPreferenceService.save(newPreference);

            String preferenceId = newPreference.getPreferenceId();

            // 创建新的airline_ctct数据
            List<UserPreferenceFullDto.AirlineCtctDto> airlinesCTCT = dto.getAirlinesCTCT();
            List<SguiUserPreferenceAirlineCtct> ctctList = new ArrayList<>();
            for (UserPreferenceFullDto.AirlineCtctDto airlineCtctDto : airlinesCTCT) {
                SguiUserPreferenceAirlineCtct airlineCtct = new SguiUserPreferenceAirlineCtct();
                BeanUtil.copyProperties(airlineCtctDto, airlineCtct);
                airlineCtct.setPreferenceId(preferenceId);
                ctctList.add(airlineCtct);
            }
            if (CollUtil.isNotEmpty(ctctList)) {
                iSguiUserPreferenceAirlineCtctService.saveBatch(ctctList);
            }

            // 创建新的check_tip数据
            if (ObjectUtil.isNotEmpty(dto.getCheckTrip())) {
                SguiUserPreferenceCheckTip checkTip = new SguiUserPreferenceCheckTip();
                BeanUtil.copyProperties(dto.getCheckTrip(), checkTip);
                checkTip.setPreferenceId(preferenceId);
                iSguiUserPreferenceCheckTipService.save(checkTip);
            }

            // 创建新的passenger_info数据
            if (ObjectUtil.isNotEmpty(dto.getPassengerInfo())) {
                SguiUserPreferencePassengerInfo passengerInfo = new SguiUserPreferencePassengerInfo();
                BeanUtil.copyProperties(dto.getPassengerInfo(), passengerInfo);
                passengerInfo.setPreferenceId(preferenceId);
                iSguiUserPreferencePassengerInfoService.save(passengerInfo);
            }

            // 创建新的remark数据
            List<String> remarkDtoList = dto.getRemarkList();
            List<SguiUserPreferenceRemark> remarkList = new ArrayList<>();
            for (String remarkDto : remarkDtoList) {
                SguiUserPreferenceRemark remark = new SguiUserPreferenceRemark();
                remark.setPreferenceId(preferenceId);
                remark.setRemarkContent(remarkDto);
                remarkList.add(remark);
            }
            if (CollUtil.isNotEmpty(remarkList)) {
                iSguiUserPreferenceRemarkService.saveBatch(remarkList);
            }
            // 刷新偏好设置的缓存
            cachePreloader.preloadCache(userId);
        }

    }

    /**
     * 获取当前登录用户ID
     *
     * @return 用户ID
     */
    private String getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof CustomUserDetails) {
            CustomUserDetails customUserDetails = (CustomUserDetails) authentication.getPrincipal();
            // 使用siId作为userId
            return customUserDetails.getUserInfo().getSiId();
        }
        return null;
    }

    /**
     * 创建默认的用户偏好设置
     *
     * @return 默认的用户偏好设置DTO
     */
    private UserPreferenceFullVo createDefaultPreference() {
        UserPreferenceFullVo vo = new UserPreferenceFullVo();
        vo.setAirlinesCTCT(new ArrayList<>());
        return vo;
    }

    /**
     * 将航司CTCT实体列表转换为VO列表
     *
     * @param entities 航司CTCT实体列表
     * @return 航司CTCT VO列表
     */
    private List<UserPreferenceFullVo.AirlineCtctVo> convertToAirlineCtctVos(List<SguiUserPreferenceAirlineCtct> entities) {
        List<UserPreferenceFullVo.AirlineCtctVo> voList = new ArrayList<>();
        if (entities != null && !entities.isEmpty()) {
            for (SguiUserPreferenceAirlineCtct entity : entities) {
                UserPreferenceFullVo.AirlineCtctVo vo = new UserPreferenceFullVo.AirlineCtctVo();
                vo.setAirline(entity.getAirline());
                vo.setCtct(entity.getCtct());
                voList.add(vo);
            }
        }
        return voList;
    }
}
