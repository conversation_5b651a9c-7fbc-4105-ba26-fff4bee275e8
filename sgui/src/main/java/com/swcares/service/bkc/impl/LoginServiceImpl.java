package com.swcares.service.bkc.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.constants.AuthCacheConstants;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.Md5PasswordEncoder;
import com.swcares.core.security.TokenJwtManager;
import com.swcares.core.utils.RedisKeyUtils;
import com.swcares.crypto.SM2DecryptUtil;
import com.swcares.entity.MnjxLevel;
import com.swcares.entity.MnjxSi;
import com.swcares.obj.dto.FirstLoginDto;
import com.swcares.obj.vo.FirstLoginVo;
import com.swcares.service.IMnjxLevelService;
import com.swcares.service.IMnjxSiService;
import com.swcares.service.bkc.ILoginService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/18 11:07
 */
@Service
public class LoginServiceImpl implements ILoginService {

    @Value("${crypto.sm2.private-key}")
    private String privateKey;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private TokenJwtManager tokenJwtManager;

    @Resource
    private Md5PasswordEncoder md5PasswordEncoder;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxLevelService iMnjxLevelService;

    /**
     * 登录
     *
     * @param request       请求
     * @param firstLoginDto 登录信息
     * @return 登录信息
     */
    @Override
    public FirstLoginVo login(HttpServletRequest request, FirstLoginDto firstLoginDto) throws SguiResultException {
        // 查询用户
        MnjxSi si = iMnjxSiService.lambdaQuery()
                .eq(MnjxSi::getSiNo, firstLoginDto.getUsername())
                .one();

        // 用户不存在
        if (ObjectUtil.isNull(si)) {
            throw new SguiResultException(CharSequenceUtil.format("{} 用户不存在", firstLoginDto.getUsername()));
        }

        String certificate = firstLoginDto.getCertificate();
        if (CharSequenceUtil.isEmpty(certificate)) {
            throw new SguiResultException("密码不能为空");
        }
        // 解密 - 使用智能解密方法
        String decryptedData = null;
        try {
            // 使用智能解密方法，自动尝试多种格式和模式
            decryptedData = SM2DecryptUtil.smartDecrypt(certificate, privateKey);
        } catch (Exception e) {
            throw new SguiResultException("密码解密失败，请检查前端加密配置: " + e.getMessage());
        }

        // 密码错误
        if (!md5PasswordEncoder.matches(decryptedData, si.getSiPassword())) {
            throw new SguiResultException("密码错误");
        }

        // 验证工作号级别
        MnjxLevel level = iMnjxLevelService.getById(si.getLevelId());
        if (!"41".equals(level.getLevelCode())) {
            throw new SguiResultException("登录了错误级别的工作号");
        }

        // 验证验证码
        String captchaKey = request.getHeader("captcha-key");
        String sguiSessionId = "";
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("sguiSessionId".equals(cookie.getName())) {
                    sguiSessionId = cookie.getValue();
                    captchaKey = AuthCacheConstants.CAPTCHA + sguiSessionId;
                    break;
                }
            }
        }
        String code = StrUtil.toString(stringRedisTemplate.opsForValue().get(captchaKey));
        if (!code.equalsIgnoreCase(firstLoginDto.getVerificationCode())) {
            throw new SguiResultException("验证码错误");
        }

        FirstLoginVo firstLoginVo = new FirstLoginVo();
        String iamToken = tokenJwtManager.createToken(firstLoginDto.getUsername());
        firstLoginVo.setIamToken(iamToken);
        firstLoginVo.setAgentAbroad(false);
        firstLoginVo.setEndUser(firstLoginDto.getUsername());
        firstLoginVo.setKickOutEnable(false);
        firstLoginVo.setSecurityLevel(4);
        firstLoginVo.setToken("");

        // 用户名密码登录成功后，需要将验证码从redis中删除
        stringRedisTemplate.delete(captchaKey);

        // 并且检查用户是否是已登录状态，如果是，需要移除之前的登录踢下线。
        // 由于sguiSessionId是新会话生成的，需要在Redis中找到用户名这一层，如果存在就删除该缓存
        String userKeyPattern = RedisKeyUtils.getUserInfoKey(firstLoginDto.getUsername()) + ":*";
        Set<String> keys = stringRedisTemplate.keys(userKeyPattern);
        if (keys != null && !keys.isEmpty()) {
            stringRedisTemplate.delete(keys);
            SecurityContextHolder.clearContext();
        }

        // 删除占位缓存
        String porCacheKey = "POR:" + firstLoginDto.getUsername();
        stringRedisTemplate.delete(porCacheKey);

        return firstLoginVo;
    }
}
