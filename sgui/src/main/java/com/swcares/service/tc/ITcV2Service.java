package com.swcares.service.tc;

import com.swcares.core.exception.SguiResultException;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:18
 */
public interface ITcV2Service {

    QueryOfficeInformationVo queryOfficeInformation(QueryOfficeInformationDto dto);

    /**
     * 按票号查询客票详情
     *
     * @param dto 查询参数
     * @return 客票详情列表
     * @throws SguiResultException 异常
     */
    List<QueryTicketDetailVo> queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException;

    /**
     * 通过DETR查询票面信息
     *
     * @param dto 查询参数
     * @return 票面信息
     * @throws SguiResultException 异常
     */
    QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException;

    /**
     * 通过RTKT查询票面信息
     *
     * @param ticketNo 票号
     * @return 票面信息
     * @throws SguiResultException 异常
     */
    TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException;

    /**
     * 按PNR查询客票信息
     *
     * @param dto 查询参数
     * @return 客票信息列表
     * @throws SguiResultException 异常
     */
    List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException;

    /**
     * 按证件号查询客票信息
     *
     * @param dto 查询参数
     * @return 客票信息列表
     * @throws SguiResultException 异常
     */
    List<QueryTicketByPnrVo> queryTicketDigestsByCert(QueryTicketByCertDto dto) throws SguiResultException;

    /**
     * 退前预览
     *
     * @param dto 查询参数
     * @return 退前预览信息
     * @throws SguiResultException 异常
     */
    PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException;

    /**
     * 初始加载退票信息
     *
     * @param dto 查询参数
     * @return 退票信息
     * @throws SguiResultException 异常
     */
    FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException;

    /**
     * 退票PNR信息查询
     *
     * @param dto 查询参数
     * @return PNR信息
     * @throws SguiResultException 异常
     */
    QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException;

    /**
     * 计算退票价格
     *
     * @param dto 查询参数
     * @return 退票价格信息
     * @throws SguiResultException 异常
     */
    BatchFindRefundFeeVo batchFindRefundFee(BatchFindRefundFeeDto dto) throws SguiResultException;

    /**
     * 自动退票
     *
     * @param dto 查询参数
     * @return 退票结果
     * @throws SguiResultException 异常
     */
    BatchAutoRefundVo batchAutoRefund(BatchAutoRefundDto dto) throws SguiResultException;

    /**
     * 退票取消PNR
     *
     * @param dto 查询参数
     * @return 取消结果
     * @throws SguiResultException 异常
     */
    DeletePnrAndDeleteInfantInfoVo deletePnrAndDeleteInfantInfo(DeletePnrAndDeleteInfantInfoDto dto) throws SguiResultException;

    /**
     * 查询客票管理机构
     *
     * @param ticketNo 票号
     * @return 客票管理机构名称
     * @throws SguiResultException 异常
     */
    String queryTicketManagementOrganization(String ticketNo) throws SguiResultException;

    /**
     * 查询退票单号
     *
     * @param dto 查询参数
     * @return 退票单号信息
     * @throws SguiResultException 异常
     */
    QueryRefundFormVo queryRefundForm(QueryRefundFormDto dto) throws SguiResultException;

    /**
     * 挂起解挂票务状态变更
     *
     * @param dto 挂起解挂请求参数
     * @return 操作结果
     * @throws SguiResultException 异常
     */
    String tssChangeTicketStatus(TssChangeTicketStatusDto dto) throws SguiResultException;

    /**
     * 批量手工退票
     *
     * @param dto 退票参数
     * @return 退票结果
     * @throws SguiResultException 异常
     */
    BatchManualRefundTicketVo batchManualRefundTicket(BatchManualRefundTicketDto dto) throws SguiResultException;

    /**
     * 废票查询
     *
     * @param dto 查询参数
     * @return 废票查询结果
     * @throws SguiResultException 异常
     */
    List<QueryTicketByInvalidVo> queryTicketByInvalid(QueryTicketByInvalidDto dto) throws SguiResultException;

    /**
     * 查询PNR所有出票旅客信息
     *
     * @param dto 查询参数
     * @return PNR所有出票旅客信息
     * @throws SguiResultException 异常
     */
    List<QueryAllPassengersByTktNumberVo> queryAllPassengersByTktNumber(QueryAllPassengersByTktNumberDto dto) throws SguiResultException;

    /**
     * 按出票日期联票挂起解挂
     *
     * @param dto 挂起解挂请求参数
     * @return 操作结果
     * @throws SguiResultException 异常
     */
    String tssChangeTicketStatusByPnr(TssChangeTicketStatusByPnrDto dto) throws SguiResultException;

    /**
     * 获取税项
     *
     * @param dto 查询参数
     * @return 税项信息
     * @throws SguiResultException 异常
     */
    QueryRtktDetailVo queryRtktDetail(QueryRtktDetailDto dto) throws SguiResultException;

    /**
     * 删除退票单
     *
     * @param dto 删除参数
     * @return 操作结果
     * @throws SguiResultException 异常
     */
    String deleteRefundForm(DeleteRefundFormDto dto) throws SguiResultException;

    /**
     * 修改退票单
     *
     * @param dto 修改参数
     * @return 修改结果
     * @throws SguiResultException 异常
     */
    ModifyRefundFormVo modifyRefundForm(ModifyRefundFormDto dto) throws SguiResultException;

    /**
     * 手工退票
     *
     * @param dto
     * @return
     */
    ManualRefundTicketVo manualRefundTicket(BatchManualRefundTicketDto.RefundInfo dto) throws SguiResultException;

    /**
     * 计算批量退票费Z
     *
     * @param dto 查询参数
     * @return 退票费计算结果
     * @throws SguiResultException 异常
     */
    BatchFindRefundFeeZVo batchFindRefundFeeZ(BatchFindRefundFeeZDto dto) throws SguiResultException;

    /**
     * 批量退票
     *
     * @param dto 查询参数
     * @return 退票结果
     * @throws SguiResultException 异常
     */
    BatchRefundVo batchRefund(BatchRefundDto dto) throws SguiResultException;

    /**
     * 修改客票状态
     *
     * @param dto 修改参数
     * @return 操作结果
     * @throws SguiResultException 异常
     */
    String etrfChangeTicketStatus(EtrfChangeTicketStatusDto dto) throws SguiResultException;

    /**
     * 运价确认
     *
     * @param dto 运价确认参数
     * @return 运价确认结果
     * @throws SguiResultException 异常
     */
    void fareConfirm(FareConfirmDto dto);

    /**
     * 按姓名查询票证
     *
     * @param dto 查询参数
     * @return 客票信息列表
     * @throws SguiResultException 异常
     */
    List<GetTicketDigestsByNameVo> getTicketDigestsByName(GetTicketDigestsByNameDto dto) throws SguiResultException;

    /**
     * 现有PNR改签出票
     *
     * @param dto 出票请求参数
     * @return 出票结果
     * @throws SguiResultException 异常
     */
    DomesticTicketVo domesticTicket(DomesticTicketDto dto) throws SguiResultException;

    /**
     * 更新航段组行动代码为RR
     *
     * @param dto 更新参数
     * @return 操作结果
     * @throws SguiResultException 异常
     */
    String updateSegmentRR(UpdateSegmentRRDto dto) throws SguiResultException;
}
