package com.swcares.service.tc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.NumberUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.bkc.IXePnrService;
import com.swcares.service.tc.IRefundTicketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 退票服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/2 13:00
 */
@Slf4j
@Service
public class RefundTicketServiceImpl implements IRefundTicketService {

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IMnjxNmFnService iMnjxNmFnService;

    @Resource
    private IMnjxPnrFnService iMnjxPnrFnService;

    @Resource
    private IMnjxRefundTicketService iMnjxRefundTicketService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private IXePnrService iXePnrService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;

    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxTicketPriceService iMnjxTicketPriceService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Override
    public FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(dto.getTktNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = dto.getTktNo().replace("-", "");

        // 1. 根据票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("票号不存在");
        }

        // 2. 根据ticket的tnId查询mnjx_pnr_nm_tn表
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("票号关联信息不存在");
        }

        // 判断是否为婴儿票
        boolean isInfant = CharSequenceUtil.isNotEmpty(pnrNmTn.getNmXnId());
        String pnrNmId;
        String passengerName = null;
        String psgType = null;

        if (isInfant) {
            // 4. 婴儿处理：根据nmXnId查询mnjx_nm_xn表
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            if (nmXn == null) {
                throw new SguiResultException("婴儿信息不存在");
            }
            pnrNmId = nmXn.getPnrNmId();
            passengerName = nmXn.getXnCname();
            psgType = "INF";
        } else {
            // 3. 成人/儿童处理：根据pnrNmId查询mnjx_pnr_nm表
            pnrNmId = pnrNmTn.getPnrNmId();
        }

        // 查询旅客详细信息
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("旅客信息不存在");
        }

        if (!isInfant) {
            passengerName = pnrNm.getName();
            psgType = this.convertPsgType(pnrNm);
        }

        // 5. 根据pnrId查询mnjx_pnr表
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("PNR信息不存在");
        }

        // 构建返回对象
        FindRefundTicketVo result = new FindRefundTicketVo();

        // 构建票务信息
        FindRefundTicketVo.TicketInfo ticketInfo = this.buildTicketInfo(pnrNmTicket, pnrNmTn, pnr, pnrNm, passengerName, psgType, isInfant, dto.getSecondFactor());
        result.setTicket(ticketInfo);

        // 设置其他信息
        MnjxSi issuedSi = iMnjxSiService.getById(pnrNmTn.getIssuedSiId());
        MnjxOffice issuedOffice = iMnjxOfficeService.getById(issuedSi.getOfficeId());
        MnjxAgent issuedAgent = iMnjxAgentService.getById(issuedOffice.getOrgId());
        result.setOffice(issuedOffice.getOfficeNo());
        result.setIata(issuedAgent.getAgentIata());
        result.setAgent(issuedSi.getSiNo());

        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        result.setOperator(userInfo.getSiNo());

        // 联票数量，用ticketInfo的票号计算
        String infoTicketNo = ticketInfo.getTicketNo();
        if (infoTicketNo.contains("-")) {
            String[] split = infoTicketNo.split("-");
            int conjunction = 1 + Integer.parseInt(split[1]) - Integer.parseInt(split[0].substring(split[0].length() - 2));
            result.setConjunction(StrUtil.toString(conjunction));
        } else {
            result.setConjunction("1");
        }

        result.setTicketManagementOrganizationCode("BSP");

        MnjxPrinter printer = iMnjxPrinterService.getById(pnrNmTn.getPrinterId());
        result.setPrinterNo(printer.getPrinterNo());

        result.setRefundPrintNumber(null);
        if (ticketInfo.getSegment().stream().anyMatch(s -> Constant.TICKET_STATUS_REFOUND.equals(s.getTicketStatus()))) {
            result.setRefundPrintNumber(printer.getPrinterNo());
        }

        result.setReceiptPrinted("0");

        return result;
    }

    @Override
    public PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(dto.getTktNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = dto.getTktNo().replace("-", "");

        // 1. 根据票号查询mnjx_pnr_nm_ticket表
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("票号不存在");
        }

        // 2. 根据ticket的tnId查询mnjx_pnr_nm_tn表
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("票号关联信息不存在");
        }

        // 判断是否为婴儿票
        boolean isInfant = CharSequenceUtil.isNotEmpty(pnrNmTn.getNmXnId());
        String pnrNmId;
        String passengerName = null;
        String psgType = null;

        if (isInfant) {
            // 4. 婴儿处理：根据nmXnId查询mnjx_nm_xn表
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            if (nmXn == null) {
                throw new SguiResultException("婴儿信息不存在");
            }
            pnrNmId = nmXn.getPnrNmId();
            passengerName = nmXn.getXnCname();
            psgType = "INF";
        } else {
            // 3. 成人/儿童处理：根据pnrNmId查询mnjx_pnr_nm表
            pnrNmId = pnrNmTn.getPnrNmId();
        }

        // 查询旅客详细信息
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("旅客信息不存在");
        }

        if (!isInfant) {
            passengerName = pnrNm.getName();
            psgType = this.convertPsgType(pnrNm);
        }

        // 5. 根据pnrId查询mnjx_pnr表
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("PNR信息不存在");
        }

        // 构建返回对象
        PreviewRefundTicketVo result = new PreviewRefundTicketVo();

        // 构建退票订单信息
        PreviewRefundTicketVo.RefundTicketOrder refundTicketOrder = this.buildRefundTicketOrder(
                pnrNmTicket, pnrNmTn, pnr, pnrNm, passengerName, psgType, isInfant, dto.getSecondFactor());
        result.setRefundTicketOrder(refundTicketOrder);

        // 构建退票计算信息
        PreviewRefundTicketVo.RefundCompute refundCompute = this.buildRefundCompute(pnrNmTicket, pnr, pnrNm, isInfant);
        result.setRefundCompute(refundCompute);

        // 设置二次验证信息
        PreviewRefundTicketVo.SecondFactor secondFactor = new PreviewRefundTicketVo.SecondFactor();
        secondFactor.setSecondFactorCode(dto.getSecondFactor().getSecondFactorCode());
        secondFactor.setSecondFactorValue(dto.getSecondFactor().getSecondFactorValue());
        if ("NM".equals(dto.getSecondFactor().getSecondFactorCode())) {
            secondFactor.setSecondFactorValue(passengerName);
        }
        result.setSecondFactor(secondFactor);

        return result;
    }

    /**
     * 转换旅客类型
     */
    private String convertPsgType(MnjxPnrNm pnrNm) {
        MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmSsr::getSsrType, "CHLD")
                .one();
        if (ObjectUtil.isNotEmpty(chldSsr)) {
            return "CHD";
        } else {
            MnjxNmRmk gmjcRmk = iMnjxNmRmkService.lambdaQuery()
                    .eq(MnjxNmRmk::getPnrNmId, pnrNm.getPnrNmId())
                    .eq(MnjxNmRmk::getRmkName, "GMJC")
                    .one();
            if (ObjectUtil.isNotEmpty(gmjcRmk)) {
                return "GMJC";
            } else {
                return "ADT";
            }
        }
    }

    /**
     * 构建票务信息
     */
    private FindRefundTicketVo.TicketInfo buildTicketInfo(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm,
            String passengerName, String psgType, boolean isInfant,
            FindRefundTicketDto.SecondFactor secondFactorDto) {

        FindRefundTicketVo.TicketInfo ticketInfo = new FindRefundTicketVo.TicketInfo();

        // 设置基本信息
        ticketInfo.setTktType("D");
        ticketInfo.setCdsTicket(false);
        // 如果是联票的，需要设置联票的格式
        ticketInfo.setTicketNo(pnrNmTn.getInputValue().replace("TN/", "").replaceAll("/P\\d+", "").replaceFirst("-", ""));
        ticketInfo.setPsgType(psgType);
        ticketInfo.setTicketPsgType(psgType);
        ticketInfo.setName(passengerName);
        ticketInfo.setPassengerNameSuffix(passengerName);
        ticketInfo.setSpecialPassengerType(psgType);
        ticketInfo.setPayType("CASH");
        ticketInfo.setCurrency("CNY");
        ticketInfo.setEtTag("1");
        ticketInfo.setAirline(pnrNmTicket.getTicketNo().substring(0, 3)); // 航司结算码
        ticketInfo.setPnr(pnr.getPnrIcs());
        ticketInfo.setCrsPnrNo(pnr.getPnrCrs());
        ticketInfo.setIsAirportCntl("0");
        ticketInfo.setExchangeTktNo("");
        ticketInfo.setGovernmentPurchase(false);
        ticketInfo.setCommissionRate("0.00");

        // 构建航段信息
        List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                .orderByAsc(MnjxPnrNmTicket::getTicketNo)
                .list();
        List<FindRefundTicketVo.SegmentInfo> segments = new ArrayList<>();
        int ticketIndex = 1;
        for (MnjxPnrNmTicket ticket : nmTicketList) {
            segments.addAll(this.buildSegmentInfos(ticket, pnr, ticketIndex));
            ticketIndex++;
        }
        ticketInfo.setIsCoupon(nmTicketList.size() > 1 ? "1" : "0");

        ticketInfo.setSegment(segments);
        if (segments.stream().anyMatch(s -> Constant.TICKET_STATUS_REFOUND.equals(s.getTicketStatus()))) {
            ticketInfo.setPnr(null);
            ticketInfo.setCrsPnrNo(null);
        }

        // 设置市场航空公司
        ticketInfo.setMarketAirline(pnrNmTn.getIssuedAirline());

        // 查询价格信息
        this.setTicketPriceInfo(ticketInfo, pnrNm.getPnrId(), pnrNm.getPnrNmId(), isInfant, (int) segments.stream().filter(s -> CharSequenceUtil.isNotEmpty(s.getFlightNo())).count());

        // 设置二次验证信息
        if (secondFactorDto != null) {
            FindRefundTicketVo.SecondFactor secondFactor = new FindRefundTicketVo.SecondFactor();
            secondFactor.setSecondFactorCode(secondFactorDto.getSecondFactorCode());
            secondFactor.setSecondFactorValue(secondFactorDto.getSecondFactorValue());
            ticketInfo.setSecondFactor(secondFactor);
        }

        return ticketInfo;
    }

    /**
     * 构建航段信息
     */
    private List<FindRefundTicketVo.SegmentInfo> buildSegmentInfos(MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr, int ticketIndex) {
        List<FindRefundTicketVo.SegmentInfo> segments = new ArrayList<>();

        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();
        int segIndex = 1;
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            MnjxPnrSeg seg1 = iMnjxPnrSegService.getById(pnrNmTicket.getS1Id());
            segments.add(this.buildSegmentInfo(seg1, pnrNmTicket, pnr, ticketIndex, segIndex++));
            // 检查是否需要在s1Id之后添加ARNK航段
            if (CharSequenceUtil.isEmpty(pnrNmTicket.getS2Id())) {
                MnjxPnrSeg nextSeg = pnrSegList.stream()
                        .filter(s -> s.getPnrSegNo().equals(seg1.getPnrSegNo() + 1))
                        .findFirst().orElse(null);
                if (nextSeg != null) {
                    segments.add(this.buildArnkSegmentInfo(nextSeg, pnrNmTicket, ticketIndex, segIndex++));
                }
            }
        }
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            MnjxPnrSeg seg2 = iMnjxPnrSegService.getById(pnrNmTicket.getS2Id());
            // 检查是否需要在s2Id之前添加ARNK航段
            if (CharSequenceUtil.isEmpty(pnrNmTicket.getS1Id())) {
                MnjxPnrSeg prevSeg = pnrSegList.stream()
                        .filter(s -> s.getPnrSegNo().equals(seg2.getPnrSegNo() - 1))
                        .findFirst().orElse(null);
                if (prevSeg != null) {
                    segments.add(this.buildArnkSegmentInfo(prevSeg, pnrNmTicket, ticketIndex, segIndex++));
                }
            }
            segments.add(this.buildSegmentInfo(seg2, pnrNmTicket, pnr, ticketIndex, segIndex));
        }

        return segments;
    }

    private FindRefundTicketVo.SegmentInfo buildArnkSegmentInfo(MnjxPnrSeg pnrSeg, MnjxPnrNmTicket pnrNmTicket, int ticketIndex, int index) {
        FindRefundTicketVo.SegmentInfo segment = new FindRefundTicketVo.SegmentInfo();
        segment.setFlightNo(null);
        segment.setDepartureTime(null);
        segment.setRph(ticketIndex + "-" + index);
        segment.setE8Rph(String.valueOf(index));
        segment.setDepartureCode(pnrSeg.getOrg());
        segment.setArriveCode(pnrSeg.getDst());
        segment.setTktTag(pnrNmTicket.getTicketNo());
        segment.setIsAble("0");
        segment.setSegmentType("1");
        segment.setTicketStatus("VOID");
        return segment;
    }

    private FindRefundTicketVo.SegmentInfo buildSegmentInfo(MnjxPnrSeg pnrSeg, MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr, int ticketIndex, int index) {
        FindRefundTicketVo.SegmentInfo segment = new FindRefundTicketVo.SegmentInfo();

        segment.setAirline(pnrSeg.getFlightNo().substring(0, 2));

        segment.setIsAble("1");
        segment.setSegmentType("2");
        segment.setDepartureCode(pnrSeg.getOrg());
        segment.setArriveCode(pnrSeg.getDst());
        segment.setRph(ticketIndex + "-" + index);
        segment.setIsCheck(null);
        segment.setTktTag(pnrNmTicket.getTicketNo());
        segment.setE8Rph(String.valueOf(index));

        // 设置票状态
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS1Id()) && pnrNmTicket.getS1Id().equals(pnrSeg.getPnrSegId())) {
            segment.setTicketStatus(CharSequenceUtil.isNotEmpty(pnrNmTicket.getTicketStatus1()) ? pnrNmTicket.getTicketStatus1() : "OPEN FOR USE");
        } else if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS2Id()) && pnrNmTicket.getS2Id().equals(pnrSeg.getPnrSegId())) {
            segment.setTicketStatus(CharSequenceUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()) ? pnrNmTicket.getTicketStatus2() : "OPEN FOR USE");
        } else {
            segment.setTicketStatus("OPEN FOR USE");
        }
        if (Constant.TICKET_STATUS_REFOUND.equals(segment.getTicketStatus())) {
            segment.setIsAble("0");
        }

        segment.setFlightNo(pnrSeg.getFlightNo());
        segment.setCabinCode(pnrSeg.getSellCabin());
        segment.setDepartureDate(pnrSeg.getFlightDate());
        segment.setDepartureTime(pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2) + ":00");

        if ("DEL".equals(pnr.getPnrStatus())) {
            segment.setIsAble("0");
            segment.setSegmentType("3");
            segment.setFlightNo(null);
            segment.setDepartureDate(null);
            segment.setDepartureTime(null);
        }

        return segment;
    }

    /**
     * 设置票务价格信息
     */
    private void setTicketPriceInfo(FindRefundTicketVo.TicketInfo ticketInfo, String pnrId, String pnrNmId,
                                    boolean isInfant, int segmentCount) {
        // 查询FN信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;
        BigDecimal commission;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnrId)
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算代理费：每个航段5元
        commission = BigDecimal.valueOf(segmentCount * 5L);

        // 设置税费信息
        List<FindRefundTicketVo.TaxInfo> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            FindRefundTicketVo.TaxInfo cnTaxInfo = new FindRefundTicketVo.TaxInfo();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            FindRefundTicketVo.TaxInfo yqTaxInfo = new FindRefundTicketVo.TaxInfo();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        BigDecimal totalTaxes = cnTax.add(yqTax);

        ticketInfo.setTotalAmount(totalAmount.toString());
        ticketInfo.setTotalTaxs(totalTaxes.toString());
        ticketInfo.setTaxs(taxes);
        ticketInfo.setCommission(NumberUtils.formatBigDecimalStr(commission.toString()));
    }

    /**
     * 构建退票订单信息
     */
    private PreviewRefundTicketVo.RefundTicketOrder buildRefundTicketOrder(
            MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm,
            String passengerName, String psgType, boolean isInfant,
            PreviewRefundTicketDto.SecondFactor secondFactorDto) throws SguiResultException {

        PreviewRefundTicketVo.RefundTicketOrder refundTicketOrder = new PreviewRefundTicketVo.RefundTicketOrder();

        // 构建票信息
        PreviewRefundTicketVo.Ticket ticket = new PreviewRefundTicketVo.Ticket();
        ticket.setTktType("D");
        ticket.setCdsTicket(false);
        ticket.setTicketNo(pnrNmTicket.getTicketNo());
        ticket.setPsgType(psgType);
        ticket.setTicketPsgType(psgType);
        ticket.setName(passengerName);
        ticket.setPassengerNameSuffix(passengerName);
        ticket.setSpecialPassengerType(psgType);
        ticket.setPayType("");
        ticket.setCurrency("CNY");
        ticket.setEtTag("1");
        ticket.setAirline(pnrNmTicket.getTicketNo().substring(0, 3)); // 航司结算码
        ticket.setPnr(pnr.getPnrIcs());
        ticket.setCrsPnrNo(pnr.getPnrCrs());
        ticket.setIsCoupon("0");
        ticket.setIsAirportCntl("0");
        ticket.setExchangeTktNo("");
        ticket.setGovernmentPurchase(false);
        ticket.setCommissionRate("0.00");

        // 构建航段信息
        List<PreviewRefundTicketVo.Segment> segments = this.buildSegments(pnrNmTicket);
        ticket.setSegment(segments);

        // 设置市场航空公司（取第一个航段的航空公司）
        if (!segments.isEmpty()) {
            ticket.setMarketAirline(segments.get(0).getAirline());
        }

        // 查询价格信息
        this.setPriceInfo(ticket, pnrNm.getPnrId(), pnrNm.getPnrNmId(), isInfant, segments.size());

        // 设置二次验证信息
        if (secondFactorDto != null) {
            boolean secondFactorError = false;
            // 查证件信息列表
            List<MnjxNmSsr> certificateSsrList = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                    .in(MnjxNmSsr::getSsrType, "FOID", "DOCS")
                    .list();
            switch (secondFactorDto.getSecondFactorCode()) {
                case "NI":
                case "UU":
                    // 居民身份证号
                    // 特殊身份证
                    if (isInfant) {
                        secondFactorError = true;
                    }
                    if (certificateSsrList.stream().noneMatch(s -> "FOID".equals(s.getSsrType()))) {
                        secondFactorError = true;
                    }
                    if (certificateSsrList.stream().noneMatch(s -> s.getSsrInfo().contains(secondFactorDto.getSecondFactorCode() + secondFactorDto.getSecondFactorValue()))) {
                        secondFactorError = true;
                    }
                    break;
                case "PP":
                    // 护照号码
                    if (certificateSsrList.stream().noneMatch(s -> "DOCS".equals(s.getSsrType()))) {
                        secondFactorError = true;
                    }
                    if (isInfant) {
                        if (certificateSsrList.stream().noneMatch(s -> (s.getInputValue().contains("/FI/") || s.getInputValue().contains("/MI/")) && s.getSsrInfo().contains("/" + secondFactorDto.getSecondFactorValue() + "/"))) {
                            secondFactorError = true;
                        }
                    } else {
                        if (certificateSsrList.stream().noneMatch(s -> (!s.getInputValue().contains("/FI/") && !s.getInputValue().contains("/MI/")) && s.getSsrInfo().contains("/" + secondFactorDto.getSecondFactorValue() + "/"))) {
                            secondFactorError = true;
                        }
                    }
                    break;
                case "NM":
                    // 姓名
                    if (!passengerName.startsWith(secondFactorDto.getSecondFactorValue())) {
                        secondFactorError = true;
                    }
                    break;
                case "CN":
                    // PNR编码
                    if (!secondFactorDto.getSecondFactorValue().equals(pnr.getPnrCrs())) {
                        secondFactorError = true;
                    }
                    break;
                default:
                    break;
            }
            if (secondFactorError) {
                throw new SguiResultException("未查询到数据");
            }
            PreviewRefundTicketVo.SecondFactor secondFactor = new PreviewRefundTicketVo.SecondFactor();
            secondFactor.setSecondFactorCode(secondFactorDto.getSecondFactorCode());
            secondFactor.setSecondFactorValue(secondFactorDto.getSecondFactorValue());
            if ("NM".equals(secondFactorDto.getSecondFactorCode())) {
                secondFactor.setSecondFactorValue(passengerName);
            }
            ticket.setSecondFactor(secondFactor);
        }

        refundTicketOrder.setTicket(ticket);

        // 设置其他订单信息
        MnjxSi issuedSi = iMnjxSiService.getById(pnrNmTn.getIssuedSiId());
        MnjxOffice issuedOffice = iMnjxOfficeService.getById(issuedSi.getOfficeId());
        MnjxAgent issuedAgent = iMnjxAgentService.getById(issuedOffice.getOrgId());
        refundTicketOrder.setOffice(issuedOffice.getOfficeNo());
        refundTicketOrder.setIata(issuedAgent.getAgentIata());
        refundTicketOrder.setAgent(issuedSi.getSiNo());
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        refundTicketOrder.setOperator(userInfo.getSiNo());
        refundTicketOrder.setConjunction("1");
        refundTicketOrder.setTicketManagementOrganizationCode("BSP");
        refundTicketOrder.setPrinterNo("1");
        refundTicketOrder.setRefundPrintNumber(null);
        refundTicketOrder.setReceiptPrinted("0");

        return refundTicketOrder;
    }

    /**
     * 构建航段信息（用于PreviewRefundTicket）
     */
    private List<PreviewRefundTicketVo.Segment> buildSegments(MnjxPnrNmTicket pnrNmTicket) {
        List<PreviewRefundTicketVo.Segment> segments = new ArrayList<>();

        // 查询航段信息
        List<String> segmentIds = new ArrayList<>();
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            segmentIds.add(pnrNmTicket.getS1Id());
        }
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            segmentIds.add(pnrNmTicket.getS2Id());
        }

        if (!segmentIds.isEmpty()) {
            List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.listByIds(segmentIds);

            for (int i = 0; i < pnrSegList.size(); i++) {
                MnjxPnrSeg pnrSeg = pnrSegList.get(i);
                PreviewRefundTicketVo.Segment segment = new PreviewRefundTicketVo.Segment();

                segment.setAirline(pnrSeg.getFlightNo().substring(0, 2));
                segment.setIsAble("1");
                segment.setDepartureCode(pnrSeg.getOrg());
                segment.setArriveCode(pnrSeg.getDst());
                segment.setRph("1-" + (i + 1));
                segment.setIsCheck(null);
                segment.setTktTag(pnrNmTicket.getTicketNo());
                segment.setE8Rph(String.valueOf(i + 1));

                // 设置票状态
                if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS1Id()) && pnrNmTicket.getS1Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(CharSequenceUtil.isNotEmpty(pnrNmTicket.getTicketStatus1()) ? pnrNmTicket.getTicketStatus1() : "OPEN FOR USE");
                } else if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS2Id()) && pnrNmTicket.getS2Id().equals(pnrSeg.getPnrSegId())) {
                    segment.setTicketStatus(CharSequenceUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()) ? pnrNmTicket.getTicketStatus2() : "OPEN FOR USE");
                } else {
                    segment.setTicketStatus("OPEN FOR USE");
                }

                segment.setFlightNo(pnrSeg.getFlightNo());
                segment.setCabinCode(pnrSeg.getSellCabin());
                segment.setDepartureDate(pnrSeg.getFlightDate());
                segment.setDepartureTime(pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2) + ":00");
                segment.setSegmentType("2");

                segments.add(segment);
            }
        }

        return segments;
    }

    /**
     * 设置价格信息（用于PreviewRefundTicket）
     */
    private void setPriceInfo(PreviewRefundTicketVo.Ticket ticket, String pnrId, String pnrNmId,
                              boolean isInfant, int segmentCount) {
        // 查询FN信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;
        BigDecimal commission;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnrId)
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算代理费：每个航段5元
        commission = BigDecimal.valueOf(segmentCount * 5L);

        // 设置税费信息
        List<PreviewRefundTicketVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax cnTaxInfo = new PreviewRefundTicketVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax yqTaxInfo = new PreviewRefundTicketVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        BigDecimal totalTaxes = cnTax.add(yqTax);

        ticket.setTotalAmount(totalAmount.toString());
        ticket.setTotalTaxs(totalTaxes.toString());
        ticket.setTaxs(taxes);
        ticket.setCommission(commission.toString());
    }

    /**
     * 构建退票计算信息
     */
    private PreviewRefundTicketVo.RefundCompute buildRefundCompute(MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr, MnjxPnrNm pnrNm, boolean isInfant) {

        PreviewRefundTicketVo.RefundCompute refundCompute = new PreviewRefundTicketVo.RefundCompute();
        refundCompute.setRemark(null);
        refundCompute.setConjunction("1.00");
        refundCompute.setCreditCard("");

        // 构建金额信息
        PreviewRefundTicketVo.Amount amount = new PreviewRefundTicketVo.Amount();

        // 查询价格信息
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算航段数量
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, pnrNmTicket.getTicketNo())
                .one();
        String[] segInfoSplit = ticketPrice.getSegInfo().split("/");
        long segmentCount = Arrays.stream(segInfoSplit).filter(s -> !"SA".equals(s.split(" ")[0])).count();

        // 计算代理费：每个航段5元
        BigDecimal commission = BigDecimal.valueOf(segmentCount * 5);

        // 计算退票手续费：票面价的10%
        BigDecimal otherDeduction = totalAmount.multiply(BigDecimal.valueOf(0.1))
                .setScale(2, RoundingMode.HALF_UP);

        // 计算总税费
        BigDecimal totalTaxes = cnTax.add(yqTax);

        // 计算应退费用：票面价+总税费-代理费-退票手续费
        BigDecimal netRefund = totalAmount.add(totalTaxes).subtract(commission).subtract(otherDeduction);

        // 设置税费列表
        List<PreviewRefundTicketVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax cnTaxInfo = new PreviewRefundTicketVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            PreviewRefundTicketVo.Tax yqTaxInfo = new PreviewRefundTicketVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        amount.setCommision(NumberUtils.formatBigDecimalStr(commission.toString()));
        amount.setTotalAmount(totalAmount.toString());
        amount.setCommisionRate("0.00");
        amount.setNetRefund(netRefund.toString());
        amount.setOtherDeduction(otherDeduction.toString());
        amount.setTaxs(taxes);
        amount.setTotalTaxs(totalTaxes.toString());
        amount.setTicketNo(null);

        refundCompute.setAmount(amount);

        return refundCompute;
    }

    @Override
    public QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 1. 通过pnrNo查询PNR信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            throw new SguiResultException("PNR不存在");
        }

        QueryPnrMessageVo result = new QueryPnrMessageVo();

        // 2. 查询PNR中所有旅客信息，包括婴儿，构建passengers数据
        List<QueryPnrMessageVo.Passenger> passengers = this.buildPassengers(pnr, dto.getPnrNo());
        result.setPassengers(passengers);

        // 5. 查询PNR中所有的航班构建flights
        List<QueryPnrMessageVo.Flight> flights = this.buildFlights(pnr);
        result.setFlights(flights);

        // 设置其他信息
        result.setTicketTypeCode(null);
        result.setPnrNo(pnr.getPnrIcs());
        result.setCsrPnrNo(pnr.getPnrCrs());

        return result;
    }

    /**
     * 构建旅客信息
     */
    private List<QueryPnrMessageVo.Passenger> buildPassengers(MnjxPnr pnr, String pnrNo) {
        List<QueryPnrMessageVo.Passenger> passengers = new ArrayList<>();

        // 查询PNR中所有旅客信息
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        for (MnjxPnrNm pnrNm : pnrNmList) {
            QueryPnrMessageVo.Passenger passenger = new QueryPnrMessageVo.Passenger();

            // 设置基本信息
            passenger.setPassengerName(pnrNm.getName());
            passenger.setPassengerNameSuffix(pnrNm.getName());
            String psgType = this.convertPsgType(pnrNm);
            passenger.setPassengerType(psgType);
            passenger.setPnrPsgType(psgType);
            passenger.setSpecialPassengerType(psgType);
            passenger.setIndex("P" + pnrNm.getPsgIndex());

            // 3. secondFactorCode设置CN，secondFactorValue设置dto的PNRNo
            QueryPnrMessageVo.SecondFactor secondFactor = new QueryPnrMessageVo.SecondFactor();
            secondFactor.setSecondFactorCode("CN");
            secondFactor.setSecondFactorValue(pnrNo);
            passenger.setSecondFactor(secondFactor);

            // 4. 通过旅客查询tn，获取票号
            List<String> ticketNos = this.getTicketNosByPnrNm(pnrNm.getPnrNmId());
            passenger.setTicketNos(ticketNos);

            // 查询婴儿信息
            QueryPnrMessageVo.Passenger infant = this.buildInfant(pnrNm.getPnrNmId(), pnrNo, "P" + pnrNm.getPsgIndex());
            passenger.setInfants(infant);

            passengers.add(passenger);
            if (ObjectUtil.isNotEmpty(infant)) {
                passengers.add(infant);
            }
        }

        return passengers;
    }

    /**
     * 构建婴儿信息
     */
    private QueryPnrMessageVo.Passenger buildInfant(String pnrNmId, String pnrNo, String parentIndex) {
        // 查询该旅客的婴儿信息
        List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                .eq(MnjxNmXn::getPnrNmId, pnrNmId)
                .list();

        if (CollUtil.isEmpty(nmXnList)) {
            return null;
        }

        // 只取第一个婴儿（通常一个成人只携带一个婴儿）
        MnjxNmXn nmXn = nmXnList.get(0);
        QueryPnrMessageVo.Passenger infant = new QueryPnrMessageVo.Passenger();

        infant.setPassengerName(nmXn.getXnCname());
        infant.setPassengerNameSuffix(nmXn.getXnCname() + " INF(OCT24)");
        infant.setPassengerType("INF");
        infant.setPnrPsgType("INF");
        infant.setSpecialPassengerType("INF");
        infant.setIndex(parentIndex); // 婴儿的index显示成人的序号
        infant.setInfants(null); // 婴儿不会再有婴儿

        // 设置二次验证信息
        QueryPnrMessageVo.SecondFactor secondFactor = new QueryPnrMessageVo.SecondFactor();
        secondFactor.setSecondFactorCode("CN");
        secondFactor.setSecondFactorValue(pnrNo);
        infant.setSecondFactor(secondFactor);

        // 查询婴儿的票号
        List<String> infantTicketNos = this.getTicketNosByNmXn(nmXn.getNmXnId());
        infant.setTicketNos(infantTicketNos);

        return infant;
    }

    /**
     * 通过pnrNmId获取票号列表
     */
    private List<String> getTicketNosByPnrNm(String pnrNmId) {
        List<String> ticketNos = new ArrayList<>();

        // 查询该旅客的tn记录
        List<MnjxPnrNmTn> tnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getPnrNmId, pnrNmId)
                .isNull(MnjxPnrNmTn::getNmXnId) // 排除婴儿票
                .list();

        for (MnjxPnrNmTn tn : tnList) {
            // 查询该tn的票号
            List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getPnrNmTnId, tn.getTnId())
                    .list();

            for (MnjxPnrNmTicket ticket : tickets) {
                // 格式化票号
                String ticketNo = ticket.getTicketNo();
                if (CharSequenceUtil.isNotEmpty(ticketNo) && ticketNo.length() >= 10) {
                    String prefix = ticketNo.substring(0, 3);
                    String suffix = ticketNo.substring(3);
                    ticketNos.add(prefix + "-" + suffix);
                } else {
                    ticketNos.add(ticketNo);
                }
            }
        }

        return ticketNos;
    }

    /**
     * 通过nmXnId获取婴儿票号列表
     */
    private List<String> getTicketNosByNmXn(String nmXnId) {
        List<String> ticketNos = new ArrayList<>();

        // 查询婴儿的tn记录
        List<MnjxPnrNmTn> tnList = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getNmXnId, nmXnId)
                .list();

        for (MnjxPnrNmTn tn : tnList) {
            // 查询该tn的票号
            List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getPnrNmTnId, tn.getTnId())
                    .list();

            for (MnjxPnrNmTicket ticket : tickets) {
                // 格式化票号
                String ticketNo = ticket.getTicketNo();
                if (CharSequenceUtil.isNotEmpty(ticketNo) && ticketNo.length() >= 10) {
                    String prefix = ticketNo.substring(0, 3);
                    String suffix = ticketNo.substring(3);
                    ticketNos.add(prefix + "-" + suffix);
                } else {
                    ticketNos.add(ticketNo);
                }
            }
        }

        return ticketNos;
    }

    /**
     * 构建航班信息
     */
    private List<QueryPnrMessageVo.Flight> buildFlights(MnjxPnr pnr) {
        List<QueryPnrMessageVo.Flight> flights = new ArrayList<>();

        // 查询PNR中所有航段信息
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        for (MnjxPnrSeg pnrSeg : pnrSegList) {
            QueryPnrMessageVo.Flight flight = new QueryPnrMessageVo.Flight();
            flight.setLineIndex(pnrSeg.getPnrIndex().toString());
            flight.setDepartureCode(pnrSeg.getOrg());
            flight.setArrivalCode(pnrSeg.getDst());

            if ("SA".equals(pnrSeg.getPnrSegType())) {
                flight.setFlightNo("0");
                flight.setTicketNos(Collections.emptyList());
                flight.setCodeShare(false);
                flight.setAirline("");
                flight.setCabin("");
                flight.setDepartureTerminal("");
                flight.setArrivalTerminal("");
                flight.setActionCode("");
                flights.add(flight);
                continue;
            }

            // 设置基本航班信息
            flight.setFlightNo(pnrSeg.getFlightNo());
            flight.setAirline(pnrSeg.getFlightNo().substring(0, 2));
            flight.setCabin(pnrSeg.getSellCabin());
            flight.setOperationAirline(null);
            flight.setActionCode(pnrSeg.getActionCode());
            flight.setTktNum(null);
            flight.setCodeShare(false);
            flight.setArrDays(null);

            // 设置时间信息
            if (CharSequenceUtil.isNotEmpty(pnrSeg.getFlightDate()) && CharSequenceUtil.isNotEmpty(pnrSeg.getEstimateOff())) {
                String departureTime = pnrSeg.getFlightDate() + " " +
                        pnrSeg.getEstimateOff().substring(0, 2) + ":" + pnrSeg.getEstimateOff().substring(2);
                flight.setDepartureTime(departureTime);
            }

            if (CharSequenceUtil.isNotEmpty(pnrSeg.getFlightDate()) && CharSequenceUtil.isNotEmpty(pnrSeg.getEstimateArr())) {
                String arrivalTime = pnrSeg.getFlightDate() + " " +
                        pnrSeg.getEstimateArr().substring(0, 2) + ":" + pnrSeg.getEstimateArr().substring(2);
                flight.setArrivalTime(arrivalTime);
            }

            // 设置航站楼信息
            flight.setDepartureTerminal("T2");
            flight.setArrivalTerminal("T2");

            // 6. 通过tnId和航班的segId查询ticket，设置flights中每个航班对应的票号ticketNos
            List<QueryPnrMessageVo.TicketNoInfo> ticketNoInfos = this.getTicketNosBySegment(pnrSeg.getPnrSegId());
            flight.setTicketNos(ticketNoInfos);

            flights.add(flight);
        }

        return flights;
    }

    /**
     * 通过航段ID获取票号信息
     */
    private List<QueryPnrMessageVo.TicketNoInfo> getTicketNosBySegment(String segmentId) {
        List<QueryPnrMessageVo.TicketNoInfo> ticketNoInfos = new ArrayList<>();

        // 查询包含该航段的票务信息
        List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                .and(wrapper -> wrapper.eq(MnjxPnrNmTicket::getS1Id, segmentId)
                        .or()
                        .eq(MnjxPnrNmTicket::getS2Id, segmentId))
                .list();

        int number = 1;
        for (MnjxPnrNmTicket ticket : tickets) {
            QueryPnrMessageVo.TicketNoInfo ticketNoInfo = new QueryPnrMessageVo.TicketNoInfo();
            ticketNoInfo.setNumber(String.valueOf(number++));
            ticketNoInfo.setTicketNo(ticket.getTicketNo()); // 不格式化，保持原格式
            ticketNoInfos.add(ticketNoInfo);
        }

        return ticketNoInfos;
    }

    @Override
    public BatchFindRefundFeeVo batchFindRefundFee(BatchFindRefundFeeDto dto) throws SguiResultException {
        // 参数验证
        if (CollUtil.isEmpty(dto.getTktNos())) {
            throw new SguiResultException("票号列表不能为空");
        }

        BatchFindRefundFeeVo result = new BatchFindRefundFeeVo();
        List<BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO> respList = new ArrayList<>();
        List<String> successTicketNos = new ArrayList<>();
        List<String> failedTicketNos = new ArrayList<>();

        // 处理每个票号
        for (String tktNo : dto.getTktNos()) {
            // 处理票号格式，去掉"-"
            String formattedTicketNo = tktNo.replace("-", "");

            // 1. 通过票号查询ticket，获取tnId
            MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                    .one();

            if (pnrNmTicket == null) {
                failedTicketNos.add(this.formatTicketNo(tktNo));
                continue;
            }

            if (CharSequenceUtil.equalsAny(Constant.TICKET_STATUS_REFOUND, pnrNmTicket.getTicketStatus1(), pnrNmTicket.getTicketStatus2())) {
                throw new SguiResultException("原票航班状态无效，仅支持EXCHANGED、OPEN FOR USE、FLOWN、VOID，请检查或手工处理。");
            }

            // 2. 通过tnId查询tn，获取pnrNmId或nmXnId
            MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
            if (pnrNmTn == null) {
                failedTicketNos.add(this.formatTicketNo(tktNo));
                continue;
            }

            // 判断是否为婴儿票
            boolean isInfant = CharSequenceUtil.isNotEmpty(pnrNmTn.getNmXnId());
            String pnrNmId = isInfant ?
                    iMnjxNmXnService.getById(pnrNmTn.getNmXnId()).getPnrNmId() :
                    pnrNmTn.getPnrNmId();

            // 查询旅客信息
            MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm == null) {
                failedTicketNos.add(this.formatTicketNo(tktNo));
                continue;
            }

            // 查询PNR信息
            MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
            if (pnr == null) {
                failedTicketNos.add(this.formatTicketNo(tktNo));
                continue;
            }

            // 4. 构建退票费用数据
            BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO respDTO = this.buildRefundFeeRespDTO(pnrNmTicket, pnr, pnrNm, isInfant, tktNo);
            respList.add(respDTO);

            successTicketNos.add(this.formatTicketNo(tktNo));
        }

        result.setQueryRefundFeeAggregateRespDTOList(respList);
        result.setStatus("SUCCESS");
        result.setMsg(null);
        result.setSuccessTicketNos(successTicketNos);
        result.setFailedTicketNos(failedTicketNos);

        return result;
    }

    /**
     * 格式化票号
     */
    private String formatTicketNo(String ticketNo) {
        if (CharSequenceUtil.isEmpty(ticketNo)) {
            return ticketNo;
        }

        // 如果已经包含"-"，直接返回
        if (ticketNo.contains("-")) {
            return ticketNo;
        }

        // 如果长度足够，添加"-"
        if (ticketNo.length() >= 10) {
            String prefix = ticketNo.substring(0, 3);
            String suffix = ticketNo.substring(3);
            return prefix + "-" + suffix;
        }

        return ticketNo;
    }

    /**
     * 构建退票费用响应DTO
     */
    private BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO buildRefundFeeRespDTO(MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr, MnjxPnrNm pnrNm, boolean isInfant, String originalTicketNo) {

        BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO respDTO = new BatchFindRefundFeeVo.QueryRefundFeeAggregateRespDTO();

        // 设置基本信息
        respDTO.setConjunction("1.00");
        respDTO.setRemark(null);
        respDTO.setCreditCard("");
        respDTO.setPayType("CASH");
        respDTO.setCurrency("");
        respDTO.setSegList(new ArrayList<>());
        respDTO.setMsg(null);

        // 5. amount参考退前预览计算退票中各价格的处理方式
        BatchFindRefundFeeVo.Amount amount = this.buildRefundAmount(pnrNmTicket, pnr, pnrNm, isInfant, originalTicketNo);
        respDTO.setAmount(amount);

        return respDTO;
    }

    /**
     * 构建退票金额信息
     */
    private BatchFindRefundFeeVo.Amount buildRefundAmount(MnjxPnrNmTicket pnrNmTicket, MnjxPnr pnr, MnjxPnrNm pnrNm, boolean isInfant, String originalTicketNo) {

        BatchFindRefundFeeVo.Amount amount = new BatchFindRefundFeeVo.Amount();

        // 3. 查询旅客运价，如果没有则通过pnrId查询pnr级别的运价
        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;

        if (nmFn != null) {
            totalAmount = nmFn.getFPrice() != null ? nmFn.getFPrice() : BigDecimal.ZERO;
            cnTax = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqTax = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                totalAmount = pnrFn.getFPrice() != null ? pnrFn.getFPrice() : BigDecimal.ZERO;
                cnTax = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqTax = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算航段数量
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, pnrNmTicket.getTicketNo())
                .one();
        String[] segInfoSplit = ticketPrice.getSegInfo().split("/");
        long segmentCount = Arrays.stream(segInfoSplit).filter(s -> !"SA".equals(s.split(" ")[0])).count();

        // 计算代理费：每个航段5元
        BigDecimal commission = BigDecimal.valueOf(segmentCount * 5);

        // 计算退票手续费：票面价的10%
        BigDecimal otherDeduction = totalAmount.multiply(BigDecimal.valueOf(0.1))
                .setScale(2, RoundingMode.HALF_UP);

        // 计算总税费
        BigDecimal totalTaxes = cnTax.add(yqTax);

        // 计算应退费用：票面价+总税费-代理费-退票手续费
        BigDecimal netRefund = totalAmount.add(totalTaxes).subtract(commission).subtract(otherDeduction);

        // 设置税费列表
        List<BatchFindRefundFeeVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            BatchFindRefundFeeVo.Tax cnTaxInfo = new BatchFindRefundFeeVo.Tax();
            cnTaxInfo.setName("CN");
            cnTaxInfo.setValue(cnTax.toString());
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            BatchFindRefundFeeVo.Tax yqTaxInfo = new BatchFindRefundFeeVo.Tax();
            yqTaxInfo.setName("YQ");
            yqTaxInfo.setValue(yqTax.toString());
            taxes.add(yqTaxInfo);
        }

        // 设置金额信息
        amount.setCommision(NumberUtils.formatBigDecimalStr(commission.toString()));
        amount.setTotalAmount(totalAmount.toString());
        amount.setCommisionRate("0.00");
        amount.setNetRefund(netRefund.toString());
        amount.setOtherDeduction(otherDeduction.toString());
        amount.setTaxs(taxes);
        amount.setTotalTaxs(totalTaxes.toString());
        amount.setTicketNo(this.formatTicketNo(originalTicketNo));

        return amount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchAutoRefundVo batchAutoRefund(BatchAutoRefundDto dto) throws SguiResultException {
        // 参数验证
        if (CollUtil.isEmpty(dto.getTicketList())) {
            throw new SguiResultException("票号列表不能为空");
        }

        BatchAutoRefundVo result = new BatchAutoRefundVo();
        List<BatchAutoRefundVo.RefundResult> refundResults = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        // 处理每个票号
        for (BatchAutoRefundDto.TicketInfo ticketInfo : dto.getTicketList()) {
            BatchAutoRefundVo.RefundResult refundResult = this.processTicketRefund(ticketInfo);
            // 为空时表示PNR的状态不是DEL，需要返回提示先取消PNR
            if (ObjectUtil.isEmpty(refundResult)) {
                result.setNeedSeatVacated(true);
                result.setRefundResult(null);
                result.setAllSuccess(false);
                result.setAllFail(false);
                result.setRefundOrder(null);
                return result;
            }
            refundResults.add(refundResult);

            if (refundResult != null && Boolean.TRUE.equals(refundResult.getSuccess())) {
                successCount++;
            } else {
                failCount++;
            }
        }

        // 设置返回结果
        result.setNeedSeatVacated(failCount > 0);
        result.setRefundResult(refundResults);
        result.setAllSuccess(failCount == 0);
        result.setAllFail(successCount == 0);
        result.setRefundOrder(null);

        return result;
    }

    /**
     * 处理单个票号的退票
     */
    private BatchAutoRefundVo.RefundResult processTicketRefund(BatchAutoRefundDto.TicketInfo ticketInfo) throws SguiResultException {

        // 处理票号格式，去掉"-"
        String formattedTicketNo = ticketInfo.getTicketNo().replace("-", "");

        // 1. 通过ticketNo查询mnjx_pnr_nm_ticket表，获取tnId
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("未查到该票号");
        }
        if (CharSequenceUtil.equalsAny(Constant.TICKET_STATUS_REFOUND, pnrNmTicket.getTicketStatus1(), pnrNmTicket.getTicketStatus2())) {
            throw new SguiResultException("该票已退票");
        }

        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());

        if (pnrNmTn == null) {
            throw new SguiResultException("未查到该票号");
        }

        MnjxPrinter printer = iMnjxPrinterService.getById(pnrNmTn.getPrinterId());

        if (printer == null) {
            throw new SguiResultException("未查到打票机信息");
        }
        if (!printer.getPrinterNo().equals(ticketInfo.getPrintNo())) {
            throw new SguiResultException("打票机错误");
        }

        // 5. 通过查询到的tn数据，查询pnrNm或nmXn（再查到pnrNm），查询pnr数据
        String pnrNmId = this.getPnrNmId(pnrNmTn);
        if (CharSequenceUtil.isEmpty(pnrNmId)) {
            throw new SguiResultException("未查到旅客信息");
        }

        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("未查到旅客信息");
        }

        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("未查到PNR信息");
        }

        // 6. 如果pnr的status不是DEL，那么按照PNR状态不是DEL的response.json的数据返回
        if (!"DEL".equals(pnr.getPnrStatus())) {
            return null;
        }

        // 7. 如果pnr的status是DEL，执行退票操作
        // 该TN下的联票都要退，退联票时只产生一个退票单号
        List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                .orderByAsc(MnjxPnrNmTicket::getTicketNo)
                .list();
        return this.executeRefund(ticketInfo, nmTicketList, pnrNmTn, pnr, pnrNm);
    }

    /**
     * 获取pnrNmId
     */
    private String getPnrNmId(MnjxPnrNmTn pnrNmTn) {
        if (CharSequenceUtil.isNotEmpty(pnrNmTn.getNmXnId())) {
            // 婴儿票，通过nmXnId查询pnrNmId
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            return nmXn != null ? nmXn.getPnrNmId() : null;
        } else {
            // 成人/儿童票，直接使用pnrNmId
            return pnrNmTn.getPnrNmId();
        }
    }

    /**
     * 执行退票操作
     */
    private BatchAutoRefundVo.RefundResult executeRefund(BatchAutoRefundDto.TicketInfo ticketInfo, List<MnjxPnrNmTicket> pnrNmTicketList,
                                                         MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm) throws SguiResultException {

        BatchAutoRefundVo.RefundResult refundResult = new BatchAutoRefundVo.RefundResult();

        // 构建退票单号
        String refundNo = this.generateRefundNo(ticketInfo.getTicketNo());
        // 1. 将对应的pnrNmTicket的ticketStatus1和ticketStatus2都设置为REFUNDED
        // 先单独计算航段数量
        int segmentCount = 0;
        for (MnjxPnrNmTicket pnrNmTicket : pnrNmTicketList) {
            segmentCount += CharSequenceUtil.isAllNotEmpty(pnrNmTicket.getS1Id(), pnrNmTicket.getS2Id()) ? 2 : 1;
        }
        for (int i = 0; i < pnrNmTicketList.size(); i++) {
            MnjxPnrNmTicket ticket = pnrNmTicketList.get(i);
            // 2. 更新票面状态
            this.updateTicketStatus(ticket, Collections.emptyList());
            // 3. 创建退票单数据存储
            this.saveRefundTicket(refundResult, refundNo, ticket.getTicketNo(), pnrNmTn, pnr, pnrNm, i == 0, segmentCount);
            // 4. 新增客票操作记录
            MnjxTicketOperateRecord operateRecord = this.structureOperateRecord(ticket, refundNo);
            iMnjxTicketOperateRecordService.save(operateRecord);

            String tnId = ticket.getPnrNmTnId();
            MnjxPnrNmTn nmTn = iMnjxPnrNmTnService.getById(tnId);
            String pnrNmId = nmTn.getPnrNmId();
            // 只退婴儿票不需要还原座位数和删除值机数据
            if (CharSequenceUtil.isNotEmpty(pnrNmId)) {
                // 恢复订座时占用的座位数
                this.updateAvailableSeat(ticket);

                // 删除出票生成的值机数据mnjx_psg_cki mnjx_psg_cki_seat
                this.deletePsgCki(ticket, pnrNmId);
            }
        }

        // 5. 退票成功后，构建refundResult
        refundResult.setTicketNo(this.formatTicketNo(ticketInfo.getTicketNo()));
        refundResult.setSuccess(true);
        refundResult.setTrfdno(refundNo);
        refundResult.setPrintNo(ticketInfo.getPrintNo());
        refundResult.setErrorCode(null);
        refundResult.setDescription(null);
        refundResult.setTransactionNo(null);
        refundResult.setSatTransactionNo(null);

        return refundResult;
    }

    /**
     * 更新票状态为REFUNDED
     */
    private void updateTicketStatus(MnjxPnrNmTicket pnrNmTicket, List<String> couponNos) throws SguiResultException {
        if (CollUtil.isNotEmpty(couponNos) && !CharSequenceUtil.equalsAny(couponNos.get(0), "1000", "1200", "0200")) {
            // couponNos不为空时只需要验证第一条数据的格式，每次只会退一张票
            throw new SguiResultException("REFUND FAILED, BECAUSE:CHECK COUPON(退票失败)");
        }
        boolean needUpdate = false;

        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getTicketStatus1())) {
            if (CollUtil.isNotEmpty(couponNos)) {
                String coupon = couponNos.get(0);
                if (coupon.charAt(0) != '0') {
                    pnrNmTicket.setTicketStatus1(Constant.TICKET_STATUS_REFOUND);
                    needUpdate = true;
                }
            } else {
                pnrNmTicket.setTicketStatus1(Constant.TICKET_STATUS_REFOUND);
                needUpdate = true;
            }
        }

        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getTicketStatus2())) {
            if (CollUtil.isNotEmpty(couponNos)) {
                String coupon = couponNos.get(0);
                if (coupon.charAt(1) != '0') {
                    pnrNmTicket.setTicketStatus2(Constant.TICKET_STATUS_REFOUND);
                    needUpdate = true;
                }
            } else {
                pnrNmTicket.setTicketStatus2(Constant.TICKET_STATUS_REFOUND);
                needUpdate = true;
            }
        }

        if (needUpdate) {
            iMnjxPnrNmTicketService.updateById(pnrNmTicket);
        }
    }

    /**
     * 生成退票单号
     */
    private String generateRefundNo(String ticketNo) {
        // 前三位为航司结算码（和当前票号前三位一样）
        String settlementCode = ticketNo.substring(0, 3);

        // 查询表中是否已有退票单数据
        List<MnjxRefundTicket> refundTicketList = iMnjxRefundTicketService.list();
        if (CollUtil.isEmpty(refundTicketList)) {
            return settlementCode + "000000000";
        }

        Optional<Long> optional = refundTicketList.stream()
                .map(r -> Long.parseLong(r.getRefundNo().substring(3)))
                .max(Comparator.naturalOrder());
        if (optional.isPresent()) {
            long maxNumber = optional.get();
            // 格式化为9位数字
            return settlementCode + String.format("%09d", maxNumber + 1);
        } else {
            return settlementCode + "000000000";
        }
    }

    /**
     * 保存退票单数据
     */
    private void saveRefundTicket(BatchAutoRefundVo.RefundResult refundResult, String refundNo, String ticketNo, MnjxPnrNmTn pnrNmTn, MnjxPnr pnr, MnjxPnrNm pnrNm, boolean isFirstTicket, int segmentCount) {
        // 非首张票只存储票号和退票单号和退票日期
        if (!isFirstTicket) {
            // 创建退票单记录
            MnjxRefundTicket refundTicket = new MnjxRefundTicket();
            refundTicket.setRefundId(IdUtil.getSnowflake(1, 1).nextIdStr());
            refundTicket.setRefundNo(refundNo);
            refundTicket.setTicketNo(ticketNo);
            refundTicket.setRefundDate(DateUtil.now());

            iMnjxRefundTicketService.save(refundTicket);
            return;
        }
        // 查询价格信息
        boolean isInfant = CharSequenceUtil.isNotEmpty(pnrNmTn.getNmXnId());

        MnjxNmFn nmFn = iMnjxNmFnService.lambdaQuery()
                .eq(MnjxNmFn::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmFn::getIsBaby, isInfant ? 1 : 0)
                .one();

        BigDecimal collection = BigDecimal.ZERO; // 票价（不含税）
        BigDecimal cnPrice = BigDecimal.ZERO;    // CN基建费价格
        BigDecimal yqPrice = BigDecimal.ZERO;    // 燃油附加费价格

        if (nmFn != null) {
            collection = nmFn.getSPrice() != null ? nmFn.getSPrice() : BigDecimal.ZERO;
            cnPrice = nmFn.getTCnPrice() != null ? nmFn.getTCnPrice() : BigDecimal.ZERO;
            yqPrice = nmFn.getTYqPrice() != null ? nmFn.getTYqPrice() : BigDecimal.ZERO;
        } else {
            // 查询PNR级别的FN信息
            MnjxPnrFn pnrFn = iMnjxPnrFnService.lambdaQuery()
                    .eq(MnjxPnrFn::getPnrId, pnr.getPnrId())
                    .eq(MnjxPnrFn::getIsBaby, isInfant ? 1 : 0)
                    .one();

            if (pnrFn != null) {
                collection = pnrFn.getSPrice() != null ? pnrFn.getSPrice() : BigDecimal.ZERO;
                cnPrice = pnrFn.getTCnPrice() != null ? pnrFn.getTCnPrice() : BigDecimal.ZERO;
                yqPrice = pnrFn.getTYqPrice() != null ? pnrFn.getTYqPrice() : BigDecimal.ZERO;
            }
        }

        // 计算退票手续费：票面价的10%
        BigDecimal comm = collection.multiply(BigDecimal.valueOf(0.1)).setScale(2, RoundingMode.HALF_UP);

        // 计算代理费：每个航段5元
        BigDecimal commission = BigDecimal.valueOf(segmentCount * 5L);

        // 计算实际退款金额：票价+税费-代理费-退票手续费
        BigDecimal netRefund = collection.add(cnPrice).add(yqPrice).subtract(commission).subtract(comm);

        // 创建退票单记录
        MnjxRefundTicket refundTicket = new MnjxRefundTicket();
        refundTicket.setRefundId(IdUtil.getSnowflake(1, 1).nextIdStr());
        refundTicket.setRefundNo(refundNo);
        refundTicket.setTicketNo(ticketNo);
        refundTicket.setCollection(collection);
        refundTicket.setCnPrice(cnPrice);
        refundTicket.setYqPrice(yqPrice);
        refundTicket.setComm(comm);
        refundTicket.setNetRefund(netRefund);
        refundTicket.setRefundDate(DateUtil.now());

        iMnjxRefundTicketService.save(refundTicket);

        BatchAutoRefundVo.Amount amount = new BatchAutoRefundVo.Amount();
        amount.setNetRefund(netRefund.toString());
        amount.setTicketNo(ticketNo);
        amount.setTotalAmount(collection.toString());
        amount.setCommision(NumberUtils.formatBigDecimalStr(commission.toString()));
        amount.setCommisionRate("0.00");
        amount.setTotalTaxs(cnPrice.add(yqPrice).toString());
        List<BatchAutoRefundVo.Tax> taxes = new ArrayList<>();
        if (cnPrice.compareTo(BigDecimal.ZERO) != 0) {
            BatchAutoRefundVo.Tax cn = new BatchAutoRefundVo.Tax();
            cn.setName("CN");
            cn.setValue(cnPrice.toString());
            taxes.add(cn);
        }
        if (yqPrice.compareTo(BigDecimal.ZERO) != 0) {
            BatchAutoRefundVo.Tax yq = new BatchAutoRefundVo.Tax();
            yq.setName("YQ");
            yq.setValue(yqPrice.toString());
            taxes.add(yq);
        }
        amount.setTaxs(taxes);
        amount.setOtherDeduction(comm.toString());
        refundResult.setAmount(amount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeletePnrAndDeleteInfantInfoVo deletePnrAndDeleteInfantInfo(DeletePnrAndDeleteInfantInfoDto dto) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(dto.getXePnr())) {
            throw new SguiResultException("取消PNR编号不能为空");
        }

        DeletePnrAndDeleteInfantInfoVo result = new DeletePnrAndDeleteInfantInfoVo();

        try {
            // 1. 将请求参数xePnr的pnr编码设置到新建XePnrDto
            XePnrDto xePnrDto = new XePnrDto();
            xePnrDto.setPnrNo(dto.getXePnr());

            // 2. 内部直接调用xePnr接口
            iXePnrService.xePnr(xePnrDto);

            // xePnr执行成功
            result.setXePnrExecutionStatus("S");

            // 3. 查询PNR是否有婴儿
            MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                    .eq(MnjxPnr::getPnrCrs, dto.getXePnr())
                    .one();
            List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                    .list();
            Integer inftCount = iMnjxNmXnService.lambdaQuery()
                    .in(MnjxNmXn::getPnrNmId, pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                    .count();
            if (inftCount > 0) {
                // 删除婴儿信息也设置为成功（根据需求，这个操作包含在xePnr中）
                result.setDeleteInfantExecutionStatus("S");
            } else {
                result.setDeleteInfantExecutionStatus("N");
            }
        } catch (Exception e) {
            // 异常情况下设置为失败
            result.setXePnrExecutionStatus("N");
            result.setDeleteInfantExecutionStatus("N");
        }

        return result;
    }

    @Override
    public String queryTicketManagementOrganization(String ticketNo) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(ticketNo)) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = ticketNo.replace("-", "");

        // 查询mnjx_pnr_nm_ticket表判断票号是否存在
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("票号不存在");
        }

        // 存在则返回BSP
        return "BSP";
    }

    @Override
    public QueryRefundFormVo queryRefundForm(QueryRefundFormDto dto) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(dto.getTicketNo()) && CharSequenceUtil.isEmpty(dto.getRefundNo())) {
            throw new SguiResultException("票号或退票单号不能为空");
        }
        List<MnjxRefundTicket> refundTicketList = new ArrayList<>();
        if (CharSequenceUtil.isEmpty(dto.getTicketNo())) {
            refundTicketList = iMnjxRefundTicketService.lambdaQuery()
                    .eq(MnjxRefundTicket::getRefundNo, dto.getRefundNo())
                    .orderByAsc(MnjxRefundTicket::getTicketNo)
                    .list();
            dto.setTicketNo(refundTicketList.get(0).getTicketNo());
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = dto.getTicketNo().replace("-", "");

        // 1. 根据ticketNo查询mnjx_pnr_nm_ticket获取票号信息和tnId
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("票号不存在");
        }

        // 2. 根据tnId查询tn数据
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("票号关联信息不存在");
        }

        MnjxPrinter printer = iMnjxPrinterService.getById(pnrNmTn.getPrinterId());
        if (ObjectUtil.isEmpty(printer) || !printer.getPrinterNo().equals(dto.getPrinterNo())) {
            throw new SguiResultException("打票机错误");
        }

        // 3. 根据ticketNo查询mnjx_refund_ticket获取退票相关信息
        if (CollUtil.isEmpty(refundTicketList)) {
            MnjxRefundTicket refundTicket = iMnjxRefundTicketService.lambdaQuery()
                    .eq(MnjxRefundTicket::getTicketNo, formattedTicketNo)
                    .one();
            if (refundTicket == null) {
                throw new SguiResultException("退票单不存在");
            }
            refundTicketList = iMnjxRefundTicketService.lambdaQuery()
                    .eq(MnjxRefundTicket::getRefundNo, refundTicket.getRefundNo())
                    .orderByAsc(MnjxRefundTicket::getTicketNo)
                    .list();
            if (CollUtil.isEmpty(refundTicketList)) {
                throw new SguiResultException("退票单不存在");
            }
        }


        // 4. 获取旅客信息
        String pnrNmId = this.getPnrNmId(pnrNmTn);
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("旅客信息不存在");
        }

        // 5. 获取PNR信息
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("PNR信息不存在");
        }

        // 6. 构建返回对象
        return this.buildQueryRefundFormVo(dto, pnrNmTn, refundTicketList, pnrNm);
    }

    /**
     * 构建查询退票单号响应对象
     */
    private QueryRefundFormVo buildQueryRefundFormVo(QueryRefundFormDto dto, MnjxPnrNmTn pnrNmTn, List<MnjxRefundTicket> refundTicketList, MnjxPnrNm pnrNm) {

        QueryRefundFormVo result = new QueryRefundFormVo();
        MnjxRefundTicket firstRefundTicket = refundTicketList.get(0);

        // 设置基本信息
        MnjxSi issuedSi = iMnjxSiService.getById(pnrNmTn.getIssuedSiId());
        MnjxOffice issuedOffice = iMnjxOfficeService.getById(issuedSi.getOfficeId());
        MnjxAgent issuedAgent = iMnjxAgentService.getById(issuedOffice.getOrgId());
        result.setOffice(issuedOffice.getOfficeNo());
        result.setIataNo(issuedAgent.getAgentIata());
        result.setAgent(issuedSi.getSiNo());
        result.setOperator(issuedSi.getSiNo());
        result.setAirlineCode(firstRefundTicket.getTicketNo().substring(0, 3));
        result.setTicketNoEnd(null);
        result.setTicketNoSecond(null);
        result.setTicketNo(firstRefundTicket.getTicketNo());
        if (refundTicketList.size() > 1) {
            result.setTicketNoView(firstRefundTicket.getTicketNo().substring(3) + "-" + refundTicketList.get(refundTicketList.size() - 1).getTicketNo().substring(6));
        } else {
            result.setTicketNoView(firstRefundTicket.getTicketNo().substring(3) + "-" + firstRefundTicket.getTicketNo().substring(6));
        }
        result.setCheck("");

        List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                .in(MnjxPnrNmTicket::getTicketNo, refundTicketList.stream().map(MnjxRefundTicket::getTicketNo).collect(Collectors.toList()))
                .orderByAsc(MnjxPnrNmTicket::getTicketNo)
                .list();
        // 设置couponNo
        List<String> couponNo = this.buildCouponNo(nmTicketList);
        result.setCouponNo(couponNo);
        result.setConjunction(refundTicketList.size() > 1 ? (int) (Long.parseLong(refundTicketList.get(refundTicketList.size() - 1).getTicketNo()) - Long.parseLong(firstRefundTicket.getTicketNo())) + 1 : 1);
        result.setPayMethod("CASH");
        result.setCurrency("CNY");
        result.setRefundFormCurrency("CNY-2");
        result.setCommissionRate(BigDecimal.ZERO);
        result.setCreditCard("");
        result.setRemark(null);
        result.setQuerySuccess(true);
        result.setCmdOption("M");
        result.setTicketType(dto.getTicketType());
        result.setTicketManagementOrganizationCode(dto.getTicketManagementOrganizationCode());
        result.setSegmentInfos(null);
        result.setDeviceNum(dto.getPrinterNo());
        result.setRefundDate(DateUtils.ymd2Com(firstRefundTicket.getRefundDate().split(" ")[0]) + "/" + firstRefundTicket.getRefundDate().split(" ")[1].substring(0, 5));
        result.setInternational("D");

        // 设置旅客信息
        boolean isInfant = CharSequenceUtil.isNotEmpty(pnrNmTn.getNmXnId());
        if (isInfant) {
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            if (nmXn != null) {
                result.setPassengerName(nmXn.getXnCname());
                result.setPassengerType("");
            }
        } else {
            result.setPassengerName(pnrNm.getName());
            result.setPassengerType("");
        }

        // 设置退票单号
        result.setCmdNo(firstRefundTicket.getRefundNo().substring(3));

        // 设置金额信息
        long s1Count = nmTicketList.stream().filter(n -> CharSequenceUtil.isNotEmpty(n.getS1Id())).count();
        long s2Count = nmTicketList.stream().filter(n -> CharSequenceUtil.isNotEmpty(n.getS2Id())).count();
        long segSize = s1Count + s2Count;
        result.setGrossRefund(firstRefundTicket.getCollection());
        result.setDeduction(firstRefundTicket.getComm());
        result.setRefund("Y");
        result.setCommission(new BigDecimal(segSize).multiply(BigDecimal.valueOf(5)));
        result.setNetRefund(firstRefundTicket.getNetRefund());

        // 设置税费信息
        List<QueryRefundFormVo.TaxInfo> taxInfos = new ArrayList<>();
        BigDecimal totalTaxs = BigDecimal.ZERO;

        if (firstRefundTicket.getCnPrice() != null && firstRefundTicket.getCnPrice().compareTo(BigDecimal.ZERO) > 0) {
            QueryRefundFormVo.TaxInfo cnTax = new QueryRefundFormVo.TaxInfo();
            cnTax.setTaxCode("CN");
            cnTax.setTaxAmount(firstRefundTicket.getCnPrice());
            taxInfos.add(cnTax);
            totalTaxs = totalTaxs.add(firstRefundTicket.getCnPrice());
        }

        if (firstRefundTicket.getYqPrice() != null && firstRefundTicket.getYqPrice().compareTo(BigDecimal.ZERO) > 0) {
            QueryRefundFormVo.TaxInfo yqTax = new QueryRefundFormVo.TaxInfo();
            yqTax.setTaxCode("YQ");
            yqTax.setTaxAmount(firstRefundTicket.getYqPrice());
            taxInfos.add(yqTax);
            totalTaxs = totalTaxs.add(firstRefundTicket.getYqPrice());
        }

        result.setTaxInfos(taxInfos);
        result.setTotalTaxs(totalTaxs);

        // 设置二次验证因素
        if (dto.getSecondFactor() != null) {
            QueryRefundFormVo.SecondFactor secondFactor = new QueryRefundFormVo.SecondFactor();
            secondFactor.setSecondFactorCode(dto.getSecondFactor().getSecondFactorCode());
            secondFactor.setSecondFactorValue(dto.getSecondFactor().getSecondFactorValue());
            result.setSecondFactor(secondFactor);
        }

        result.setOriginalTickets(Collections.emptyList());

        return result;
    }


    /**
     * 构建couponNo
     */
    private List<String> buildCouponNo(List<MnjxPnrNmTicket> nmTicketList) {
        // 初始化4条数据，每条都是"0000"
        // 规则：一张票最多两个航段，每一条couponNo对应一张票，因此一张票的构成为 1000 1200 0200 这三种
        List<String> couponNo = new ArrayList<>();
        couponNo.add("0000");
        couponNo.add("0000");
        couponNo.add("0000");
        couponNo.add("0000");

        // 判断当前查询的退票单是不是联票退票单
        boolean singleRefundTicket = nmTicketList.size() == 1;

        for (int i = 0; i < nmTicketList.size(); i++) {
            MnjxPnrNmTicket pnrNmTicket = nmTicketList.get(i);
            // 查询s1Id对应的航段号
            if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
                MnjxPnrSeg pnrSeg1 = iMnjxPnrSegService.getById(pnrNmTicket.getS1Id());
                if (pnrSeg1 != null && pnrSeg1.getPnrSegNo() != null && Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus1())) {
                    int segNo1 = pnrSeg1.getPnrSegNo();
                    if (singleRefundTicket) {
                        String currentCoupon = couponNo.get(0);
                        StringBuilder sb = new StringBuilder(currentCoupon);
                        sb.setCharAt(0, '1');
                        couponNo.set(0, sb.toString());
                    } else {
                        String currentCoupon = couponNo.get((segNo1 - 1) / 2);
                        StringBuilder sb = new StringBuilder(currentCoupon);
                        sb.setCharAt(0, '1');
                        couponNo.set((segNo1 - 1) / 2, sb.toString());
                    }
                }
            }

            // 查询s2Id对应的航段号
            if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
                MnjxPnrSeg pnrSeg2 = iMnjxPnrSegService.getById(pnrNmTicket.getS2Id());
                if (pnrSeg2 != null && pnrSeg2.getPnrSegNo() != null && Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus2())) {
                    int segNo2 = pnrSeg2.getPnrSegNo();
                    if (singleRefundTicket) {
                        String currentCoupon = couponNo.get(0);
                        StringBuilder sb = new StringBuilder(currentCoupon);
                        sb.setCharAt(1, '2');
                        couponNo.set(0, sb.toString());
                    } else {
                        String currentCoupon = couponNo.get((segNo2 - 1) / 2);
                        StringBuilder sb = new StringBuilder(currentCoupon);
                        sb.setCharAt(1, '2');
                        couponNo.set((segNo2 - 1) / 2, sb.toString());
                    }
                }
            }
        }

        return couponNo;
    }

    /**
     * 构建客票退票操作记录
     */
    private MnjxTicketOperateRecord structureOperateRecord(MnjxPnrNmTicket pnrNmTicket, String refundNo) throws SguiResultException {
        MnjxTicketOperateRecord operateRecord = new MnjxTicketOperateRecord();
        operateRecord.setTicketOperateRecordId(IdUtil.getSnowflake(1, 1).nextIdStr());
        operateRecord.setOperateTime(DateUtil.now());
        operateRecord.setSettlementCode(CharSequenceUtil.subPre(pnrNmTicket.getTicketNo(), 3));
        UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
        operateRecord.setSiNo(userInfo.getSiNo());
        operateRecord.setTicketStatus1(pnrNmTicket.getTicketStatus1());
        operateRecord.setTicketStatus2(pnrNmTicket.getTicketStatus2());
        operateRecord.setTicketNo(pnrNmTicket.getTicketNo());
        operateRecord.setRefundNo(refundNo);
        return operateRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchManualRefundTicketVo batchManualRefundTicket(BatchManualRefundTicketDto dto) throws SguiResultException {
        // 参数验证
        if (dto == null || CollUtil.isEmpty(dto.getRefundList())) {
            throw new SguiResultException("退票列表不能为空");
        }

        BatchManualRefundTicketVo result = new BatchManualRefundTicketVo();
        List<BatchManualRefundTicketVo.PassengerStatus> passengerStatuses = new ArrayList<>();
        boolean allSuccess = true;

        // 处理每个退票请求
        if (dto.getRefundList().size() == 1) {
            BatchManualRefundTicketVo.PassengerStatus passengerStatus = this.processManualRefund(dto.getRefundList().get(0));
            passengerStatuses.add(passengerStatus);
        } else {
            // 记录票失败信息，如果所有票都是以相同的报错失败，则抛出业务异常，如果都报错但报错信息不同，抛出其中一种业务异常。如果部分报错，则按部分退票成功处理。
            Map<String, Integer> errorMap = new HashMap<>();
            for (BatchManualRefundTicketDto.RefundInfo refundInfo : dto.getRefundList()) {
                try {
                    BatchManualRefundTicketVo.PassengerStatus passengerStatus = this.processManualRefund(refundInfo);
                    passengerStatuses.add(passengerStatus);
                    if (Boolean.FALSE.equals(passengerStatus.getSuccess())) {
                        allSuccess = false;
                    }
                } catch (SguiResultException e) {
                    // 业务异常处理
                    log.error("批量退票发生业务异常:{}", e.getMessage());
                    if (errorMap.containsKey(e.getMessage())) {
                        errorMap.put(e.getMessage(), errorMap.get(e.getMessage()) + 1);
                    } else {
                        errorMap.put(e.getMessage(), 1);
                    }
                    BatchManualRefundTicketVo.PassengerStatus passengerStatus = new BatchManualRefundTicketVo.PassengerStatus();
                    passengerStatus.setTicketNo(refundInfo.getResultpre().getTicket().getTicketNo());
                    passengerStatus.setPassengerName("");
                    passengerStatus.setSuccess(false);
                    passengerStatus.setTrfdno(null);
                    passengerStatuses.add(passengerStatus);
                    allSuccess = false;
                } catch (Exception e) {
                    // 其他异常处理
                    log.error("发生未知异常:{}", e.getMessage());
                    BatchManualRefundTicketVo.PassengerStatus passengerStatus = new BatchManualRefundTicketVo.PassengerStatus();
                    passengerStatus.setTicketNo(refundInfo.getResultpre().getTicket().getTicketNo());
                    passengerStatus.setPassengerName("");
                    passengerStatus.setSuccess(false);
                    passengerStatus.setTrfdno(null);
                    passengerStatuses.add(passengerStatus);
                    allSuccess = false;
                }
            }
            // 全部抛出的相同错误
            Optional<Map.Entry<String, Integer>> findThrowError = errorMap.entrySet().stream()
                    .filter(entry -> entry.getValue() == dto.getRefundList().size())
                    .findFirst();
            if (findThrowError.isPresent()) {
                throw new SguiResultException(findThrowError.get().getKey());
            }
            // 全部报错但错误不同
            int errorSum = errorMap.values().stream()
                    .mapToInt(i -> i)
                    .sum();
            if (errorSum == dto.getRefundList().size()) {
                throw new SguiResultException(errorMap.keySet().iterator().next());
            }
        }

        result.setPassengerStatuses(passengerStatuses);
        result.setStatus(allSuccess ? "ALL_SUCCESS" : "PARTIAL_SUCCESS");
        result.setMsg(null);

        return result;
    }

    /**
     * 处理单个手工退票
     */
    private BatchManualRefundTicketVo.PassengerStatus processManualRefund(BatchManualRefundTicketDto.RefundInfo refundInfo) throws SguiResultException {
        BatchManualRefundTicketDto.Ticket ticket = refundInfo.getResultpre().getTicket();
        String ticketNo = ticket.getTicketNo();

        // 票号只允许单张票
        if (ticketNo.matches("\\d{3}-\\d{10}-\\d+")) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:TICKET NUMBER(退票失败)");
        }
        // 1. 数据获取
        // 处理票号格式，去掉"-"
        String formattedTicketNo = ticketNo.replace("-", "");

        // 2. 数据验证
        this.validateManualRefundData(refundInfo);

        // 根据请求ticketNo查询mnjx_pnr_nm_ticket表，获取票信息，获取tnId
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:REFUND & TKT NBR NOT MATCH(退票失败)");
        }

        // 检查该票的状态是否可以退票
        if (Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus1()) || Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus2())) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:REFUND & TKT NBR NOT MATCH(退票失败)");
        }

        // 根据tnId查询mnjx_pnr_nm_tn表，获取tn信息
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:REFUND & TKT NBR NOT MATCH(退票失败)");
        }

        // 验证打票机序号
        MnjxPrinter printer = iMnjxPrinterService.getById(pnrNmTn.getPrinterId());
        if (printer == null || !printer.getPrinterNo().equals(refundInfo.getPrntNo())) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:DEVICE ID(退票失败)");
        }

        // 验证航司结算码
        MnjxAirline airline = iMnjxAirlineService.lambdaQuery()
                .eq(MnjxAirline::getAirlineSettlementCode, ticket.getAirline())
                .one();
        if (airline == null) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:TICKET NUMBER - AIRLINE CODE(退票失败)");
        }

        // 根据tn信息获取旅客信息（注意区分查询的是婴儿还是非婴儿）
        String pnrNmId = this.getPnrNmId(pnrNmTn);
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:旅客信息不存在(退票失败)");
        }

        // 如果modificationType为ONLY_REFUND时只需要做退票操作，不取消PNR，不需要验证PNR状态是不是DEL
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (!"ONLY_REFUND".equals(refundInfo.getModificationType()) && !"DEL".equals(pnr.getPnrStatus())) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:PNR状态不是DEL(退票失败)");
        }

        // 3. 执行退票操作
        return this.executeManualRefund(refundInfo, pnrNmTicket);
    }

    /**
     * 验证手工退票数据
     */
    private void validateManualRefundData(BatchManualRefundTicketDto.RefundInfo refundInfo) throws SguiResultException {
        BatchManualRefundTicketDto.ResultPre resultpre = refundInfo.getResultpre();
        BatchManualRefundTicketDto.Amount amount = resultpre.getAmount();
        BatchManualRefundTicketDto.Ticket ticket = resultpre.getTicket();

        if (Integer.parseInt(resultpre.getConjunction()) != 1) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:TICKET NUMBER(退票失败)");
        }

        // 验证amount数据
        if (amount != null && CollUtil.isNotEmpty(amount.getTaxs())) {
            // 如果taxs不为空：name只能为CN或YQ，且每一种最多只有一条，且如果存在则value必须大于0
            Set<String> taxNames = new HashSet<>();
            for (BatchManualRefundTicketDto.Tax tax : amount.getTaxs()) {
                if (!"CN".equals(tax.getName()) && !"YQ".equals(tax.getName())) {
                    throw new SguiResultException("REFUND FAILED, BECAUSE:ONLY 'CN' AND 'YQ' TAX(退票失败)");
                }
                if (taxNames.contains(tax.getName())) {
                    throw new SguiResultException("REFUND FAILED, BECAUSE:每种税费最多只能有一条");
                }
                taxNames.add(tax.getName());

                try {
                    BigDecimal taxValue = new BigDecimal(tax.getValue());
                    if (taxValue.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new SguiResultException("REFUND FAILED, BECAUSE:CHECK TAX AMOUNT(退票失败)");
                    }
                } catch (NumberFormatException e) {
                    throw new SguiResultException("REFUND FAILED, BECAUSE:税费金额格式错误");
                }
            }
        }

        // 不支持信用卡
        if (CharSequenceUtil.isNotEmpty(resultpre.getCreditCard())) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:不支持信用卡(退票失败)");
        }

        // 验证ticket数据
        if (ticket != null) {
            if (CharSequenceUtil.isEmpty(ticket.getCurrency())) {
                throw new SguiResultException("REFUND FAILED, BECAUSE:CURRENCY CODE不能为空");
            }
            if (!"CNY".equals(ticket.getCurrency())) {
                throw new SguiResultException("REFUND FAILED, BECAUSE:CHECK CURRENCY CODE(退票失败)");
            }
            if (CharSequenceUtil.isEmpty(ticket.getPayType())) {
                throw new SguiResultException("REFUND FAILED, BECAUSE:PAYMENT不能为空");
            }
            if (!"CASH".equals(ticket.getPayType())) {
                throw new SguiResultException("REFUND FAILED, BECAUSE:CHECK FORM OF PAYMENT(退票失败)");
            }
        }

    }

    /**
     * 执行手工退票操作
     */
    private BatchManualRefundTicketVo.PassengerStatus executeManualRefund(BatchManualRefundTicketDto.RefundInfo refundInfo, MnjxPnrNmTicket pnrNmTicket) throws SguiResultException {

        BatchManualRefundTicketVo.PassengerStatus passengerStatus = new BatchManualRefundTicketVo.PassengerStatus();

        // 1. 将对应的pnrNmTicket的ticketStatus1和ticketStatus2都设置为REFUNDED
        this.updateTicketStatus(pnrNmTicket, refundInfo.getResultpre().getTicket().getCouponNos());

        // 2. 创建退票单号
        String refundNo = this.generateRefundNo(refundInfo.getResultpre().getTicket().getTicketNo());

        // 3. 退票单数据存储（使用传入参数的数据）
        this.saveManualRefundTicket(refundInfo, refundNo);

        // 4. 新增客票操作记录
        MnjxTicketOperateRecord operateRecord = this.structureOperateRecord(pnrNmTicket, refundNo);
        iMnjxTicketOperateRecordService.save(operateRecord);

        String tnId = pnrNmTicket.getPnrNmTnId();
        MnjxPnrNmTn nmTn = iMnjxPnrNmTnService.getById(tnId);
        String pnrNmId = nmTn.getPnrNmId();
        // 只退婴儿票不需要还原座位数和删除值机数据
        if (CharSequenceUtil.isNotEmpty(pnrNmId)) {
            // 恢复订座时占用的座位数
            // 勾选仅退票不恢复占用座位数
            if (!"ONLY_REFUND".equals(refundInfo.getModificationType())) {
                this.updateAvailableSeat(pnrNmTicket);
            }

            // 删除出票生成的值机数据mnjx_psg_cki mnjx_psg_cki_seat
            this.deletePsgCki(pnrNmTicket, pnrNmId);
        }

        // 5. 退票成功后，构建passengerStatus
        passengerStatus.setTicketNo(refundInfo.getResultpre().getTicket().getTicketNo());
        passengerStatus.setSuccess(true);
        passengerStatus.setTrfdno(refundNo);

        return passengerStatus;
    }

    /**
     * 保存手工退票单数据（使用传入参数的数据）
     */
    private void saveManualRefundTicket(BatchManualRefundTicketDto.RefundInfo refundInfo, String refundNo) {

        BatchManualRefundTicketDto.Amount amount = refundInfo.getResultpre().getAmount();
        String ticketNo = refundInfo.getResultpre().getTicket().getTicketNo();

        // 使用传入参数的数据，而不是查询数据库中的运价数据
        BigDecimal collection = CharSequenceUtil.isNotEmpty(amount.getTotalAmount()) ? new BigDecimal(amount.getTotalAmount()) : BigDecimal.ZERO; // 票价（不含税）
        BigDecimal netRefund = new BigDecimal(amount.getNetRefund());    // 实际退款金额

        // 退票手续费otherDeduction可能为0
        BigDecimal otherDeduction = CharSequenceUtil.isNotEmpty(amount.getOtherDeduction()) ?
                new BigDecimal(amount.getOtherDeduction()) : BigDecimal.ZERO;

        // 处理税费
        BigDecimal cnPrice = BigDecimal.ZERO;    // CN基建费价格
        BigDecimal yqPrice = BigDecimal.ZERO;    // 燃油附加费价格

        if (CollUtil.isNotEmpty(amount.getTaxs())) {
            for (BatchManualRefundTicketDto.Tax tax : amount.getTaxs()) {
                if ("CN".equals(tax.getName())) {
                    cnPrice = new BigDecimal(tax.getValue());
                } else if ("YQ".equals(tax.getName())) {
                    yqPrice = new BigDecimal(tax.getValue());
                }
            }
        }

        // 创建退票单记录
        MnjxRefundTicket refundTicket = new MnjxRefundTicket();
        refundTicket.setRefundId(IdUtil.getSnowflake(1, 1).nextIdStr());
        refundTicket.setRefundNo(refundNo);
        refundTicket.setTicketNo(ticketNo.replace("-", ""));
        refundTicket.setCollection(collection);
        refundTicket.setCnPrice(cnPrice);
        refundTicket.setYqPrice(yqPrice);
        refundTicket.setComm(otherDeduction); // 退票手续费
        refundTicket.setNetRefund(netRefund);
        refundTicket.setRefundDate(DateUtil.now());

        iMnjxRefundTicketService.save(refundTicket);
    }

    @Override
    public QueryRtktDetailVo queryRtktDetail(QueryRtktDetailDto dto) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(dto.getTicketNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = dto.getTicketNo().replace("-", "");

        // 1. 根据请求参数ticketNo查询mnjx_pnr_nm_ticket，获取pnrNmTnId
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("票号不存在");
        }

        // 2. 根据pnrNmTnId查询mnjx_pnr_nm_tn，获取pnrNmId或xnNmId
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("票号关联信息不存在");
        }

        // 3. 根据pnrNmId或xnNmId查询mnjx_pnr_nm，获取pnrId
        String pnrNmId = this.getPnrNmId(pnrNmTn);
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("旅客信息不存在");
        }

        // 4. 查询fn信息
        boolean isInfant = CharSequenceUtil.isNotEmpty(pnrNmTn.getNmXnId());
        MnjxNmFn nmFn = null;
        MnjxPnrFn pnrFn = null;

        // 先查询nmFn，如果tn中是xnNmId，需要添加isBaby=1条件
        if (isInfant) {
            nmFn = iMnjxNmFnService.lambdaQuery()
                    .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                    .eq(MnjxNmFn::getIsBaby, 1)
                    .one();
        } else {
            nmFn = iMnjxNmFnService.lambdaQuery()
                    .eq(MnjxNmFn::getPnrNmId, pnrNmId)
                    .eq(MnjxNmFn::getIsBaby, 0)
                    .one();
        }

        // 如果nmFn为空，查询pnrFn，如果tn中是xnNmId，需要添加isBaby=1条件
        if (nmFn == null) {
            if (isInfant) {
                pnrFn = iMnjxPnrFnService.lambdaQuery()
                        .eq(MnjxPnrFn::getPnrId, pnrNm.getPnrId())
                        .eq(MnjxPnrFn::getIsBaby, 1)
                        .one();
            } else {
                pnrFn = iMnjxPnrFnService.lambdaQuery()
                        .eq(MnjxPnrFn::getPnrId, pnrNm.getPnrId())
                        .eq(MnjxPnrFn::getIsBaby, 0)
                        .one();
            }
        }

        // 6. 根据查询的fn信息构建返回数据
        return this.buildQueryRtktDetailVo(nmFn, pnrFn, pnrNmTicket);
    }

    /**
     * 构建获取税项响应数据
     */
    private QueryRtktDetailVo buildQueryRtktDetailVo(MnjxNmFn nmFn, MnjxPnrFn pnrFn, MnjxPnrNmTicket pnrNmTicket) {
        QueryRtktDetailVo result = new QueryRtktDetailVo();

        // 优先使用nmFn，如果为空则使用pnrFn
        if (nmFn != null) {
            result.setCommissionRate("0.00");
            result.setTicketAmount(nmFn.getFPrice() != null ? nmFn.getFPrice().toString() : "0.00");
            result.setCurrency(CharSequenceUtil.isNotEmpty(nmFn.getFCurrency()) ? nmFn.getFCurrency() : "CNY");

            // 构建税费明细
            List<QueryRtktDetailVo.RtKTTax> rtKTTaxes = new ArrayList<>();
            BigDecimal totalTaxes = BigDecimal.ZERO;

            // CN税费
            if (nmFn.getTCnPrice() != null && nmFn.getTCnPrice().compareTo(BigDecimal.ZERO) > 0) {
                QueryRtktDetailVo.RtKTTax cnTax = new QueryRtktDetailVo.RtKTTax();
                cnTax.setTaxType("CN");
                cnTax.setTaxAmount(nmFn.getTCnPrice().toString());
                cnTax.setCurrency(CharSequenceUtil.isNotEmpty(nmFn.getTCnCurrency()) ? nmFn.getTCnCurrency() : "CNY");
                rtKTTaxes.add(cnTax);
                totalTaxes = totalTaxes.add(nmFn.getTCnPrice());
            }

            // YQ税费
            if (nmFn.getTYqPrice() != null && nmFn.getTYqPrice().compareTo(BigDecimal.ZERO) > 0) {
                QueryRtktDetailVo.RtKTTax yqTax = new QueryRtktDetailVo.RtKTTax();
                yqTax.setTaxType("YQ");
                yqTax.setTaxAmount(nmFn.getTYqPrice().toString());
                yqTax.setCurrency(CharSequenceUtil.isNotEmpty(nmFn.getTYqCurrency()) ? nmFn.getTYqCurrency() : "CNY");
                rtKTTaxes.add(yqTax);
                totalTaxes = totalTaxes.add(nmFn.getTYqPrice());
            }

            result.setRtKTTaxes(rtKTTaxes);
            result.setTotalTaxes(totalTaxes.toString());

        } else if (pnrFn != null) {
            result.setCommissionRate("0.00");
            result.setTicketAmount(pnrFn.getFPrice() != null ? pnrFn.getFPrice().toString() : "0.00");
            result.setCurrency(CharSequenceUtil.isNotEmpty(pnrFn.getFCurrency()) ? pnrFn.getFCurrency() : "CNY");

            // 构建税费明细
            List<QueryRtktDetailVo.RtKTTax> rtKTTaxes = new ArrayList<>();
            BigDecimal totalTaxes = BigDecimal.ZERO;

            // CN税费
            if (pnrFn.getTCnPrice() != null && pnrFn.getTCnPrice().compareTo(BigDecimal.ZERO) > 0) {
                QueryRtktDetailVo.RtKTTax cnTax = new QueryRtktDetailVo.RtKTTax();
                cnTax.setTaxType("CN");
                cnTax.setTaxAmount(pnrFn.getTCnPrice().toString());
                cnTax.setCurrency(CharSequenceUtil.isNotEmpty(pnrFn.getTCnCurrency()) ? pnrFn.getTCnCurrency() : "CNY");
                rtKTTaxes.add(cnTax);
                totalTaxes = totalTaxes.add(pnrFn.getTCnPrice());
            }

            // YQ税费
            if (pnrFn.getTYqPrice() != null && pnrFn.getTYqPrice().compareTo(BigDecimal.ZERO) > 0) {
                QueryRtktDetailVo.RtKTTax yqTax = new QueryRtktDetailVo.RtKTTax();
                yqTax.setTaxType("YQ");
                yqTax.setTaxAmount(pnrFn.getTYqPrice().toString());
                yqTax.setCurrency(CharSequenceUtil.isNotEmpty(pnrFn.getTYqCurrency()) ? pnrFn.getTYqCurrency() : "CNY");
                rtKTTaxes.add(yqTax);
                totalTaxes = totalTaxes.add(pnrFn.getTYqPrice());
            }

            result.setRtKTTaxes(rtKTTaxes);
            result.setTotalTaxes(totalTaxes.toString());

        } else {
            // 如果都为空，返回默认值
            result.setCommissionRate("0.00");
            result.setTicketAmount("0.00");
            result.setCurrency("CNY");
            result.setRtKTTaxes(new ArrayList<>());
            result.setTotalTaxes("0.00");
        }

        // 7. 返回的commission值为票中存在的航段数量 * 5
        int segmentCount = this.getSegmentCount(pnrNmTicket);
        result.setCommission(String.valueOf(segmentCount * 5));

        return result;
    }

    /**
     * 获取航段数量
     */
    private int getSegmentCount(MnjxPnrNmTicket pnrNmTicket) {
        int count = 0;
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS1Id())) {
            count++;
        }
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            count++;
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteRefundForm(DeleteRefundFormDto dto) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(dto.getRefundNo())) {
            throw new SguiResultException("退票单号不能为空");
        }

        // 1. 根据请求refundNo查询mnjx_refund_ticket表，获取退票单信息
        List<MnjxRefundTicket> refundTicketList = iMnjxRefundTicketService.list();

        if (CollUtil.isEmpty(refundTicketList)) {
            throw new SguiResultException("退票单不存在");
        }

        refundTicketList = refundTicketList.stream()
                .filter(r -> r.getRefundNo().substring(3).equals(dto.getRefundNo()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(refundTicketList)) {
            throw new SguiResultException("退票单不存在");
        }

        // 2. 根据退票单信息中的ticketNo查询mnjx_pnr_nm_ticket表，获取票信息
        // 3. 如果请求参数resetTicketStatus为true，更新ticket表的状态为OPEN FOR USE
        // 4. 同时恢复退票时删除的值机数据
        if (Boolean.TRUE.equals(dto.getResetTicketStatus())) {
            MnjxPnr pnr = null;
            for (MnjxRefundTicket refundTicket : refundTicketList) {
                String ticketNo = refundTicket.getTicketNo();

                MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                        .one();

                if (pnrNmTicket == null) {
                    throw new SguiResultException("票号 " + ticketNo + " 不存在");
                }

                pnr = this.resetTicketStatus(pnrNmTicket);
            }

            if (pnr == null) {
                throw new SguiResultException("未找到PNR");
            }

            // 如果pnr的status为DEL，并且该PNR所有票都是OPEN FOR USE状态了，PNR状态更新还原为OP
            if (Constant.PNR_DEL.equals(pnr.getPnrStatus())) {
                List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                        .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                        .list();
                List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                        .in(MnjxNmXn::getPnrNmId, pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                        .list();
                List<MnjxPnrNmTn> nmTnList = iMnjxPnrNmTnService.lambdaQuery()
                        .in(MnjxPnrNmTn::getPnrNmId, pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList()))
                        .list();
                if (CollUtil.isNotEmpty(nmXnList)) {
                    nmTnList.addAll(iMnjxPnrNmTnService.lambdaQuery()
                            .in(MnjxPnrNmTn::getNmXnId, nmXnList.stream().map(MnjxNmXn::getNmXnId).collect(Collectors.toList()))
                            .list());
                }
                List<MnjxPnrNmTicket> allNmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .in(MnjxPnrNmTicket::getPnrNmTnId, nmTnList.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList()))
                        .list();
                if (allNmTicketList.stream().noneMatch(t -> CharSequenceUtil.equalsAny(Constant.TICKET_STATUS_REFOUND, t.getTicketStatus1(), t.getTicketStatus2()))) {
                    pnr.setPnrStatus(Constant.PNR_OP);
                    iMnjxPnrService.updateById(pnr);

                    // 取消PNR时修改的航段、SSR行动代码XX改回出票后的RR
                    List<MnjxPnrRecord> recordsToUpdate = iMnjxPnrRecordService.lambdaQuery()
                            .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                            .and(wrapper -> wrapper.isNull(MnjxPnrRecord::getChangeMark)
                                    .or()
                                    .eq(MnjxPnrRecord::getChangeMark, ""))
                            .list();

                    if (CollUtil.isNotEmpty(recordsToUpdate)) {
                        for (MnjxPnrRecord pnrRecord : recordsToUpdate) {
                            if (CharSequenceUtil.equalsAny(pnrRecord.getPnrType(), "SEG", "SSR")) {
                                String inputValue = pnrRecord.getInputValue();
                                inputValue = inputValue.replace(" XX", " RR");
                                pnrRecord.setInputValue(inputValue);
                            }
                        }
                        iMnjxPnrRecordService.updateBatchById(recordsToUpdate);
                    }
                }
            }
        }

        // 4. 删除退票单数据
        iMnjxRefundTicketService.removeByIds(refundTicketList.stream().map(MnjxRefundTicket::getRefundId).collect(Collectors.toList()));

        return "SUCCESS";
    }

    /**
     * 重置票状态
     */
    private MnjxPnr resetTicketStatus(MnjxPnrNmTicket pnrNmTicket) throws SguiResultException {
        // 更新ticket表的状态为OPEN FOR USE
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getTicketStatus1()) && Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus1())) {
            pnrNmTicket.setTicketStatus1(Constant.TICKET_STATUS_OPEN_FOR_USE);
        }
        if (CharSequenceUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()) && Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus2())) {
            pnrNmTicket.setTicketStatus2(Constant.TICKET_STATUS_OPEN_FOR_USE);
        }
        iMnjxPnrNmTicketService.updateById(pnrNmTicket);

        // 获取PNR信息
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("票号关联信息不存在");
        }

        String pnrNmId = this.getPnrNmId(pnrNmTn);
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("旅客信息不存在");
        }

        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("PNR信息不存在");
        }

        // 新增涉及的票号状态为OPEN FOR USE的mnjx_ticket_operate_record记录
        MnjxTicketOperateRecord operateRecord = this.structureOperateRecord(pnrNmTicket, null);
        iMnjxTicketOperateRecordService.save(operateRecord);

        // 如果不是删除的婴儿退票单，再次生成mnjx_psg_cki和mnjx_psg_seat数据
        if (CharSequenceUtil.isEmpty(pnrNmTn.getNmXnId())) {
            this.generatePsgCkiAndSeat(pnr, pnrNm, pnrNmTicket);
        }
        return pnr;
    }

    /**
     * 生成psgCki和psgSeat数据
     */
    private void generatePsgCkiAndSeat(MnjxPnr pnr, MnjxPnrNm passenger, MnjxPnrNmTicket pnrNmTicket) {
        // 获取航段信息
        List<MnjxPnrSeg> segments = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrSeg::getPnrSegNo)
                .list();

        for (MnjxPnrSeg segment : segments) {
            if (!"SA".equals(segment.getPnrSegType()) && CharSequenceUtil.equalsAny(segment.getPnrSegId(), pnrNmTicket.getS1Id(), pnrNmTicket.getS2Id())) {
                // 生成psgCki记录
                MnjxPsgCki psgCki = new MnjxPsgCki();
                psgCki.setPsgCkiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                psgCki.setPnrNmId(passenger.getPnrNmId());
                psgCki.setPnrSegNo(StrUtil.toString(segment.getPnrSegNo()));
                psgCki.setCabinClass(segment.getCabinClass());
                psgCki.setSellCabin(segment.getSellCabin());
                psgCki.setAbdStatusInfi("0");
                psgCki.setCkiStatus("NACC"); // 未值机

                iMnjxPsgCkiService.save(psgCki);

                // 生成psgSeat记录
                MnjxPsgSeat psgSeat = new MnjxPsgSeat();
                psgSeat.setPsgSeatId(IdUtil.getSnowflake(1, 1).nextIdStr());
                psgSeat.setPsgCkiId(psgCki.getPsgCkiId());

                iMnjxPsgSeatService.save(psgSeat);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ModifyRefundFormVo modifyRefundForm(ModifyRefundFormDto dto) throws SguiResultException {
        // 1. 参数验证
        this.validateModifyRefundFormData(dto);

        // 2. 根据请求refundNo和ticketNo查询mnjx_refund_ticket表，获取退票单信息
        String formattedTicketNo = dto.getTicketNo().replace("-", "");
        MnjxRefundTicket refundTicket = iMnjxRefundTicketService.lambdaQuery()
                .eq(MnjxRefundTicket::getRefundNo, formattedTicketNo.substring(0, 3) + dto.getRefundNo())
                .eq(MnjxRefundTicket::getTicketNo, formattedTicketNo)
                .one();

        if (refundTicket == null) {
            throw new SguiResultException("退票单不存在");
        }

        // 3. 根据请求参数refundFormPriceItem的数据对退票单字段值进行更新
        this.updateRefundTicketFields(refundTicket, dto.getRefundFormPriceItem());

        // 保存更新
        iMnjxRefundTicketService.updateById(refundTicket);

        // 构建返回结果
        ModifyRefundFormVo result = new ModifyRefundFormVo();
        result.setRefundFormNo(dto.getRefundNo());
        return result;
    }

    @Override
    public BatchFindRefundFeeZVo batchFindRefundFeeZ(BatchFindRefundFeeZDto dto) throws SguiResultException {
        // 参数验证
        if (CollUtil.isEmpty(dto.getTicketList())) {
            throw new SguiResultException("票号列表不能为空");
        }

        BatchFindRefundFeeZVo result = new BatchFindRefundFeeZVo();

        // 去重处理：如果输入的参数中有多条相同的ticketNo，只处理其中一条，其他忽略
        Set<String> processedTicketNos = new HashSet<>();
        List<String> extResponseIDList = new ArrayList<>();

        for (BatchFindRefundFeeZDto.TicketInfo ticketInfo : dto.getTicketList()) {
            String ticketNo = ticketInfo.getTicketNo();

            // 跳过重复的票号
            if (processedTicketNos.contains(ticketNo)) {
                continue;
            }
            processedTicketNos.add(ticketNo);

            BatchFindRefundFeeZVo.RefundFeeResult refundResult = this.processTicketRefundFeeZ(ticketInfo, extResponseIDList);
            result.add(refundResult);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchRefundVo batchRefund(BatchRefundDto dto) throws SguiResultException {
        // 参数验证
        if (CollUtil.isEmpty(dto.getTicketList())) {
            throw new SguiResultException("票号列表不能为空");
        }

        BatchRefundVo result = new BatchRefundVo();
        result.setNeedSeatVacated(false);
        result.setRefundOrder(null);

        List<BatchRefundVo.RefundResult> refundResults = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        // 去重处理：如果输入的参数中有多条相同的ticketNo，只处理其中一条，其他忽略
        Set<String> processedTicketNos = new HashSet<>();

        for (BatchRefundDto.TicketInfo ticketInfo : dto.getTicketList()) {
            String ticketNo = ticketInfo.getTicketNo();

            // 跳过重复的票号
            if (processedTicketNos.contains(ticketNo)) {
                continue;
            }
            processedTicketNos.add(ticketNo);

            BatchRefundVo.RefundResult refundResult = this.processTicketRefundBatch(ticketInfo);

            // 如果返回null，表示PNR状态不是DEL，需要先退座
            if (refundResult == null) {
                result.setNeedSeatVacated(true);
                result.setRefundResult(null);
                result.setAllSuccess(false);
                result.setAllFail(false);
                return result;
            }

            refundResults.add(refundResult);

            if (Boolean.TRUE.equals(refundResult.getSuccess())) {
                successCount++;
            } else {
                failCount++;
            }
        }

        result.setRefundResult(refundResults);
        result.setAllSuccess(failCount == 0 && successCount > 0);
        result.setAllFail(successCount == 0 && failCount > 0);

        return result;
    }

    /**
     * 处理单个票号的批量退票
     */
    private BatchRefundVo.RefundResult processTicketRefundBatch(BatchRefundDto.TicketInfo ticketInfo) {
        BatchRefundVo.RefundResult result = new BatchRefundVo.RefundResult();
        result.setTicketNo(ticketInfo.getTicketNo());
        result.setSuccess(false);
        result.setAmount(null);
        result.setTrfdno(null);
        result.setPrintNo(ticketInfo.getPrintNo());
        result.setErrorCode(null);
        result.setDescription(null);
        result.setTransactionNo(null);
        result.setSatTransactionNo(null);

        try {
            // 处理票号格式，去掉"-"
            String formattedTicketNo = ticketInfo.getTicketNo().replace("-", "");

            // 1. 通过ticketNo查询mnjx_pnr_nm_ticket表，获取tnId
            MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                    .one();

            if (pnrNmTicket == null) {
                result.setErrorCode("TRR-6015-77");
                result.setDescription("TICKET NOT FOUND");
                result.setSatTransactionNo("XXXSAT41182025062415350900147795");
                return result;
            }

            // 2. 检查票面状态
            if (CharSequenceUtil.equalsAny(Constant.TICKET_STATUS_REFOUND, pnrNmTicket.getTicketStatus1(), pnrNmTicket.getTicketStatus2())) {
                result.setErrorCode("TRR-2651-13");
                result.setDescription("Segment Status of Old Ticket is invalid.\r{原票航班状态无效}");
                result.setSatTransactionNo("XXXSAT41182025062415350900147795");
                return result;
            }

            // 3. 获取mnjx_pnr_nm_tn信息
            MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
            if (pnrNmTn == null) {
                result.setErrorCode("TRR-6015-79");
                result.setDescription("TN信息不存在");
                result.setSatTransactionNo("XXXSAT41182025062415350900147796");
                return result;
            }

            // 4. 根据tn中pnrNmId或nmXnId查询对应的旅客或婴儿
            String pnrNmId = this.getPnrNmId(pnrNmTn);
            MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            if (pnrNm == null) {
                result.setErrorCode("TRR-6015-80");
                result.setDescription("旅客信息不存在");
                result.setSatTransactionNo("XXXSAT41182025062415350900147797");
                return result;
            }

            // 5. 查询pnr信息
            MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
            if (pnr == null) {
                result.setErrorCode("TRR-6015-81");
                result.setDescription("PNR信息不存在");
                result.setSatTransactionNo("XXXSAT41182025062415350900147798");
                return result;
            }

            // 6. 如果pnr的status不是DEL，返回null表示需要先退座
            if (!"DEL".equals(pnr.getPnrStatus())) {
                return null;
            }

            // 7. 执行退票操作
            List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getPnrNmTnId, pnrNmTn.getTnId())
                    .orderByAsc(MnjxPnrNmTicket::getTicketNo)
                    .list();

            // 转换为BatchAutoRefundDto.TicketInfo以复用executeRefund方法
            BatchAutoRefundDto.TicketInfo autoRefundTicketInfo = new BatchAutoRefundDto.TicketInfo();
            autoRefundTicketInfo.setTicketNo(ticketInfo.getTicketNo());
            autoRefundTicketInfo.setDomestic(ticketInfo.getDomestic());
            autoRefundTicketInfo.setPrintNo(ticketInfo.getPrintNo());

            BatchAutoRefundVo.RefundResult autoRefundResult = this.executeRefund(autoRefundTicketInfo, nmTicketList, pnrNmTn, pnr, pnrNm);

            // 转换结果
            result.setSuccess(autoRefundResult.getSuccess());
            result.setTrfdno(autoRefundResult.getTrfdno());

        } catch (Exception e) {
            log.error("处理票号{}退票时发生异常", ticketInfo.getTicketNo(), e);
            result.setErrorCode("TRR-6015-99");
            result.setDescription("系统异常：" + e.getMessage());
            result.setSatTransactionNo("XXXSAT41182025062415350900147999");
        }

        return result;
    }

    @Override
    public String etrfChangeTicketStatus(EtrfChangeTicketStatusDto dto) throws SguiResultException {
        // 参数验证
        if (CharSequenceUtil.isEmpty(dto.getTicketNumber())) {
            throw new SguiResultException("票号不能为空");
        }
        if (CharSequenceUtil.isEmpty(dto.getCouponStatus())) {
            throw new SguiResultException("客票状态不能为空");
        }

        // 处理票号格式，去掉"-"
        String formattedTicketNo = dto.getTicketNumber().replace("-", "");

        // 根据couponStatus进行不同的处理
        // OPEN FOR USE 将OPEN FOR USE改为REFUNDED
        if ("OPEN FOR USE".equals(dto.getCouponStatus())) {
            this.handleOpenForUseStatus(formattedTicketNo, dto.getSegId());
        }
        // REFUNDED 将REFUNDED改为OPEN FOR USE
        else if ("REFUNDED".equals(dto.getCouponStatus())) {
            this.handleRefundedStatus(formattedTicketNo, dto.getSegId());
        } else {
            throw new SguiResultException("不支持的客票状态：" + dto.getCouponStatus());
        }

        return "OK";
    }

    /**
     * 处理OPEN FOR USE状态的验证
     * OPEN FOR USE -> REFUNDED
     */
    private void handleOpenForUseStatus(String formattedTicketNo, int segNo) throws SguiResultException {
        // 1. 通过ticketNumber查询mnjx_refund_ticket表，是否能查到退票信息，如果能查到，报错
        MnjxRefundTicket refundTicket = iMnjxRefundTicketService.lambdaQuery()
                .eq(MnjxRefundTicket::getTicketNo, formattedTicketNo)
                .one();

        if (refundTicket == null) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:TICKET NUMBER(退票失败)");
        }

        // 2. 通过ticketNumber查询mnjx_pnr_nm_ticket表，获取s1和s2的status
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:TICKET NUMBER(退票失败)");
        }

        // 对于不为空的status如果其中任意一个不是OPEN FOR USE，报错
        String status1 = pnrNmTicket.getTicketStatus1();
        String status2 = pnrNmTicket.getTicketStatus2();

        if (segNo == 1 && CharSequenceUtil.isNotEmpty(status1) && !"OPEN FOR USE".equals(status1)) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:客票/航段状态错误(退票失败)");
        }
        if (segNo == 2 && CharSequenceUtil.isNotEmpty(status2) && !"OPEN FOR USE".equals(status2)) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:客票/航段状态错误(退票失败)");
        }

        // OPEN FOR USE参数请求验证通过后，更新对应ticket表不为空的status为REFUNDED
        boolean needUpdate = false;
        if (CharSequenceUtil.isNotEmpty(status1) && segNo == 1) {
            pnrNmTicket.setTicketStatus1("REFUNDED");
            needUpdate = true;
        }
        if (CharSequenceUtil.isNotEmpty(status2) && segNo == 2) {
            pnrNmTicket.setTicketStatus2("REFUNDED");
            needUpdate = true;
        }

        if (needUpdate) {
            // 同时生成一条客票操作记录
            MnjxTicketOperateRecord operateRecord = this.structureOperateRecord(pnrNmTicket, null);
            iMnjxTicketOperateRecordService.save(operateRecord);
            iMnjxPnrNmTicketService.updateById(pnrNmTicket);
        }
    }

    /**
     * 处理REFUNDED状态的验证和更新
     */
    private void handleRefundedStatus(String formattedTicketNo, int segNo) throws SguiResultException {
        // 4. 通过ticketNumber查询mnjx_refund_ticket表，是否能查到退票信息，如果能查到，报错
        MnjxRefundTicket refundTicket = iMnjxRefundTicketService.lambdaQuery()
                .eq(MnjxRefundTicket::getTicketNo, formattedTicketNo)
                .one();

        if (refundTicket != null) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:TICKET NUMBER(退票失败)");
        }

        // 5. 通过ticketNumber查询mnjx_pnr_nm_ticket表，获取s1和s2的status
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:TICKET NUMBER(退票失败)");
        }

        // 对于不为空的status如果其中任意一个不是REFUNDED，报错
        String status1 = pnrNmTicket.getTicketStatus1();
        String status2 = pnrNmTicket.getTicketStatus2();

        if (segNo == 1 && CharSequenceUtil.isNotEmpty(status1) && !"REFUNDED".equals(status1)) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:客票/航段状态错误(退票失败)");
        }
        if (segNo == 2 && CharSequenceUtil.isNotEmpty(status2) && !"REFUNDED".equals(status2)) {
            throw new SguiResultException("REFUND FAILED, BECAUSE:客票/航段状态错误(退票失败)");
        }

        // 6. REFUNDED参数请求验证通过后，更新对应ticket表不为空的status为OPEN FOR USE
        boolean needUpdate = false;
        if (CharSequenceUtil.isNotEmpty(status1) && segNo == 1) {
            pnrNmTicket.setTicketStatus1("OPEN FOR USE");
            needUpdate = true;
        }
        if (CharSequenceUtil.isNotEmpty(status2) && segNo == 2) {
            pnrNmTicket.setTicketStatus2("OPEN FOR USE");
            needUpdate = true;
        }

        if (needUpdate) {
            // 同时生成一条客票操作记录
            MnjxTicketOperateRecord operateRecord = this.structureOperateRecord(pnrNmTicket, null);
            iMnjxTicketOperateRecordService.save(operateRecord);
            iMnjxPnrNmTicketService.updateById(pnrNmTicket);
        }
    }

    /**
     * 处理单个票号的退票费计算
     */
    private BatchFindRefundFeeZVo.RefundFeeResult processTicketRefundFeeZ(
            BatchFindRefundFeeZDto.TicketInfo ticketInfo, List<String> extResponseIDList) {

        BatchFindRefundFeeZVo.RefundFeeResult result = new BatchFindRefundFeeZVo.RefundFeeResult();
        result.setTicketNo(ticketInfo.getTicketNo());
        result.setFailure(false);
        result.setErrorCode(null);
        result.setDescription(null);
        result.setSatTransactionNo(null);
        result.setTransactionNo(null);

        try {
            // 处理票号格式，去掉"-"
            String formattedTicketNo = ticketInfo.getTicketNo().replace("-", "");

            // 1. 根据票号查询mnjx_pnr_nm_ticket表，获取票信息，验证票号是否存在
            MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getTicketNo, formattedTicketNo)
                    .one();

            if (pnrNmTicket == null) {
                result.setFailure(true);
                result.setErrorCode("TRR-6015-77");
                result.setDescription("TICKET NOT FOUND");
                result.setSatTransactionNo("XXXSAT41182025062415050900147665");
                extResponseIDList.add("travelsky.air.doc.QueryDomesticRefund：XXXSAT41182025062415050900147665");
                return result;
            }

            // 2. 如果查到的票面状态中status1或status2至少有一个是REFUNDED状态了，报错
            if (CharSequenceUtil.equalsAny(Constant.TICKET_STATUS_REFOUND, pnrNmTicket.getTicketStatus1(), pnrNmTicket.getTicketStatus2())) {
                result.setFailure(true);
                result.setErrorCode("TRR-6015-78");
                result.setDescription("Segment Status of Old Ticket is invalid.\r{原票航班状态无效}");
                result.setSatTransactionNo("XXXSAT41182025062415050900147666");
                extResponseIDList.add("travelsky.air.doc.QueryDomesticRefund：XXXSAT41182025062415050900147666");
                return result;
            }

            // 3. 获取mnjx_pnr_nm_tn信息
            MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
            if (pnrNmTn == null) {
                result.setFailure(true);
                result.setErrorCode("TRR-6015-79");
                result.setDescription("TN信息不存在");
                result.setSatTransactionNo("XXXSAT41182025062415050900147667");
                extResponseIDList.add("travelsky.air.doc.QueryDomesticRefund：XXXSAT41182025062415050900147667");
                return result;
            }

            // 4. 根据tn中pnrNmId或nmXnId查询对应的旅客或婴儿
            String pnrNmId = this.getPnrNmId(pnrNmTn);
            MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
            boolean isInfant = CharSequenceUtil.isNotEmpty(pnrNmTn.getNmXnId());

            if (pnrNm == null) {
                result.setFailure(true);
                result.setErrorCode("TRR-6015-80");
                result.setDescription("旅客信息不存在");
                result.setSatTransactionNo("XXXSAT41182025062415050900147668");
                extResponseIDList.add("travelsky.air.doc.QueryDomesticRefund：XXXSAT41182025062415050900147668");
                return result;
            }

            // 5. 查询pnr信息
            MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
            if (pnr == null) {
                result.setFailure(true);
                result.setErrorCode("TRR-6015-81");
                result.setDescription("PNR信息不存在");
                result.setSatTransactionNo("XXXSAT41182025062415050900147669");
                extResponseIDList.add("travelsky.air.doc.QueryDomesticRefund：XXXSAT41182025062415050900147669");
                return result;
            }

            // 6. 如果pnr中status不是DEL，报错
            if (!"DEL".equals(pnr.getPnrStatus())) {
                result.setFailure(true);
                result.setErrorCode("TRR-6015-82");
                result.setDescription("Please Release the Segment Booking Before Refund the Ticket.\r{请先退座，再执行退票指令}");
                result.setSatTransactionNo("XXXSAT41182025062415050900147670");
                extResponseIDList.add("travelsky.air.doc.QueryDomesticRefund：XXXSAT41182025062415050900147670");
                return result;
            }


            // 7. 计算退票费，逻辑和batchFindRefundFee接口中调用的buildRefundAmount方法一样
            BatchFindRefundFeeVo.Amount amount = this.buildRefundAmount(pnrNmTicket, pnr, pnrNm, isInfant, ticketInfo.getTicketNo());

            // 8. 转换为BatchFindRefundFeeZVo.RefundFeeDTO格式
            BatchFindRefundFeeZVo.RefundFeeDTO refundFeeDTO = new BatchFindRefundFeeZVo.RefundFeeDTO();
            refundFeeDTO.setCommissionAmount(amount.getCommision());
            refundFeeDTO.setCommissionRate(amount.getCommisionRate());
            refundFeeDTO.setOtherDeduction(amount.getOtherDeduction());
            refundFeeDTO.setNetRefund(amount.getNetRefund());
            refundFeeDTO.setCurrency("");

            result.setRefundFeeDTO(refundFeeDTO);

            // 添加成功的外部响应ID（这里只是示例，实际应该从真实的调用中获取）
            extResponseIDList.add("travelsky.air.doc.QueryDomesticRefund：XXXSAT41182025062415050900147665");

        } catch (Exception e) {
            log.error("处理票号{}退票费计算时发生异常", ticketInfo.getTicketNo(), e);
            result.setFailure(true);
            result.setErrorCode("TRR-6015-99");
            result.setDescription("系统异常：" + e.getMessage());
            result.setSatTransactionNo("XXXSAT41182025062415050900147999");
            extResponseIDList.add("travelsky.air.doc.QueryDomesticRefund：XXXSAT41182025062415050900147999");
        }

        return result;
    }

    /**
     * 验证修改退票单数据
     */
    private void validateModifyRefundFormData(ModifyRefundFormDto dto) throws SguiResultException {
        // 基本参数验证
        if (CharSequenceUtil.isEmpty(dto.getRefundNo())) {
            throw new SguiResultException("退票单号不能为空");
        }
        if (CharSequenceUtil.isEmpty(dto.getTicketNo())) {
            throw new SguiResultException("票号不能为空");
        }

        ModifyRefundFormDto.RefundFormPassengerItem passengerItem = dto.getRefundFormPassengerItem();
        ModifyRefundFormDto.RefundFormPriceItem priceItem = dto.getRefundFormPriceItem();

        if (passengerItem != null) {
            // 验证currency
            if (CharSequenceUtil.isEmpty(passengerItem.getCurrency())) {
                throw new SguiResultException("CURRENCY CODE不能为空");
            }
            if (!"CNY".equals(passengerItem.getCurrency())) {
                throw new SguiResultException("CHECK CURRENCY CODE");
            }

            // 验证payMethod
            if (CharSequenceUtil.isEmpty(passengerItem.getPayMethod())) {
                throw new SguiResultException("PAYMENT不能为空");
            }
            if (!"CASH".equals(passengerItem.getPayMethod())) {
                throw new SguiResultException("CHECK FORM OF PAYMENT");
            }

            // 验证creditCard（如果不为空，需要进一步验证）
            // 根据手工退票逻辑，这里可能需要特定的验证规则
            // 不支持信用卡
            if (CharSequenceUtil.isNotEmpty(passengerItem.getCreditCard())) {
                throw new SguiResultException("不支持信用卡");
            }
        }

        if (priceItem != null && CollUtil.isNotEmpty(priceItem.getTaxInfos())) {
            // 验证taxInfos：name只能为CN或YQ，且每一种最多只有一条，且如果存在则value必须大于0
            Set<String> taxCodes = new HashSet<>();
            for (ModifyRefundFormDto.TaxInfo taxInfo : priceItem.getTaxInfos()) {
                if (!"CN".equals(taxInfo.getTaxCode()) && !"YQ".equals(taxInfo.getTaxCode())) {
                    throw new SguiResultException("ONLY 'CN' AND 'YQ' TAX");
                }
                if (taxCodes.contains(taxInfo.getTaxCode())) {
                    throw new SguiResultException("每种税费最多只能有一条");
                }
                taxCodes.add(taxInfo.getTaxCode());

                if (taxInfo.getTaxAmount() == null || taxInfo.getTaxAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new SguiResultException("税费金额必须大于0");
                }
            }
        }
    }

    /**
     * 更新退票单字段
     */
    private void updateRefundTicketFields(MnjxRefundTicket refundTicket, ModifyRefundFormDto.RefundFormPriceItem priceItem) {
        if (priceItem == null) {
            return;
        }

        // 更新净退款
        if (CharSequenceUtil.isNotEmpty(priceItem.getNetRefund())) {
            refundTicket.setNetRefund(new BigDecimal(priceItem.getNetRefund()));
        }

        // 更新票面价
        if (CharSequenceUtil.isNotEmpty(priceItem.getGrossRefund())) {
            refundTicket.setCollection(new BigDecimal(priceItem.getGrossRefund()));
        }

        // 更新手续费
        if (CharSequenceUtil.isNotEmpty(priceItem.getDeduction())) {
            refundTicket.setComm(new BigDecimal(priceItem.getDeduction()));
        }

        // 更新税费信息
        this.updateTaxFields(refundTicket, priceItem.getTaxInfos());
    }

    /**
     * 更新税费字段
     */
    private void updateTaxFields(MnjxRefundTicket refundTicket, List<ModifyRefundFormDto.TaxInfo> taxInfos) {
        // 先将原有税费设置为null
        refundTicket.setCnPrice(null);
        refundTicket.setYqPrice(null);

        // 如果请求参数中有税费信息，则更新
        if (CollUtil.isNotEmpty(taxInfos)) {
            for (ModifyRefundFormDto.TaxInfo taxInfo : taxInfos) {
                if ("CN".equals(taxInfo.getTaxCode())) {
                    refundTicket.setCnPrice(taxInfo.getTaxAmount());
                } else if ("YQ".equals(taxInfo.getTaxCode())) {
                    refundTicket.setYqPrice(taxInfo.getTaxAmount());
                }
            }
        }
    }

    /**
     * Title: updateAvailableSeat
     * Description: 更新对应销售舱位剩余座位数
     */
    private void updateAvailableSeat(MnjxPnrNmTicket ticket) {
        List<String> pnrSegIdList = new ArrayList<>();
        if (Constant.TICKET_STATUS_REFOUND.equals(ticket.getTicketStatus1())) {
            pnrSegIdList.add(ticket.getS1Id());
        }
        if (Constant.TICKET_STATUS_REFOUND.equals(ticket.getTicketStatus2())) {
            pnrSegIdList.add(ticket.getS2Id());
        }

        // 恢复消耗的座位数
        List<MnjxPnrSeg> pnrSegListForSeat = iMnjxPnrSegService.lambdaQuery()
                .in(MnjxPnrSeg::getPnrSegId, pnrSegIdList)
                .ne(MnjxPnrSeg::getPnrSegType, "SA") // 排除地面段
                .isNotNull(MnjxPnrSeg::getSeatNumber)
                .list();

        for (MnjxPnrSeg pnrSeg : pnrSegListForSeat) {
            // 获取该航段的开舱信息
            List<MnjxOpenCabin> openCabinList = iSguiCommonService.getOpenCabinListByFlightNo(
                    pnrSeg.getFlightNo(),
                    pnrSeg.getFlightDate(),
                    pnrSeg.getOrg(),
                    pnrSeg.getDst()
            );

            // 恢复对应舱位的座位数
            openCabinList.stream()
                    .filter(o -> o.getSellCabin().equals(pnrSeg.getSellCabin()))
                    .forEach(k -> {
                        int availableNumber = k.getSeatAvailable() + pnrSeg.getSeatNumber();
                        k.setSeatAvailable(availableNumber);
                    });

            // 更新开舱数据
            if (CollUtil.isNotEmpty(openCabinList)) {
                iMnjxOpenCabinService.updateBatchById(openCabinList);
            }
        }
    }

    /**
     * 删除PsgCki数据
     */
    private void deletePsgCki(MnjxPnrNmTicket pnrNmTicket, String pnrNmId) {
        String s1Id = pnrNmTicket.getS1Id();
        String s2Id = pnrNmTicket.getS2Id();
        List<String> segNoList = new ArrayList<>();
        MnjxPnrSeg segId2;
        MnjxPnrSeg segId1;
        //处理多个航段时，进行退票时，只退其中某一段
        if (CharSequenceUtil.isNotEmpty(s1Id) && Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus1()) && CharSequenceUtil.isEmpty(s2Id)) {
            //缺口程在第二段或者第四段，或者只有一段
            segId1 = iMnjxPnrSegService.getById(s1Id);
            Integer pnrSegNo = segId1.getPnrSegNo();
            segNoList.add(String.valueOf(pnrSegNo));
            segNoList.add(String.valueOf(pnrSegNo + 1));
        } else if (CharSequenceUtil.isEmpty(s1Id) && CharSequenceUtil.isNotEmpty(s2Id) && Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus2())) {
            //缺口程在第一段或者第三段
            segId2 = iMnjxPnrSegService.getById(s2Id);
            Integer pnrSegNo = segId2.getPnrSegNo();
            segNoList.add(String.valueOf(pnrSegNo));
            segNoList.add(String.valueOf(pnrSegNo - 1));
        } else if (Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus1()) && Constant.TICKET_STATUS_REFOUND.equals(pnrNmTicket.getTicketStatus2())) {
            //两段都有
            segId1 = iMnjxPnrSegService.getById(s1Id);
            segId2 = iMnjxPnrSegService.getById(s2Id);
            Integer pnrSegNo1 = segId1.getPnrSegNo();
            Integer pnrSegNo2 = segId2.getPnrSegNo();
            segNoList.add(String.valueOf(pnrSegNo1));
            segNoList.add(String.valueOf(pnrSegNo2));
        }
        List<MnjxPsgCki> psgCkis = iMnjxPsgCkiService.lambdaQuery()
                .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                .list();
        List<String> psgCkiIds = psgCkis.stream().filter(p -> segNoList.contains(p.getPnrSegNo()))
                .map(MnjxPsgCki::getPsgCkiId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(psgCkiIds)) {
            //根据SegNo筛选删除cki相关记录
            iMnjxPsgSeatService.lambdaUpdate().in(MnjxPsgSeat::getPsgCkiId, psgCkiIds).remove();
            iMnjxPsgCkiService.removeByIds(psgCkiIds);
        }
    }
}
