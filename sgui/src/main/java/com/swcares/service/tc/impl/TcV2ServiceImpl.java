package com.swcares.service.tc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.bkc.IIssueService;
import com.swcares.service.bkc.IUpdatePnrService;
import com.swcares.service.tc.IRefundTicketService;
import com.swcares.service.tc.ITcV2Service;
import com.swcares.service.tc.ITicketService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:18
 */
@Service
public class TcV2ServiceImpl implements ITcV2Service {

    @Resource
    private IIssueService iIssueService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxAgentAirlineService iMnjxAgentAirlineService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private IMnjxPrinterService iMnjxPrinterService;

    @Resource
    private ITicketService iTicketService;

    @Resource
    private IRefundTicketService iRefundTicketService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxPnrService iMnjxPnrService;

    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;

    @Resource
    private IUpdatePnrService iUpdatePnrService;

    @Resource
    private IMnjxPnrRecordService iMnjxPnrRecordService;

    @Resource
    private IMnjxTicketPriceService iMnjxTicketPriceService;

    @Resource
    private IMnjxNmRmkService iMnjxNmRmkService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Resource
    private IMnjxNmOiService iMnjxNmOiService;

    @Override
    public QueryOfficeInformationVo queryOfficeInformation(QueryOfficeInformationDto dto) {
        QueryOfficeInformationVo vo = new QueryOfficeInformationVo();
        // 查询OFFICE
        MnjxOffice office = iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeNo, dto.getOffice())
                .one();
        // 创建返回office对象
        QueryOfficeInformationVo.OfficeInformationVo officeInformationVo = new QueryOfficeInformationVo.OfficeInformationVo();
        officeInformationVo.setOffice(office.getOfficeNo());
        officeInformationVo.setAllowTicket(true);
        officeInformationVo.setUseAms(false);
        // 只处理代理人类型的office
        if ("0".equals(office.getOfficeType())) {
            // 查询代理人信息
            MnjxAgent agent = iMnjxAgentService.getById(office.getOrgId());
            officeInformationVo.setIata(agent.getAgentIata());
            // 查询代理航司信息
            List<MnjxAgentAirline> agentAirlineList = iMnjxAgentAirlineService.lambdaQuery()
                    .eq(MnjxAgentAirline::getAgentId, agent.getAgentId())
                    .list();
            // 查询航司
            List<MnjxAirline> airlineList = iMnjxAirlineService.listByIds(agentAirlineList.stream()
                    .map(MnjxAgentAirline::getAirlineId)
                    .collect(Collectors.toList()));
            // 设置代理航司数据
            for (MnjxAgentAirline agentAirline : agentAirlineList) {
                QueryOfficeInformationVo.AirlineTicketVo airlineTicketVo = new QueryOfficeInformationVo.AirlineTicketVo();
                airlineTicketVo.setAirlineCode(airlineList.stream()
                        .filter(a -> agentAirline.getAirlineId().equals(a.getAirlineId()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getAirlineCode());
                airlineTicketVo.setTicketPromise(true);
                vo.getAirlineTickets().add(airlineTicketVo);
            }

            // 设置代理人数据
            QueryOfficeInformationVo.AgentVo agentVo = new QueryOfficeInformationVo.AgentVo();
            agentVo.setAddress(agent.getAgentContactAddress());
            agentVo.setContact(agent.getAgentContactCname());
            agentVo.setFax("NULL");
            agentVo.setPhone(agent.getAgentContactPhone());
            vo.setAgent(agentVo);
        }

        // 查询打票机
        List<MnjxPrinter> printerList = iMnjxPrinterService.lambdaQuery()
                .eq(MnjxPrinter::getOfficeId, office.getOfficeId())
                .isNotNull(MnjxPrinter::getTicketStart)
                .isNotNull(MnjxPrinter::getTicketEnd)
                .list();
        // 设置打票机数据
        for (MnjxPrinter printer : printerList) {
            QueryOfficeInformationVo.TicketMachineVo ticketMachineVo = new QueryOfficeInformationVo.TicketMachineVo();
            ticketMachineVo.setPid(printer.getPrinterPid());
            ticketMachineVo.setDevno(printer.getPrinterNo());
            ticketMachineVo.setCurrency("CNY");
            ticketMachineVo.setType("4");
            ticketMachineVo.setTkt("BSP");
            ticketMachineVo.setCtlPid(printer.getSiId());
            ticketMachineVo.setCtlAgnt("");
            String end = StrUtil.toString(printer.getTicketEnd());
            ticketMachineVo.setTnRange(CharSequenceUtil.format("{}-{}", printer.getTicketStart(), end.substring(end.length() - 5)));
            ticketMachineVo.setCurrentTicketNumber(StrUtil.toString(printer.getLastTicket()));
            ticketMachineVo.setTicketRemainder(null);
            vo.getTicketMachines().add(ticketMachineVo);
        }

        // 设置office数据
        vo.setOffice(officeInformationVo);
        return vo;
    }

    @Override
    public List<QueryTicketDetailVo> queryTicketDetail(QueryTicketDetailDto dto) throws SguiResultException {
        return iTicketService.queryTicketDetail(dto);
    }

    @Override
    public QueryTicketByDetrVo queryTicketByDetr(QueryTicketByDetrDto dto) throws SguiResultException {
        return iTicketService.queryTicketByDetr(dto);
    }

    @Override
    public TicketByRtktVo queryTicketByRtkt(String ticketNo) throws SguiResultException {
        return iTicketService.queryTicketByRtkt(ticketNo);
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketByPnr(QueryTicketByPnrDto dto) throws SguiResultException {
        return iTicketService.queryTicketByPnr(dto);
    }

    @Override
    public List<QueryTicketByPnrVo> queryTicketDigestsByCert(QueryTicketByCertDto dto) throws SguiResultException {
        return iTicketService.queryTicketByCert(dto);
    }

    @Override
    public PreviewRefundTicketVo previewRefundTicket(PreviewRefundTicketDto dto) throws SguiResultException {
        return iRefundTicketService.previewRefundTicket(dto);
    }

    @Override
    public FindRefundTicketVo findRefundTicket(FindRefundTicketDto dto) throws SguiResultException {
        return iRefundTicketService.findRefundTicket(dto);
    }

    @Override
    public QueryPnrMessageVo queryPnrMessage(QueryPnrMessageDto dto) throws SguiResultException {
        return iRefundTicketService.queryPnrMessage(dto);
    }

    @Override
    public BatchFindRefundFeeVo batchFindRefundFee(BatchFindRefundFeeDto dto) throws SguiResultException {
        return iRefundTicketService.batchFindRefundFee(dto);
    }

    @Override
    public BatchAutoRefundVo batchAutoRefund(BatchAutoRefundDto dto) throws SguiResultException {
        return iRefundTicketService.batchAutoRefund(dto);
    }

    @Override
    public DeletePnrAndDeleteInfantInfoVo deletePnrAndDeleteInfantInfo(DeletePnrAndDeleteInfantInfoDto dto) throws SguiResultException {
        return iRefundTicketService.deletePnrAndDeleteInfantInfo(dto);
    }

    @Override
    public String queryTicketManagementOrganization(String ticketNo) throws SguiResultException {
        return iRefundTicketService.queryTicketManagementOrganization(ticketNo);
    }

    @Override
    public QueryRefundFormVo queryRefundForm(QueryRefundFormDto dto) throws SguiResultException {
        return iRefundTicketService.queryRefundForm(dto);
    }

    @Override
    public String tssChangeTicketStatus(TssChangeTicketStatusDto dto) throws SguiResultException {
        // 1. 根据ticketNumber查询mnjx_pnr_nm_ticket表
        List<MnjxPnrNmTicket> ticketList = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, dto.getTicketNumber().replace("-", ""))
                .list();

        if (ticketList.isEmpty()) {
            throw new SguiResultException("票号不存在");
        }

        MnjxPnrNmTicket ticket = ticketList.get(0);

        // 2. 根据tssType设置ticketStatus1和ticketStatus2
        if ("Suspend".equals(dto.getTssType())) {
            // 3. 如果tssType是Suspend，验证ticketStatus1和ticketStatus2是否是OPEN FOR USE
            boolean hasValidStatus = false;

            // 检查status1
            if (CharSequenceUtil.isNotEmpty(ticket.getTicketStatus1())) {
                if (Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticket.getTicketStatus1())) {
                    hasValidStatus = true;
                } else {
                    throw new SguiResultException("COUPON STATUS CODE INVALID");
                }
            }

            // 检查status2
            if (CharSequenceUtil.isNotEmpty(ticket.getTicketStatus2())) {
                if (Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticket.getTicketStatus2())) {
                    hasValidStatus = true;
                } else {
                    throw new SguiResultException("COUPON STATUS CODE INVALID");
                }
            }

            if (!hasValidStatus) {
                throw new SguiResultException("COUPON STATUS CODE INVALID");
            }

            // 5. Suspend设置ticketStatus1和ticketStatus2为SUSPENDED
            if (CharSequenceUtil.isNotEmpty(ticket.getTicketStatus1()) &&
                Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticket.getTicketStatus1())) {
                ticket.setTicketStatus1(Constant.TICKET_STATUS_SUSPENDED);
            }
            if (CharSequenceUtil.isNotEmpty(ticket.getTicketStatus2()) &&
                Constant.TICKET_STATUS_OPEN_FOR_USE.equals(ticket.getTicketStatus2())) {
                ticket.setTicketStatus2(Constant.TICKET_STATUS_SUSPENDED);
            }
            MnjxTicketOperateRecord ticketOperateRecord = new MnjxTicketOperateRecord();
            ticketOperateRecord.setTicketNo(ticket.getTicketNo());
            ticketOperateRecord.setTicketStatus1(ticket.getTicketStatus1());
            ticketOperateRecord.setTicketStatus2(ticket.getTicketStatus2());
            ticketOperateRecord.setOperateTime(DateUtil.now());
            ticketOperateRecord.setSettlementCode(ticket.getTicketNo().substring(0, 3));
            UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
            ticketOperateRecord.setSiNo(userInfo.getSiNo());
            iMnjxTicketOperateRecordService.save(ticketOperateRecord);
        } else if ("Resume".equals(dto.getTssType())) {
            // 4. 如果tssType是Resume，验证ticketStatus1和ticketStatus2是否是SUSPENDED
            boolean hasValidStatus = false;

            // 检查status1
            if (CharSequenceUtil.isNotEmpty(ticket.getTicketStatus1())) {
                if (Constant.TICKET_STATUS_SUSPENDED.equals(ticket.getTicketStatus1())) {
                    hasValidStatus = true;
                } else {
                    throw new SguiResultException("COUPON STATUS CODE INVALID");
                }
            }

            // 检查status2
            if (CharSequenceUtil.isNotEmpty(ticket.getTicketStatus2())) {
                if (Constant.TICKET_STATUS_SUSPENDED.equals(ticket.getTicketStatus2())) {
                    hasValidStatus = true;
                } else {
                    throw new SguiResultException("COUPON STATUS CODE INVALID");
                }
            }

            if (!hasValidStatus) {
                throw new SguiResultException("COUPON STATUS CODE INVALID");
            }

            // 6. Resume设置ticketStatus1和ticketStatus2为OPEN FOR USE
            if (CharSequenceUtil.isNotEmpty(ticket.getTicketStatus1()) &&
                Constant.TICKET_STATUS_SUSPENDED.equals(ticket.getTicketStatus1())) {
                ticket.setTicketStatus1(Constant.TICKET_STATUS_OPEN_FOR_USE);
            }
            if (CharSequenceUtil.isNotEmpty(ticket.getTicketStatus2()) &&
                Constant.TICKET_STATUS_SUSPENDED.equals(ticket.getTicketStatus2())) {
                ticket.setTicketStatus2(Constant.TICKET_STATUS_OPEN_FOR_USE);
            }
            MnjxTicketOperateRecord ticketOperateRecord = new MnjxTicketOperateRecord();
            ticketOperateRecord.setTicketNo(ticket.getTicketNo());
            ticketOperateRecord.setTicketStatus1(ticket.getTicketStatus1());
            ticketOperateRecord.setTicketStatus2(ticket.getTicketStatus2());
            ticketOperateRecord.setOperateTime(DateUtil.now());
            ticketOperateRecord.setSettlementCode(ticket.getTicketNo().substring(0, 3));
            UserInfo userInfo = iSguiCommonService.getCurrentUserInfo();
            ticketOperateRecord.setSiNo(userInfo.getSiNo());
            iMnjxTicketOperateRecordService.save(ticketOperateRecord);
        } else {
            throw new SguiResultException("无效的操作类型");
        }

        // 7. 更新mnjx_pnr_nm_ticket表
        boolean updateResult = iMnjxPnrNmTicketService.updateById(ticket);
        if (!updateResult) {
            throw new SguiResultException("更新票务状态失败");
        }

        return "SUCCESS";
    }

    @Override
    public BatchManualRefundTicketVo batchManualRefundTicket(BatchManualRefundTicketDto dto) throws SguiResultException {
        return iRefundTicketService.batchManualRefundTicket(dto);
    }

    @Override
    public QueryRtktDetailVo queryRtktDetail(QueryRtktDetailDto dto) throws SguiResultException {
        return iRefundTicketService.queryRtktDetail(dto);
    }

    @Override
    public String deleteRefundForm(DeleteRefundFormDto dto) throws SguiResultException {
        return iRefundTicketService.deleteRefundForm(dto);
    }

    @Override
    public ModifyRefundFormVo modifyRefundForm(ModifyRefundFormDto dto) throws SguiResultException {
        return iRefundTicketService.modifyRefundForm(dto);
    }

    @Override
    public ManualRefundTicketVo manualRefundTicket(BatchManualRefundTicketDto.RefundInfo dto) throws SguiResultException {
        BatchManualRefundTicketDto batchDto = new BatchManualRefundTicketDto();
        batchDto.setRefundList(Collections.singletonList(dto));
        BatchManualRefundTicketVo batchVo = iRefundTicketService.batchManualRefundTicket(batchDto);
        BatchManualRefundTicketVo.PassengerStatus passengerStatus = batchVo.getPassengerStatuses().get(0);
        ManualRefundTicketVo vo = new ManualRefundTicketVo();
        vo.setRefundNumber(passengerStatus.getTrfdno().substring(0, 3) + "-" + passengerStatus.getTrfdno().substring(3));
        vo.setRefundStatus(Boolean.TRUE.equals(passengerStatus.getSuccess()) ? "success" : "fail");
        return vo;
    }

    @Override
    public BatchFindRefundFeeZVo batchFindRefundFeeZ(BatchFindRefundFeeZDto dto) throws SguiResultException {
        return iRefundTicketService.batchFindRefundFeeZ(dto);
    }

    @Override
    public BatchRefundVo batchRefund(BatchRefundDto dto) throws SguiResultException {
        return iRefundTicketService.batchRefund(dto);
    }

    @Override
    public String etrfChangeTicketStatus(EtrfChangeTicketStatusDto dto) throws SguiResultException {
        return iRefundTicketService.etrfChangeTicketStatus(dto);
    }

    @Override
    public List<QueryTicketByInvalidVo> queryTicketByInvalid(QueryTicketByInvalidDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || CharSequenceUtil.isEmpty(dto.getTicketNo())) {
            throw new SguiResultException("票号不能为空");
        }

        // 去掉票号中的"-"
        String ticketNo = dto.getTicketNo().replace("-", "");

        // 1. 根据请求参数票号查询mnjx_pnr_nm_ticket表，获取票信息，获取tnId
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                .one();

        if (pnrNmTicket == null) {
            throw new SguiResultException("未找到票面信息");
        }

        // 2. 查询mnjx_pnr_nm_tn表，根据tnId查询，获取pnrNmId或nmXnId
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (pnrNmTn == null) {
            throw new SguiResultException("未找到票务信息");
        }

        String pnrNmId = pnrNmTn.getPnrNmId();
        String nmXnId = pnrNmTn.getNmXnId();
        boolean isInfant = false;

        // 如果是nmXnId，查询nmXn后获取pnrNmId
        MnjxNmXn nmXn = null;
        if (CharSequenceUtil.isNotEmpty(nmXnId)) {
            nmXn = iMnjxNmXnService.getById(nmXnId);
            if (nmXn != null) {
                pnrNmId = nmXn.getPnrNmId();
                isInfant = true;
            }
        }

        if (CharSequenceUtil.isEmpty(pnrNmId)) {
            throw new SguiResultException("未找到旅客信息");
        }

        // 3. 查询mnjx_pnr_nm表，根据pnrNmId查询，获取pnrId
        MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(pnrNmId);
        if (pnrNm == null) {
            throw new SguiResultException("未找到旅客信息");
        }

        // 4. 查询mnjx_pnr表，根据pnrId查询，获取crsPnrNo
        MnjxPnr pnr = iMnjxPnrService.getById(pnrNm.getPnrId());
        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 5. 构建返回数据
        return this.buildQueryTicketByInvalidResult(pnrNmTicket, pnrNm, nmXn, pnr, isInfant);
    }

    /**
     * 构建废票查询结果
     */
    private List<QueryTicketByInvalidVo> buildQueryTicketByInvalidResult(MnjxPnrNmTicket pnrNmTicket,
                                                                         MnjxPnrNm pnrNm, MnjxNmXn nmXn,
                                                                         MnjxPnr pnr, boolean isInfant) {
        QueryTicketByInvalidVo vo = new QueryTicketByInvalidVo();

        // 设置旅客姓名：根据该票对应的是婴儿还是非婴儿确定
        String passengerName;
        if (isInfant && nmXn != null) {
            passengerName = nmXn.getXnCname();
        } else {
            passengerName = pnrNm.getName();
        }
        vo.setPassengerName(passengerName);
        vo.setPassengerNameSuffix(passengerName);

        // 设置旅客类型
        if (isInfant) {
            vo.setSpecialPassengerType("INF");
        } else {
            vo.setSpecialPassengerType(this.convertPsgType(pnrNm));
        }

        // 设置票号（格式化为带"-"的格式）
        String formattedTicketNo = this.formatTicketNo(pnrNmTicket.getTicketNo());
        vo.setTicketNo(formattedTicketNo);

        // 设置其他固定字段
        vo.setEtType("BSP");
        vo.setTicketTypeCode("D");
        vo.setGovernmentPurchase(false);
        vo.setCrsPnrNo(pnr.getPnrCrs());
        vo.setInvalid(true);
        vo.setAlreadyInvalid(false);
        vo.setPaymentBOP(false);
        vo.setTicketManagementOrganizationCode("BSP");

        return Collections.singletonList(vo);
    }

    /**
     * 转换旅客类型
     */
    private String convertPsgType(MnjxPnrNm pnrNm) {
        MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
                .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                .eq(MnjxNmSsr::getSsrType, "CHLD")
                .one();
        if (ObjectUtil.isNotEmpty(chldSsr)) {
            return "CHD";
        } else {
            MnjxNmRmk gmjcRmk = iMnjxNmRmkService.lambdaQuery()
                    .eq(MnjxNmRmk::getPnrNmId, pnrNm.getPnrNmId())
                    .eq(MnjxNmRmk::getRmkName, "GMJC")
                    .one();
            if (ObjectUtil.isNotEmpty(gmjcRmk)) {
                return "GMJC";
            } else {
                return "ADT";
            }
        }
    }

    /**
     * 格式化票号为带"-"的格式
     */
    private String formatTicketNo(String ticketNo) {
        if (CharSequenceUtil.isEmpty(ticketNo) || ticketNo.length() < 4) {
            return ticketNo;
        }
        // 格式：前3位-后面的位数
        return ticketNo.substring(0, 3) + "-" + ticketNo.substring(3);
    }

    @Override
    public List<QueryAllPassengersByTktNumberVo> queryAllPassengersByTktNumber(QueryAllPassengersByTktNumberDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || CharSequenceUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 1. 根据请求参数pnrNo查询mnjx_pnr表，获取pnr信息
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 2. 根据pnrId查询mnjxPnrNm，获取旅客信息
        List<MnjxPnrNm> passengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .orderByAsc(MnjxPnrNm::getPsgIndex)
                .list();

        if (CollUtil.isEmpty(passengers)) {
            throw new SguiResultException("未找到旅客信息");
        }

        List<QueryAllPassengersByTktNumberVo> result = new ArrayList<>();

        // 3. 处理每个旅客的出票信息
        for (MnjxPnrNm passenger : passengers) {
            // 处理成人/儿童旅客的出票信息
            this.processPassengerTickets(passenger, null, pnr, result);

            // 4. 根据pnrNmId查询nmXn获取婴儿信息（如果存在）
            List<MnjxNmXn> infants = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                    .list();

            // 处理婴儿的出票信息
            for (MnjxNmXn infant : infants) {
                this.processPassengerTickets(passenger, infant, pnr, result);
            }
        }

        if (CollUtil.isEmpty(result)) {
            throw new SguiResultException("未找到出票信息");
        }

        return result;
    }

    /**
     * 处理旅客的出票信息
     */
    private void processPassengerTickets(MnjxPnrNm passenger, MnjxNmXn infant, MnjxPnr pnr,
                                       List<QueryAllPassengersByTktNumberVo> result) {
        // 5. 根据pnrNmId和nmXnId查询mnjx_pnr_nm_tn，获取tn中的票号信息
        List<MnjxPnrNmTn> tnList;
        if (infant != null) {
            // 婴儿票
            tnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getNmXnId, infant.getNmXnId())
                    .list();
        } else {
            // 成人/儿童票
            tnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getPnrNmId, passenger.getPnrNmId())
                    .isNull(MnjxPnrNmTn::getNmXnId) // 确保不是婴儿票
                    .list();
        }

        // 如果查不到tn说明该旅客未出票，跳过
        if (CollUtil.isEmpty(tnList)) {
            return;
        }

        // 6. 处理每个tn的票号信息
        for (MnjxPnrNmTn tn : tnList) {
            QueryAllPassengersByTktNumberVo vo = this.buildPassengerTicketVo(passenger, infant, pnr, tn);
            result.add(vo);
        }
    }

    /**
     * 构建旅客票务信息VO
     */
    private QueryAllPassengersByTktNumberVo buildPassengerTicketVo(MnjxPnrNm passenger, MnjxNmXn infant,
                                                                  MnjxPnr pnr, MnjxPnrNmTn tn) {
        QueryAllPassengersByTktNumberVo vo = new QueryAllPassengersByTktNumberVo();

        // 设置旅客姓名和类型
        if (infant != null) {
            // 婴儿
            vo.setPassengerName(CharSequenceUtil.isNotEmpty(infant.getXnFullName()) ? infant.getXnFullName() : infant.getXnCname());
            vo.setPassengerNameSuffix(vo.getPassengerName() + " INF(" + DateUtils.ym2Com(infant.getXnBirthday()) + ")");
            vo.setPassengerType("3");
            vo.setPnrPsgType("INF");
            vo.setSpecialPassengerType("INF");
        } else {
            // 成人/儿童
            vo.setPassengerName(passenger.getName());
            MnjxNmSsr chldSsr = iMnjxNmSsrService.lambdaQuery()
                    .eq(MnjxNmSsr::getPnrNmId, passenger.getPnrNmId())
                    .eq(MnjxNmSsr::getSsrType, "CHLD")
                    .one();

            if (ObjectUtil.isNotEmpty(chldSsr)) {
                // 儿童
                vo.setPassengerNameSuffix(passenger.getName().contains(" CHD") ? passenger.getName() : passenger.getName() + " CHD");
                vo.setPassengerType("1");
                vo.setPnrPsgType("CHD");
                vo.setSpecialPassengerType("CHD");
            } else {
                // 默认成人
                vo.setPassengerNameSuffix(passenger.getName());
                vo.setPassengerType("0");
                vo.setPnrPsgType("ADT");
                vo.setSpecialPassengerType("ADT");
                MnjxNmRmk gmjcRmk = iMnjxNmRmkService.lambdaQuery()
                        .eq(MnjxNmRmk::getPnrNmId, passenger.getPnrNmId())
                        .eq(MnjxNmRmk::getRmkName, "GMJC")
                        .one();
                if (ObjectUtil.isNotEmpty(gmjcRmk)) {
                    if (passenger.getName().endsWith("GM JC") || passenger.getName().endsWith("JC GM")) {
                        vo.setPnrPsgType("GMJC");
                        vo.setSpecialPassengerType("GMJC");
                    } else if (passenger.getName().endsWith("GM")) {
                        vo.setPnrPsgType("GM");
                        vo.setSpecialPassengerType("GM");
                    } else {
                        vo.setPnrPsgType("JC");
                        vo.setSpecialPassengerType("JC");
                    }
                    vo.setPassengerNameSuffix(passenger.getName() + " (GMJC)");
                }
            }
        }

        vo.setTicketNo(tn.getInputValue().replace("TN/", "").replaceAll("/P\\d+", "").replace("IN/", ""));

        // 设置其他固定字段
        vo.setEtType("BSP");
        vo.setTicketTypeCode("D");
        vo.setGovernmentPurchase(false);
        vo.setCrsPnrNo(pnr.getPnrCrs());
        vo.setInvalid(true);
        vo.setAlreadyInvalid(false);
        vo.setPaymentBOP(false);
        vo.setTicketManagementOrganizationCode("BSP");

        return vo;
    }

    @Override
    public String tssChangeTicketStatusByPnr(TssChangeTicketStatusByPnrDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || CharSequenceUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }
        if (CharSequenceUtil.isEmpty(dto.getTssType())) {
            throw new SguiResultException("操作类型不能为空");
        }
        if (CharSequenceUtil.isEmpty(dto.getIssueDate())) {
            throw new SguiResultException("出票日期不能为空");
        }
        if (!"Suspend".equals(dto.getTssType()) && !"Resume".equals(dto.getTssType())) {
            throw new SguiResultException("操作类型只能是Suspend或Resume");
        }

        // 1. 根据请求参数pnrNo查询PNR信息，获取pnrId
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();

        if (pnr == null) {
            throw new SguiResultException("未找到PNR信息");
        }

        // 2. 根据pnrId查询mnjx_pnr_nm，获取pnrNmId
        List<MnjxPnrNm> passengers = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();

        if (CollUtil.isEmpty(passengers)) {
            throw new SguiResultException("未找到旅客信息");
        }

        // 收集所有需要处理的票号
        List<String> ticketNumbers = new ArrayList<>();

        // 3. 处理每个旅客的票务信息
        for (MnjxPnrNm passenger : passengers) {
            // 处理成人/儿童旅客的票务信息
            this.collectTicketNumbers(passenger.getPnrNmId(), null, dto.getIssueDate(), ticketNumbers);

            // 4. 根据pnrNmId查询nmXn获取婴儿信息（如果存在）
            List<MnjxNmXn> infants = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, passenger.getPnrNmId())
                    .list();

            // 处理婴儿的票务信息
            for (MnjxNmXn infant : infants) {
                this.collectTicketNumbers(passenger.getPnrNmId(), infant.getNmXnId(), dto.getIssueDate(), ticketNumbers);
            }
        }

        // 如果为空报错
        if (CollUtil.isEmpty(ticketNumbers)) {
            throw new SguiResultException("USAS OPERATE FAILED, BECAUSE:COUPON STATUS CODE INVALID");
        }

        // 9. 批量验证和操作票遍历调用 "挂起解挂" 需求的接口
        for (String ticketNumber : ticketNumbers) {
            TssChangeTicketStatusDto tssDto = new TssChangeTicketStatusDto();
            tssDto.setTicketNumber(ticketNumber);
            tssDto.setTssType(dto.getTssType());

            // 调用现有的挂起解挂接口
            this.tssChangeTicketStatus(tssDto);
        }

        // 10. 全部成功返回SUCCESS
        return "SUCCESS";
    }

    /**
     * 收集指定出票日期的票号
     */
    private void collectTicketNumbers(String pnrNmId, String nmXnId, String issueDate, List<String> ticketNumbers) {
        // 5. 根据pnrNmId、nmXnId和请求参数issueDate查询mnjx_pnr_nm_tn表
        List<MnjxPnrNmTn> tnList;
        if (CharSequenceUtil.isNotEmpty(nmXnId)) {
            // 婴儿票
            tnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getNmXnId, nmXnId)
                    .list();
        } else {
            // 成人/儿童票
            tnList = iMnjxPnrNmTnService.lambdaQuery()
                    .eq(MnjxPnrNmTn::getPnrNmId, pnrNmId)
                    .isNull(MnjxPnrNmTn::getNmXnId)
                    .list();
        }

        tnList = tnList.stream()
                .filter(t -> issueDate.equals(DateUtil.format(DateUtil.parseDate(t.getIssuedTime()), "yyyy-MM-dd")))
                .collect(Collectors.toList());

        // 6. 根据tnId查询mnjx_pnr_nm_ticket表获取票信息
        for (MnjxPnrNmTn tn : tnList) {
            List<MnjxPnrNmTicket> tickets = iMnjxPnrNmTicketService.lambdaQuery()
                    .eq(MnjxPnrNmTicket::getPnrNmTnId, tn.getTnId())
                    .list();

            // 7. 收集票号（格式化为带"-"的格式）
            for (MnjxPnrNmTicket ticket : tickets) {
                String formattedTicketNo = this.formatTicketNo(ticket.getTicketNo());
                ticketNumbers.add(formattedTicketNo);
            }
        }
    }

    @Override
    public void fareConfirm(FareConfirmDto dto) {
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();

        List<Object> addList = new ArrayList<>();
        List<Object> updateList = new ArrayList<>();
        List<Object> deleteList = new ArrayList<>();

        for (FareConfirmDto.FareParameterRequest fareParameterRequest : dto.getFareParameterRequestList()) {
            FareConfirmDto.FareInfo fareInfo = fareParameterRequest.getFareInfo();
            // 存在OI
            if (CharSequenceUtil.isNotEmpty(fareInfo.getOi())) {
                StringBuilder oi = new StringBuilder();
                oi.append("OI/");
                String psgIndex = fareParameterRequest.getPsgindex();
                MnjxPnrNm pnrNm = pnrNmList.stream()
                        .filter(p -> p.getPsgIndex() == Integer.parseInt(psgIndex))
                        .collect(Collectors.toList())
                        .get(0);
                if (Boolean.TRUE.equals(fareParameterRequest.getInfant())) {
                    oi.append("IN/");
                }
                // 781-2000300001#1000 SHA001 18JUL5 08300028
                String decodedText = new String(Base64.getDecoder().decode(fareInfo.getOi()), StandardCharsets.UTF_8);
                String[] split = decodedText.split(" ");
                oi.append(split[0])
                        .append(split[1], 0, 3)
                        .append(split[2])
                        .append(" ")
                        .append(split[3])
                        .append("/P")
                        .append(psgIndex);
                // 查询该旅客是否有OI，有则更新inputValue，没有新增
                MnjxNmOi dbNmOi = iMnjxNmOiService.lambdaQuery()
                        .eq(MnjxNmOi::getPnrNmId, pnrNm.getPnrNmId())
                        .like(fareParameterRequest.getInfant(), MnjxNmOi::getOiInfo, "IN/")
                        .one();
                if (dbNmOi != null) {
                    dbNmOi.setOiInfo(oi.toString());
                    updateList.add(dbNmOi);
                } else {
                    MnjxNmOi nmOi = new MnjxNmOi();
                    nmOi.setNmOiId(IdUtil.getSnowflake(1, 1).nextIdStr());
                    nmOi.setPnrIndex(0);
                    nmOi.setPnrNmId(pnrNm.getPnrNmId());
                    nmOi.setOiInfo(oi.toString());
                    nmOi.setOldTicketNo(split[0].split("#")[0].replace("-", ""));
                    addList.add(nmOi);
                }
            }

            // 免改时构建新的SSR TKNE
            if (CollUtil.isNotEmpty(fareInfo.getTkneStrList())) {
                List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                        .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                        .list();
                List<MnjxPnrNmTn> tnList = new ArrayList<>();
                List<MnjxPnrNmTicket> ticketList = new ArrayList<>();
                for (String tkneStr : fareInfo.getTkneStrList()) {
                    // SSR TKNE MU HK1 PVGCKG 5427 K21JUL25 781-2000300000/1/P1
                    String psgIndex = tkneStr.split("/P")[1];
                    MnjxPnrNm passenger = pnrNmList.stream()
                            .filter(p -> p.getPsgIndex() == Integer.parseInt(psgIndex))
                            .collect(Collectors.toList())
                            .get(0);
                    String[] split = tkneStr.split(" ");
                    MnjxNmSsr nmSsr = new MnjxNmSsr();
                    String ssrId = IdUtil.getSnowflake(1, 1).nextIdStr();
                    nmSsr.setNmSsrId(ssrId);
                    nmSsr.setPnrNmId(passenger.getPnrNmId());
                    int pnrSegNo = Integer.parseInt(tkneStr.split("/P")[0].split("/")[1]);
                    nmSsr.setPnrSegNo(pnrSegNo);
                    nmSsr.setSsrType(Constant.TKNE);
                    nmSsr.setActionCode("HK");
                    nmSsr.setOrgDst(split[4]);
                    nmSsr.setAirlineCode(split[2]);
                    nmSsr.setFltDate(DateUtils.com2ymd(split[6].substring(1)));
                    nmSsr.setSsrInfo(tkneStr);
                    nmSsr.setInputValue(tkneStr);
                    addList.add(nmSsr);

                    // 免改时更新nmTicket的航段id
                    String ticketNo = split[7].replace("INF", "").split("/")[0].replace("-", "");
                    MnjxPnrSeg pnrSeg = pnrSegList.stream()
                            .filter(s -> s.getPnrSegNo() == pnrSegNo)
                            .collect(Collectors.toList())
                            .get(0);
                    MnjxPnrNmTicket nmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                            .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                            .one();
                    if (pnrSegNo % 2 == 0) {
                        nmTicket.setS2Id(pnrSeg.getPnrSegId());
                    } else {
                        nmTicket.setS1Id(pnrSeg.getPnrSegId());
                    }
                    updateList.add(nmTicket);

                    pnrSegList.stream()
                            .filter(s -> "1".equals(s.getExchanged()))
                            .forEach(s -> {
                                s.setExchanged("0");
                                updateList.add(s);
                            });

                    // 免改时更新票号对应的ticketPrice信息
                    MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                            .eq(MnjxTicketPrice::getTicketNo, ticketNo)
                            .one();
                    StringBuilder segInfoBuilder = this.getSegInfoBuilder(pnrSegList);
                    ticketPrice.setSegInfo(segInfoBuilder.toString());
                    iMnjxTicketPriceService.updateById(ticketPrice);

                    ticketList.add(nmTicket);
                    tnList.add(iMnjxPnrNmTnService.getById(nmTicket.getPnrNmTnId()));
                }
                iIssueService.structurePsgNum(tnList, ticketList, pnrSegList);
            }
        }
        iUpdatePnrService.batchExecuteOperations(addList, updateList, deleteList);

        List<MnjxPnrRecord> recordList = new ArrayList<>();
        // 重新排序所有项的pnr_index
        iUpdatePnrService.reorderAllPnrIndexesAndUpdate(pnr, recordList);
        // 历史记录，先删除以前changeMark为空的，再批量添加这次封口所有已排好序的记录
        iMnjxPnrRecordService.lambdaUpdate()
                .eq(MnjxPnrRecord::getPnrId, pnr.getPnrId())
                .isNull(MnjxPnrRecord::getChangeMark)
                .remove();
        iMnjxPnrRecordService.saveBatch(recordList);
    }

    @Override
    public List<GetTicketDigestsByNameVo> getTicketDigestsByName(GetTicketDigestsByNameDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || CharSequenceUtil.isEmpty(dto.getCertNo())) {
            throw new SguiResultException("姓名信息不能为空");
        }

        try {
            // 1. 请求certNo用base64解密，获取输入的姓名
            String passengerName = new String(Base64.getDecoder().decode(dto.getCertNo()), StandardCharsets.UTF_8);

            // 获取当前登录用户的Office信息
            UserInfo currentUser = iSguiCommonService.getCurrentUserInfo();
            MnjxOffice office = currentUser.getMnjxOffice();
            if (office == null) {
                throw new SguiResultException("获取当前Office信息失败");
            }

            // 计算查询时间范围：当天及过去30天
            String currentDate = DateUtil.today();
            String startDate = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -30), "yyyy-MM-dd");

            // 2. 根据姓名查询mnjx_pnr_nm表name字段，获取pnrNmList
            List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                    .eq(MnjxPnrNm::getName, passengerName)
                    .list();

            if (CollUtil.isEmpty(pnrNmList)) {
                return new ArrayList<>();
            }

            // 获取pnrIdList和pnrNmIdList
            List<String> pnrIdList = pnrNmList.stream().map(MnjxPnrNm::getPnrId).collect(Collectors.toList());
            List<String> pnrNmIdList = pnrNmList.stream().map(MnjxPnrNm::getPnrNmId).collect(Collectors.toList());

            // 2.1. 通过pnrIdList查询mnjx_pnr，获取pnrList
            List<MnjxPnr> pnrList = iMnjxPnrService.lambdaQuery()
                    .in(MnjxPnr::getPnrId, pnrIdList)
                    .list();

            // 2.1. 通过pnrNmIdList查询mnjx_pnr_nm_tn，获取tnList
            List<MnjxPnrNmTn> tnList = iMnjxPnrNmTnService.lambdaQuery()
                    .in(MnjxPnrNmTn::getPnrNmId, pnrNmIdList)
                    .list();

            if (CollUtil.isEmpty(tnList)) {
                return new ArrayList<>();
            }

            // 筛选issuedTime符合条件的tnList（当天及过去30天，且为当前OFFICE所出的票）
            List<MnjxPnrNmTn> filteredTnList = tnList.stream()
                    .filter(tn -> {
                        if (CharSequenceUtil.isEmpty(tn.getIssuedTime())) {
                            return false;
                        }
                        try {
                            // 检查时间范围
                            String issuedDate = tn.getIssuedTime().substring(0, 10); // 取日期部分
                            boolean timeMatch = issuedDate.compareTo(startDate) >= 0 && issuedDate.compareTo(currentDate) <= 0;

                            // 检查是否为当前OFFICE所出的票（通过打票机关联）
                            boolean officeMatch = true; // 默认为true，如果需要严格筛选可以添加具体逻辑
                            if (CharSequenceUtil.isNotEmpty(tn.getPrinterId())) {
                                // 可以通过打票机ID查询是否属于当前Office，这里暂时跳过具体实现
                                // MnjxPrinter printer = iMnjxPrinterService.getById(tn.getPrinterId());
                                // officeMatch = printer != null && office.getOfficeId().equals(printer.getOfficeId());
                            }

                            return timeMatch && officeMatch;
                        } catch (Exception e) {
                            return false;
                        }
                    })
                    .collect(Collectors.toList());

            if (CollUtil.isEmpty(filteredTnList)) {
                return new ArrayList<>();
            }

            // 获取tnIdList查询mnjx_pnr_nm_ticket获取ticketList
            List<String> tnIdList = filteredTnList.stream().map(MnjxPnrNmTn::getTnId).collect(Collectors.toList());
            List<MnjxPnrNmTicket> ticketList = iMnjxPnrNmTicketService.lambdaQuery()
                    .in(MnjxPnrNmTicket::getPnrNmTnId, tnIdList)
                    .list();

            if (CollUtil.isEmpty(ticketList)) {
                return new ArrayList<>();
            }

            List<GetTicketDigestsByNameVo> result = new ArrayList<>();

            // 2.2. 遍历pnrNmList构建返回数据
            for (MnjxPnrNm pnrNm : pnrNmList) {
                // 首先判断pnrNmId是否在筛选后的tnList中存在
                List<MnjxPnrNmTn> matchedTnList = filteredTnList.stream()
                        .filter(tn -> pnrNm.getPnrNmId().equals(tn.getPnrNmId()))
                        .collect(Collectors.toList());

                if (CollUtil.isEmpty(matchedTnList)) {
                    continue;
                }

                // 根据pnrId筛选pnrList，获取pnrStatus
                MnjxPnr pnr = pnrList.stream()
                        .filter(p -> p.getPnrId().equals(pnrNm.getPnrId()))
                        .findFirst()
                        .orElse(null);

                if (pnr == null) {
                    continue;
                }

                // 对每个匹配的tn进行处理
                for (MnjxPnrNmTn tn : matchedTnList) {
                    // 根据tn数据筛选ticketList数据
                    List<MnjxPnrNmTicket> matchedTicketList = ticketList.stream()
                            .filter(ticket -> ticket.getPnrNmTnId().equals(tn.getTnId()))
                            .collect(Collectors.toList());

                    // 对筛选的ticketList遍历
                    for (MnjxPnrNmTicket ticket : matchedTicketList) {
                        GetTicketDigestsByNameVo vo = new GetTicketDigestsByNameVo();

                        // etNumber：ticketNo，中间4-7位以*号代替
                        String ticketNo = ticket.getTicketNo();
                        if (CharSequenceUtil.isNotEmpty(ticketNo) && ticketNo.length() >= 10) {
                            String maskedTicketNo = ticketNo.substring(0, 3) + "-" +
                                    ticketNo.substring(3, 6) + "****" + ticketNo.substring(10);
                            vo.setEtNumber(maskedTicketNo);
                        } else {
                            vo.setEtNumber(ticketNo);
                        }

                        // passengerName：pnrNm的name
                        vo.setPassengerName(pnrNm.getName());

                        // passengerType：根据psgType为1设置为CHD，其他设置为ADT
                        vo.setPassengerType("1".equals(pnrNm.getPsgType()) ? "CHD" : "ADT");

                        // 构建航段信息
                        List<GetTicketDigestsByNameVo.AirSeg> airSegList = this.buildAirSegList(ticket, pnr);
                        vo.setAirSeg(airSegList);

                        result.add(vo);
                    }
                }
            }

            return result;

        } catch (Exception e) {
            log.error("按姓名查询客票异常", e);
            throw new SguiResultException("查询客票信息失败：" + e.getMessage());
        }
    }

    private StringBuilder getSegInfoBuilder(List<MnjxPnrSeg> pnrSegList) {
        StringBuilder segInfoBuilder = new StringBuilder();
        for (int i = 0; i < pnrSegList.size(); i++) {
            MnjxPnrSeg segment = pnrSegList.get(i);
            if (!"SA".equals(segment.getPnrSegType())) {
                segInfoBuilder.append(String.format("%s %s %s %s %s %s %s",
                        segment.getFlightNo(), segment.getOrg(), segment.getDst(), segment.getFlightDate(), segment.getSellCabin(), segment.getEstimateOff(), segment.getEstimateArr()));
            } else {
                segInfoBuilder.append(String.format("SA %s %s", segment.getOrg(), segment.getDst()));
            }
            if (i < pnrSegList.size() - 1) {
                segInfoBuilder.append("/");
            }
        }
        return segInfoBuilder;
    }

    @Override
    public DomesticTicketVo domesticTicket(DomesticTicketDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || CharSequenceUtil.isEmpty(dto.getPnr())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        if (CharSequenceUtil.isEmpty(dto.getPrintNo())) {
            throw new SguiResultException("打票机编号不能为空");
        }

        if (CollUtil.isEmpty(dto.getPassengerTickets())) {
            throw new SguiResultException("旅客出票信息不能为空");
        }

        // 1. 根据请求参数pnr查询mnjx_pnr，获取pnrId
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnr())
                .one();
        if (ObjectUtil.isEmpty(pnr)) {
            throw new SguiResultException("未找到对应的PNR信息");
        }

        // 构建出票请求接口的参数IssueTicketDto
        IssueTicketDto issueTicketDto = new IssueTicketDto();
        issueTicketDto.setTicketType(dto.getIssueType()); // issueType -> ticketType
        issueTicketDto.setFareType(dto.getPayType()); // payType -> fareType
        issueTicketDto.setPnrNo(dto.getPnr()); // pnr -> pnrNo
        issueTicketDto.setFpList(Collections.emptyList()); // fpList设置空列表
        issueTicketDto.setCreditCardValidate(new IssueTicketDto.CreditCardValidateDto()); // creditCardValidate设置为空对象
        issueTicketDto.setValidateIet(false); // validateIet设置为false
        issueTicketDto.setCheckPinYinName(false); // checkPinYinName设置为false

        // 创建issue对象
        IssueTicketDto.IssueDto issue = new IssueTicketDto.IssueDto();
        issue.setPrinterNo(dto.getPrintNo()); // printNo -> printerNo
        issue.setValidateAirline(dto.getAirline()); // airline -> validateAirline
        issue.setDorr(false); // dorr设置为false
        issue.setDpayPass(""); // dpayPass设置为空字符串
        issue.setDpayTktType(""); // dpayTktType设置为空字符串
        issue.setSalvageChange(false); // salvageChange设置为false

        // 2. 根据pnrId查询mnjx_pnr_nm，获取该PNR的所有旅客
        List<MnjxPnrNm> pnrNmList = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrId, pnr.getPnrId())
                .list();
        if (CollUtil.isEmpty(pnrNmList)) {
            throw new SguiResultException("未找到PNR旅客信息");
        }

        // 对所有旅客遍历进行issueItems初步构建
        List<IssueTicketDto.IssueItemDto> issueItems = new ArrayList<>();
        List<String> pnrNmIdList = new ArrayList<>();

        for (MnjxPnrNm pnrNm : pnrNmList) {
            pnrNmIdList.add(pnrNm.getPnrNmId());

            // 按pnrNm数据构建passengerId、name（name需要base64加密）
            IssueTicketDto.IssueItemDto issueItem = new IssueTicketDto.IssueItemDto();
            issueItem.setPassengerId("P" + pnrNm.getPsgIndex());
            issueItem.setName(Base64.getEncoder().encodeToString(pnrNm.getName().getBytes(StandardCharsets.UTF_8)));

            // 根据psgType确定passengerType，1设置为CHD，其他设置为ADT
            issueItem.setPassengerType("1".equals(pnrNm.getPsgType()) ? "CHD" : "ADT");
            issueItem.setChineseName(""); // chineseName设置为空字符串
            issueItem.setInfName(""); // 初始设置为空字符串

            issueItems.add(issueItem);

            // 查询mnjx_nm_xn表获取婴儿信息
            List<MnjxNmXn> nmXnList = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getPnrNmId, pnrNm.getPnrNmId())
                    .list();

            // 如果能查到婴儿信息，额外构建一份婴儿的issueItem
            for (MnjxNmXn nmXn : nmXnList) {
                IssueTicketDto.IssueItemDto infIssueItem = new IssueTicketDto.IssueItemDto();
                infIssueItem.setPassengerId("P" + pnrNm.getPsgIndex());
                infIssueItem.setName(Base64.getEncoder().encodeToString(pnrNm.getName().getBytes(StandardCharsets.UTF_8)));
                infIssueItem.setPassengerType("INF"); // 成人旅客携带婴儿
                infIssueItem.setChineseName("");
                infIssueItem.setInfName(Base64.getEncoder().encodeToString(nmXn.getXnCname().getBytes(StandardCharsets.UTF_8))); // 设置婴儿姓名（需要base64加密）

                issueItems.add(infIssueItem);
            }
        }

        // 3. issueItems初步构建完后再对issueItems进行遍历，设置selected状态
        for (IssueTicketDto.IssueItemDto issueItem : issueItems) {
            String passengerId = issueItem.getPassengerId();
            String passengerIndex = passengerId.replace("P", "");

            // 在passengerTickets中查找匹配的数据
            List<DomesticTicketDto.PassengerTicket> matchingTickets = dto.getPassengerTickets().stream()
                    .filter(pt -> passengerIndex.equals(pt.getPassengerIndex()))
                    .collect(Collectors.toList());

            if (!matchingTickets.isEmpty()) {
                // 当前issueItems的infName为空时，并且筛选的passengerTickets中存在hasInf为false的数据
                if (CharSequenceUtil.isEmpty(issueItem.getInfName())) {
                    issueItem.setSelected(matchingTickets.stream().anyMatch(pt -> pt.getHasInf() != null && !pt.getHasInf()));
                } else {
                    // 当前issueItems的infName不为空时，并且筛选的passengerTickets中存在hasInf为true的数据
                    issueItem.setSelected(matchingTickets.stream().anyMatch(DomesticTicketDto.PassengerTicket::getHasInf));
                }
            } else {
                issueItem.setSelected(false);
            }
        }

        issue.setIssueItems(issueItems);

        // 4. 根据所有pnrNmIdList查询mnjx_nm_oi，是否能查到数据
        List<MnjxNmOi> nmOiList = iMnjxNmOiService.lambdaQuery()
                .in(MnjxNmOi::getPnrNmId, pnrNmIdList)
                .list();
        // 如果至少能查到一条，构建出票请求的selectPassengerExistOi设置为true
        issue.setSelectPassengerExistOi(CollUtil.isNotEmpty(nmOiList));

        issueTicketDto.setIssue(issue);

        // 5. 调用出票接口
        IssueTicketVo issueResult = iIssueService.issueTicket(issueTicketDto);

        // 6. 成功后按response构建返回数据
        DomesticTicketVo result = new DomesticTicketVo();
        result.setStatus("SUCCESS");

        List<DomesticTicketVo.PassengerTicketInfo> passengerTicketList = new ArrayList<>();

        // 从出票结果中提取旅客出票信息
        if (issueResult != null && CollUtil.isNotEmpty(issueResult.getBopBookTicketRes())) {
            for (IssueTicketVo.BopBookTicketResVo bopRes : issueResult.getBopBookTicketRes()) {
                if (bopRes.getSuccessType()) {
                    DomesticTicketVo.PassengerTicketInfo ticketInfo = new DomesticTicketVo.PassengerTicketInfo();
                    ticketInfo.setTicketNo("OK"); // 根据response.json设置为"OK"
                    ticketInfo.setPassengerName(bopRes.getPassengerNames());
                    passengerTicketList.add(ticketInfo);
                }
            }
        }

        result.setPassengerTicketList(passengerTicketList);

        return result;
    }

    @Override
    public String updateSegmentRR(UpdateSegmentRRDto dto) throws SguiResultException {
        // 参数校验
        if (dto == null || CharSequenceUtil.isEmpty(dto.getPnrNo())) {
            throw new SguiResultException("PNR编号不能为空");
        }

        // 1. 通过pnrNo查询mnjx_pnr表获取pnrId
        MnjxPnr pnr = iMnjxPnrService.lambdaQuery()
                .eq(MnjxPnr::getPnrCrs, dto.getPnrNo())
                .one();
        if (pnr == null) {
            throw new SguiResultException("未找到对应的PNR信息");
        }

        // 2. 查询mnjx_pnr_seg表，更新action_code为RR，并将input_value的" HK数字 "替换为" RR数字 "
        List<MnjxPnrSeg> pnrSegList = iMnjxPnrSegService.lambdaQuery()
                .eq(MnjxPnrSeg::getPnrId, pnr.getPnrId())
                .ne(MnjxPnrSeg::getPnrSegType, "SA")
                .list();

        if (CollUtil.isEmpty(pnrSegList)) {
            throw new SguiResultException("未找到PNR航段信息");
        }

        // 更新航段信息
        for (MnjxPnrSeg pnrSeg : pnrSegList) {
            // 更新action_code为RR
            pnrSeg.setActionCode("RR");

            // 将input_value的" HK数字 "替换为" RR数字 "，保留原有数字
            if (CharSequenceUtil.isNotEmpty(pnrSeg.getInputValue())) {
                String updatedInputValue = pnrSeg.getInputValue().replaceAll(" HK(\\d+) ", " RR$1 ");
                pnrSeg.setInputValue(updatedInputValue);
            }
        }

        // 批量更新航段信息
        boolean updateSegResult = iMnjxPnrSegService.updateBatchById(pnrSegList);
        if (!updateSegResult) {
            throw new SguiResultException("更新航段信息失败");
        }

        // 3. 按该航段的pnr_index和pnr_id查询mnjx_pnr_record，替换对应的input_value的" HK数字 "为" RR数字 "
        for (MnjxPnrSeg pnrSeg : pnrSegList) {
            List<MnjxPnrRecord> pnrRecordList = iMnjxPnrRecordService.lambdaQuery()
                    .eq(MnjxPnrRecord::getPnrId, pnrSeg.getPnrId())
                    .eq(MnjxPnrRecord::getPnrIndex, pnrSeg.getPnrIndex())
                    .list();

            if (CollUtil.isNotEmpty(pnrRecordList)) {
                for (MnjxPnrRecord pnrRecord : pnrRecordList) {
                    if (CharSequenceUtil.isNotEmpty(pnrRecord.getInputValue())) {
                        String updatedInputValue = pnrRecord.getInputValue().replaceAll(" HK(\\d+) ", " RR$1 ");
                        pnrRecord.setInputValue(updatedInputValue);
                    }
                }

                // 批量更新记录
                boolean updateRecordResult = iMnjxPnrRecordService.updateBatchById(pnrRecordList);
                if (!updateRecordResult) {
                    throw new SguiResultException("更新PNR记录失败");
                }
            }
        }

        return "SUCCESS";
    }

    /**
     * 构建航段信息列表
     */
    private List<GetTicketDigestsByNameVo.AirSeg> buildAirSegList(MnjxPnrNmTicket ticket, MnjxPnr pnr) {
        List<GetTicketDigestsByNameVo.AirSeg> airSegList = new ArrayList<>();

        try {
            // 通过ticketNo查询mnjx_ticket_price表，获取seg_info和issue_info
            MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                    .eq(MnjxTicketPrice::getTicketNo, ticket.getTicketNo())
                    .one();

            if (ticketPrice == null || CharSequenceUtil.isEmpty(ticketPrice.getSegInfo())
                    || CharSequenceUtil.isEmpty(ticketPrice.getIssueInfo())) {
                return airSegList;
            }

            // issueInfo按空格分隔获取最后一条数据，数据示例：1，2，1-2，表示这张票涉及的航段序号
            String[] issueInfoParts = ticketPrice.getIssueInfo().split("\\s+");
            String segmentIndexes = issueInfoParts[issueInfoParts.length - 1];

            // 解析航段序号
            List<Integer> segmentNumbers = this.parseSegmentNumbers(segmentIndexes);

            // segInfo按/分隔，数据示例：CA1311 PEK CTU 2025-08-22 K 0100 0200/CA4119 CTU PEK 2025-08-22 K 0700 0945
            String[] segInfoParts = ticketPrice.getSegInfo().split("/");

            // 通过前面的航段序号拿到对应的航段信息列表，进行遍历构建airSeg
            for (int i = 0; i < segmentNumbers.size() && i < segInfoParts.length; i++) {
                int segmentNumber = segmentNumbers.get(i);
                String segInfo = segInfoParts[i];

                GetTicketDigestsByNameVo.AirSeg airSeg = this.buildAirSeg(segInfo, segmentNumber, ticket, pnr);
                if (airSeg != null) {
                    airSegList.add(airSeg);
                }
            }

        } catch (Exception e) {
            log.error("构建航段信息异常", e);
        }

        return airSegList;
    }

    /**
     * 解析航段序号字符串
     */
    private List<Integer> parseSegmentNumbers(String segmentIndexes) {
        List<Integer> numbers = new ArrayList<>();

        if (CharSequenceUtil.isEmpty(segmentIndexes)) {
            return numbers;
        }

        try {
            if (segmentIndexes.contains("-")) {
                // 处理范围格式，如 "1-2"
                String[] range = segmentIndexes.split("-");
                int start = Integer.parseInt(range[0]);
                int end = Integer.parseInt(range[1]);
                for (int i = start; i <= end; i++) {
                    numbers.add(i);
                }
            } else if (segmentIndexes.contains(",")) {
                // 处理逗号分隔格式，如 "1,3"
                String[] parts = segmentIndexes.split(",");
                for (String part : parts) {
                    numbers.add(Integer.parseInt(part.trim()));
                }
            } else {
                // 单个数字
                numbers.add(Integer.parseInt(segmentIndexes));
            }
        } catch (Exception e) {
            log.error("解析航段序号异常: {}", segmentIndexes, e);
        }

        return numbers;
    }

    /**
     * 构建单个航段信息
     */
    private GetTicketDigestsByNameVo.AirSeg buildAirSeg(String segInfo, int segmentNumber, MnjxPnrNmTicket ticket, MnjxPnr pnr) {
        try {
            // 解析segInfo，格式：CA1311 PEK CTU 2025-08-22 K 0100 0200
            String[] parts = segInfo.trim().split("\\s+");
            if (parts.length < 4) {
                return null;
            }

            GetTicketDigestsByNameVo.AirSeg airSeg = new GetTicketDigestsByNameVo.AirSeg();

            // status：票面状态，当前航段序号是单数时，获取ticketStatus1，当前航段序号是双数获取ticketStatus2
            String status;
            if (segmentNumber % 2 == 1) {
                status = ticket.getTicketStatus1();
            } else {
                status = ticket.getTicketStatus2();
            }
            airSeg.setStatus(status);

            // airlineCode：下标0的前两位
            String flightInfo = parts[0];
            if (flightInfo.length() >= 2) {
                airSeg.setAirlineCode(flightInfo.substring(0, 2));
            }

            // fltNo：下标0的后两位；当status是EXCHANGED或REFUNDED、或pnrStatus是DEL的时候设置为OPEN
            if ("EXCHANGED".equals(status) || "REFUNDED".equals(status) || "DEL".equals(pnr.getPnrStatus())) {
                airSeg.setFltNo("OPEN");
            } else if (flightInfo.length() > 2) {
                airSeg.setFltNo(flightInfo.substring(2));
            }

            // depAirportCode：下标1
            airSeg.setDepAirportCode(parts[1]);

            // arrAirportCode：下标2
            airSeg.setArrAirportCode(parts[2]);

            // depDate：下标3；当status是EXCHANGED或REFUNDED、或pnrStatus是DEL的时候设置为""
            if ("EXCHANGED".equals(status) || "REFUNDED".equals(status) || "DEL".equals(pnr.getPnrStatus())) {
                airSeg.setDepDate("");
            } else {
                airSeg.setDepDate(parts[3]);
            }

            // operateAirline：null
            airSeg.setOperateAirline(null);

            return airSeg;

        } catch (Exception e) {
            log.error("构建航段信息异常: {}", segInfo, e);
            return null;
        }
    }
}
