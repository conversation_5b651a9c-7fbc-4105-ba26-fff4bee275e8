package com.swcares.service.et.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.mapper.EtV2Mapper;
import com.swcares.obj.dto.QuerySkDto;
import com.swcares.obj.vo.SkVo;
import com.swcares.service.*;
import com.swcares.service.et.IEtCommonService;
import com.swcares.service.et.IEtSkService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:46
 */
@Service
public class EtSkServiceImpl implements IEtSkService {

    // 舱等排序
    private static final Map<String, Integer> CABIN_ORDER = new HashMap<>();

    static {
        CABIN_ORDER.put("J", 1); // 头等舱
        CABIN_ORDER.put("G", 2); // 公务舱
        CABIN_ORDER.put("Y", 3); // 经济舱
    }

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IEtCommonService iEtCommonService;

    @Resource
    private EtV2Mapper etV2Mapper;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxPlaneService iMnjxPlaneService;

    @Resource
    private IMnjxPlaneModelService iMnjxPlaneModelService;

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Override
    public SkVo querySk(QuerySkDto skDto) throws SguiResultException {
        List<String> departureAirportIdList = iSguiCommonService.getAirportIdList(skDto.getDepartureCity());
        List<String> arriveAirportIdList = iSguiCommonService.getAirportIdList(skDto.getArriveCity());

        // 如果输入了中转点，强制查询中转航班，不查询直达航班
        if (CharSequenceUtil.isNotEmpty(skDto.getTransitCity())) {
            // 勾选仅直飞不查询中转，这里实际不会走这个逻辑，前端已经限制了输入中转点不允许勾选仅直飞，但是接口还是做个判断处理
            if (CharSequenceUtil.isNotEmpty(skDto.getOnlyDirect())) {
                return null;
            }
            return this.transit(departureAirportIdList, arriveAirportIdList, skDto);
        } else {
            return this.queryNormalSk(departureAirportIdList, arriveAirportIdList, skDto, true);
        }
    }

    /**
     * Title: transit
     * Description: SK查询中转处理<br>
     *
     * @param departureAirportIdList
     * @param arriveAirportIdList
     * @param skDto
     * @return {@link SkVo}
     * <AUTHOR>
     * @date 2025/5/6 16:55
     */
    private SkVo transit(List<String> departureAirportIdList, List<String> arriveAirportIdList, QuerySkDto skDto) throws SguiResultException {
        SkVo skVo = new SkVo();
        List<String> transitAirportIdList = new ArrayList<>();
        // 如果输入了中转点，按中转城市处理。如CTU-SHA，中转城市为PEK，查询所有CTU-PEK,PEK-SHA城市的航班组合
        if (CharSequenceUtil.isNotEmpty(skDto.getTransitCity())) {
            transitAirportIdList = iSguiCommonService.getAirportIdList(skDto.getTransitCity());
        }
        // 没有输入则表示正常查直飞没有查询到，选择所有符合到达的中转城市。如CTU-SHA，查询所有CTU-PEK,PEK-SHA、CTU-SZX,SZX-SHA等等CTU-XXX,XXX-SHA城市的航班组合
        else {
            List<MnjxPlanFlight> planFlightList = iMnjxPlanFlightService.lambdaQuery()
                    .eq(MnjxPlanFlight::getFlightDate, skDto.getDepartureDate())
                    .list();
            List<String> planFlightIdList = planFlightList.stream()
                    .map(MnjxPlanFlight::getPlanFlightId)
                    .collect(Collectors.toList());

            // CTU-XXX
            List<MnjxPlanSection> departurePlanSectionList = iMnjxPlanSectionService.lambdaQuery()
                    .in(MnjxPlanSection::getPlanFlightId, planFlightIdList)
                    .in(MnjxPlanSection::getDepAptId, departureAirportIdList)
                    .notIn(MnjxPlanSection::getArrAptId, arriveAirportIdList)
                    .list();
            // 提取有经停的航班计划id
            List<String> haveStopDeparturePlanFlightIdList = departurePlanSectionList.stream()
                    .filter(a -> a.getIsLastSection() == 0)
                    .map(MnjxPlanSection::getPlanFlightId)
                    .collect(Collectors.toList());
            // 经停的需要取尾段的到达机场id作为中转机场id
            transitAirportIdList.addAll(iMnjxPlanSectionService.lambdaQuery()
                    .in(MnjxPlanSection::getPlanFlightId, haveStopDeparturePlanFlightIdList)
                    .eq(MnjxPlanSection::getIsLastSection, 1)
                    .list()
                    .stream()
                    .map(MnjxPlanSection::getArrAptId)
                    .collect(Collectors.toList()));
            // 没有经停的直接取到达机场id作为中转机场id
            transitAirportIdList.addAll(departurePlanSectionList.stream()
                    .filter(a -> !haveStopDeparturePlanFlightIdList.contains(a.getPlanFlightId()))
                    .map(MnjxPlanSection::getArrAptId)
                    .collect(Collectors.toList()));


            // XXX-SHA
            List<MnjxPlanSection> arrivePlanSectionList = iMnjxPlanSectionService.lambdaQuery()
                    .in(MnjxPlanSection::getPlanFlightId, planFlightIdList)
                    .notIn(MnjxPlanSection::getDepAptId, departureAirportIdList)
                    .in(MnjxPlanSection::getArrAptId, arriveAirportIdList)
                    .list();
            // 提取有经停的航班计划id
            List<String> haveStopArrivePlanFlightIdList = arrivePlanSectionList.stream()
                    .filter(a -> a.getIsLastSection() == 0)
                    .map(MnjxPlanSection::getPlanFlightId)
                    .collect(Collectors.toList());
            // 经停的需要取首段的出发机场id作为中转机场id
            if (CollUtil.isNotEmpty(haveStopArrivePlanFlightIdList)) {
                transitAirportIdList.addAll(iMnjxPlanSectionService.lambdaQuery()
                        .in(MnjxPlanSection::getPlanFlightId, haveStopArrivePlanFlightIdList)
                        .list()
                        .stream()
                        .collect(Collectors.groupingBy(MnjxPlanSection::getPlanFlightId, Collectors.minBy(Comparator.comparing(MnjxPlanSection::getEstimateOff))))
                        .values()
                        .stream()
                        .map(Optional::get)
                        .map(MnjxPlanSection::getDepAptId)
                        .collect(Collectors.toList()));
            }
            // 没有经停的直接取出发机场id作为中转机场id
            transitAirportIdList.addAll(arrivePlanSectionList.stream()
                    .filter(a -> !haveStopArrivePlanFlightIdList.contains(a.getPlanFlightId()))
                    .map(MnjxPlanSection::getDepAptId)
                    .collect(Collectors.toList()));

            // 去重
            transitAirportIdList = transitAirportIdList.stream().distinct().collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(transitAirportIdList)) {
            return null;
        }
        // 查出发到中转的数据
        SkVo departureSkVo = this.queryNormalSk(departureAirportIdList, transitAirportIdList, skDto, false);
        // 查中转到到达的数据
        SkVo arriveSkVo = this.queryNormalSk(transitAirportIdList, arriveAirportIdList, skDto, false);
        if (ObjectUtil.isEmpty(departureSkVo) || ObjectUtil.isEmpty(arriveSkVo)) {
            return null;
        }
        // 合并departureSkVo和arriveSkVo
        skVo.setOriginData(departureSkVo.getOriginData());
        skVo.setEndData(arriveSkVo.getEndData());
        List<SkVo.Voyage> voyageList = skVo.getVoyage();
        departureSkVo.getVoyage().forEach(departureVoyage -> {
            SkVo.Segment departureSegment = departureVoyage.getSegments().get(0);
            // 不组合共享航班
            if (Boolean.FALSE.equals(departureSegment.getAirlines().getIsShared())) {
                arriveSkVo.getVoyage().forEach(arriveVoyage -> {
                    SkVo.Segment arriveSegment = arriveVoyage.getSegments().get(0);
                    // 不组合共享航班
                    if (Boolean.FALSE.equals(arriveSegment.getAirlines().getIsShared())) {
                        DateTime arriveTime = DateUtil.parseTimeToday(departureSegment.getArrivalTime());
                        DateTime departureTime = DateUtil.parseTimeToday(arriveSegment.getDepartureTime());
                        long minute = DateUtil.between(arriveTime, departureTime, DateUnit.MINUTE);
                        if (arriveSegment.getDepartureAirportCode().equals(departureSegment.getArrivalAirportCode())
                                && Boolean.FALSE.equals(arriveSegment.getAirlines().getIsShared())
                                && arriveTime.isBefore(departureTime)
                                && (minute >= 60 && minute <= 6 * 60)) {
                            SkVo.Voyage voyage = new SkVo.Voyage();
                            voyage.getSegments().add(departureSegment);
                            voyage.getSegments().add(arriveSegment);
                            voyage.setStopCity(StrUtil.toString(departureSegment.getStopNum() + arriveSegment.getStopNum()));
                            voyageList.add(voyage);
                        }
                    }
                });
            }
        });
        return skVo;
    }

    /**
     * Title: queryNormalSk
     * Description: SK查询处理<br>
     *
     * @param departureAirportIdList
     * @param arriveAirportIdList
     * @param skDto
     * @param allowInvokeTransit
     * @return {@link SkVo}
     * <AUTHOR>
     * @date 2025/5/6 16:55
     */
    private SkVo queryNormalSk(List<String> departureAirportIdList, List<String> arriveAirportIdList, QuerySkDto skDto, boolean allowInvokeTransit) throws SguiResultException {
        if (CollUtil.isEmpty(departureAirportIdList) || CollUtil.isEmpty(arriveAirportIdList)) {
            return null;
        }
        SkVo skVo = new SkVo();
        // 查询mnjx_tcard_section表筛选 符合条件的tcard id，出发机场列表和到达机场列表的所有组合
        List<String> tcardIds = etV2Mapper.retrieveTcardIdsByCityPair(departureAirportIdList, arriveAirportIdList);
        if (CollUtil.isEmpty(tcardIds)) {
            // 勾选仅直飞不查询中转直接返回空，或者不允许再调用查询中转（该方法已经由中转进行调用了）直接返回空
            if (CharSequenceUtil.isNotEmpty(skDto.getOnlyDirect()) || !allowInvokeTransit) {
                return null;
            }
            // 查不到城市对的航班时，查询中转
            return this.transit(departureAirportIdList, arriveAirportIdList, skDto);
        }
        // 查询mnjx_tcard表
        List<MnjxTcard> tcardList = iMnjxTcardService.listByIds(tcardIds);

        // 查询航班计划
        List<MnjxPlanFlight> planFlightList = iEtCommonService.retrievePlanFlight(skDto.getDepartureDate(), tcardIds);
        if (CollUtil.isEmpty(planFlightList)) {
            // 勾选仅直飞不查询中转
            if (CharSequenceUtil.isNotEmpty(skDto.getOnlyDirect()) || !allowInvokeTransit) {
                return null;
            }
            // 查不到城市对的航班时，查询中转
            return this.transit(departureAirportIdList, arriveAirportIdList, skDto);
        }
        // 获取航班计划ID
        List<String> planFlightIdList = planFlightList.stream()
                .map(MnjxPlanFlight::getPlanFlightId)
                .collect(Collectors.toList());
        // 获取航节计划，并排序
        List<MnjxPlanSection> planSectionList = iEtCommonService.retrievePlanSection(skDto.getDepartureTime(), planFlightIdList);
        if (CollUtil.isEmpty(planSectionList)) {
            // 勾选仅直飞不查询中转
            if (CharSequenceUtil.isNotEmpty(skDto.getOnlyDirect()) || !allowInvokeTransit) {
                return null;
            }
            // 查不到城市对的航班时，查询中转
            return this.transit(departureAirportIdList, arriveAirportIdList, skDto);
        }
        List<String> planSectionIdList = planSectionList.stream()
                .map(MnjxPlanSection::getPlanSectionId)
                .collect(Collectors.toList());

        // 先查所有的开舱数据，后面进行筛选
        List<MnjxOpenCabin> openCabinList = iMnjxOpenCabinService.lambdaQuery()
                .in(MnjxOpenCabin::getPlanSectionId, planSectionIdList)
                .list();
        // 查询飞机和飞机模型
        List<String> planeIdList = planSectionList.stream()
                .map(MnjxPlanSection::getPlaneId)
                .collect(Collectors.toList());
        List<MnjxPlane> planes = iMnjxPlaneService.listByIds(planeIdList);
        List<String> planeModelIdList = planes.stream()
                .map(MnjxPlane::getPlaneModelId)
                .collect(Collectors.toList());
        List<MnjxPlaneModel> planeModels = iMnjxPlaneModelService.listByIds(planeModelIdList);
        // 获取需要用到的数据
        List<MnjxFlight> flights = iMnjxFlightService.list();
        List<MnjxAirport> airports = iMnjxAirportService.list();
        //航班日期的前三天
        Date beforeDate = DateUtil.offsetDay(DateUtils.ymd2Date(skDto.getDepartureDate()), -Constant.THREE);
        skVo.setOriginData(DateUtil.toLocalDateTime(beforeDate));

        //航班日期的后三天
        DateTime nextDate = DateUtil.offsetDay(DateUtils.ymd2Date(skDto.getDepartureDate()), Constant.THREE);
        skVo.setEndData(DateUtil.toLocalDateTime(nextDate));

        // 处理返回数据
        for (MnjxPlanFlight planFlight : planFlightList) {
            SkVo.Voyage voyage = new SkVo.Voyage();
            SkVo.Segment segment = new SkVo.Segment();

            //Tcard
            String tcardId = planFlight.getTcardId();
            MnjxTcard tcard = tcardList.stream()
                    .filter(t -> t.getTcardId().equals(tcardId))
                    .collect(Collectors.toList())
                    .get(0);
            String flightId = tcard.getFlightId();
            //航班
            MnjxFlight flight = flights.stream()
                    .filter(f -> f.getFlightId().equals(flightId))
                    .collect(Collectors.toList())
                    .get(0);

            SkVo.Airlines airlines = new SkVo.Airlines();
            airlines.setFlightNo(flight.getFlightNo().substring(2));
            airlines.setAirCode(flight.getFlightNo().substring(0, 2));
            airlines.setIsShared(Constant.STR_ONE.equals(flight.getShareState()));
            airlines.setDepartureDate(DateUtil.formatDate(tcard.getStartDate()));
            airlines.setArrivalDate(DateUtil.formatDate(tcard.getEndDate()));
            String schedule = "每天飞";
            String cycle = tcard.getCycle();
            Map<Object, Object> build = MapUtil.builder().put("1", "一")
                    .put("2", "二")
                    .put("3", "三")
                    .put("4", "四")
                    .put("5", "五")
                    .put("6", "六")
                    .put("7", "日")
                    .build();
            if (!"D".equals(cycle)) {
                if (cycle.contains("X")) {
                    cycle = cycle.replace("X", "");
                    StringBuilder sb = new StringBuilder("每周");
                    for (int i = 0; i < 6; i++) {
                        if (!cycle.contains(StrUtil.toString(i + 1))) {
                            if (i > 0) {
                                sb.append("、");
                            }
                            sb.append(build.get(StrUtil.toString(i + 1)));
                        }
                    }
                    sb.append("飞");
                    schedule = sb.toString();
                } else {
                    String[] split = cycle.split("");
                    StringBuilder sb = new StringBuilder("每周");
                    for (int i = 0; i < split.length; i++) {
                        if (i > 0) {
                            sb.append("、");
                        }
                        sb.append(build.get(StrUtil.toString(i + 1)));
                    }
                    sb.append("飞");
                    schedule = sb.toString();
                }
            }
            airlines.setSchedule(schedule);
            segment.setAirlines(airlines);
            segment.setEt("E".equals(flight.getIsE()));
            segment.setLnk("DS#");

            //计划航节
            List<MnjxPlanSection> multiPlanSectionList = planSectionList.stream()
                    .filter(p -> p.getPlanFlightId().equals(planFlight.getPlanFlightId()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(multiPlanSectionList)) {
                continue;
            }
            // 获取所有的开舱数据
            List<MnjxOpenCabin> mnjxOpenCabins = iEtCommonService.getOpenCabins(multiPlanSectionList, departureAirportIdList, arriveAirportIdList, openCabinList);
            // 如果开舱数据是空的，说明该航班的出发到达和查询的是相反的或不符合输入城市对的航班，跳过
            if (CollUtil.isEmpty(mnjxOpenCabins)) {
                continue;
            }

            segment.setAsr(Constant.STR_ONE.equals(planFlight.getAllowAsr()) ? "^" : "");
            // 设置返回数据出发到达信息
            this.setSkResultDto(multiPlanSectionList, planes, planeModels, segment, airports, departureAirportIdList, arriveAirportIdList);
            // 设置返回销售舱位
            this.setSellCabinAndSeat(segment, mnjxOpenCabins);

            voyage.setStopCity(segment.getStopNum().toString());
            voyage.getSegments().add(segment);
            skVo.getVoyage().add(voyage);
        }
        // 按条件进行筛选与排序，并将结果设置到unifiedResult
        List<SkVo.Voyage> voyageList = skVo.getVoyage();
        voyageList = this.handleByParams(voyageList, skDto);
        if (CollUtil.isEmpty(voyageList)) {
            if (!allowInvokeTransit) {
                return null;
            }
            // 筛选后查不到城市对的航班时，查询中转
            return this.transit(departureAirportIdList, arriveAirportIdList, skDto);
        }
        skVo.setVoyage(voyageList);
        List<SkVo.Voyage> resultList = new ArrayList<>();
        if (Boolean.FALSE.equals(skDto.getUnsharedFlight())) {
            // 排序完成后插入共享航班
            for (SkVo.Voyage voyage : voyageList) {
                resultList.add(voyage);
                for (SkVo.Segment segment : voyage.getSegments()) {
                    String flightNo = segment.getAirlines().getAirCode() + segment.getAirlines().getFlightNo();
                    // 如果存在共享航班
                    if (flights.stream().anyMatch(f -> flightNo.equals(f.getCarrierFlight()) && Constant.STR_ONE.equals(f.getShareState()))) {
                        List<MnjxFlight> shareFlights = flights.stream()
                                .filter(f -> flightNo.equals(f.getCarrierFlight()) && Constant.STR_ONE.equals(f.getShareState()))
                                .collect(Collectors.toList());
                        for (MnjxFlight shareFlight : shareFlights) {
                            SkVo.Voyage shareVoyage = new SkVo.Voyage();
                            List<SkVo.Segment> shareSegmentList = new ArrayList<>();
                            BeanUtil.copyProperties(voyage, shareVoyage);
                            voyage.getSegments().forEach(s -> {
                                SkVo.Segment shareSegment = new SkVo.Segment();
                                BeanUtil.copyProperties(s, shareSegment);

                                SkVo.Airlines shareAirlines = new SkVo.Airlines();
                                BeanUtil.copyProperties(s.getAirlines(), shareAirlines);

                                List<SkVo.Cabin> shareCabinList = new ArrayList<>();
                                s.getCabins().forEach(c -> {
                                    SkVo.Cabin shareCabin = new SkVo.Cabin();
                                    BeanUtil.copyProperties(c, shareCabin);
                                    shareCabinList.add(shareCabin);
                                });

                                shareSegment.setCabins(shareCabinList);
                                shareSegment.setAirlines(shareAirlines);
                                shareSegmentList.add(shareSegment);
                            });
                            shareVoyage.setSegments(shareSegmentList);
                            shareVoyage.getSegments().forEach(s -> {
                                s.getAirlines().setAirCode(shareFlight.getFlightNo().substring(0, 2));
                                s.getAirlines().setFlightNo(shareFlight.getFlightNo().substring(2));
                                s.getAirlines().setIsShared(true);
                            });
                            resultList.add(shareVoyage);
                        }
                    }
                }
            }
            skVo.setVoyage(resultList);
        }
        // 处理时间分割符号
        skVo.getVoyage().stream()
                .flatMap(s -> s.getSegments().stream())
                .forEach(s -> {
                    s.setDepartureTime(CharSequenceUtil.format("{}:{}", s.getDepartureTime().substring(0, 2), s.getDepartureTime().substring(2)));
                    s.setArrivalTime(CharSequenceUtil.format("{}:{}", s.getArrivalTime().substring(0, 2), s.getArrivalTime().substring(2)));
                });
        return skVo;
    }

    /**
     * 对查询结果进行参数过滤
     *
     * @param voyageList 所有航班情况
     * @param skDto      查询条件
     * @return 过滤后的结果
     */
    private List<SkVo.Voyage> handleByParams(List<SkVo.Voyage> voyageList, QuerySkDto skDto) {
        Stream<SkVo.Voyage> voyageStream = voyageList.stream();
        // 起飞时间筛选
        if (CharSequenceUtil.isNotEmpty(skDto.getDepartureTime())) {
            voyageStream = voyageStream
                    .filter(a -> a.getSegments().stream()
                            .anyMatch(s -> Integer.parseInt(s.getDepartureTime().replace(":", "")) >= Integer.parseInt(skDto.getDepartureTime().replace(":", "").substring(0, 4)))
                    );
        }
        // 航司筛选
        if (CharSequenceUtil.isNotEmpty(skDto.getAirCode())) {
            voyageStream = voyageStream
                    .filter(a -> a.getSegments().stream()
                            .anyMatch(s -> s.getAirlines().getAirCode().equals(skDto.getAirCode()))
                    );
        }
        // 经停、直达
        if (CharSequenceUtil.isNotEmpty(skDto.getOnlyDirect())) {
            voyageStream = voyageStream.filter(a -> "0".equals(a.getStopCity()));
        }
        voyageList = voyageStream.collect(Collectors.toList());
        // 按segment中最小的出发时间进行排序
        voyageList = voyageList.stream()
                .sorted(Comparator.comparingInt(b ->
                        b.getSegments().stream()
                                .mapToInt(c -> Integer.parseInt(c.getDepartureTime().replace(":", "")))
                                .min()
                                .orElse(9999)
                ))
                .collect(Collectors.toList());
        return voyageList;
    }

    /**
     * Title: setSellCabinAndSeat
     * Description: 设置回显销售舱位和座位数
     *
     * @param segment    segment
     * @param openCabins openCabins
     * <AUTHOR>
     */
    private void setSellCabinAndSeat(SkVo.Segment segment, List<MnjxOpenCabin> openCabins) {
        // 开舱按舱等排序，按价格降序排序
        openCabins = openCabins.stream()
                .sorted(Comparator.comparing((MnjxOpenCabin o) -> CABIN_ORDER.getOrDefault(o.getCabinClass(), Integer.MAX_VALUE))
                        .thenComparing(Comparator.comparing(MnjxOpenCabin::getSellCabinPrice).reversed())
                )
                .collect(Collectors.toList());
        for (MnjxOpenCabin mnjxOpenCabin : openCabins) {
            SkVo.Cabin cabin = new SkVo.Cabin();
            cabin.setCabinName(mnjxOpenCabin.getSellCabin());
            cabin.setState(mnjxOpenCabin.getOpenCabinStatus());
            segment.getCabins().add(cabin);
        }
        segment.setCabins(segment.getCabins().stream()
                .distinct()
                .collect(Collectors.toList()));
    }

    /**
     * Title: setSkResultDto
     * Description: 设置返回数据中的出发到达和经停信息
     *
     * @param planSections planSections
     * @param planes       planes
     * @param planeModels  planeModels
     * @param segment      segment
     * @param airports     airports
     * @param orgs         orgs
     * @param dsts         dsts
     * <AUTHOR>
     */
    private void setSkResultDto(List<MnjxPlanSection> planSections, List<MnjxPlane> planes, List<MnjxPlaneModel> planeModels,
                                SkVo.Segment segment, List<MnjxAirport> airports, List<String> orgs, List<String> dsts) {
        int stopPoint = 0;
        // 标识根据查询条件找到这个多航段航班有从指令输入开始的航站，开始记录
        boolean findFirstAirport = false;
        for (MnjxPlanSection mnjxPlanSection : planSections) {
            segment.setCommonMeal(mnjxPlanSection.getMealCode());
            this.setPlaneModelType(planes, planeModels, segment, mnjxPlanSection);
            MnjxAirport orgAirport = airports.stream()
                    .filter(a -> a.getAirportId().equals(mnjxPlanSection.getDepAptId()))
                    .collect(Collectors.toList())
                    .get(0);
            MnjxAirport dstAirport = airports.stream()
                    .filter(a -> a.getAirportId().equals(mnjxPlanSection.getArrAptId()))
                    .collect(Collectors.toList())
                    .get(0);
            // 输入的航段是多航段中的某一个航段，或者是单航段
            if (orgs.contains(mnjxPlanSection.getDepAptId()) && dsts.contains(mnjxPlanSection.getArrAptId())) {
                // 构建出发到达信息
                this.setOrgDstInfo(segment, orgAirport, dstAirport, mnjxPlanSection);
            }
            // 输入的航段是多航段中多个航段的组合
            else {
                // 出发
                if (orgs.contains(mnjxPlanSection.getDepAptId())) {
                    findFirstAirport = true;
                    segment.setDepartureAirportCode(orgAirport.getAirportCode());
                    segment.setDepartureTime(mnjxPlanSection.getEstimateOff());
                }
                // 到达
                else if (dsts.contains(mnjxPlanSection.getArrAptId()) && findFirstAirport) {
                    stopPoint++;
                    segment.setArrivalAirportCode(dstAirport.getAirportCode());
                    segment.setArrivalTime(mnjxPlanSection.getEstimateArr());
                    segment.setSegDays(CharSequenceUtil.isNotEmpty(mnjxPlanSection.getEstimateArrChange()) ? "1" : "0");
                }
                // 如果都没有匹配到，说明是中间航段，也需要把开舱数据加进去进行筛选
                else if (findFirstAirport) {
                    stopPoint++;
                }
            }
        }
        segment.setStopNum(stopPoint);
    }

    /**
     * Title: setOrgDstInfo
     * Description: 设置出发到达数据
     *
     * @param segment         segment
     * @param orgAirport      orgAirport
     * @param dstAirport      dstAirport
     * @param mnjxPlanSection mnjxPlanSection
     * <AUTHOR>
     */
    private void setOrgDstInfo(SkVo.Segment segment, MnjxAirport orgAirport, MnjxAirport dstAirport, MnjxPlanSection mnjxPlanSection) {
        segment.setDepartureAirportCode(orgAirport.getAirportCode());
        segment.setDepartureTime(mnjxPlanSection.getEstimateOff());
        segment.setArrivalAirportCode(dstAirport.getAirportCode());
        segment.setArrivalTime(mnjxPlanSection.getEstimateArr());
        segment.setSegDays(CharSequenceUtil.isNotEmpty(mnjxPlanSection.getEstimateArrChange()) ? "1" : "0");
    }

    /**
     * Title: setPlaneModelType
     * Description: 设置机型数据
     *
     * @param planes          planes
     * @param planeModels     planeModels
     * @param segment         segment
     * @param mnjxPlanSection mnjxPlanSection
     * <AUTHOR>
     */
    private void setPlaneModelType(List<MnjxPlane> planes, List<MnjxPlaneModel> planeModels, SkVo.Segment segment, MnjxPlanSection mnjxPlanSection) {
        MnjxPlane plane = planes.stream()
                .filter(p -> p.getPlaneId().equals(mnjxPlanSection.getPlaneId()))
                .collect(Collectors.toList())
                .get(0);
        MnjxPlaneModel planeModel = planeModels.stream()
                .filter(p -> p.getPlaneModelId().equals(plane.getPlaneModelId()))
                .collect(Collectors.toList())
                .get(0);
        segment.getAirlines().setPlaneType(planeModel.getPlaneModelType());
    }
}
