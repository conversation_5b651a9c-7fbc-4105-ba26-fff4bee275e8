package com.swcares.service.et.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.entity.MnjxOpenCabin;
import com.swcares.entity.MnjxPlanFlight;
import com.swcares.entity.MnjxPlanSection;
import com.swcares.entity.MnjxStandardPat;
import com.swcares.service.IMnjxPlanFlightService;
import com.swcares.service.IMnjxPlanSectionService;
import com.swcares.service.IMnjxStandardPatService;
import com.swcares.service.et.IEtCommonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 航班查询公共服务实现类
 *
 * <AUTHOR>
 * @date 2025/5/8 13:51
 */
@Service
public class EtCommonServiceImpl implements IEtCommonService {

    @Resource
    private IMnjxStandardPatService iMnjxStandardPatService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Override
    public Integer getFlightDistance(String depCityId, String arrCityId) {
        if (CharSequenceUtil.isEmpty(depCityId) || CharSequenceUtil.isEmpty(arrCityId)) {
            return null;
        }

        // 先查询正向的距离
        MnjxStandardPat standardPat = iMnjxStandardPatService.lambdaQuery()
                .eq(MnjxStandardPat::getOrgCityId, depCityId)
                .eq(MnjxStandardPat::getDstCityId, arrCityId)
                .one();

        if (standardPat != null && standardPat.getDistance() != null) {
            return standardPat.getDistance();
        }

        // 如果没有找到，则查询反向的距离
        standardPat = iMnjxStandardPatService.lambdaQuery()
                .eq(MnjxStandardPat::getOrgCityId, arrCityId)
                .eq(MnjxStandardPat::getDstCityId, depCityId)
                .one();

        if (standardPat != null && standardPat.getDistance() != null) {
            return standardPat.getDistance();
        }

        // 如果仍然没有找到，则返回null
        return null;
    }

    @Override
    public List<MnjxOpenCabin> getOpenCabins(List<MnjxPlanSection> planSections, List<String> orgs, List<String> dsts, List<MnjxOpenCabin> openCabinList) {
        // 记录所有航节的开舱数据
        List<MnjxOpenCabin> allOpenCabinList = new ArrayList<>();

        // 如果出发机场和到达机场都为空，说明是通过航班号查询，直接返回所有开舱数据
        if (orgs == null || dsts == null) {
            for (MnjxPlanSection planSection : planSections) {
                List<MnjxOpenCabin> filterOpenCabinList = openCabinList.stream()
                        .filter(o -> o.getPlanSectionId().equals(planSection.getPlanSectionId()))
                        .collect(Collectors.toList());
                allOpenCabinList.addAll(filterOpenCabinList);
            }
            return allOpenCabinList;
        }

        if (planSections.size() == 1) {
            //单航段
            if (orgs.contains(planSections.get(0).getDepAptId()) && dsts.contains(planSections.get(0).getArrAptId())) {
                List<MnjxOpenCabin> filterOpenCabinList = openCabinList.stream()
                        .filter(o -> o.getPlanSectionId().equals(planSections.get(0).getPlanSectionId()))
                        .collect(Collectors.toList());
                allOpenCabinList.addAll(filterOpenCabinList);
            }
        } else {
            boolean findStart = false;
            boolean findEnd = false;
            for (MnjxPlanSection mnjxPlanSection : planSections) {
                String planSectionId = mnjxPlanSection.getPlanSectionId();
                // 输入的航段是多航段中的某一个航段
                if (orgs.contains(mnjxPlanSection.getDepAptId()) && dsts.contains(mnjxPlanSection.getArrAptId())) {
                    List<MnjxOpenCabin> filterOpenCabinList = openCabinList.stream()
                            .filter(o -> o.getPlanSectionId().equals(planSectionId))
                            .collect(Collectors.toList());
                    allOpenCabinList.addAll(filterOpenCabinList);
                    findStart = true;
                    findEnd = true;
                    break;
                }
                // 输入的航段是多航段中多个航段的组合
                else {
                    List<MnjxOpenCabin> filterOpenCabinList = openCabinList.stream()
                            .filter(o -> o.getPlanSectionId().equals(planSectionId))
                            .collect(Collectors.toList());
                    // 出发
                    if (orgs.contains(mnjxPlanSection.getDepAptId())) {
                        allOpenCabinList.addAll(filterOpenCabinList);
                        findStart = true;
                    }
                    // 到达
                    else if (dsts.contains(mnjxPlanSection.getArrAptId())) {
                        allOpenCabinList.addAll(filterOpenCabinList);
                        findEnd = true;
                        break;
                    } else {
                        if (CollUtil.isNotEmpty(allOpenCabinList)) {
                            allOpenCabinList.addAll(filterOpenCabinList);
                        }
                    }
                }
            }
            if (!findEnd || !findStart) {
                return new ArrayList<>();
            }
        }
        return allOpenCabinList;
    }


    /**
     * 查询航班计划
     *
     * @param date     查询参数
     * @param tcardIds 航节ID列表
     * @return 航班计划列表
     */
    @Override
    public List<MnjxPlanFlight> retrievePlanFlight(String date, List<String> tcardIds) {
        return iMnjxPlanFlightService.lambdaQuery()
                .in(MnjxPlanFlight::getTcardId, tcardIds)
                .eq(MnjxPlanFlight::getFlightDate, date)
                .list();
    }

    /**
     * 查询计划航节，并排序
     */
    @Override
    public List<MnjxPlanSection> retrievePlanSection(String time, List<String> planFlightIdList) {
        return iMnjxPlanSectionService.lambdaQuery()
                .in(MnjxPlanSection::getPlanFlightId, planFlightIdList)
                .ge(CharSequenceUtil.isNotEmpty(time), MnjxPlanSection::getEstimateOff, time)
                .orderByAsc(MnjxPlanSection::getPlanFlightId)
                .orderByAsc(MnjxPlanSection::getIsLastSection)
                .orderByAsc(MnjxPlanSection::getEstimateOffChange)
                .orderByAsc(MnjxPlanSection::getEstimateOff)
                .list();
    }
}