package com.swcares.service.sor.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.constants.PatternRegexConstants;
import com.swcares.core.constants.PnrExceptionConstants;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.security.custom.UserInfo;
import com.swcares.core.util.DateUtils;
import com.swcares.entity.*;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.*;
import com.swcares.service.bkc.IAgencyFrequentService;
import com.swcares.service.sor.IFareDomesticService;
import com.swcares.service.sor.ISorV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Slf4j
@Service
public class SorV2ServiceImpl implements ISorV2Service {

    @Resource
    private ISguiDataService iSguiDataService;

    @Resource
    private ISguiCommonService iSguiCommonService;

    @Resource
    private IFareDomesticService iFareDomesticService;

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;

    @Resource
    private IMnjxPnrNmService iMnjxPnrNmService;

    @Resource
    private IMnjxNmXnService iMnjxNmXnService;

    @Resource
    private IMnjxReschedulingRulesService iMnjxReschedulingRulesService;

    @Resource
    private IMnjxAgentService iMnjxAgentService;

    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private IMnjxSiService iMnjxSiService;

    @Resource
    private IAgencyFrequentService iAgencyFrequentService;

    @Resource
    private IMnjxCnYqService iMnjxCnYqService;

    @Resource
    private IMnjxStandardPatService iMnjxStandardPatService;

    @Resource
    private IMnjxTicketPriceService iMnjxTicketPriceService;

    @Resource
    private IMnjxNmSsrService iMnjxNmSsrService;

    @Override
    public String computeInterAirPrice(ComputeInterAirPriceDto dto) {
        return "调用SAT接口，解析返回结果异常";
    }

    @Override
    public QueryFareDomesticVo queryFareDomestic(QueryFareDomesticDto dto) throws SguiResultException {
        return iFareDomesticService.queryFareDomestic(dto);
    }

    @Override
    public QueryDomesticFreeBaggageVo queryDomesticFreeBaggage(QueryDomesticFreeBaggageDto dto) {
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "domestic_free_baggage")
                .one();
        if (data != null) {
            QueryDomesticFreeBaggageVo vo = JSONUtil.parseObj(data.getValue()).toBean(QueryDomesticFreeBaggageVo.class);
            for (QueryDomesticFreeBaggageVo.FreeBaggageSegInfo segInfo : vo.getFreeBaggageSegInfo()) {
                segInfo.setOcAirline(dto.getSegmentInfos().get(0).getOcAirline());
                segInfo.setDepartureDateTime(dto.getSegmentInfos().get(0).getDepartureDate() + "T" + dto.getSegmentInfos().get(0).getDepartureTime());
            }
            return vo;
        }
        return null;
    }

    @Override
    public QueryRulesVo queryRules(QueryRulesDto dto) {
        // 从模拟数据中获取
        SguiData data = iSguiDataService.lambdaQuery()
                .eq(SguiData::getName, "ticket_rules")
                .one();
        if (data == null) {
            return new QueryRulesVo();
        }

        QueryRulesVo vo = JSONUtil.parseObj(data.getValue()).toBean(QueryRulesVo.class);

        // 如果没有旅客信息或航段信息，直接返回
        if (CollUtil.isEmpty(dto.getPassengers()) ||
                CollUtil.isEmpty(dto.getPassengers().get(0).getSegInfos())) {
            return vo;
        }

        // 获取出发机场和到达机场代码
        String departureCode = dto.getPassengers().get(0).getSegInfos().get(0).getDepartureCode();
        String arrivalCode = dto.getPassengers().get(0).getSegInfos().get(0).getArrivalCode();

        // 如果没有规则信息，直接返回
        if (CollUtil.isEmpty(vo.getFlightRules()) ||
                CollUtil.isEmpty(vo.getFlightRules().get(0).getRules())) {
            return vo;
        }

        // 获取机场信息
        MnjxAirport departureAirport = iSguiCommonService.getAirportByCode(departureCode);
        MnjxAirport arrivalAirport = iSguiCommonService.getAirportByCode(arrivalCode);

        // 更新机场信息
        QueryRulesVo.Rule rule = vo.getFlightRules().get(0).getRules().get(0);
        rule.setDepartureAirport(departureCode);
        rule.setArrivalAirport(arrivalCode);

        // 设置机场中文名
        if (departureAirport != null) {
            rule.setDepartureAirportCityCh(departureAirport.getAirportCname());
        }
        if (arrivalAirport != null) {
            rule.setArrivalAirportCityCh(arrivalAirport.getAirportCname());
        }

        return vo;
    }

    @Override
    public HistoryAndNewPriceComputeVo historyAndNewPriceCompute(HistoryAndNewPriceComputeDto dto) throws SguiResultException {
        HistoryAndNewPriceComputeVo vo = new HistoryAndNewPriceComputeVo();
        if (CharSequenceUtil.isEmpty(dto.getTicketNo())) {
            return vo;
        }
        // 1. 根据ticketNo查询MnjxPnrNmTicket表
        MnjxPnrNmTicket nmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, dto.getTicketNo().replace("-", ""))
                .one();

        if (nmTicket == null) {
            throw new SguiResultException("票号不存在：" + dto.getTicketNo());
        }

        String tnId = nmTicket.getPnrNmTnId();

        // 2. 根据tnId查询MnjxPnrNmTn表
        MnjxPnrNmTn nmTn = iMnjxPnrNmTnService.lambdaQuery()
                .eq(MnjxPnrNmTn::getTnId, tnId)
                .one();

        if (nmTn == null) {
            throw new SguiResultException("出票记录不存在：" + tnId);
        }

        String pnrNmId = nmTn.getPnrNmId();
        String nmXnId = nmTn.getNmXnId();
        boolean isBaby = CharSequenceUtil.isNotBlank(nmXnId);

        // 3. 获取pnrId
        if (isBaby) {
            // 婴儿：通过nmXnId查询MnjxNmXn表获取pnrNmId，再查询MnjxPnrNm表获取pnrId
            MnjxNmXn nmXn = iMnjxNmXnService.lambdaQuery()
                    .eq(MnjxNmXn::getNmXnId, nmXnId)
                    .one();
            if (nmXn == null) {
                throw new SguiResultException("婴儿记录不存在：" + nmXnId);
            }
            pnrNmId = nmXn.getPnrNmId();
        }

        MnjxPnrNm pnrNm = iMnjxPnrNmService.lambdaQuery()
                .eq(MnjxPnrNm::getPnrNmId, pnrNmId)
                .one();

        if (pnrNm == null) {
            throw new SguiResultException("旅客记录不存在：" + pnrNmId);
        }

        // 4. 查询运价信息
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, dto.getTicketNo().replace("-", ""))
                .one();

        // 6. 构建返回数据
        HistoryAndNewPriceComputeVo.HistoryPriceCompute historyPriceCompute = this.buildHistoryPriceCompute(ticketPrice.getFnInfo(), nmTn, isBaby, ticketPrice.getSegInfo());

        vo.setHistoryPriceCompute(historyPriceCompute);
        vo.setNewPriceCompute(null); // 新运价计算暂时返回null

        return vo;
    }

    /**
     * 构建历史运价计算信息
     */
    private HistoryAndNewPriceComputeVo.HistoryPriceCompute buildHistoryPriceCompute(String fnInfo, MnjxPnrNmTn nmTn, boolean isBaby, String segInfo) {

        HistoryAndNewPriceComputeVo.HistoryPriceCompute historyPriceCompute = new HistoryAndNewPriceComputeVo.HistoryPriceCompute();

        this.buildPriceFromHistoryFnInfo(historyPriceCompute, fnInfo);

        // 设置旅客类型
        historyPriceCompute.setPsgType(isBaby ? "INF" : "ADT");

        // 设置出票日期
        if (nmTn != null && CharSequenceUtil.isNotBlank(nmTn.getIssuedTime())) {
            String issuedTime = nmTn.getIssuedTime();
            if (issuedTime.length() >= 8) {
                String dateStr = issuedTime.substring(0, 8);
                try {
                    LocalDateTime dateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                    historyPriceCompute.setIssueDate(dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                } catch (Exception e) {
                    historyPriceCompute.setIssueDate(issuedTime);
                }
            } else {
                historyPriceCompute.setIssueDate(issuedTime);
            }
        }


        // 设置航段信息（基于删除的航段信息构建）
        List<HistoryAndNewPriceComputeVo.Segment> segments = this.buildSegmentsFromDeletedInfo(segInfo);
        historyPriceCompute.setSegments(segments);

        // 设置运价基础和免费行李额
        List<String> freeBaggageAllowance = new ArrayList<>();
        List<String> fareBasis = new ArrayList<>();
        segments.forEach(s -> {
            if (CharSequenceUtil.isNotEmpty(s.getCabinCode())) {
                fareBasis.add(s.getCabinCode());
                freeBaggageAllowance.add("20K");
            } else {
                fareBasis.add("-");
                freeBaggageAllowance.add("-");
            }
        });
        historyPriceCompute.setFreeBaggageAllowance(freeBaggageAllowance);
        historyPriceCompute.setFareBasis(fareBasis);

        // 设置其他默认值
        historyPriceCompute.setTcInfo("");
        historyPriceCompute.setCommissionAmount(new BigDecimal("5.00").multiply(new BigDecimal(segments.size())).toString());
        historyPriceCompute.setCommissionPercent("0.00");

        return historyPriceCompute;
    }

    /**
     * 从历史Fn构建价格信息
     */
    private void buildPriceFromHistoryFnInfo(HistoryAndNewPriceComputeVo.HistoryPriceCompute historyPriceCompute, String fnInfo) {
        BigDecimal fPrice = BigDecimal.ZERO;
        BigDecimal cnTax = BigDecimal.ZERO;
        BigDecimal yqTax = BigDecimal.ZERO;
        BigDecimal aPrice = BigDecimal.ZERO;
        // FN/A/FCNY1000.00/SCNY1000.00/C0.00/XCNY190.00/TCNY50.00CN/TCNY140.00YQ/ACNY1190.00/P1
        // FN/A/RCNY1000.00/SCNY1000.00/C0.00/XCNY190.00/TCNY80.00OB/OCNY50.00CN/OCNY140.00YQ/ACNY1190.00/P1
        String[] split = fnInfo.split("/");
        for (String info : split) {
            if (info.startsWith("RCNY") || info.startsWith("FCNY")) {
                fPrice = new BigDecimal(info.replace("RCNY", "").replace("FCNY", ""));
            } else if (info.endsWith("CN") && (info.startsWith("TCNY") || info.startsWith("OCNY"))) {
                cnTax = new BigDecimal(info.replace("TCNY", "").replace("OCNY", "").replace("CN", ""));
            } else if (info.endsWith("YQ") && (info.startsWith("TCNY") || info.startsWith("OCNY"))) {
                yqTax = new BigDecimal(info.replace("TCNY", "").replace("OCNY", "").replace("YQ", ""));
            } else if (info.startsWith("ACNY")) {
                aPrice = new BigDecimal(info.replace("ACNY", ""));
            }
        }
        // 设置票面价
        historyPriceCompute.setPriceTotal(fPrice.toString());
        historyPriceCompute.setStotal(fPrice.toString());

        // 设置货币
        String currency = "CNY";
        historyPriceCompute.setCurrency(currency);

        // 计算税费
        BigDecimal totalTax = cnTax.add(yqTax);
        historyPriceCompute.setTaxTotal(totalTax.toString());

        // 设置总价
        historyPriceCompute.setAtotal(aPrice.toString());

        // 构建税费明细
        List<HistoryAndNewPriceComputeVo.Tax> taxes = new ArrayList<>();
        if (cnTax.compareTo(BigDecimal.ZERO) > 0) {
            HistoryAndNewPriceComputeVo.Tax cnTaxInfo = new HistoryAndNewPriceComputeVo.Tax();
            cnTaxInfo.setTaxCode("CN");
            cnTaxInfo.setTaxAmount(cnTax.toString());
            cnTaxInfo.setCurrency(currency);
            taxes.add(cnTaxInfo);
        }
        if (yqTax.compareTo(BigDecimal.ZERO) > 0) {
            HistoryAndNewPriceComputeVo.Tax yqTaxInfo = new HistoryAndNewPriceComputeVo.Tax();
            yqTaxInfo.setTaxCode("YQ");
            yqTaxInfo.setTaxAmount(yqTax.toString());
            yqTaxInfo.setCurrency(currency);
            taxes.add(yqTaxInfo);
        }
        historyPriceCompute.setTaxes(taxes);
    }

    /**
     * 从删除的航段信息构建航段列表
     */
    private List<HistoryAndNewPriceComputeVo.Segment> buildSegmentsFromDeletedInfo(String segInfo) {
        List<HistoryAndNewPriceComputeVo.Segment> segments = new ArrayList<>();

        List<String> deletedSegments = Arrays.asList(segInfo.split("/"));
        // 解析删除的航段信息构建航段列表
        for (int i = 0; i < deletedSegments.size(); i++) {
            String segmentInfo = deletedSegments.get(i);
            HistoryAndNewPriceComputeVo.Segment segment = this.parseSegmentFromInputValue(segmentInfo, i + 1);
            if (segment != null) {
                segments.add(segment);
            }
        }

        return segments;
    }

    /**
     * 从inputValue解析航段信息
     */
    private HistoryAndNewPriceComputeVo.Segment parseSegmentFromInputValue(String segmentInfo, int couponNumber) {
        if (CharSequenceUtil.isBlank(segmentInfo)) {
            return null;
        }

        HistoryAndNewPriceComputeVo.Segment segment = new HistoryAndNewPriceComputeVo.Segment();
        segment.setEtCouponNumber(String.valueOf(couponNumber));

        String[] split = segmentInfo.split(" ");
        String flightNo = split[0];
        if ("SA".equals(flightNo)) {
            segment.setSegANRK(true);
        } else {
            segment.setSegANRK(false);
            segment.setCompanyCode(flightNo.substring(0, 2));
            segment.setMarketingCarrier(flightNo.substring(0, 2));
            segment.setFlightNumber(flightNo.substring(2));
            segment.setCabinCode(split[4]);
        }
        segment.setDepartureAirport(split[1]);
        segment.setArrivalAirport(split[2]);
        return segment;
    }

    @Override
    public QueryDomesticRepriceVo queryDomesticReprice(QueryDomesticRepriceDto dto) throws SguiResultException {
        // 0. 检查航段数量限制
        if (CollUtil.isEmpty(dto.getFlights()) || dto.getFlights().size() > 2) {
            throw new SguiResultException("暂不支持联票改签");
        }
        if (dto.getPassengers().stream().anyMatch(p -> CharSequenceUtil.equalsAny(p.getPassengerType(), "GM", "JC", "GMJC"))) {
            throw new SguiResultException("无法自动获取军警残改签运价");
        }

        // 1. 根据passenger数量生成airRepriceBOS
        List<QueryDomesticRepriceVo.AirRepriceBO> airRepriceBOS = new ArrayList<>();

        if (CollUtil.isNotEmpty(dto.getPassengers())) {
            for (QueryDomesticRepriceDto.Passenger passenger : dto.getPassengers()) {
                QueryDomesticRepriceVo.AirRepriceBO airRepriceBO = new QueryDomesticRepriceVo.AirRepriceBO();
                if (CharSequenceUtil.isEmpty(passenger.getTicketNo())) {
                    airRepriceBO.setSuccess("0");
                    airRepriceBO.setPassengerName(passenger.getPassengerName());
                    airRepriceBO.setNamePnr(passenger.getNamePnr());
                    airRepriceBO.setPassengerID(passenger.getPassengerID());
                    airRepriceBO.setPassengerType(passenger.getPassengerType());
                    airRepriceBO.setIdNoType(passenger.getIdNoType());
                    airRepriceBO.setPassengeIdNo(passenger.getPassengeIdNo());
                    airRepriceBO.setGpIndicator(false);
                    airRepriceBOS.add(airRepriceBO);
                    continue;
                }
                // 查所有的票，如果是联票暂时不允许改签
                String ticketNo = passenger.getTicketNo().replace("-", "");
                MnjxPnrNmTicket nmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                        .one();
                if (ObjectUtil.isEmpty(nmTicket)) {
                    throw new SguiResultException("票号不存在");
                }
                List<MnjxPnrNmTicket> nmTicketList = iMnjxPnrNmTicketService.lambdaQuery()
                        .eq(MnjxPnrNmTicket::getPnrNmTnId, nmTicket.getPnrNmTnId())
                        .list();
                if (nmTicketList.size() > 1) {
                    throw new SguiResultException("暂不支持联票改签");
                }
                // 验证姓名和证件
                MnjxPnrNmTn tn = iMnjxPnrNmTnService.getById(nmTicket.getPnrNmTnId());
                // 验证非婴儿
                if (CharSequenceUtil.isNotEmpty(tn.getPnrNmId())) {
                    MnjxPnrNm pnrNm = iMnjxPnrNmService.getById(tn.getPnrNmId());
                    if (!pnrNm.getName().equals(passenger.getPassengerName())) {
                        throw new SguiResultException(PnrExceptionConstants.NAME_NOT_MATCH);
                    }
                    // 验证证件
                    List<MnjxNmSsr> nmSsrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, pnrNm.getPnrNmId())
                            .in(MnjxNmSsr::getSsrType, "FOID", "DOCS")
                            .list();
                    List<MnjxNmSsr> foidSsrList = nmSsrList.stream()
                            .filter(ssr -> "FOID".equals(ssr.getSsrType()))
                            .collect(Collectors.toList());

                    if (CollUtil.isNotEmpty(foidSsrList)) {
                        MnjxNmSsr foidSsr = foidSsrList.get(0);
                        String foidType;
                        if (foidSsr.getInputValue().contains(" UU")) {
                            foidType = "UU";
                        } else {
                            foidType = "NI";
                        }
                        if (!foidType.equals(passenger.getIdNoType())) {
                            throw new SguiResultException(PnrExceptionConstants.ID_CARD_NOT_MATCH);
                        }
                        String idCardNumber = this.extractFoidCardNumber(foidSsr.getInputValue());
                        if (!idCardNumber.equals(passenger.getPassengeIdNo())) {
                            throw new SguiResultException(PnrExceptionConstants.ID_CARD_NOT_MATCH);
                        }
                    } else {
                        List<MnjxNmSsr> docsSsrList = nmSsrList.stream()
                                .filter(ssr -> "DOCS".equals(ssr.getSsrType()))
                                .collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(docsSsrList)) {
                            // SSR DOCS CA HK1 P/CN/32322110/CN/10MAY20/M/22MAY22/ZHANG/SAN/H/P1
                            for (MnjxNmSsr docsSsr : docsSsrList) {
                                List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(PatternRegexConstants.SSR_DOCS), docsSsr.getInputValue());

                                if (allGroups.get(6).contains("I")) {
                                    continue;
                                }

                                if (!allGroups.get(1).equals(passenger.getIdNoType()) || !allGroups.get(3).equals(passenger.getPassengeIdNo())) {
                                    throw new SguiResultException(PnrExceptionConstants.ID_CARD_NOT_MATCH);
                                }
                            }
                        }
                    }
                }
                // 验证婴儿
                else {
                    MnjxNmXn nmXn = iMnjxNmXnService.getById(tn.getNmXnId());
                    if (!nmXn.getXnFullName().equals(passenger.getPassengerName()) && !nmXn.getXnCname().equals(passenger.getPassengerName())) {
                        throw new SguiResultException(PnrExceptionConstants.NAME_NOT_MATCH);
                    }
                    // 验证证件
                    List<MnjxNmSsr> nmSsrList = iMnjxNmSsrService.lambdaQuery()
                            .eq(MnjxNmSsr::getPnrNmId, nmXn.getPnrNmId())
                            .eq(MnjxNmSsr::getSsrType, "DOCS")
                            .list();
                    if (CollUtil.isNotEmpty(nmSsrList)) {
                        // SSR DOCS CA HK1 P/CN/32322110/CN/10MAY20/M/22MAY22/ZHANG/SAN/H/P1
                        for (MnjxNmSsr docsSsr : nmSsrList) {
                            List<String> allGroups = ReUtil.getAllGroups(Pattern.compile(PatternRegexConstants.SSR_DOCS), docsSsr.getInputValue());

                            if (allGroups.get(6).contains("I") && (!allGroups.get(1).equals(passenger.getIdNoType()) || !allGroups.get(3).equals(passenger.getPassengeIdNo()))) {
                                throw new SguiResultException(PnrExceptionConstants.ID_CARD_NOT_MATCH);
                            }
                        }
                    }
                }

                // 查票记录的旧航段信息
                MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                        .eq(MnjxTicketPrice::getTicketNo, ticketNo)
                        .one();
                String oldSegInfo = ticketPrice.getSegInfo();
                String[] oldSegSplit = oldSegInfo.split("/");
                List<String> oldNormalSegList = Arrays.stream(oldSegSplit)
                        .filter(s -> !"SA".equals(s.split(" ")[0]))
                        .collect(Collectors.toList());
                // 改签航段航段数量必须相同
                if (oldNormalSegList.size() != dto.getFlights().size()) {
                    throw new SguiResultException("改签航段数量必须相同");
                }
                for (int i = 0; i < oldSegSplit.length; i++) {
                    String info = oldSegSplit[i];
                    if ("SA".equals(info.split(" ")[0])) {
                        continue;
                    }
                    QueryDomesticRepriceDto.Flight flight = dto.getFlights().get(i);
                    QueryDomesticRepriceDto.Segment segment = flight.getSegments().get(0);
                    // 改签航司必须相同
                    if (!info.substring(0, 2).equals(segment.getMarketingAirline())) {
                        throw new SguiResultException("改签航段航司必须相同");
                    }
                }

                airRepriceBO = this.buildAirRepriceBO(passenger, dto);
                airRepriceBOS.add(airRepriceBO);
            }
        }

        QueryDomesticRepriceVo vo = new QueryDomesticRepriceVo();
        vo.setAirRepriceBOS(airRepriceBOS);

        return vo;
    }

    /**
     * 从SSR信息中提取身份证号码
     *
     * @param ssrInfo SSR信息
     * @return 身份证号码
     */
    private String extractFoidCardNumber(String ssrInfo) {
        if (CharSequenceUtil.isEmpty(ssrInfo)) {
            return "";
        }

        // 假设格式为 "SSR FOID XX HK1 NI123456789012345678/P1" "SSR FOID XX HK1 UU123456789012345678/P1"
        if (ssrInfo.contains(" NI")) {
            int startIndex = ssrInfo.indexOf(" NI") + 3;
            int endIndex = ssrInfo.indexOf("/", startIndex);
            if (endIndex == -1) {
                endIndex = ssrInfo.length();
            }
            return ssrInfo.substring(startIndex, endIndex).trim();
        } else if (ssrInfo.contains(" UU")) {
            int startIndex = ssrInfo.indexOf(" UU") + 3;
            int endIndex = ssrInfo.indexOf("/", startIndex);
            if (endIndex == -1) {
                endIndex = ssrInfo.length();
            }
            return ssrInfo.substring(startIndex, endIndex).trim();
        }

        return "";
    }

    /**
     * 构建AirRepriceBO对象
     */
    private QueryDomesticRepriceVo.AirRepriceBO buildAirRepriceBO(QueryDomesticRepriceDto.Passenger passenger, QueryDomesticRepriceDto dto) throws SguiResultException {

        QueryDomesticRepriceVo.AirRepriceBO airRepriceBO = new QueryDomesticRepriceVo.AirRepriceBO();

        // 获取当前登录用户的office信息
        UserInfo currentUser = iSguiCommonService.getCurrentUserInfo();
        if (currentUser == null) {
            throw new SguiResultException("获取当前用户信息失败");
        }

        MnjxSi currentSi = iMnjxSiService.getById(currentUser.getSiId());
        MnjxOffice office = iMnjxOfficeService.getById(currentSi.getOfficeId());
        if (office == null) {
            throw new SguiResultException("获取Office信息失败");
        }

        // 设置基本信息
        airRepriceBO.setSuccess("1");
        airRepriceBO.setOffice(office.getOfficeNo()); // 使用当前登录用户的office号
        airRepriceBO.setPassengerName(passenger.getPassengerName());
        airRepriceBO.setNamePnr(passenger.getPassengerName());
        airRepriceBO.setPassengerID(passenger.getPassengerID());
        airRepriceBO.setInfRph(passenger.getInfRph());
        airRepriceBO.setPassengerType(passenger.getPassengerType());
        airRepriceBO.setPayType(passenger.getPayType());
        airRepriceBO.setTicketNumber(passenger.getTicketNo());
        airRepriceBO.setTicketNo(passenger.getTicketNo());
        airRepriceBO.setFarebasic(passenger.getFarebasic());
        airRepriceBO.setGpIndicator(false);

        // 生成序列号
        String seriaNo = "SGUI_D" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")) + "946";
        airRepriceBO.setSeriaNo(seriaNo);

        // 设置证件信息
        this.setCertificateInfo(airRepriceBO, passenger);

        // 构建运价信息
        List<QueryDomesticRepriceVo.Fare> fares = new ArrayList<>();
        // 付费改签
        QueryDomesticRepriceVo.Fare oiFare = this.buildOiFare(passenger, dto, office);
        // 如果改签总额为0，同时返回一份免费改签数据
        if (new BigDecimal(oiFare.getRepriceAmount()).compareTo(BigDecimal.ZERO) == 0) {
            QueryDomesticRepriceVo.Fare tkneFare = this.buildTkneFare(oiFare, dto, passenger);
            fares.add(tkneFare);
        }
        fares.add(oiFare);
        airRepriceBO.setFares(fares);

        return airRepriceBO;
    }

    /**
     * 构建免费改签数据
     *
     * @param oiFare
     * @param dto
     * @param passenger
     * @return
     */
    private QueryDomesticRepriceVo.Fare buildTkneFare(QueryDomesticRepriceVo.Fare oiFare, QueryDomesticRepriceDto dto, QueryDomesticRepriceDto.Passenger passenger) {
        QueryDomesticRepriceVo.Fare tkneFare = new QueryDomesticRepriceVo.Fare();
        BeanUtil.copyProperties(oiFare, tkneFare);

        tkneFare.setFareType("revalidation");
        tkneFare.setOi(null);
        List<String> openTKNEList = new ArrayList<>();
        List<String> tkneList = new ArrayList<>();
        for (int i = 0; i < dto.getFlights().size(); i++) {
            QueryDomesticRepriceDto.Segment segment = dto.getFlights().get(i).getSegments().get(0);
            String tkne = CharSequenceUtil.format("SSR TKNE {} HK1 {}{} {} {}{} {}/{}/P{}", segment.getMarketingAirline(), segment.getDepartureAirport(), segment.getArrivalAirport(),
                    segment.getFlightNumber(), segment.getResBookDesigCode(), DateUtils.ymd2Com(segment.getDepartureDateTime().substring(0, 10)), passenger.getTicketNo(), i + 1, passenger.getPassengerID());
            openTKNEList.add(tkne);
            tkneList.add(tkne);
        }
        tkneFare.setOpenTKNE(openTKNEList);
        tkneFare.setTkne(tkneList);

        return tkneFare;
    }

    /**
     * 设置证件信息
     */
    private void setCertificateInfo(QueryDomesticRepriceVo.AirRepriceBO airRepriceBO,
                                    QueryDomesticRepriceDto.Passenger passenger) {
        // 从请求中获取证件信息
        airRepriceBO.setIdNoType(passenger.getIdNoType());
        airRepriceBO.setPassengeIdNo(passenger.getPassengeIdNo());
    }

    /**
     * 构建付费改签运价信息
     */
    private QueryDomesticRepriceVo.Fare buildOiFare(QueryDomesticRepriceDto.Passenger passenger, QueryDomesticRepriceDto dto, MnjxOffice office) throws SguiResultException {

        QueryDomesticRepriceVo.Fare fare = new QueryDomesticRepriceVo.Fare();

        fare.setFareType("reissue");
        fare.setCurreny("CNY");
        fare.setFareIndex("0");
        fare.setRevalidationNumber("0");

        // 构建运价基础信息
        this.buildFareBasicInfo(fare, dto);

        // 计算价格信息（参考FareDomesticServiceImpl的逻辑）
        this.calculatePriceInfoWithFareDomesticLogic(fare, passenger, dto);

        // 构建各种信息字符串
        this.buildInfoStrings(fare, passenger, dto, office);

        return fare;
    }

    /**
     * 构建运价基础信息
     */
    private void buildFareBasicInfo(QueryDomesticRepriceVo.Fare fare, QueryDomesticRepriceDto dto) {
        List<String> fareBasic = new ArrayList<>();
        List<String> fareBasicDetail = new ArrayList<>();
        List<QueryDomesticRepriceVo.FareBasicDetailOb> fareBasicDetailOb = new ArrayList<>();

        for (QueryDomesticRepriceDto.Flight flight : dto.getFlights()) {
            for (QueryDomesticRepriceDto.Segment segment : flight.getSegments()) {
                // 运价基础
                fareBasic.add(segment.getResBookDesigCode());

                // 运价基础详情
                String detail = segment.getDepartureAirport() + "-" + segment.getArrivalAirport() + " " + segment.getResBookDesigCode();
                fareBasicDetail.add(detail);

                // 运价基础详情对象
                QueryDomesticRepriceVo.FareBasicDetailOb detailOb = new QueryDomesticRepriceVo.FareBasicDetailOb();
                detailOb.setSegmentStr(segment.getDepartureAirport() + "-" + segment.getArrivalAirport());
                detailOb.setFareBasic(segment.getResBookDesigCode());
                fareBasicDetailOb.add(detailOb);
            }
        }

        fare.setFareBasic(fareBasic);
        fare.setFareBasicDetail(fareBasicDetail);
        fare.setFareBasicDetailOb(fareBasicDetailOb);
    }

    /**
     * 计算价格信息（参考FareDomesticServiceImpl的逻辑）
     */
    private void calculatePriceInfoWithFareDomesticLogic(QueryDomesticRepriceVo.Fare fare,
                                                         QueryDomesticRepriceDto.Passenger passenger,
                                                         QueryDomesticRepriceDto dto) throws SguiResultException {

        // 获取基建燃油费用配置
        MnjxCnYq cnYq = this.getCnYq();
        if (cnYq == null) {
            throw new SguiResultException("未找到基建燃油费用配置");
        }

        String passengerType = passenger.getPassengerType();

        // 计算总票面价
        BigDecimal totalTicketAmount = BigDecimal.ZERO;
        for (QueryDomesticRepriceDto.Flight flight : dto.getFlights()) {
            for (QueryDomesticRepriceDto.Segment segment : flight.getSegments()) {
                BigDecimal segmentPrice = this.getTicketAmountForSegment(segment);
                totalTicketAmount = totalTicketAmount.add(segmentPrice);
            }
        }

        // 根据旅客类型计算折扣后的票面价
        BigDecimal discountedTicketAmount = this.calculateDiscountedTicketAmount(totalTicketAmount, passengerType, cnYq);
        fare.setNewFare(discountedTicketAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());

        // 计算基建费和燃油费
        BigDecimal fundAmount = this.calculateFundAmount(passengerType, cnYq, dto);
        BigDecimal fuelAmount = this.calculateFuelAmount(passengerType, cnYq, dto);

        fare.setTaxCN(fundAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
        fare.setTaxYQ(fuelAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());

        // 计算总税费
        BigDecimal totalTaxAmount = fundAmount.add(fuelAmount);
        fare.setTotalnewTaxes(totalTaxAmount.setScale(2, BigDecimal.ROUND_HALF_UP).toString());

        // 获取原票价
        MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                .eq(MnjxTicketPrice::getTicketNo, passenger.getTicketNo().replace("-", ""))
                .one();
        BigDecimal oldAmount = ticketPrice.getFnInfo().contains("RCNY") ? new BigDecimal(ticketPrice.getFnInfo().split("RCNY")[1].split("/")[0]) : new BigDecimal(ticketPrice.getFnInfo().split("FCNY")[1].split("/")[0]);

        // 票面差价
        BigDecimal fareDifference = discountedTicketAmount.subtract(oldAmount);
        if (fareDifference.compareTo(BigDecimal.ZERO) < 0) {
            throw new SguiResultException("当前改签价格无法使用");
        }
        fare.setFareDifference(fareDifference.toString());

        // 计算改签手续费
        BigDecimal exchangeFee = new BigDecimal("0.00");
        for (QueryDomesticRepriceDto.Flight flight : dto.getFlights()) {
            exchangeFee = exchangeFee.add(this.calculateExchangeFee(flight.getSegments().get(0).getDepartureDateTime(), flight.getSegments().get(0).getResBookDesigCode(), discountedTicketAmount));
        }
        fare.setExchangeFee(exchangeFee.toString());
        fare.setServiceFee(exchangeFee.toString());

        // 改签总额
        fare.setRepriceAmount(fareDifference.add(exchangeFee).toString());

        // 设置税费差额
        fare.setTaxDifference("0.0");
    }

    /**
     * 计算改签手续费
     */
    @Override
    public BigDecimal calculateExchangeFee(String departureDateTime, String cabinCode, BigDecimal totalPrice) {
        // 解析起飞时间
        LocalDateTime departureTime = LocalDateTime.parse(departureDateTime.replace(" ", "T"));
        LocalDateTime currentTime = LocalDateTime.now();

        // 计算时间差（小时）
        long hoursDiff = ChronoUnit.HOURS.between(currentTime, departureTime);

        // 查询改签规则
        MnjxReschedulingRules rule = iMnjxReschedulingRulesService.lambdaQuery()
                .like(MnjxReschedulingRules::getSellCabin, cabinCode)
                .one();

        double feeRate = 0.00; // 默认0
        if (rule != null) {
            if (hoursDiff < 2) {
                feeRate = rule.getWithinTwoHoursDiscount() != null ? rule.getWithinTwoHoursDiscount() : 0.00;
            } else if (hoursDiff < 24) {
                feeRate = rule.getTwoIncludingDiscount() != null ? rule.getTwoIncludingDiscount() : 0.00;
            } else if (hoursDiff < 48) {
                feeRate = rule.getTwentyFourIncludingDiscount() != null ? rule.getTwentyFourIncludingDiscount() : 0.00;
            } else if (hoursDiff < 72) {
                feeRate = rule.getFortyEightIncludingDiscount() != null ? rule.getFortyEightIncludingDiscount() : 0.00;
            } else {
                feeRate = rule.getSeventyTwoIncludingDiscount() != null ? rule.getSeventyTwoIncludingDiscount() : 0.00;
            }
        }

        return totalPrice.multiply(BigDecimal.valueOf(feeRate)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    @Override
    public QueryFareRtktDetailVo queryRTKTDetail(QueryFareRtktDetailDto dto) throws SguiResultException {
        QueryFareRtktDetailVo vo = new QueryFareRtktDetailVo();
        List<QueryFareRtktDetailVo.Oi> voOis = new ArrayList<>();
        for (QueryFareRtktDetailDto.Oi oi : dto.getOis()) {
            QueryFareRtktDetailVo.Oi voOi = new QueryFareRtktDetailVo.Oi();
            // ET/781-2847391256
            String text = oi.getText();
            String ticketNo = text.split("/")[1].replace("-", "");
            MnjxTicketPrice ticketPrice = iMnjxTicketPriceService.lambdaQuery()
                    .eq(MnjxTicketPrice::getTicketNo, ticketNo)
                    .one();
            // 1568244046172786690 CZ 2025-07-18 14:24:11 1568245223279038465 1
            String issueInfo = ticketPrice.getIssueInfo();
            String[] split = issueInfo.split(" ");
            String selectSegNo = split[split.length - 1];
            String[] segNoSplit = selectSegNo.split("-");
            String coupon = "0000";
            for (String s : segNoSplit) {
                if (Integer.parseInt(s) % 2 == 0) {
                    coupon = CharSequenceUtil.replace(coupon, 1, 2, "2");
                } else {
                    coupon = CharSequenceUtil.replace(coupon, 0, 1, "1");
                }
            }
            text = text + "#" + coupon + " ";

            // 获取当前登录用户的office信息
            UserInfo currentUser = iSguiCommonService.getCurrentUserInfo();
            MnjxSi currentSi = iMnjxSiService.getById(currentUser.getSiId());
            MnjxOffice office = iMnjxOfficeService.getById(currentSi.getOfficeId());
            text = text + office.getOfficeNo().substring(0, 3) + " " + DateUtils.ymd2Com(DateUtil.today()).substring(0, 5) + "5 " + this.getIataNumber(office);
            voOi.setText(text);
            voOi.setPasgType(oi.getPasgType());
            voOi.setPasgId(oi.getPasgId());
            voOi.setPassName(oi.getPassName());
            voOis.add(voOi);
        }
        vo.setOis(voOis);
        return vo;
    }

    /**
     * 获取基建燃油费用配置
     */
    private MnjxCnYq getCnYq() {
        List<MnjxCnYq> cnYqList = iMnjxCnYqService.list();
        if (CollUtil.isNotEmpty(cnYqList)) {
            return cnYqList.get(0);
        }
        return null;
    }

    /**
     * 模拟获取航段的票面价（实际应该查询运价表）
     */
    private BigDecimal getTicketAmountForSegment(QueryDomesticRepriceDto.Segment segment) {
        String cabinCode = segment.getResBookDesigCode();
        List<MnjxOpenCabin> openCabinList = iSguiCommonService.getOpenCabinListByFlightNo(
                segment.getMarketingAirline() + segment.getFlightNumber(),
                segment.getDepartureDateTime().substring(0, 10),
                segment.getDepartureAirport(),
                segment.getArrivalAirport()
        );
        Integer sellCabinPrice = openCabinList.stream()
                .filter(o -> o.getSellCabin().equals(cabinCode))
                .collect(Collectors.toList())
                .get(0)
                .getSellCabinPrice();
        return new BigDecimal(sellCabinPrice);
    }

    /**
     * 根据旅客类型计算折扣后的票面价
     */
    private BigDecimal calculateDiscountedTicketAmount(BigDecimal ticketAmount, String passengerType, MnjxCnYq cnYq) {
        if ("CHD".equals(passengerType)) {
            // 儿童票价为成人票价的50%
            return ticketAmount.multiply(BigDecimal.valueOf(cnYq.getChildDiscount()));
        } else if ("INF".equals(passengerType)) {
            // 婴儿票价为成人票价的10%
            return ticketAmount.multiply(BigDecimal.valueOf(cnYq.getInfiDiscount()));
        }
        // 成人票价不打折
        return ticketAmount;
    }

    /**
     * 计算基建费
     */
    private BigDecimal calculateFundAmount(String passengerType, MnjxCnYq cnYq, QueryDomesticRepriceDto dto) {
        // 婴儿和儿童无基建费
        if ("INF".equals(passengerType) || "CHD".equals(passengerType)) {
            return BigDecimal.ZERO;
        }

        // 成人基建费，每个航段都有基建费
        int segmentCount = 0;
        for (QueryDomesticRepriceDto.Flight flight : dto.getFlights()) {
            segmentCount += flight.getSegments().size();
        }
        return new BigDecimal(cnYq.getCn()).multiply(new BigDecimal(segmentCount));
    }

    /**
     * 计算燃油费
     */
    private BigDecimal calculateFuelAmount(String passengerType, MnjxCnYq cnYq, QueryDomesticRepriceDto dto) {
        // 婴儿无燃油费
        if ("INF".equals(passengerType)) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalFuelAmount = BigDecimal.ZERO;

        for (QueryDomesticRepriceDto.Flight flight : dto.getFlights()) {
            for (QueryDomesticRepriceDto.Segment segment : flight.getSegments()) {
                // 获取当前航段的飞行距离
                Integer segmentDistance = this.calculateSegmentDistance(segment);

                // 根据飞行距离确定燃油费
                Integer segmentFuelAmount;
                if (segmentDistance != null && segmentDistance > cnYq.getThreshold()) {
                    segmentFuelAmount = cnYq.getYqHigh();
                } else {
                    segmentFuelAmount = cnYq.getYqLow();
                }

                // 儿童燃油费为成人的50%，并且个位向下取0
                if ("CHD".equals(passengerType)) {
                    BigDecimal childFuel = new BigDecimal(segmentFuelAmount).multiply(BigDecimal.valueOf(cnYq.getChildDiscount()));
                    // 向下取整到10的倍数
                    childFuel = BigDecimal.valueOf(Math.floor(childFuel.doubleValue() / 10) * 10);
                    totalFuelAmount = totalFuelAmount.add(childFuel);
                } else {
                    // 成人燃油费
                    totalFuelAmount = totalFuelAmount.add(new BigDecimal(segmentFuelAmount));
                }
            }
        }

        return totalFuelAmount;
    }

    /**
     * 计算航段距离
     */
    private Integer calculateSegmentDistance(QueryDomesticRepriceDto.Segment segment) {
        try {
            String depAirport = segment.getDepartureAirport();
            String arrAirport = segment.getArrivalAirport();

            // 获取出发和到达城市信息
            MnjxCity depCity = iSguiCommonService.getCityByAirportCode(depAirport);
            MnjxCity arrCity = iSguiCommonService.getCityByAirportCode(arrAirport);

            if (depCity != null && arrCity != null) {
                // 查询基准运价表获取距离
                MnjxStandardPat standardPat = iMnjxStandardPatService.lambdaQuery()
                        .eq(MnjxStandardPat::getOrgCityId, depCity.getCityId())
                        .eq(MnjxStandardPat::getDstCityId, arrCity.getCityId())
                        .one();

                // 如果查询结果为空，尝试将城市对反过来再查询一次
                if (standardPat == null) {
                    standardPat = iMnjxStandardPatService.lambdaQuery()
                            .eq(MnjxStandardPat::getOrgCityId, arrCity.getCityId())
                            .eq(MnjxStandardPat::getDstCityId, depCity.getCityId())
                            .one();
                }

                if (standardPat != null && standardPat.getDistance() != null) {
                    return standardPat.getDistance();
                }
            }
        } catch (Exception e) {
            log.warn("计算航段距离失败: {}-{}", segment.getDepartureAirport(), segment.getArrivalAirport(), e);
        }

        return 0; // 如果无法获取距离，返回0
    }

    /**
     * 构建各种信息字符串
     */
    private void buildInfoStrings(QueryDomesticRepriceVo.Fare fare,
                                  QueryDomesticRepriceDto.Passenger passenger,
                                  QueryDomesticRepriceDto dto, MnjxOffice office) {

        String passengerType = passenger.getPassengerType();
        boolean isInf = "INF".equals(passengerType);
        // 获取IATA信息
        String iataNumber = this.getIataNumber(office);

        // 构建FC字符串
        StringBuilder fcBuilder = new StringBuilder("FC/");
        if (isInf) {
            fcBuilder.append("IN/");
        }
        fcBuilder.append(DateUtils.ymd2Com(DateUtil.today()));

        for (int i = 0; i < dto.getFlights().size(); i++) {
            if (i > 0) {
                fcBuilder.append(" ");
            }
            QueryDomesticRepriceDto.Segment segment = dto.getFlights().get(i).getSegments().get(0);
            BigDecimal segmentPrice = this.getTicketAmountForSegment(segment);
            if (isInf) {
                segmentPrice = segmentPrice.multiply(BigDecimal.valueOf(0.1));
            } else if ("CHD".equals(passengerType)) {
                segmentPrice = segmentPrice.multiply(BigDecimal.valueOf(0.5));
            }
            fcBuilder.append(" ").append(segment.getDepartureAirport())
                    .append(" ").append(segment.getMarketingAirline())
                    .append(" ").append(segment.getArrivalAirport());

            fcBuilder.append(" ").append(segmentPrice).append(segment.getResBookDesigCode());
        }
        fcBuilder.append(" CNY").append(fare.getNewFare()).append("END");
        if (isInf) {
            fcBuilder.append("TEXT/*(IN)");
        } else if ("CHD".equals(passengerType)) {
            fcBuilder.append("TEXT/*(CH)");
        }
        fare.setFc(fcBuilder.toString());

        // 构建FN字符串
        StringBuilder fnBuilder = new StringBuilder("FN/");
        if (isInf) {
            fnBuilder.append("IN/");
        }
        fnBuilder.append("RCNY").append(fare.getNewFare()).append("/")
                .append("SCNY")
                .append(fare.getFareDifference())
                .append("/")
                .append("C0.00/")
                .append("XCNY").append(fare.getExchangeFee()).append("/")
                .append("TCNY").append(fare.getExchangeFee()).append("OB/");

        // 根据旅客类型和实际计算的税费构建税费部分
        BigDecimal cnTax = new BigDecimal(fare.getTaxCN());
        BigDecimal yqTax = new BigDecimal(fare.getTaxYQ());

        // 儿童、婴儿的CN YQ为空的情况
        if ("CHD".equals(passengerType) || "INF".equals(passengerType)) {
            if (cnTax.compareTo(BigDecimal.ZERO) != 0) {
                fnBuilder.append("OCNY").append(fare.getTaxCN()).append("CN/");
            }

            if (yqTax.compareTo(BigDecimal.ZERO) != 0) {
                fnBuilder.append("OCNY").append(fare.getTaxYQ()).append("YQ/");
            }
        } else {
            // 成人正常添加税费
            fnBuilder.append("OCNY").append(fare.getTaxCN()).append("CN/")
                    .append("OCNY").append(fare.getTaxYQ()).append("YQ/");
        }

        fnBuilder.append("ACNY").append(fare.getRepriceAmount());
        fare.setFn(fnBuilder.toString());

        // 构建OI字符串
        String segNoCoupon = dto.getFlights().size() == 1 ? "1000" : "1200";

        String oi = passenger.getTicketNo() + "#" + segNoCoupon + " " + office.getOfficeNo() + " " +
                DateUtils.ymd2Com(DateUtil.today()).substring(0, 5) + "5 " + iataNumber;
        if (isInf) {
            oi = "/IN/" + oi;
        }
        fare.setOi(oi);

        // 构建RMK OT字符串
        StringBuilder rmkOtBuilder = new StringBuilder("RMK OT/");
        if (isInf) {
            rmkOtBuilder.append("IN");
        } else {
            rmkOtBuilder.append("A");
        }
        rmkOtBuilder.append("/1/231/0-");
        for (int i = 0; i < dto.getFlights().size(); i++) {
            QueryDomesticRepriceDto.Segment segment = dto.getFlights().get(i).getSegments().get(0);
            if (i > 0) {
                rmkOtBuilder.append(".");
            }
            rmkOtBuilder.append("1")
                    .append("MU5973P").append(passenger.getPassengerID())
                    .append(iSguiCommonService.getCityByAirportCode(segment.getArrivalAirport()).getCityCode());
        }
        rmkOtBuilder.append("#").append(fare.getNewFare()).append("*5");
        fare.setRmkOT(rmkOtBuilder.toString());

        // 设置其他字符串
        if (isInf) {
            fare.setEi("IN/Q/BUDEQIANZHUAN不得签转/BIANGENGTUIPIAOSHOUFEI变更退票收费");
            fare.setFp("FP/IN/CASH,CNY");
        } else {
            fare.setEi("Q/BUDEQIANZHUAN不得签转/BIANGENGTUIPIAOSHOUFEI变更退票收费");
            fare.setFp("FP/CASH,CNY");
        }
    }

    /**
     * 获取IATA信息
     */
    private String getIataNumber(MnjxOffice office) {
        try {
            if ("0".equals(office.getOfficeType())) {
                // 代理人Office，通过orgId查询agent获取IATA
                MnjxAgent agent = iMnjxAgentService.getById(office.getOrgId());
                if (agent != null && CharSequenceUtil.isNotBlank(agent.getAgentIata())) {
                    return agent.getAgentIata();
                }
            }
            // 其他类型或查询失败时返回默认值
            return "08303234";
        } catch (Exception e) {
            log.warn("获取IATA信息失败", e);
            return "08303234";
        }
    }

    @Override
    public List<QueryEmployeesVo> queryEmployees(QueryEmployeesDto dto) throws SguiResultException {
        // 1. 参数验证
        this.validateQueryEmployeesParams(dto);

        // 2. customerTypeCode验证
        if (CharSequenceUtil.isNotBlank(dto.getCustomerTypeCode()) && !"A".equals(dto.getCustomerTypeCode())) {
            return new ArrayList<>();
        }

        // 3. 构建代理常客查询参数
        AgencyFrequentQueryDto queryDto = this.buildAgencyFrequentQueryDto(dto);

        // 4. 调用代理常客查询接口
        AgencyFrequentQueryVo agencyResult = iAgencyFrequentService.queryAgencyFrequent(queryDto);
        if (agencyResult == null || agencyResult.getResult() == null || agencyResult.getResult().isEmpty()) {
            return new ArrayList<>();
        }

        // 5. 转换为PNR代理常客格式
        return this.convertToQueryEmployeesVo(agencyResult.getResult());
    }

    /**
     * 验证查询参数
     */
    private void validateQueryEmployeesParams(QueryEmployeesDto dto) throws SguiResultException {
        if (CharSequenceUtil.isBlank(dto.getPassengerName()) &&
                CharSequenceUtil.isBlank(dto.getCsmIdentifier()) &&
                CharSequenceUtil.isBlank(dto.getMobilePhone())) {
            throw new SguiResultException("请输入查询条件");
        }
    }

    /**
     * 构建代理常客查询参数
     */
    private AgencyFrequentQueryDto buildAgencyFrequentQueryDto(QueryEmployeesDto dto) {
        AgencyFrequentQueryDto queryDto = new AgencyFrequentQueryDto();
        queryDto.setPassengerName(dto.getPassengerName());
        queryDto.setMobilePhone(dto.getMobilePhone());
        queryDto.setCsmIdentifier(dto.getCsmIdentifier());

        // 由于代理常客查询需要documentNumber，但PNR查询没有提供，我们需要特殊处理
        // 这里我们需要修改查询逻辑，不依赖documentNumber
        queryDto.setDocumentNumber(""); // 设置为空，在查询逻辑中特殊处理

        return queryDto;
    }

    /**
     * 转换为PNR代理常客格式
     */
    private List<QueryEmployeesVo> convertToQueryEmployeesVo(List<AgencyFrequentQueryVo.AgencyFrequentInfo> agencyList) {
        return agencyList.stream().map(agency -> {
            QueryEmployeesVo vo = new QueryEmployeesVo();
            vo.setCsmIdentifier(agency.getCsmIdentifier());
            vo.setEmployeeType(agency.getFrequentFlyerLevelNumberInfo());
            vo.setEmployeeLevelCode(agency.getFrequentFlyerLevelNumberInfo());
            vo.setChineseName(agency.getChineseName());
            vo.setEnglishName(agency.getEnglishName());
            vo.setCorporationCode(agency.getFrequentFlyerNumberInfo()); // 客户编码
            vo.setEmployeeNumber(null); // 员工编号暂时为空

            // 转换证件信息
            List<QueryEmployeesVo.DocumentInfo> documentInfos = agency.getDocumentList().stream().map(doc -> {
                QueryEmployeesVo.DocumentInfo docInfo = new QueryEmployeesVo.DocumentInfo();
                docInfo.setDocumentTypeCode(doc.getDocumentTypeCode());
                docInfo.setDocumentNumber(this.maskDocumentNumberForPnr(doc.getDocumentNumber()));
                docInfo.setDocumentNumberOri(doc.getDocumentNumber());
                return docInfo;
            }).collect(Collectors.toList());
            vo.setDocumentInfos(documentInfos);

            // 转换手机号信息
            List<String> mobilePhones = new ArrayList<>();
            if (CharSequenceUtil.isNotBlank(agency.getMobilePhone())) {
                mobilePhones.add(agency.getMobilePhone());
            }
            vo.setMobilePhones(mobilePhones);

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * PNR证件号脱敏处理（与代理常客查询的脱敏格式不同）
     */
    private String maskDocumentNumberForPnr(String documentNumber) {
        if (CharSequenceUtil.isBlank(documentNumber) || documentNumber.length() < 8) {
            return documentNumber;
        }
        // PNR格式：显示前6位，后面全部用*替代
        String prefix = documentNumber.substring(0, 6);
        int maskLen = documentNumber.length() - 6;
        return prefix + CharSequenceUtil.repeat("*", maskLen);
    }

    @Override
    public QueryEmployeeDetailVo queryEmployeeDetail(QueryEmployeeDetailDto dto) throws SguiResultException {
        // 1. 参数验证
        if (dto.getCsmIdentifier() == null) {
            throw new SguiResultException("客户编号不能为空");
        }

        // 3. 构建代理常客查询参数
        AgencyFrequentQueryDto queryDto = new AgencyFrequentQueryDto();
        queryDto.setCsmIdentifier(String.valueOf(dto.getCsmIdentifier()));
        queryDto.setPassengerName(""); // 设置为空，通过csmIdentifier查询
        queryDto.setDocumentNumber(""); // 设置为空，跳过证件号验证

        // 4. 调用代理常客查询接口
        AgencyFrequentQueryVo agencyResult = iAgencyFrequentService.queryAgencyFrequent(queryDto);
        if (agencyResult == null || agencyResult.getResult() == null || agencyResult.getResult().isEmpty()) {
            return null;
        }

        // 5. 获取第一条数据（应该只有一条）
        AgencyFrequentQueryVo.AgencyFrequentInfo agencyInfo = agencyResult.getResult().get(0);

        // 6. 转换为详情格式
        return this.convertToQueryEmployeeDetailVo(agencyInfo, dto.getCustomerTypeCode());
    }

    /**
     * 转换为PNR代理常客详情格式
     */
    private QueryEmployeeDetailVo convertToQueryEmployeeDetailVo(AgencyFrequentQueryVo.AgencyFrequentInfo agencyInfo, String customerTypeCode) {
        QueryEmployeeDetailVo detailVo = new QueryEmployeeDetailVo();

        // 基本信息
        detailVo.setCsmIdentifier(agencyInfo.getCsmIdentifier());
        detailVo.setCustomerTypeCode(CharSequenceUtil.isBlank(customerTypeCode) ? "A" : customerTypeCode);
        detailVo.setChineseName(agencyInfo.getChineseName());
        detailVo.setEnglishName(agencyInfo.getEnglishName());
        detailVo.setGenderCode(agencyInfo.getGenderCode());
        detailVo.setBirthDate(agencyInfo.getBirthDate());

        // 员工信息
        QueryEmployeeDetailVo.EmployeeInfo employeeInfo = new QueryEmployeeDetailVo.EmployeeInfo();

        // 手机号列表
        List<String> phoneList = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(agencyInfo.getMobilePhone())) {
            phoneList.add(agencyInfo.getMobilePhone());
        }
        employeeInfo.setFrequentPhoneList(phoneList);

        // 邮箱列表
        List<String> emailList = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(agencyInfo.getEmailAddress())) {
            emailList.add(agencyInfo.getEmailAddress());
        }
        employeeInfo.setFrequentEmailList(emailList);

        // 证件列表
        List<QueryEmployeeDetailVo.FrequentDocument> documentList = new ArrayList<>();
        if (agencyInfo.getDocumentList() != null && !agencyInfo.getDocumentList().isEmpty()) {
            documentList = agencyInfo.getDocumentList().stream().map(doc -> {
                QueryEmployeeDetailVo.FrequentDocument freqDoc = new QueryEmployeeDetailVo.FrequentDocument();
                freqDoc.setDocumentTypeCode(doc.getDocumentTypeCode());
                freqDoc.setDocumentNumber(doc.getDocumentNumber()); // 使用原始证件号
                freqDoc.setExpiryDate(doc.getExpiryDate());
                freqDoc.setNationalityCode(doc.getNationalityCode());
                freqDoc.setIssueCountryCode(doc.getIssueCountryCode());
                return freqDoc;
            }).collect(Collectors.toList());
        }
        employeeInfo.setFrequentDocumentList(documentList);

        // 航司卡列表
        List<QueryEmployeeDetailVo.AirlineCard> cardList = new ArrayList<>();
        if (agencyInfo.getCardList() != null && !agencyInfo.getCardList().isEmpty()) {
            cardList = agencyInfo.getCardList().stream().map(card -> {
                QueryEmployeeDetailVo.AirlineCard airlineCard = new QueryEmployeeDetailVo.AirlineCard();
                airlineCard.setAirlineCode(card.getAirlineCode());
                airlineCard.setFrequentFlyerNumber(card.getFrequentFlyerNumber());
                return airlineCard;
            }).collect(Collectors.toList());
        }
        employeeInfo.setAirlineCardLsit(cardList);

        // 其他固定字段
        employeeInfo.setBookingRelatedFieldList(new ArrayList<>());
        employeeInfo.setBookingTip(null);
        employeeInfo.setBookingPreferenceList(null);
        employeeInfo.setCorporationId(0);
        employeeInfo.setKeyAccountAgreementList(null);
        employeeInfo.setEmployeeId(agencyInfo.getFrequentFlyerNumberInfo()); // 客户编码作为员工ID
        employeeInfo.setEmployeeLevelCode(agencyInfo.getFrequentFlyerLevelNumberInfo());

        detailVo.setEmployeeInfo(employeeInfo);

        return detailVo;
    }

}
