package com.swcares.controller.et;

import cn.hutool.core.util.ObjectUtil;
import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.et.IEtV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25 14:00
 */
@Slf4j
@Api(tags = "ET V2接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-et/v2")
public class EtV2Controller {

    @Resource
    private IEtV2Service iEtV2Service;

    @ApiOperation(value = "querySk", notes = "SK查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QuerySkDto.class)
    })
    @PostMapping("/apiAvSearch/querySk")
    public SguiResult<SkVo> querySk(@RequestBody QuerySkDto dto) throws SguiResultException {
        SkVo vo = iEtV2Service.querySk(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryFlights", notes = "AV单日期查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryFlightsDto.class)
    })
    @PostMapping("/flight/queryFlights")
    public SguiResult<QueryFlightsVo> queryFlights(@RequestBody QueryFlightsDto dto) throws SguiResultException {
        QueryFlightsVo vo = iEtV2Service.queryFlights(dto);
        if (ObjectUtil.isEmpty(vo)) {
            return SguiResult.ok("200", null, Collections.emptyList(), null, null, new String[]{"travelsky.air.av.QueryOneFlightStatus：XXXSAT41182025050720031300071749"});
        }
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryMultiInfos", notes = "AV多日查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryMultiInfosDto.class)
    })
    @PostMapping("/flight/queryMultiInfos")
    public SguiResult<QueryMultiInfosVo> queryMultiInfos(@RequestBody QueryMultiInfosDto dto) throws SguiResultException {
        QueryMultiInfosVo vo = iEtV2Service.queryMultiInfos(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "quota", notes = "AV特殊服务配额查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryQuotaDto.class)
    })
    @PostMapping("/flight/quota")
    public SguiResult<List<QueryQuotaVo>> quota(@RequestBody QueryQuotaDto dto) throws SguiResultException {
        List<QueryQuotaVo> voList = iEtV2Service.quota(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "queryPublicFares", notes = "queryPublicFares")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryPublicFaresDto.class)
    })
    @PostMapping("/apiAvSearch/queryPublicFares")
    public SguiResult<List<PublicFaresVo>> queryPublicFares(@RequestBody QueryPublicFaresDto dto) throws SguiResultException {
        List<PublicFaresVo> voList = iEtV2Service.queryPublicFares(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "queryRuleDisplay", notes = "queryRuleDisplay")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryRuleDisplayDto.class)
    })
    @PostMapping("/apiAvSearch/queryRuleDisplay")
    public SguiResult<FaresRuleVo> queryRuleDisplay(@RequestBody QueryRuleDisplayDto dto) throws SguiResultException {
        FaresRuleVo vo = iEtV2Service.queryRuleDisplay(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryByOffice", notes = "queryByOffice（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryByOfficeDto.class)
    })
    @PostMapping("/quickQuery/authority/queryByOffice")
    public SguiResult<Object> queryByOffice(@RequestBody QueryByOfficeDto dto) {
        return SguiResult.ok(null, null);
    }

    @ApiOperation(value = "queryByPnr", notes = "queryByPnr（忽略）")
    @GetMapping("/quickQuery/authority/queryByPnr/{pnr}")
    public SguiResult<Object> queryByPnr(@PathVariable String pnr) {
        return SguiResult.ok(null, null);
    }

    @ApiOperation(value = "visa", notes = "visa（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryVisaDto.class)
    })
    @PostMapping("/quickQuery/visa")
    public SguiResult<Object> visa(@RequestBody QueryVisaDto dto) {
        return SguiResult.ok("INF-0276-17", "调用SITA服务失败", null);
    }

    @ApiOperation(value = "country", notes = "country")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryCountryDto.class)
    })
    @PostMapping("/quickQuery/country")
    public SguiResult<List<QueryCountryVo>> country(@RequestBody QueryCountryDto dto) throws SguiResultException {
        List<QueryCountryVo> voList = iEtV2Service.country(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "airline", notes = "airline")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryAirlineDto.class)
    })
    @PostMapping("/quickQuery/airline")
    public SguiResult<List<QueryAirlineVo>> airline(@RequestBody QueryAirlineDto dto) throws SguiResultException {
        List<QueryAirlineVo> voList = iEtV2Service.airline(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "airlineAgency", notes = "airlineAgency")
    @GetMapping({"/quickQuery/airlineAgency/{airlineCode}", "/quickQuery/airlineAgency/{airlineCode}/{cityCode}"})
    public SguiResult<QueryAirlineAgencyVo> airlineAgency(@PathVariable String airlineCode, @PathVariable(required = false) String cityCode) throws SguiResultException {
        QueryAirlineAgencyVo vo = iEtV2Service.airlineAgency(airlineCode, cityCode);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "airport", notes = "airport")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryAirportDecodeDto.class)
    })
    @PostMapping("/quickQuery/airport")
    public SguiResult<List<QueryAirportVo>> airport(@RequestBody QueryAirportDecodeDto dto) throws SguiResultException {
        List<QueryAirportVo> voList = iEtV2Service.airport(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "assistQuery", notes = "assistQuery（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = AssistQueryDto.class)
    })
    @PostMapping("/apiAvSearch/assistQuery")
    public SguiResult<AssistQueryVo> assistQuery(@RequestBody AssistQueryDto dto) {
        AssistQueryVo vo = iEtV2Service.assistQuery(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "scm", notes = "scm（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = ScmDto.class)
    })
    @PostMapping("/apiAvSearch/scm")
    public SguiResult<ScmVo> scm(@RequestBody ScmDto dto) {
        ScmVo vo = iEtV2Service.scm(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryAirNetFare", notes = "queryAirNetFare（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryAirNetFareDto.class)
    })
    @PostMapping("/apiAvSearch/queryAirNetFare")
    public SguiResult<List<Object>> queryAirNetFare(@RequestBody QueryAirNetFareDto dto) {
        return SguiResult.ok(null, Collections.emptyList());
    }

    @ApiOperation(value = "internationalPublic", notes = "internationalPublic（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = InternationalPublicDto.class)
    })
    @PostMapping("/fare/international/public")
    public SguiResult<List<InternationalPublicVo>> internationalPublic(@RequestBody InternationalPublicDto dto) {
        List<InternationalPublicVo> voList = iEtV2Service.internationalPublic(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "airFareCalculateCurrency", notes = "airFareCalculateCurrency（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = AirFareCalculateCurrencyDto.class)
    })
    @PostMapping("/apiAvSearch/airFareCalculateCurrency")
    public SguiResult<Object> airFareCalculateCurrency(@RequestBody AirFareCalculateCurrencyDto dto) {
        return SguiResult.ok(null, null);
    }

    @ApiOperation(value = "queryInternationalTax", notes = "queryInternationalTax（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryInternationalTaxDto.class)
    })
    @PostMapping("/apiAvSearch/queryInternationalTax")
    public SguiResult<InternationalPublicVo> queryInternationalTax(@RequestBody QueryInternationalTaxDto dto) {
        InternationalPublicVo vo = iEtV2Service.queryInternationalTax(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "currencyOfLocation", notes = "currencyOfLocation（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = CurrencyOfLocationDto.class)
    })
    @PostMapping("/apiAvSearch/currencyOfLocation")
    public SguiResult<CurrencyOfLocationVo> currencyOfLocation(@RequestBody CurrencyOfLocationDto dto) {
        CurrencyOfLocationVo vo = iEtV2Service.currencyOfLocation(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "timeDiff", notes = "timeDiff（忽略）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTimeDiffDto.class)
    })
    @PostMapping("/apiAvSearch/timeDiff")
    public SguiResult<List<QueryTimeDiffVo>> timeDiff(@RequestBody QueryTimeDiffDto dto) throws SguiResultException {
        List<QueryTimeDiffVo> voList = iEtV2Service.timeDiff(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "airPreOccupy", notes = "航班占位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = AirPreOccupyDto.class)
    })
    @PostMapping("/apiAvSearch/airPreOccupy")
    public SguiResult<AirPreOccupyVo> airPreOccupy(@RequestBody AirPreOccupyDto dto) throws SguiResultException {
        AirPreOccupyVo vo = iEtV2Service.airPreOccupy(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "airRetrievePor", notes = "航班POR检索")
    @PostMapping("/apiAvSearch/airRetrievePor")
    public SguiResult<AirRetrievePorVo> airRetrievePor() throws SguiResultException {
        AirRetrievePorVo vo = iEtV2Service.airRetrievePor();
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "airIgnorePor", notes = "取消占位")
    @PostMapping("/apiAvSearch/airIgnorePor")
    public SguiResult<String> airIgnorePor() throws SguiResultException {
        String result = iEtV2Service.airIgnorePor();
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "bookPnr", notes = "生成PNR")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reqDto", required = true, dataTypeClass = BookPnrReqDto.class)
    })
    @PostMapping("/book/pnr")
    public SguiResult<BookPnrVo> bookPnr(@RequestBody BookPnrReqDto reqDto) throws SguiResultException {
        BookPnrVo vo = this.iEtV2Service.bookPnr(reqDto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "airCancelPor", notes = "删除PNR中添加的航段")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = AirCancelPorDto.class)
    })
    @PostMapping("/apiAvSearch/airCancelPor")
    public SguiResult<AirCancelPorVo> airCancelPor(@RequestBody AirCancelPorDto dto) throws SguiResultException {
        AirCancelPorVo vo = iEtV2Service.airCancelPor(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "modifySegmentStatus", notes = "修改航段状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = ModifySegStatusDto.class)
    })
    @PostMapping("/flight/segment/modifyStatus")
    public SguiResult<ModifySegStatusVo> modifySegmentStatus(@RequestBody ModifySegStatusDto dto) throws SguiResultException {
        ModifySegStatusVo vo = iEtV2Service.modifySegmentStatus(dto);
        return SguiResult.ok(null, vo);
    }
}
