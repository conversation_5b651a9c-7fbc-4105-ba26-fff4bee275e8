package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.vo.CommonCitiesVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/16 15:41
 */
@Slf4j
@Api(tags = "city接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/city")
public class CityController {

    @ApiOperation(value = "commonCities", notes = "commonCities")
    @GetMapping("/commonCities")
    public SguiResult<CommonCitiesVo> rules() {
        return SguiResult.ok(null, new CommonCitiesVo());
    }
}
