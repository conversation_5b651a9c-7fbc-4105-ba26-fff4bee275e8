package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.vo.MenuVo;
import com.swcares.service.bkc.IMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:22
 */
@Slf4j
@Api(tags = "菜单接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/menu")
public class MenuController {

    @Resource
    private IMenuService iMenuService;

    @ApiOperation(value = "queryRoleMenu", notes = "queryRoleMenu的接口")
    @PostMapping("/queryRoleMenu")
    public SguiResult<List<MenuVo>> queryRoleMenu() {
        List<MenuVo> menuVoList = iMenuService.queryRoleMenu();
        return SguiResult.ok(null, menuVoList);
    }
}
