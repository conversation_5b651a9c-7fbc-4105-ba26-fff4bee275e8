package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.obj.dto.FirstLoginDto;
import com.swcares.obj.vo.FirstLoginVo;
import com.swcares.service.bkc.ILoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


/**
 * 第一次登录的接口
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "认证授权接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/login")
public class LoginController {

    @Resource
    private ILoginService iLoginService;

    @ApiOperation(value = "登录", notes = "登录的接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "certificate", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "username", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "verificationCode", required = true, dataTypeClass = String.class),
            @ApiImplicitParam(name = "system", dataTypeClass = String.class)
    })
    @PostMapping("")
    public UnifiedResult<FirstLoginVo> login(HttpServletRequest request, @RequestParam String certificate,
                                             @RequestParam String username,@RequestParam String verificationCode,
                                             @RequestParam String system) throws SguiResultException {
        FirstLoginDto firstLoginDto = new FirstLoginDto();
        firstLoginDto.setCertificate(certificate);
        firstLoginDto.setSystem(system);
        firstLoginDto.setUsername(username);
        firstLoginDto.setVerificationCode(verificationCode);
        FirstLoginVo firstLoginVo = iLoginService.login(request, firstLoginDto);
        log.info("登录成功");
        return UnifiedResult.ok(null, firstLoginVo);
    }
}
