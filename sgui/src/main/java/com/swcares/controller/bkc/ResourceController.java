package com.swcares.controller.bkc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.vo.ResourceVo;
import com.swcares.service.bkc.IResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/23 15:25
 */
@Slf4j
@Api(tags = "资源接口")
@SwaggerGroup(SwaggerGroup.GroupType.COMMON)
@RestController
@RequestMapping("/sgui-bkc/resource")
public class ResourceController {

    @Resource
    private IResourceService iResourceService;

    @ApiOperation(value = "queryRoleResource", notes = "queryRoleResource的接口")
    @PostMapping("/queryRoleResource")
    public SguiResult<List<ResourceVo>> queryRoleResource() {
        List<ResourceVo> resourceVoList = iResourceService.queryRoleResource();
        return SguiResult.ok(null, resourceVoList);
    }
}
