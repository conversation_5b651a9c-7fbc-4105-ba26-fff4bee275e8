package com.swcares.controller.tc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.*;
import com.swcares.obj.vo.*;
import com.swcares.service.tc.ITcV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 16:16
 */
@Slf4j
@Api(tags = "TC V2接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-tc/v2")
public class TcV2Controller {

    @Resource
    private ITcV2Service iTcV2Service;

    @ApiOperation(value = "getETReceiptDictEntry", notes = "getETReceiptDictEntry的接口")
    @GetMapping("/itinerary/getETReceiptDictEntry")
    public SguiResult<Boolean> getETReceiptDictEntry() {
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "getSguiRefresh", notes = "getSguiRefresh的接口")
    @GetMapping("/itinerary/getSguiRefresh")
    public SguiResult<Boolean> getSguiRefresh() {
        return SguiResult.ok(null, true);
    }

    @ApiOperation(value = "queryOfficeInformation", notes = "queryOfficeInformation的接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryOfficeInformationDto.class)
    })
    @PostMapping("/crs/office/queryOfficeInformation")
    public SguiResult<QueryOfficeInformationVo> queryOfficeInformation(@RequestBody QueryOfficeInformationDto dto) {
        QueryOfficeInformationVo vo = iTcV2Service.queryOfficeInformation(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryTicketDetail", notes = "按票号查询客票详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketDetailDto.class)
    })
    @PostMapping("/crs/ticket/queryTicketDetail")
    public SguiResult<List<QueryTicketDetailVo>> queryTicketDetail(@RequestBody QueryTicketDetailDto dto) throws SguiResultException {
        List<QueryTicketDetailVo> voList = iTcV2Service.queryTicketDetail(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "queryTicketByDetr", notes = "通过DETR查询票面信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketByDetrDto.class)
    })
    @PostMapping("/crs/ticket/queryTicketByDetr")
    public SguiResult<QueryTicketByDetrVo> queryTicketByDetr(@RequestBody QueryTicketByDetrDto dto) throws SguiResultException {
        QueryTicketByDetrVo vo = iTcV2Service.queryTicketByDetr(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryRtktByEtermMode", notes = "通过RTKT查询票面信息")
    @GetMapping("/crs/ticket/queryRtktByEtermMode/{ticketNo}")
    public SguiResult<TicketByRtktVo> queryRtktByEtermMode(@PathVariable String ticketNo) throws SguiResultException {
        TicketByRtktVo vo = this.iTcV2Service.queryTicketByRtkt(ticketNo);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryTicketByRtkt", notes = "通过RTKT查询票面信息")
    @GetMapping("/crs/ticket/queryTicketByRtkt/{ticketNo}")
    public SguiResult<TicketByRtktVo> queryTicketByRtkt(@PathVariable String ticketNo) throws SguiResultException {
        return this.queryRtktByEtermMode(ticketNo);
    }

    @ApiOperation(value = "queryTicketByPnr", notes = "按PNR查询客票信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketByPnrDto.class)
    })
    @PostMapping("/crs/ticket/queryByPnr")
    public SguiResult<List<QueryTicketByPnrVo>> queryTicketByPnr(@RequestBody QueryTicketByPnrDto dto) throws SguiResultException {
        List<QueryTicketByPnrVo> voList = this.iTcV2Service.queryTicketByPnr(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "queryTicketDigestsByCert", notes = "按证件号查询客票信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketByCertDto.class)
    })
    @PostMapping("/crs/ticket/queryTicketDigestsByCert")
    public SguiResult<List<QueryTicketByPnrVo>> queryTicketDigestsByCert(@RequestBody QueryTicketByCertDto dto) throws SguiResultException {
        List<QueryTicketByPnrVo> voList = this.iTcV2Service.queryTicketDigestsByCert(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "getTicketDigestsByName", notes = "按姓名查询客票信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = GetTicketDigestsByNameDto.class)
    })
    @PostMapping("/crs/ticket/getTicketDigestsByName")
    public SguiResult<List<GetTicketDigestsByNameVo>> getTicketDigestsByName(@RequestBody GetTicketDigestsByNameDto dto) throws SguiResultException {
        List<GetTicketDigestsByNameVo> voList = this.iTcV2Service.getTicketDigestsByName(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "queryTicketWindowSwitch", notes = "")
    @GetMapping("/crs/business/queryTicketWindowSwitch")
    public SguiResult<Boolean> queryTicketWindowSwitch() {
        return SguiResult.ok(null, false);
    }

    @ApiOperation(value = "findRefundTicket", notes = "初始加载退票信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = FindRefundTicketDto.class)
    })
    @PostMapping("/apiRefundTicket/findRefundTicket")
    public SguiResult<FindRefundTicketVo> findRefundTicket(@RequestBody FindRefundTicketDto dto) throws SguiResultException {
        FindRefundTicketVo vo = iTcV2Service.findRefundTicket(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryPnrMessage", notes = "退票PNR信息查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryPnrMessageDto.class)
    })
    @PostMapping("/crs/involuntary/queryPnrMessage")
    public SguiResult<QueryPnrMessageVo> queryPnrMessage(@RequestBody QueryPnrMessageDto dto) throws SguiResultException {
        QueryPnrMessageVo vo = iTcV2Service.queryPnrMessage(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "batchFindRefundFee", notes = "计算退票价格")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = BatchFindRefundFeeDto.class)
    })
    @PostMapping("/apiRefundTicket/batchFindRefundFee")
    public SguiResult<BatchFindRefundFeeVo> batchFindRefundFee(@RequestBody BatchFindRefundFeeDto dto) throws SguiResultException {
        BatchFindRefundFeeVo vo = iTcV2Service.batchFindRefundFee(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "batchAutoRefund", notes = "自动退票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = BatchAutoRefundDto.class)
    })
    @PostMapping("/apiRefundTicket/batchAutoRefund")
    public SguiResult<BatchAutoRefundVo> batchAutoRefund(@RequestBody BatchAutoRefundDto dto) throws SguiResultException {
        BatchAutoRefundVo vo = iTcV2Service.batchAutoRefund(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "deletePnrAndDeleteInfantInfo", notes = "退票取消PNR")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = DeletePnrAndDeleteInfantInfoDto.class)
    })
    @PostMapping("/pnrManager/deletePnrAndDeleteInfantInfo")
    public SguiResult<DeletePnrAndDeleteInfantInfoVo> deletePnrAndDeleteInfantInfo(@RequestBody DeletePnrAndDeleteInfantInfoDto dto) throws SguiResultException {
        DeletePnrAndDeleteInfantInfoVo vo = iTcV2Service.deletePnrAndDeleteInfantInfo(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryTicketManagementOrganization", notes = "查询客票管理机构")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "ticketNo", value = "票号", required = true, dataType = "String", paramType = "path")
    })
    @GetMapping("/crs/ticket/queryTicketManagementOrganization/{ticketNo}")
    public SguiResult<String> queryTicketManagementOrganization(@PathVariable String ticketNo) throws SguiResultException {
        String organization = iTcV2Service.queryTicketManagementOrganization(ticketNo);
        return SguiResult.ok(null, organization);
    }

    @ApiOperation(value = "queryRefundForm", notes = "查询退票单号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryRefundFormDto.class)
    })
    @PostMapping("/crs/ticket/queryRefundForm")
    public SguiResult<QueryRefundFormVo> queryRefundForm(@RequestBody QueryRefundFormDto dto) throws SguiResultException {
        QueryRefundFormVo vo = iTcV2Service.queryRefundForm(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "previewRefundTicket", notes = "退前预览")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = PreviewRefundTicketDto.class)
    })
    @PostMapping("/apiRefundTicket/previewRefundTicket")
    public SguiResult<PreviewRefundTicketVo> previewRefundTicket(@RequestBody PreviewRefundTicketDto dto) throws SguiResultException {
        PreviewRefundTicketVo vo = iTcV2Service.previewRefundTicket(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "tssChangeTicketStatus", notes = "挂起解挂票务状态变更")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = TssChangeTicketStatusDto.class)
    })
    @PostMapping("/crs/ticket/tssChangeTicketStatus")
    public SguiResult<String> tssChangeTicketStatus(@RequestBody TssChangeTicketStatusDto dto) throws SguiResultException {
        String result = iTcV2Service.tssChangeTicketStatus(dto);
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "batchManualRefundTicket", notes = "批量手工退票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = BatchManualRefundTicketDto.class)
    })
    @PostMapping("/apiRefundTicket/batchManualRefundTicket")
    public SguiResult<BatchManualRefundTicketVo> batchManualRefundTicket(@RequestBody BatchManualRefundTicketDto dto) throws SguiResultException {
        BatchManualRefundTicketVo vo = iTcV2Service.batchManualRefundTicket(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "manualRefundTicket", notes = "手工退票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = BatchManualRefundTicketDto.class)
    })
    @PostMapping("/apiRefundTicket/manualRefundTicket")
    public SguiResult<ManualRefundTicketVo> manualRefundTicket(@RequestBody BatchManualRefundTicketDto.RefundInfo dto) throws SguiResultException {
        ManualRefundTicketVo vo = iTcV2Service.manualRefundTicket(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "queryRtktDetail", notes = "获取税项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryRtktDetailDto.class)
    })
    @PostMapping("/apiRefundTicket/queryRtktDetail")
    public SguiResult<QueryRtktDetailVo> queryRtktDetail(@RequestBody QueryRtktDetailDto dto) throws SguiResultException {
        QueryRtktDetailVo vo = iTcV2Service.queryRtktDetail(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "deleteRefundForm", notes = "删除退票单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = DeleteRefundFormDto.class)
    })
    @PostMapping("/apiRefundTicket/deleteRefundForm")
    public SguiResult<String> deleteRefundForm(@RequestBody DeleteRefundFormDto dto) throws SguiResultException {
        String result = iTcV2Service.deleteRefundForm(dto);
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "modifyRefundForm", notes = "修改退票单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = ModifyRefundFormDto.class)
    })
    @PostMapping("/apiRefundTicket/modifyRefundForm")
    public SguiResult<ModifyRefundFormVo> modifyRefundForm(@RequestBody ModifyRefundFormDto dto) throws SguiResultException {
        ModifyRefundFormVo result = iTcV2Service.modifyRefundForm(dto);
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "batchFindRefundFeeZ", notes = "计算批量退票费Z")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = BatchFindRefundFeeZDto.class)
    })
    @PostMapping("/apiRefundTicket/batchFindRefundFeeZ")
    public SguiResult<BatchFindRefundFeeZVo> batchFindRefundFeeZ(@RequestBody BatchFindRefundFeeZDto dto) throws SguiResultException {
        BatchFindRefundFeeZVo vo = iTcV2Service.batchFindRefundFeeZ(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "batchRefund", notes = "批量退票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = BatchRefundDto.class)
    })
    @PostMapping("/apiRefundTicket/batchRefund")
    public SguiResult<BatchRefundVo> batchRefund(@RequestBody BatchRefundDto dto) throws SguiResultException {
        BatchRefundVo vo = iTcV2Service.batchRefund(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "etrfChangeTicketStatus", notes = "修改客票状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = EtrfChangeTicketStatusDto.class)
    })
    @PostMapping("/crs/ticket/etrfChangeTicketStatus")
    public SguiResult<String> etrfChangeTicketStatus(@RequestBody EtrfChangeTicketStatusDto dto) throws SguiResultException {
        String result = iTcV2Service.etrfChangeTicketStatus(dto);
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "queryCRSCreditCardInfo", notes = "CCCF")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryCRSCreditCardInfoDto.class)
    })
    @PostMapping("/crs/cccf/queryCRSCreditCardInfo")
    public SguiResult<Object> queryCRSCreditCardInfo(@RequestBody QueryCRSCreditCardInfoDto dto) throws SguiResultException {
        throw new SguiResultException("暂不支持该功能");
    }

    @ApiOperation(value = "findRefundTicketAndFee", notes = "补退查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = Object.class)
    })
    @PostMapping("/crs/ticket/findRefundTicketAndFee")
    public SguiResult<Object> findRefundTicketAndFee(@RequestBody Object dto) throws SguiResultException {
        throw new SguiResultException("暂不支持该功能");
    }

    @ApiOperation(value = "queryTicketByInvalid", notes = "废票查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryTicketByInvalidDto.class)
    })
    @PostMapping("/crs/passenger/queryTicketByInvalid")
    public SguiResult<List<QueryTicketByInvalidVo>> queryTicketByInvalid(@RequestBody QueryTicketByInvalidDto dto) throws SguiResultException {
        List<QueryTicketByInvalidVo> voList = iTcV2Service.queryTicketByInvalid(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "queryAllPassengersByTktNumber", notes = "查询PNR所有出票旅客信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryAllPassengersByTktNumberDto.class)
    })
    @PostMapping("/crs/passenger/queryAllPassengersByTktNumber")
    public SguiResult<List<QueryAllPassengersByTktNumberVo>> queryAllPassengersByTktNumber(@RequestBody QueryAllPassengersByTktNumberDto dto) throws SguiResultException {
        List<QueryAllPassengersByTktNumberVo> voList = iTcV2Service.queryAllPassengersByTktNumber(dto);
        return SguiResult.ok(null, voList);
    }

    @ApiOperation(value = "tssChangeTicketStatusByPnr", notes = "按出票日期联票挂起解挂")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = TssChangeTicketStatusByPnrDto.class)
    })
    @PostMapping("/crs/ticket/tssChangeTicketStatusByPnr")
    public SguiResult<String> tssChangeTicketStatusByPnr(@RequestBody TssChangeTicketStatusByPnrDto dto) throws SguiResultException {
        String result = iTcV2Service.tssChangeTicketStatusByPnr(dto);
        return SguiResult.ok(null, result);
    }

    @ApiOperation(value = "invalid", notes = "废票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = Object.class)
    })
    @PostMapping("/crs/ticket/invalid")
    public SguiResult<Object> invalid(@RequestBody Object dto) throws SguiResultException {
        throw new SguiResultException("当前系统暂不支持废票");
    }

    @ApiOperation(value = "fareConfirm", notes = "运价确认")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = FareConfirmDto.class)
    })
    @PostMapping("/crs/rebook/fareConfirm")
    public SguiResult<Object> fareConfirm(@RequestBody FareConfirmDto dto) {
        iTcV2Service.fareConfirm(dto);
        return SguiResult.ok(null, null);
    }

    @ApiOperation(value = "domesticTicket", notes = "现有PNR改签出票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = DomesticTicketDto.class)
    })
    @PostMapping("/crs/rebook/domesticTicket")
    public SguiResult<DomesticTicketVo> domesticTicket(@RequestBody DomesticTicketDto dto) throws SguiResultException {
        DomesticTicketVo vo = iTcV2Service.domesticTicket(dto);
        return SguiResult.ok(null, vo);
    }

    @ApiOperation(value = "updateSegmentRR", notes = "更新航段组行动代码为RR")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = UpdateSegmentRRDto.class)
    })
    @PostMapping("/crs/rebook/updateSegmentRR")
    public SguiResult<String> updateSegmentRR(@RequestBody UpdateSegmentRRDto dto) throws SguiResultException {
        String result = iTcV2Service.updateSegmentRR(dto);
        return SguiResult.ok(null, result);
    }
}
