package com.swcares.controller.tc;

import com.swcares.core.config.SwaggerGroup;
import com.swcares.core.exception.SguiResultException;
import com.swcares.core.unified.SguiResult;
import com.swcares.obj.dto.QueryAllPassengersByPnrNoDto;
import com.swcares.obj.vo.QueryAllPassengersByPnrNoVo;
import com.swcares.service.tc.ITcV3Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * TC V3接口控制器
 *
 * <AUTHOR>
 * @date 2025/1/14 10:30
 */
@Slf4j
@Api(tags = "TC V3接口")
@SwaggerGroup(SwaggerGroup.GroupType.BUSINESS)
@RestController
@RequestMapping("/sgui-tc/v3")
public class TcV3Controller {

    @Resource
    private ITcV3Service iTcV3Service;

    @ApiOperation(value = "queryAllPassengersByPnrNo", notes = "通过PNR编码查询所有旅客")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "dto", required = true, dataTypeClass = QueryAllPassengersByPnrNoDto.class)
    })
    @PostMapping("/crs/passenger/queryAllPassengersByPnrNo")
    public SguiResult<QueryAllPassengersByPnrNoVo> queryAllPassengersByPnrNo(@RequestBody QueryAllPassengersByPnrNoDto dto) throws SguiResultException {
        QueryAllPassengersByPnrNoVo vo = iTcV3Service.queryAllPassengersByPnrNo(dto);
        return SguiResult.ok(null, vo);
    }
}
