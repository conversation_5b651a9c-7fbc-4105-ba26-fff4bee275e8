package com.swcares.core.unified;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * Sgui除登录外的返回结果
 *
 * <AUTHOR>
 */
@ApiModel("Sgui除登录外的返回结果")
@Data
@Builder
public class SguiResult<T> implements Serializable {

    /**
     * 返回代码
     * 200：成功
     * 500：服务器内部错误
     * 400：程序发生异常
     * 403: 没有响应权限
     */
    @ApiModelProperty(value = "code", notes = "返回代码")
    private String code;

    /**
     * 上面的代码本身不能让人很清楚的知道发生了什么什么问题
     * 所以用message来表示具体的说明
     * 取值只能是对应上面code的集中取值
     */
    @ApiModelProperty(value = "msg", notes = "状态码说明")
    private String msg;

    @ApiModelProperty(value = "transactionID", notes = "transactionID")
    private String transactionID;

    /**
     * 消息返回的的具体时间
     */
    @ApiModelProperty(value = "time", notes = "消息返回的的具体时间")
    private String time;

    /**
     * 需要返回的消息体
     */
    @ApiModelProperty(value = "data", notes = "需要返回的数据")
    private T data;

    @ApiModelProperty(value = "firstError", notes = "firstError")
    private String firstError;

    @ApiModelProperty(value = "satTransactionID", notes = "satTransactionID")
    private String satTransactionID;

    @ApiModelProperty(value = "slnDesc", notes = "slnDesc")
    private String slnDesc;

    @ApiModelProperty(value = "extResponseIDList", notes = "extResponseIDList")
    private String[] extResponseIDList;

    //===========================成功返回==========================
    public static SguiResult ok() {
        return SguiResult.ok(null);
    }

    public static SguiResult ok(String msg) {
        return SguiResult.ok(msg, null);
    }

    public static <T> SguiResult ok(String msg, T data) {
        return SguiResult.ok(StrUtil.toString(HttpStatus.HTTP_OK), msg, data);
    }

    public static <T> SguiResult ok(String code, String msg, T data) {
        return SguiResult.ok(code, msg, data, null);
    }

    public static <T> SguiResult ok(String code, String msg, T data, String firstError) {
        return SguiResult.ok(code, msg, data, firstError, null);
    }

    public static <T> SguiResult ok(String code, String msg, T data, String firstError, String satTransactionID) {
        return SguiResult.ok(code, msg, data, firstError, satTransactionID, new String[]{});
    }

    public static <T> SguiResult ok(String code, String msg, T data, String firstError, String satTransactionID, String[] extResponseIDList) {
        return SguiResult.ok(code, msg, data, firstError, satTransactionID, extResponseIDList, null);
    }

    public static <T> SguiResult ok(String code, String msg, T data, String firstError, String satTransactionID, String[] extResponseIDList, String slnDesc) {
        return SguiResult.builder()
                .code(code)
                .msg(msg)
                .time(DateUtil.now() + " 000")
                .data(data)
                .firstError(firstError)
                .satTransactionID(satTransactionID)
                .extResponseIDList(extResponseIDList)
                .slnDesc(slnDesc)
                .build();
    }

    //===========================失败返回==========================
    public static SguiResult fail(String msg) {
        return SguiResult.fail(StrUtil.toString(HttpStatus.HTTP_BAD_REQUEST), msg);
    }

    public static SguiResult fail(String code, String msg) {
        return SguiResult.fail(code, msg, null);
    }

    public static <T> SguiResult fail(String code, String msg, T data) {
        return SguiResult.fail(code, msg, data, null);
    }

    public static <T> SguiResult fail(String code, String msg, T data, String transactionID) {
        return SguiResult.fail(code, msg, data, transactionID, null);
    }

    public static <T> SguiResult fail(String code, String msg, T data, String transactionID, String firstError) {
        return SguiResult.fail(code, msg, data, transactionID, firstError, null);
    }

    public static <T> SguiResult fail(String code, String msg, T data, String transactionID, String firstError, String satTransactionID) {
        return SguiResult.fail(code, msg, data, transactionID, firstError, satTransactionID, new String[]{});
    }

    public static <T> SguiResult fail(String code, String msg, T data, String transactionID, String firstError, String satTransactionID, String[] extResponseIDList) {
        return SguiResult.fail(code, msg, data, transactionID, firstError, satTransactionID, extResponseIDList, null);
    }

    public static <T> SguiResult fail(String code, String msg, T data, String transactionID, String firstError, String satTransactionID, String[] extResponseIDList, String slnDesc) {
        return SguiResult.builder()
                .code(code)
                .msg(msg)
                .time(DateUtil.now() + " 000")
                .data(data)
                .transactionID(transactionID)
                .firstError(firstError)
                .satTransactionID(satTransactionID)
                .extResponseIDList(extResponseIDList)
                .slnDesc(slnDesc)
                .build();
    }

    /**
     * 打印数据
     *
     * @param response   返回对象
     * @param sguiResult 需要输出的内容
     */
    public static void writeJson(HttpServletResponse response, SguiResult sguiResult) {
        response.setCharacterEncoding(CharsetUtil.UTF_8);
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, CharSequenceUtil.format("{},{}", HttpMethod.POST.name(), HttpMethod.GET.name()));
        JSONConfig jsonConfig = JSONConfig.create();
        jsonConfig.setIgnoreNullValue(false);
        ServletUtil.write(response, JSONUtil.toJsonStr(sguiResult, jsonConfig), ContentType.JSON.getValue());
    }
}
