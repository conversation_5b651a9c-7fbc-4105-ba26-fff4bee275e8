package com.swcares.core.cache;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.core.constants.CacheConstants;
import com.swcares.entity.*;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户偏好设置缓存预加载器
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
@Slf4j
@Component
public class UserPreferenceCachePreloader implements CachePreloader {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ISguiUserPreferenceService iSguiUserPreferenceService;

    @Resource
    private ISguiUserPreferenceAirlineCtctService iSguiUserPreferenceAirlineCtctService;

    @Resource
    private ISguiUserPreferenceCheckTipService iSguiUserPreferenceCheckTipService;

    @Resource
    private ISguiUserPreferencePassengerInfoService iSguiUserPreferencePassengerInfoService;

    @Resource
    private ISguiUserPreferenceRemarkService iSguiUserPreferenceRemarkService;

    @Override
    public void preloadCache(String userId) {
        log.info("开始预加载用户偏好设置缓存...");

        // 分批加载，每批100条，避免一次加载过多数据
        long total = iSguiUserPreferenceService.count();
        if (CharSequenceUtil.isNotEmpty(userId)) {
            total = iSguiUserPreferenceService.lambdaQuery()
                    .eq(SguiUserPreference::getSiId, userId)
                    .count();
        }
        int batchSize = 100;
        int totalBatches = (int) Math.ceil((double) total / batchSize);

        log.info("用户偏好设置总数: {}, 分批数: {}, 每批大小: {}", total, totalBatches, batchSize);

        for (int batch = 0; batch < totalBatches; batch++) {
            // 分页查询
            List<SguiUserPreference> preferences = iSguiUserPreferenceService.lambdaQuery()
                    .last("LIMIT " + (batch * batchSize) + "," + batchSize)
                    .list();
            if (CharSequenceUtil.isNotEmpty(userId)) {
                preferences = iSguiUserPreferenceService.lambdaQuery()
                        .eq(SguiUserPreference::getSiId, userId)
                        .last("LIMIT " + (batch * batchSize) + "," + batchSize)
                        .list();
            }

            log.info("正在处理第 {} 批, 实际数据量: {}", batch + 1, preferences.size());

            for (SguiUserPreference preference : preferences) {
                try {
                    // 缓存主数据
                    String preferenceKey = CacheConstants.USER_PREFERENCE_PREFIX + preference.getSiId();
                    stringRedisTemplate.opsForValue().set(
                            preferenceKey,
                            JSONUtil.toJsonStr(preference),
                            CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                            TimeUnit.HOURS
                    );

                    // 缓存航司CTCT设置
                    List<SguiUserPreferenceAirlineCtct> airlineCtcts = iSguiUserPreferenceAirlineCtctService.lambdaQuery()
                            .eq(SguiUserPreferenceAirlineCtct::getPreferenceId, preference.getPreferenceId())
                            .list();
                    if (CollUtil.isNotEmpty(airlineCtcts)) {
                        String airlineCtctKey = CacheConstants.USER_PREFERENCE_AIRLINE_CTCT_PREFIX + preference.getPreferenceId();
                        stringRedisTemplate.opsForValue().set(
                                airlineCtctKey,
                                JSONUtil.toJsonStr(airlineCtcts),
                                CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                                TimeUnit.HOURS
                        );
                    }

                    // 缓存check tip设置
                    SguiUserPreferenceCheckTip checkTip = iSguiUserPreferenceCheckTipService.lambdaQuery()
                            .eq(SguiUserPreferenceCheckTip::getPreferenceId, preference.getPreferenceId())
                            .one();
                    String checkTipKey = CacheConstants.USER_PREFERENCE_CHECK_TIP_PREFIX + preference.getPreferenceId();
                    stringRedisTemplate.opsForValue().set(
                            checkTipKey,
                            JSONUtil.toJsonStr(checkTip),
                            CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                            TimeUnit.HOURS
                    );

                    // 缓存passenger info设置
                    SguiUserPreferencePassengerInfo passengerInfo = iSguiUserPreferencePassengerInfoService.lambdaQuery()
                            .eq(SguiUserPreferencePassengerInfo::getPreferenceId, preference.getPreferenceId())
                            .one();
                    String passengerInfoKey = CacheConstants.USER_PREFERENCE_PASSENGER_INFO_PREFIX + preference.getPreferenceId();
                    stringRedisTemplate.opsForValue().set(
                            passengerInfoKey,
                            JSONUtil.toJsonStr(passengerInfo),
                            CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                            TimeUnit.HOURS
                    );

                    // 缓存remark设置
                    List<SguiUserPreferenceRemark> remarkList = iSguiUserPreferenceRemarkService.lambdaQuery()
                            .eq(SguiUserPreferenceRemark::getPreferenceId, preference.getPreferenceId())
                            .list();
                    if (CollUtil.isNotEmpty(remarkList)) {
                        String remarkKey = CacheConstants.USER_PREFERENCE_REMARK_PREFIX + preference.getPreferenceId();
                        stringRedisTemplate.opsForValue().set(
                                remarkKey,
                                JSONUtil.toJsonStr(remarkList),
                                CacheConstants.USER_PREFERENCE_EXPIRE_HOURS,
                                TimeUnit.HOURS
                        );
                    }
                } catch (Exception e) {
                    log.error("缓存用户偏好设置失败, userId: {}, preferenceId: {}",
                            preference.getSiId(), preference.getPreferenceId(), e);
                }
            }
        }

        log.info("用户偏好设置缓存预加载完成");
    }

    @Override
    public int getOrder() {
        return 10; // 设置优先级
    }

    @Override
    public String getName() {
        return "用户偏好设置缓存预加载器";
    }
}
