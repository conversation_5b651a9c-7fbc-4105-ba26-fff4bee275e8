package com.swcares.core.cache;

import cn.hutool.json.JSONUtil;
import com.swcares.entity.*;
import com.swcares.obj.dto.FlightInfoDto;
import com.swcares.obj.dto.FlightSectionDto;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 航班信息缓存预加载器
 *
 * <AUTHOR>
 * @date 2025/4/14 15:30
 */
@Slf4j
@Component
public class FlightInfoCachePreloader implements CachePreloader {

    @Resource
    private IMnjxFlightService iMnjxFlightService;

    @Resource
    private IMnjxTcardService iMnjxTcardService;

    @Resource
    private IMnjxPlanFlightService iMnjxPlanFlightService;

    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Resource
    private IMnjxTcardSectionService iMnjxTcardSectionService;

    @Resource
    private IMnjxCityService iMnjxCityService;

    @Resource
    private IMnjxAirportService iMnjxAirportService;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void preloadCache(String userId) {
        log.info("开始预加载航班信息缓存...");

//        log.info("预加载航班信息缓存，FLIGHT_PREFIX:flightNo->entry");
//        List<MnjxFlight> mnjxFlightList = iMnjxFlightService.list();
//        for (MnjxFlight mnjxFlight : mnjxFlightList) {
//            String mnjxFlightKey = FlightCacheConstants.FLIGHT_PREFIX + mnjxFlight.getFlightNo();
//            stringRedisTemplate.opsForValue().set(
//                    mnjxFlightKey,
//                    JSONUtil.toJsonStr(mnjxFlight),
//                    FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
//                    TimeUnit.DAYS
//            );
//        }
//
//        log.info("预加载航节信息缓存，TCARD_PREFIX:flightNo->entry");
//        List<MnjxTcard> mnjxTcardList = iMnjxTcardService.list();
//        for (MnjxTcard mnjxTcard : mnjxTcardList) {
//            MnjxFlight mnjxFlight = mnjxFlightList.stream()
//                    .filter(f -> f.getFlightId().equals(mnjxTcard.getFlightId()))
//                    .collect(Collectors.toList())
//                    .get(0);
//            String mnjxTcardKey = FlightCacheConstants.TCARD_PREFIX + mnjxFlight.getFlightNo();
//            stringRedisTemplate.opsForValue().set(
//                    mnjxTcardKey,
//                    JSONUtil.toJsonStr(mnjxTcard),
//                    FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
//                    TimeUnit.DAYS
//            );
//        }
//
//        log.info("预加载城市信息缓存，CITY_PREFIX:cityCode->entry");
//        List<MnjxCity> mnjxCityList = iMnjxCityService.list();
//        for (MnjxCity mnjxCity : mnjxCityList) {
//            String mnjxCityKey = FlightCacheConstants.CITY_PREFIX + mnjxCity.getCityCode();
//            stringRedisTemplate.opsForValue().set(
//                    mnjxCityKey,
//                    JSONUtil.toJsonStr(mnjxCity),
//                    FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
//                    TimeUnit.DAYS
//            );
//        }
//
//        log.info("预加载机场信息缓存，AIRPORT_PREFIX:airportCode->entry");
//        List<MnjxAirport> mnjxAirportList = iMnjxAirportService.list();
//        for (MnjxAirport mnjxAirport : mnjxAirportList) {
//            String mnjxAirportKey = FlightCacheConstants.AIRPORT_PREFIX + mnjxAirport.getAirportCode();
//            stringRedisTemplate.opsForValue().set(
//                    mnjxAirportKey,
//                    JSONUtil.toJsonStr(mnjxAirport),
//                    FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
//                    TimeUnit.DAYS
//            );
//        }
//
//        log.info("预加载航司信息缓存，AIRLINE_PREFIX:airlineCode->entry");
//        List<MnjxAirline> mnjxAirlineList = iMnjxAirlineService.list();
//        for (MnjxAirline mnjxAirline : mnjxAirlineList) {
//            String mnjxAirlineKey = FlightCacheConstants.AIRLINE_PREFIX + mnjxAirline.getAirlineCode();
//            stringRedisTemplate.opsForValue().set(
//                    mnjxAirlineKey,
//                    JSONUtil.toJsonStr(mnjxAirline),
//                    FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
//                    TimeUnit.DAYS
//            );
//        }

        log.info("航班信息缓存预加载完成");
    }

    /**
     * 加载指定日期的航班信息
     *
     * @param date 日期，格式：yyyy-MM-dd
     */
    public void loadFlightInfoByDate(String date) {
        log.info("加载 {} 日期的航班信息", date);

        try {
            // 查询指定日期的航班计划
            List<MnjxPlanFlight> planFlights = iMnjxPlanFlightService.lambdaQuery()
                    .eq(MnjxPlanFlight::getFlightDate, date)
                    .list();

            if (planFlights.isEmpty()) {
                log.info("{} 日期没有航班计划", date);
                return;
            }

            log.info("{} 日期共有 {} 个航班计划", date, planFlights.size());

            // 存储该日期的所有航班ID
            List<String> flightIds = new ArrayList<>();

            // 分批处理航班信息，每批100条
            int batchSize = 100;
            int totalBatches = (int) Math.ceil((double) planFlights.size() / batchSize);

            for (int batch = 0; batch < totalBatches; batch++) {
                int start = batch * batchSize;
                int end = Math.min(start + batchSize, planFlights.size());
                List<MnjxPlanFlight> batchPlanFlights = planFlights.subList(start, end);

                for (MnjxPlanFlight planFlight : batchPlanFlights) {
                    String tcardId = planFlight.getTcardId();

                    // 查询tcard信息
                    MnjxTcard tcard = iMnjxTcardService.getById(tcardId);

                    String flightId = tcard.getFlightId();

                    // 查询航班信息
                    MnjxFlight flight = iMnjxFlightService.getById(flightId);

                    // 查询航节计划信息
                    List<MnjxPlanSection> planSections = iMnjxPlanSectionService.lambdaQuery()
                            .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                            .orderByAsc(MnjxPlanSection::getDepAptId)
                            .list();

                    // 查询tcard航节信息
                    List<MnjxTcardSection> tcardSections = iMnjxTcardSectionService.lambdaQuery()
                            .eq(MnjxTcardSection::getTcardId, tcardId)
                            .orderByAsc(MnjxTcardSection::getSectionNo)
                            .list();

                    // 构建航班信息DTO
                    FlightInfoDto flightInfoDto = new FlightInfoDto();
                    flightInfoDto.setFlightId(flightId);
                    flightInfoDto.setFlightNo(flight.getFlightNo());
                    flightInfoDto.setAirlineCode(planFlight.getAirlineCode());
                    flightInfoDto.setFlightDate(date);
                    flightInfoDto.setTcardId(tcardId);
                    flightInfoDto.setPlanFlightId(planFlight.getPlanFlightId());

                    // 构建航节信息
                    List<FlightSectionDto> sectionDtos = new ArrayList<>();
                    this.constructPlanSection(planSections, tcardSections, sectionDtos);


                    // 按航节顺序排序
                    sectionDtos.sort(Comparator.comparing(FlightSectionDto::getSectionNo));
                    flightInfoDto.setSections(sectionDtos);

                    // 缓存航班详细信息
                    String flightDetailKey = FlightCacheConstants.FLIGHT_DETAIL_PREFIX + flightId;
                    stringRedisTemplate.opsForValue().set(
                            flightDetailKey,
                            JSONUtil.toJsonStr(flightInfoDto),
                            FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
                            TimeUnit.DAYS
                    );

                    // 添加到日期航班列表
                    flightIds.add(flightId);
                }
            }

            // 缓存日期对应的航班ID列表
            String dateKey = FlightCacheConstants.FLIGHT_DATE_PREFIX + date;
            stringRedisTemplate.opsForValue().set(
                    dateKey,
                    JSONUtil.toJsonStr(flightIds),
                    FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
                    TimeUnit.DAYS
            );

            log.info("{} 日期共缓存了 {} 个航班信息", date, flightIds.size());

        } catch (Exception e) {
            log.error("加载 {} 日期的航班信息失败", date, e);
        }
    }

    private void constructPlanSection(List<MnjxPlanSection> planSections, List<MnjxTcardSection> tcardSections, List<FlightSectionDto> sectionDtos) {
        for (MnjxPlanSection planSection : planSections) {
            FlightSectionDto sectionDto = new FlightSectionDto();
            sectionDto.setPlanSectionId(planSection.getPlanSectionId());

            // 查找对应的tcardSection
            MnjxTcardSection matchingTcardSection = null;
            for (MnjxTcardSection tcardSection : tcardSections) {
                if (tcardSection.getAirportId().equals(planSection.getDepAptId())) {
                    matchingTcardSection = tcardSection;
                    break;
                }
            }

            if (matchingTcardSection != null) {
                sectionDto.setTcardSectionId(matchingTcardSection.getTcardSectionId());
                sectionDto.setSectionNo(matchingTcardSection.getSectionNo());
            }

            sectionDto.setDepAptId(planSection.getDepAptId());
            sectionDto.setArrAptId(planSection.getArrAptId());
            sectionDto.setEstimateOff(planSection.getEstimateOff());
            sectionDto.setEstimateOffChange(planSection.getEstimateOffChange());
            sectionDto.setEstimateArr(planSection.getEstimateArr());
            sectionDto.setEstimateArrChange(planSection.getEstimateArrChange());
            sectionDto.setEstimateBoarding(planSection.getEstimateBoarding());
            sectionDto.setEstimateBoardingChange(planSection.getEstimateBoardingChange());
            sectionDto.setActualOff(planSection.getActualOff());
            sectionDto.setActualOffChange(planSection.getActualOffChange());
            sectionDto.setActualArr(planSection.getActualArr());
            sectionDto.setActualArrChange(planSection.getActualArrChange());
            sectionDto.setActualBoarding(planSection.getActualBoarding());
            sectionDto.setActualBoardingChange(planSection.getActualBoardingChange());
            sectionDto.setGate(planSection.getGate());
            sectionDto.setPlaneId(planSection.getPlaneId());
            sectionDto.setIsLastSection(planSection.getIsLastSection());

            sectionDtos.add(sectionDto);
        }
    }

    /**
     * 加载指定航班ID的航班信息
     *
     * @param flightId 航班ID
     */
    public void loadFlightInfoById(String flightId) {
        log.info("加载航班ID {} 的信息", flightId);

        try {
            // 查询航班信息
            MnjxFlight flight = iMnjxFlightService.getById(flightId);

            // 查询tcard信息
            List<MnjxTcard> tcards = iMnjxTcardService.lambdaQuery()
                    .eq(MnjxTcard::getFlightId, flightId)
                    .list();

            for (MnjxTcard tcard : tcards) {
                // 查询航班计划信息
                List<MnjxPlanFlight> planFlights = iMnjxPlanFlightService.lambdaQuery()
                        .eq(MnjxPlanFlight::getTcardId, tcard.getTcardId())
                        .list();

                for (MnjxPlanFlight planFlight : planFlights) {
                    // 构建航班信息DTO
                    FlightInfoDto flightInfoDto = new FlightInfoDto();
                    flightInfoDto.setFlightId(flightId);
                    flightInfoDto.setFlightNo(flight.getFlightNo());
                    flightInfoDto.setAirlineCode(planFlight.getAirlineCode());
                    flightInfoDto.setFlightDate(planFlight.getFlightDate());
                    flightInfoDto.setTcardId(tcard.getTcardId());
                    flightInfoDto.setPlanFlightId(planFlight.getPlanFlightId());

                    // 查询航节计划信息
                    List<MnjxPlanSection> planSections = iMnjxPlanSectionService.lambdaQuery()
                            .eq(MnjxPlanSection::getPlanFlightId, planFlight.getPlanFlightId())
                            .orderByAsc(MnjxPlanSection::getDepAptId)
                            .list();

                    // 查询tcard航节信息
                    List<MnjxTcardSection> tcardSections = iMnjxTcardSectionService.lambdaQuery()
                            .eq(MnjxTcardSection::getTcardId, tcard.getTcardId())
                            .orderByAsc(MnjxTcardSection::getSectionNo)
                            .list();

                    // 构建航节信息
                    List<FlightSectionDto> sectionDtos = new ArrayList<>();
                    this.constructPlanSection(planSections, tcardSections, sectionDtos);

                    // 按航节顺序排序
                    sectionDtos.sort(Comparator.comparing(FlightSectionDto::getSectionNo));
                    flightInfoDto.setSections(sectionDtos);

                    // 缓存航班详细信息
                    String flightDetailKey = FlightCacheConstants.FLIGHT_DETAIL_PREFIX + flightId;
                    stringRedisTemplate.opsForValue().set(
                            flightDetailKey,
                            JSONUtil.toJsonStr(flightInfoDto),
                            FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
                            TimeUnit.DAYS
                    );

                    // 更新日期航班列表
                    String dateKey = FlightCacheConstants.FLIGHT_DATE_PREFIX + planFlight.getFlightDate();
                    String flightIdsJson = stringRedisTemplate.opsForValue().get(dateKey);

                    if (flightIdsJson != null) {
                        List<String> flightIds = JSONUtil.toList(JSONUtil.parseArray(flightIdsJson), String.class);
                        if (!flightIds.contains(flightId)) {
                            flightIds.add(flightId);
                            stringRedisTemplate.opsForValue().set(
                                    dateKey,
                                    JSONUtil.toJsonStr(flightIds),
                                    FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
                                    TimeUnit.DAYS
                            );
                        }
                    } else {
                        // 如果日期航班列表不存在，创建新的
                        List<String> flightIds = new ArrayList<>();
                        flightIds.add(flightId);
                        stringRedisTemplate.opsForValue().set(
                                dateKey,
                                JSONUtil.toJsonStr(flightIds),
                                FlightCacheConstants.FLIGHT_CACHE_EXPIRE_DAYS,
                                TimeUnit.DAYS
                        );
                    }
                }
            }

            log.info("加载航班ID {} 的信息成功", flightId);

        } catch (Exception e) {
            log.error("加载航班ID {} 的信息失败", flightId, e);
        }
    }

    @Override
    public int getOrder() {
        return 20; // 设置优先级，比用户偏好设置低一些
    }

    @Override
    public String getName() {
        return "航班信息缓存预加载器";
    }
}
