package com.swcares.core.config;

import cn.hutool.core.text.CharSequenceUtil;
import com.swcares.core.security.TokenJwtManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootVersion;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.plugin.core.OrderAwarePluginRegistry;
import org.springframework.plugin.core.PluginRegistry;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.DocumentationPlugin;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.spring.web.plugins.DocumentationPluginsManager;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * ClassName：com.swcares.common.config.SwaggerConfig <br>
 * Description：自动装配时当出现多个Bean候选者时，被注解为@Primary的Bean将作为首选者，否则将抛出异常。
 * （只对接口的多个实现生效）覆盖swagger自己的配置 <br>
 * date 2019年5月20日 下午3:05:26 <br>
 *
 * <AUTHOR> @travelsky] <br>
 * @version v1.0 <br>
 */
@Primary
@Configuration
public class Swagger3Config extends DocumentationPluginsManager {
    /**
     * 在api接口文件显示的应用名
     */
    @Value("${swagger.application-name}")
    private String applicationName;
    /**
     * 接口描述
     */
    @Value("${swagger.application-description}")
    private String description;
    /**
     * 接口版本
     */
    @Value("${swagger.application-version}")
    private String version;

    @Value("${swagger.enable}")
    private boolean enable;

    /**
     * 设置授权信息应用范围
     *
     * @return 授权应用范围
     */
    private List<SecurityContext> getSecurityContexts() {
        List<SecurityReference> securityReferences = Collections.singletonList(
                new SecurityReference(TokenJwtManager.KEY_AUTHORIZATION, new AuthorizationScope[]{new AuthorizationScope("global", "设置所有接口的访问控制")})
        );
        return Collections.singletonList(SecurityContext.builder().securityReferences(securityReferences).build());
    }

    /**
     * 全局token认证
     *
     * @return 全局token认证
     */
    private List<SecurityScheme> getSecuritySchemes() {
        return Collections.singletonList(new ApiKey(TokenJwtManager.KEY_AUTHORIZATION, TokenJwtManager.KEY_AUTHORIZATION, "header"));
    }

    /**
     * 配置支持的协议
     *
     * @return 协议列表
     */
    private LinkedHashSet<String> getProtocols() {
        return new LinkedHashSet<>(Arrays.asList("https", "http"));
    }

    /**
     * 显示信息
     *
     * @return ApiInfo
     */
    private ApiInfo getApiInfo() {
        return new ApiInfoBuilder()
                // 标题
                .title(CharSequenceUtil.format("{} REST ful API 文档", applicationName))
                // 版本
                .version(CharSequenceUtil.format("Application Version: {}, Spring Boot Version: {}", version, SpringBootVersion.getVersion()))
                // 描述
                .description(description)
                // 联系人信息
                // 名字 网址 邮箱
                .contact(new Contact("棒棒糖项目组", "http://xxxx.com.cn", "<EMAIL>"))
                // License
                .license("Apache 2.0")
                // License 网址
                .licenseUrl("http://www.apache.org/licenses/LICENSE-2.0")
                .build();
    }

    /**
     * 重写父类中的方法。该方法就是返回了需要使用的文档插件
     *
     * @return 文档插件列表
     * @throws IllegalStateException 异常
     */
    @Override
    public Collection<DocumentationPlugin> documentationPlugins() throws IllegalStateException {
        SwaggerPluginRegistry swaggerPluginRegistry = this.registry();
        List<DocumentationPlugin> plugins = swaggerPluginRegistry.getPlugins();
        this.ensureNoDuplicateGroups(plugins);
        return plugins.isEmpty() ? Collections.singleton(new Docket(DocumentationType.OAS_30)) : plugins;
    }

    /**
     * 获取所有的 Docket 对象列表
     *
     * @return 对象列表
     */
    public SwaggerPluginRegistry registry() {
        List<Docket> dockets = new ArrayList<>();
        for (SwaggerGroup.GroupType groupType : SwaggerGroup.GroupType.values()) {
            Docket docket =
                    // swagger所使用的版本
                    new Docket(DocumentationType.OAS_30)
                            // 将servlet路径映射（如果有）添加到apis基本路径
                            .pathMapping("/")
                            // 定义是否开启swagger，false为关闭，可以通过变量控制，这个地方通过的是是否启用swagger配置
                            .enable(enable)
                            // 文档信息
                            .apiInfo(getApiInfo())
                            // 支持的通讯协议集合
                            .protocols(getProtocols())
                            // 授权信息设置，必要的header token等认证信息
                            .securitySchemes(getSecuritySchemes())
                            // 授权信息全局应用
                            .securityContexts(getSecurityContexts())
                            .groupName(groupType.getDisplay())
                            .forCodeGeneration(Boolean.TRUE)
                            .select()
                            // 配置需要扫描的位置
                            .apis(input -> {
                                if (groupType.equals(SwaggerGroup.GroupType.DEFAULT)) {
                                    // 指定扫描有Api注解的类
                                    return input.findControllerAnnotation(RestController.class).isPresent();
                                }
                                // 指定扫描有此版本的SwaggerGroup注解的方法
                                Optional<SwaggerGroup> swaggerGroup = input.findControllerAnnotation(SwaggerGroup.class);
                                return swaggerGroup.filter(item -> Objects.equals(item.value(), groupType)).isPresent();
                            })
                            // 配置路径
                            .paths(PathSelectors.any())
                            // 构建
                            .build();
            dockets.add(docket);
        }
        return new SwaggerPluginRegistry(dockets, new AnnotationAwareOrderComparator());
    }

    public static class SwaggerPluginRegistry extends OrderAwarePluginRegistry<DocumentationPlugin, DocumentationType> implements PluginRegistry<DocumentationPlugin, DocumentationType> {
        protected SwaggerPluginRegistry(List<Docket> plugins, Comparator<? super DocumentationPlugin> comparator) {
            super(plugins, comparator);
        }
    }

    private void ensureNoDuplicateGroups(List<DocumentationPlugin> allPlugins) throws IllegalStateException {
        Map<String, List<DocumentationPlugin>> plugins = allPlugins.stream()
                .collect(Collectors.groupingBy(plugin -> Optional.ofNullable(plugin.getGroupName()).orElse("default"), LinkedHashMap::new, Collectors.toList()));
        Iterable<String> duplicateGroups = plugins.entrySet().stream()
                .filter(input -> (input.getValue()).size() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        if (StreamSupport.stream(duplicateGroups.spliterator(), false).findAny().isPresent()) {
            throw new IllegalStateException(String.format("Multiple Dockets with the same group name are not supported. The following duplicate groups were discovered. %s", String.join(",", duplicateGroups)));
        }
    }
}
