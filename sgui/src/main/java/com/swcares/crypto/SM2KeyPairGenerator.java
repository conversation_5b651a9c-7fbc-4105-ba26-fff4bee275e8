package com.swcares.crypto;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECKeyGenerationParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECCurve;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;

/**
 * SM2密钥对生成器
 * 生成数学上正确的SM2密钥对
 * 
 * <AUTHOR> Team
 * @date 2025/06/18 15:30
 */
@Slf4j
public class SM2KeyPairGenerator {
    
    // SM2椭圆曲线参数 (国密标准参数)
    private static final BigInteger SM2_ECC_P = new BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF", 16);
    private static final BigInteger SM2_ECC_A = new BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC", 16);
    private static final BigInteger SM2_ECC_B = new BigInteger("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93", 16);
    private static final BigInteger SM2_ECC_N = new BigInteger("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123", 16);
    private static final BigInteger SM2_ECC_GX = new BigInteger("32C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7", 16);
    private static final BigInteger SM2_ECC_GY = new BigInteger("BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0", 16);
    
    private static final ECDomainParameters DOMAIN_PARAMETERS;
    
    static {
        // 添加BouncyCastle提供者
        Security.addProvider(new BouncyCastleProvider());
        
        // 初始化SM2椭圆曲线参数
        ECCurve curve = new ECCurve.Fp(SM2_ECC_P, SM2_ECC_A, SM2_ECC_B, SM2_ECC_N, BigInteger.ONE);
        ECPoint g = curve.createPoint(SM2_ECC_GX, SM2_ECC_GY);
        DOMAIN_PARAMETERS = new ECDomainParameters(curve, g, SM2_ECC_N);
    }
    
    /**
     * 生成有效的SM2密钥对
     */
    public static KeyPair generateValidKeyPair() {
        try {
            ECKeyPairGenerator keyPairGenerator = new ECKeyPairGenerator();
            ECKeyGenerationParameters keyGenParams = new ECKeyGenerationParameters(DOMAIN_PARAMETERS, new SecureRandom());
            keyPairGenerator.init(keyGenParams);
            
            AsymmetricCipherKeyPair keyPair = keyPairGenerator.generateKeyPair();
            ECPrivateKeyParameters privateKey = (ECPrivateKeyParameters) keyPair.getPrivate();
            ECPublicKeyParameters publicKey = (ECPublicKeyParameters) keyPair.getPublic();
            
            String privateKeyHex = leftPad(privateKey.getD().toString(16));
            String publicKeyHex = Hex.toHexString(publicKey.getQ().getEncoded(false)).toUpperCase();
            
            return new KeyPair(privateKeyHex, publicKeyHex);
        } catch (Exception e) {
            throw new IllegalArgumentException("生成密钥对失败", e);
        }
    }
    
    /**
     * 测试密钥对的加密解密功能
     */
    public static boolean testKeyPair(KeyPair keyPair, String testMessage) {
        try {
            // 解析私钥
            BigInteger privateKeyBigInt = new BigInteger(keyPair.getPrivateKey(), 16);
            ECPrivateKeyParameters privateKeyParams = new ECPrivateKeyParameters(privateKeyBigInt, DOMAIN_PARAMETERS);

            // 从十六进制字符串计算公钥点
            byte[] publicKeyBytes = Hex.decode(keyPair.getPublicKey());
            ECPoint publicKeyPoint = DOMAIN_PARAMETERS.getCurve().decodePoint(publicKeyBytes);
            byte[] encryptedBytes = getEncryptedBytes(testMessage, publicKeyPoint);

            // 测试解密
            SM2Engine decryptEngine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            decryptEngine.init(false, privateKeyParams);
            byte[] decryptedBytes = decryptEngine.processBlock(encryptedBytes, 0, encryptedBytes.length);
            String decryptedMessage = new String(decryptedBytes, StandardCharsets.UTF_8);

            return testMessage.equals(decryptedMessage);
        } catch (Exception e) {
            log.info("密钥对测试失败: {}", e.getMessage());
            return false;
        }
    }

    private static byte[] getEncryptedBytes(String testMessage, ECPoint publicKeyPoint) throws InvalidCipherTextException {
        ECPublicKeyParameters publicKeyParams = new ECPublicKeyParameters(publicKeyPoint, DOMAIN_PARAMETERS);

        // 测试加密 - 使用ParametersWithRandom包装公钥参数
        SM2Engine encryptEngine = new SM2Engine(SM2Engine.Mode.C1C3C2);
        org.bouncycastle.crypto.params.ParametersWithRandom encryptParams =
            new org.bouncycastle.crypto.params.ParametersWithRandom(publicKeyParams, new SecureRandom());
        encryptEngine.init(true, encryptParams);

        byte[] messageBytes = testMessage.getBytes(StandardCharsets.UTF_8);
        return encryptEngine.processBlock(messageBytes, 0, messageBytes.length);
    }

    /**
     * 密钥对类
     */
    @Getter
    public static class KeyPair {
        private final String privateKey;
        private final String publicKey;
        
        public KeyPair(String privateKey, String publicKey) {
            this.privateKey = privateKey;
            this.publicKey = publicKey;
        }

        @Override
        public String toString() {
            return "KeyPair{" +
                    "privateKey='" + privateKey + '\'' +
                    ", publicKey='" + publicKey + '\'' +
                    '}';
        }
    }
    
    /**
     * 左填充零
     */
    private static String leftPad(String str) {
        StringBuilder strBuilder = new StringBuilder(str);
        while (strBuilder.length() < 64) {
            strBuilder.insert(0, "0");
        }
        str = strBuilder.toString();
        return str.toUpperCase();
    }
    
    /**
     * 主方法 - 生成并测试密钥对
     */
    public static void main(String[] args) {
        log.info("=== SM2密钥对生成器 ===");
        
        // 生成多个密钥对供选择
        for (int i = 1; i <= 3; i++) {
            log.info("=== 密钥对选项 {} ===", i);
            
            KeyPair keyPair = generateValidKeyPair();

            log.info("私钥: {}", keyPair.getPrivateKey());
            log.info("公钥: {}", keyPair.getPublicKey());
            
            // 测试密钥对
            String testMessage = "666666";
            boolean testResult = testKeyPair(keyPair, testMessage);

            log.info("测试消息: {}", testMessage);
            log.info("测试结果: {}", testResult ? "成功" : "失败");
            
            if (testResult) {
                log.info("=== 前端配置 ===");
                log.info("更新 encrypt-facb275d.js:");
                log.info("const e = \"{}\";", keyPair.getPublicKey());

                log.info("=== 后端配置 ===");
                log.info("application.yml:");
                log.info("crypto:");
                log.info("  sm2:");
                log.info("    private-key: {}", keyPair.getPrivateKey());
                log.info("    public-key: {}", keyPair.getPublicKey());
            }

            log.info("----------------------------------------");
        }
        
        log.info("=== 使用说明 ===");
        log.info("1. 选择一个测试成功的密钥对");
        log.info("2. 用公钥更新前端JS文件");
        log.info("3. 重新构建前端项目");
        log.info("4. 用私钥配置后端");
        log.info("5. 测试登录功能");
        
        log.info("=== 验证步骤 ===");
        log.info("1. 前端用新公钥加密 '666666'");
        log.info("2. 后端用对应私钥解密");
        log.info("3. 结果应该是 '666666'");
    }
}
