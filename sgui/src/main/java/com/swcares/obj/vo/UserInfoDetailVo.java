package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/4/18 13:25
 */
@Data
@ApiModel(value = "用户详细信息", description = "用户详细信息")
public class UserInfoDetailVo implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "")
    private String accountStatus;

    @ApiModelProperty(value = "")
    private Integer securityLevel;

    @ApiModelProperty(value = "")
    private Integer maxSession;

    @ApiModelProperty(value = "")
    private String isSys;

    @ApiModelProperty(value = "")
    private String isCpublic;

    @ApiModelProperty(value = "")
    private String createTime;

    @ApiModelProperty(value = "")
    private String lockTime;

    @ApiModelProperty(value = "")
    private String expiredTime;

    @ApiModelProperty(value = "")
    private String pswdUptTime;

    @ApiModelProperty(value = "")
    private Long departmentId;

    @ApiModelProperty(value = "")
    private String departmentName;

    @ApiModelProperty(value = "")
    private String employeeId;

    @ApiModelProperty(value = "")
    private String channel;

    @ApiModelProperty(value = "")
    private String employeeName;

    @ApiModelProperty(value = "")
    private String seq;

    @ApiModelProperty(value = "")
    private String email;

    @ApiModelProperty(value = "")
    private String telephone;

    @ApiModelProperty(value = "")
    private String mobile;

    @ApiModelProperty(value = "")
    private String note;

    @ApiModelProperty(value = "")
    private String locale;

    @ApiModelProperty(value = "")
    private String createOpr;

    @ApiModelProperty(value = "")
    private String lastUptOpr;

    @ApiModelProperty(value = "")
    private String lastUptTime;

    @ApiModelProperty(value = "")
    private String mgrOrgIds;

    @ApiModelProperty(value = "")
    private String mgrOrgNames;

    @ApiModelProperty(value = "")
    private String field1;

    @ApiModelProperty(value = "")
    private String field2;

    @ApiModelProperty(value = "")
    private String field3;

    @ApiModelProperty(value = "")
    private String field4;

    @ApiModelProperty(value = "")
    private String lastLoginTime;

    @ApiModelProperty(value = "")
    private String certDn;

    @ApiModelProperty(value = "")
    private String lastErrorLoginTime;

    @ApiModelProperty(value = "")
    private String loginErrorNum;

    @ApiModelProperty(value = "")
    private String certId;

    @ApiModelProperty(value = "")
    private String signature;

    @ApiModelProperty(value = "")
    private String mobileaccess;

    @ApiModelProperty(value = "")
    private String etermId;

    @ApiModelProperty(value = "")
    private String etermPwd;

    @ApiModelProperty(value = "")
    private String etermServerAddress;

    @ApiModelProperty(value = "")
    private String loginServerip;

    @ApiModelProperty(value = "")
    private String isTamtrace;

    @ApiModelProperty(value = "")
    private Integer isOnline;

    @ApiModelProperty(value = "")
    private Integer authType;

    @ApiModelProperty(value = "")
    private Integer configType;

    @ApiModelProperty(value = "")
    private String lastPwdErrorTime;

    @ApiModelProperty(value = "")
    private String pwdErrorNum;

    @ApiModelProperty(value = "")
    private String createEmpName;

    @ApiModelProperty(value = "")
    private String mgrAirline;

    @ApiModelProperty(value = "")
    private Integer skin;

    @ApiModelProperty(value = "")
    private String memo;

    @ApiModelProperty(value = "")
    private String switchAirline;

    @ApiModelProperty(value = "")
    private String iamToken;

    @ApiModelProperty(value = "")
    private String iamTokenForSceneFive;

    @ApiModelProperty(value = "")
    private String officeCodeAll;

    @ApiModelProperty(value = "")
    private String[] officeCodes;

    @ApiModelProperty(value = "")
    private String defaultRole;

    @ApiModelProperty(value = "")
    private String defaultOffice;

    @ApiModelProperty(value = "")
    private String defaultUserGroup;

    @ApiModelProperty(value = "")
    private String agent;

    @ApiModelProperty(value = "")
    private String defaultAgent;

    @ApiModelProperty(value = "")
    private String agentCertificate;

    @ApiModelProperty(value = "")
    private String defaultSystem;

    @ApiModelProperty(value = "")
    private Boolean defaultRoleWithPid;

    @ApiModelProperty(value = "")
    private String defaultSellingGuiIataNum;

    @ApiModelProperty(value = "")
    private String defaultOfficeIataNum;

    @ApiModelProperty(value = "")
    private String userGroup;

    @ApiModelProperty(value = "")
    private String system;

    @ApiModelProperty(value = "")
    private String sellingGuiIataNum;

    @ApiModelProperty(value = "")
    private String tssellingguicurrency;

    @ApiModelProperty(value = "")
    private String tssellingguiairportcode;

    @ApiModelProperty(value = "")
    private String office;

    @ApiModelProperty(value = "")
    private String agentNo;

    @ApiModelProperty(value = "")
    private Boolean kickOutEnable;

    @ApiModelProperty(value = "")
    private Integer pageSize;

    @ApiModelProperty(value = "")
    private Boolean operator;

    @ApiModelProperty(value = "")
    private String storageType;

    @ApiModelProperty(value = "")
    private String pid;

    @ApiModelProperty(value = "")
    private String frontId;

    @ApiModelProperty(value = "")
    private String frontAccount;

    @ApiModelProperty(value = "")
    private ConcurrentHashMap<String, List<ConcurrentHashMap<String, String>>> roleMap = new ConcurrentHashMap<>();

    @ApiModelProperty(value = "")
    private String ibeAuthInfo;

    @ApiModelProperty(value = "")
    private String ibeExtendAuthInfo;

    @ApiModelProperty(value = "")
    private Boolean longTimeNoChangePwd;

    @ApiModelProperty(value = "")
    private String oldIamToken;

    @ApiModelProperty(value = "")
    private Integer unlockTimes;

    @ApiModelProperty(value = "")
    private String flowLimit;

    @ApiModelProperty(value = "")
    private Boolean overFlowed;

    @ApiModelProperty(value = "")
    private String sessionToken;

    @ApiModelProperty(value = "")
    private Boolean mutiFactorAuth;

    @ApiModelProperty(value = "")
    private Boolean twoPwdMismatch;

    @ApiModelProperty(value = "")
    private String entityType;

    @ApiModelProperty(value = "")
    private String renewCertificate;

    @ApiModelProperty(value = "")
    private String newOrgCode;

    @ApiModelProperty(value = "")
    private String currentRoleLevel;

    @ApiModelProperty(value = "")
    private Boolean crsSystem;

    @ApiModelProperty(value = "")
    private Boolean manualInputEtermInfo;

    @ApiModelProperty(value = "")
    private String globalId;

    @ApiModelProperty(value = "")
    private String bioIdentificationToken;

    @ApiModelProperty(value = "")
    private String bioExpireTime;

    @ApiModelProperty(value = "")
    private String bioIntervalTime;

    @ApiModelProperty(value = "")
    private String orgNumber;

    @ApiModelProperty(value = "")
    private Boolean defaultOfficeInternational;

    @ApiModelProperty(value = "")
    private String belongGroup;

    @ApiModelProperty(value = "")
    private Boolean agentLogin;

    @ApiModelProperty(value = "")
    private String externalLink;

    @ApiModelProperty(value = "")
    private String team;

    @ApiModelProperty(value = "")
    private String tokenType;

    @ApiModelProperty(value = "")
    private String publicIp;

    @ApiModelProperty(value = "")
    private String publicIpLocation;

    @ApiModelProperty(value = "")
    private Boolean oneToOneAccount;
}
