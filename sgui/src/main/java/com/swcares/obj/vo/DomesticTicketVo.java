package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 现有PNR改签出票响应VO
 *
 * <AUTHOR>
 * @date 2025/09/08 16:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "DomesticTicketVo", description = "现有PNR改签出票响应VO")
public class DomesticTicketVo {

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "旅客出票列表")
    private List<PassengerTicketInfo> passengerTicketList;

    @Data
    @ApiModel(value = "PassengerTicketInfo", description = "旅客出票信息")
    public static class PassengerTicketInfo {

        @ApiModelProperty(value = "票号")
        private String ticketNo;

        @ApiModelProperty(value = "旅客姓名")
        private String passengerName;
    }
}
