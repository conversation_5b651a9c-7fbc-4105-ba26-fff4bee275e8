package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询关注PNR列表返回VO
 *
 * <AUTHOR>
 * @date 2025/08/04 17:30
 */
@Data
@ApiModel(value = "SubQueueCrsPnrVo", description = "查询关注PNR列表返回VO")
public class SubQueueCrsPnrVo implements Serializable {

    @ApiModelProperty(value = "代理人")
    private String agent;

    @ApiModelProperty(value = "错误信息")
    private String errorInfo;

    @ApiModelProperty(value = "关注信息列表")
    private List<SubscriptionsInfo> subscriptionsInfos;

    @ApiModelProperty(value = "同步时间")
    private String synchTime;

    /**
     * 关注信息
     */
    @Data
    @ApiModel(value = "SubscriptionsInfo", description = "关注信息")
    public static class SubscriptionsInfo implements Serializable {

        @ApiModelProperty(value = "PNR编号")
        private String pnr;

        @ApiModelProperty(value = "时间")
        private String time;

        @ApiModelProperty(value = "读取状态信息列表")
        private List<ReadStatusInfo> readStatusInfos;
    }

    /**
     * 读取状态信息
     */
    @Data
    @ApiModel(value = "ReadStatusInfo", description = "读取状态信息")
    public static class ReadStatusInfo implements Serializable {

        @ApiModelProperty(value = "状态代码")
        private String statusCode;

        @ApiModelProperty(value = "原因")
        private String reason;
    }
}
