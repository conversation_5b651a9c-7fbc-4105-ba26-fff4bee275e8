package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/23 14:41
 */
@Data
@ApiModel(value = "EntryVo", description = "EntryVo")
public class EntryVo implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "typeId")
    private Long typeId;

    @ApiModelProperty(value = "typeCode")
    private String typeCode;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "content")
    private String content;

    @ApiModelProperty(value = "lastOpe")
    private String lastOpe;

    @ApiModelProperty(value = "lastDate")
    private String lastDate;

    @ApiModelProperty(value = "seq")
    private Integer seq;

    @ApiModelProperty(value = "status")
    private String status;

    @ApiModelProperty(value = "filter1")
    private String filter1;

    @ApiModelProperty(value = "filter2")
    private String filter2;

    @ApiModelProperty(value = "field1")
    private String field1;

    @ApiModelProperty(value = "field2")
    private String field2;

    @ApiModelProperty(value = "field3")
    private String field3;

    @ApiModelProperty(value = "field4")
    private String field4;

    @ApiModelProperty(value = "isSys")
    private String isSys;

    @ApiModelProperty(value = "operatorId")
    private String operatorId;

    @ApiModelProperty(value = "dataType")
    private String dataType;

    @ApiModelProperty(value = "name")
    private String name;
}
