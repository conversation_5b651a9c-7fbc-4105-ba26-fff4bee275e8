package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryFareDomesticVo", description = "国内运价查询返回对象")
public class QueryFareDomesticVo implements Serializable {

    @ApiModelProperty(value = "运价项目")
    private Map<String, List<FareItem>> fareItems;

    @ApiModelProperty(value = "原始运价结果")
    private Object originalFareResult;

    @ApiModelProperty(value = "运价类型")
    private String fareType;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "FareItem", description = "运价项目")
    public static class FareItem implements Serializable {
        @ApiModelProperty(value = "票面价")
        private String tktAmount;

        @ApiModelProperty(value = "总价")
        private String totalAmount;

        @ApiModelProperty(value = "总价货币")
        private String totalAmountCurrency;

        @ApiModelProperty(value = "燃油费")
        private String fuel;

        @ApiModelProperty(value = "基建费")
        private String fund;

        @ApiModelProperty(value = "rmkcms")
        private String rmkcms;

        @ApiModelProperty(value = "货币")
        private String currency;

        @ApiModelProperty(value = "序列号")
        private String sequenceNmbr;

        @ApiModelProperty(value = "总税费")
        private String totalTaxAmount;

        @ApiModelProperty(value = "协议运价代码")
        private String negotiatedFareCode;

        @ApiModelProperty(value = "FC信息")
        private String fc;

        @ApiModelProperty(value = "FN信息")
        private String fn;

        @ApiModelProperty(value = "FP信息")
        private String fp;

        @ApiModelProperty(value = "EI信息")
        private String ei;

        @ApiModelProperty(value = "TC信息")
        private String tc;

        @ApiModelProperty(value = "OI信息")
        private String oi;

        @ApiModelProperty(value = "航段信息")
        private List<SegInfo> segInfos;

        @ApiModelProperty(value = "国内税费")
        private List<TaxDom> taxDom;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "SegInfo", description = "航段信息")
    public static class SegInfo implements Serializable {
        @ApiModelProperty(value = "出发日期时间")
        private String departureDateTime;

        @ApiModelProperty(value = "运价基础代码")
        private String fareBasisCodes;

        @ApiModelProperty(value = "航空公司代码")
        private String companyCode;

        @ApiModelProperty(value = "出发代码")
        private String departureCode;

        @ApiModelProperty(value = "到达代码")
        private String arrivalCode;

        @ApiModelProperty(value = "预订舱位代码")
        private String resBookDesigCode;

        @ApiModelProperty(value = "航班号")
        private String flightNumber;

        @ApiModelProperty(value = "规则ID列表")
        private List<String> ruleIds;

        @ApiModelProperty(value = "票面价")
        private String ticketAmount;

        @ApiModelProperty(value = "规则信息列表")
        private List<RuleInfo> ruleInfoList;

        @ApiModelProperty(value = "行李额度")
        private String baggageAllowance;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "RuleInfo", description = "规则信息")
    public static class RuleInfo implements Serializable {
        @ApiModelProperty(value = "规则ID")
        private String ruleId;

        @ApiModelProperty(value = "位置")
        private String location;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "TaxDom", description = "国内税费")
    public static class TaxDom implements Serializable {
        @ApiModelProperty(value = "税费代码")
        private String taxCode;

        @ApiModelProperty(value = "价格")
        private Price price;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "Price", description = "价格")
    public static class Price implements Serializable {
        @ApiModelProperty(value = "金额")
        private String amount;

        @ApiModelProperty(value = "货币")
        private String currency;
    }
}
