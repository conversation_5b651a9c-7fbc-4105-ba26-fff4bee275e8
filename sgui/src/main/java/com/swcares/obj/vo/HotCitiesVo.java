package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/4/24 14:00
 */
@Data
@ApiModel(value = "HotCitiesVo", description = "HotCitiesVo")
public class HotCitiesVo implements Serializable {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "dictTypeId")
    private Long dictTypeId;

    @ApiModelProperty(value = "dictTypeCode")
    private String dictTypeCode;

    @ApiModelProperty(value = "dictCode")
    private String dictCode;

    @ApiModelProperty(value = "dictName")
    private String dictName;

    @ApiModelProperty(value = "status")
    private Integer status;

    @ApiModelProperty(value = "seq")
    private Integer seq;

    @ApiModelProperty(value = "dictLevel")
    private Integer dictLevel;

    @ApiModelProperty(value = "path")
    private String path;

    @ApiModelProperty(value = "filter1")
    private String filter1;

    @ApiModelProperty(value = "filter2")
    private String filter2;

    @ApiModelProperty(value = "parentId")
    private Long parentId;

    @ApiModelProperty(value = "field1")
    private String field1;

    @ApiModelProperty(value = "field2")
    private String field2;

    @ApiModelProperty(value = "field3")
    private String field3;

    @ApiModelProperty(value = "field4")
    private String field4;

    @ApiModelProperty(value = "dictEnName")
    private String dictEnName;

    @ApiModelProperty(value = "field1EnName")
    private String field1EnName;
}
