package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/10 12:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "ScmVo", description = "机场查询返回对象")
public class ScmVo implements Serializable {

    @ApiModelProperty(value = "子MCT信息列表")
    private List<SubmctInfoDTO> submctInfoDTOList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "SubmctInfoDTO", description = "子MCT信息")
    public static class SubmctInfoDTO implements Serializable {
        
        @ApiModelProperty(value = "规则编号")
        private String ruleNo;
        
        @ApiModelProperty(value = "到达机场")
        private String arrAirport;
        
        @ApiModelProperty(value = "出发机场")
        private String depAirport;
        
        @ApiModelProperty(value = "MCT时间")
        private String mctTime;
        
        @ApiModelProperty(value = "MCT状态")
        private String mctStatus;
        
        @ApiModelProperty(value = "到达航空公司")
        private String arrAirline;
        
        @ApiModelProperty(value = "到达航班号起始")
        private String arrFltnumFrom;
        
        @ApiModelProperty(value = "到达航班号结束")
        private String arrFltnumTo;
        
        @ApiModelProperty(value = "到达时间")
        private String arrTm;
        
        @ApiModelProperty(value = "到达共享")
        private String arrCodeshare;
        
        @ApiModelProperty(value = "到达设备")
        private String arrEquipment;
        
        @ApiModelProperty(value = "前站")
        private String preStation;
        
        @ApiModelProperty(value = "前站国家")
        private String preCountry;
        
        @ApiModelProperty(value = "前站区域")
        private String preRegion;
        
        @ApiModelProperty(value = "出发航空公司")
        private String depAirline;
        
        @ApiModelProperty(value = "出发航班号起始")
        private String depFltnumFrom;
        
        @ApiModelProperty(value = "出发航班号结束")
        private String depFltnumTo;
        
        @ApiModelProperty(value = "出发时间")
        private String depTm;
        
        @ApiModelProperty(value = "出发共享")
        private String depCodeshare;
        
        @ApiModelProperty(value = "出发设备")
        private String depEquipment;
        
        @ApiModelProperty(value = "下一站")
        private String nextStation;
        
        @ApiModelProperty(value = "下一站国家")
        private String nextCountry;
        
        @ApiModelProperty(value = "下一站区域")
        private String nextRegion;
        
        @ApiModelProperty(value = "生效日期起始")
        private String effDateFrom;
        
        @ApiModelProperty(value = "生效日期结束")
        private String effDateTo;
    }
}
