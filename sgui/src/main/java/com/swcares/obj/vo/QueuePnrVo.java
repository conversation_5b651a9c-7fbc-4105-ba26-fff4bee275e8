package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 刷新关注PNR返回VO
 *
 * <AUTHOR>
 * @date 2025/08/04 18:30
 */
@Data
@ApiModel(value = "QueuePnrVo", description = "刷新关注PNR返回VO")
public class QueuePnrVo implements Serializable {

    @ApiModelProperty(value = "PNR编号")
    private String pnrNo;

    @ApiModelProperty(value = "是否团队")
    private Boolean group;

    @ApiModelProperty(value = "是否取消")
    private Boolean canceled;

    @ApiModelProperty(value = "旅客姓名列表")
    private List<String> passengerNames;

    @ApiModelProperty(value = "航班信息列表")
    private List<FlightInfo> flights;

    @ApiModelProperty(value = "出票状态")
    private String issueStatus;

    /**
     * 航班信息
     */
    @Data
    @ApiModel(value = "FlightInfo", description = "航班信息")
    public static class FlightInfo implements Serializable {

        @ApiModelProperty(value = "航班号")
        private String flightNo;

        @ApiModelProperty(value = "出发机场代码")
        private String departureCode;

        @ApiModelProperty(value = "到达机场代码")
        private String arrivalCode;

        @ApiModelProperty(value = "出发日期")
        private String departureDate;

        @ApiModelProperty(value = "出发日期时间")
        private String departureDateTime;

        @ApiModelProperty(value = "舱位名称")
        private String cabinName;

        @ApiModelProperty(value = "动作代码")
        private String actionCode;
    }
}
