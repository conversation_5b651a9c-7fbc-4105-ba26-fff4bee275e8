package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 历史运价查询响应数据
 *
 * <AUTHOR>
 * @date 2025/07/14 17:00
 */
@Data
@ApiModel(value = "HistoryAndNewPriceComputeVo", description = "历史运价查询响应数据")
public class HistoryAndNewPriceComputeVo {

    @ApiModelProperty(value = "历史运价计算信息")
    private HistoryPriceCompute historyPriceCompute;

    @ApiModelProperty(value = "新运价计算信息")
    private Object newPriceCompute;

    @Data
    @ApiModel(value = "HistoryPriceCompute", description = "历史运价计算信息")
    public static class HistoryPriceCompute {

        @ApiModelProperty(value = "票面价总计")
        private String priceTotal;

        @ApiModelProperty(value = "税费总计")
        private String taxTotal;

        @ApiModelProperty(value = "货币代码")
        private String currency;

        @ApiModelProperty(value = "税费明细")
        private List<Tax> taxes;

        @ApiModelProperty(value = "运价基础")
        private List<String> fareBasis;

        @ApiModelProperty(value = "旅客类型")
        private String psgType;

        @ApiModelProperty(value = "航段信息")
        private List<Segment> segments;

        @ApiModelProperty(value = "出票日期")
        private String issueDate;

        @ApiModelProperty(value = "TC信息")
        private String tcInfo;

        @ApiModelProperty(value = "免费行李额")
        private List<String> freeBaggageAllowance;

        @ApiModelProperty(value = "代理费金额")
        private String commissionAmount;

        @ApiModelProperty(value = "代理费百分比")
        private String commissionPercent;

        @ApiModelProperty(value = "总价（含税）")
        private String atotal;

        @ApiModelProperty(value = "票面价总计")
        private String stotal;
    }

    @Data
    @ApiModel(value = "Tax", description = "税费信息")
    public static class Tax {

        @ApiModelProperty(value = "税费代码")
        private String taxCode;

        @ApiModelProperty(value = "税费金额")
        private String taxAmount;

        @ApiModelProperty(value = "货币代码")
        private String currency;
    }

    @Data
    @ApiModel(value = "Segment", description = "航段信息")
    public static class Segment {

        @ApiModelProperty(value = "ET票联号")
        private String etCouponNumber;

        @ApiModelProperty(value = "起飞日期时间")
        private String departureDateTime;

        @ApiModelProperty(value = "到达日期时间")
        private String arrivalDateTime;

        @ApiModelProperty(value = "航班号")
        private String flightNumber;

        @ApiModelProperty(value = "起飞机场")
        private String departureAirport;

        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;

        @ApiModelProperty(value = "航班类型")
        private String flightType;

        @ApiModelProperty(value = "舱位代码")
        private String cabinCode;

        @ApiModelProperty(value = "航空公司代码")
        private String companyCode;

        @ApiModelProperty(value = "开放标识")
        private String openFlag;

        @ApiModelProperty(value = "销售承运人")
        private String marketingCarrier;

        @ApiModelProperty(value = "实际承运人")
        private String operatingCarrier;

        @ApiModelProperty(value = "是否为ARNK段")
        private Boolean segANRK;
    }
}
