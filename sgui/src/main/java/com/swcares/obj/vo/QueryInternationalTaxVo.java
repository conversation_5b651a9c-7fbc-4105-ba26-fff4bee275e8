package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/10 19:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryInternationalTaxVo", description = "国际税费查询返回对象")
public class QueryInternationalTaxVo implements Serializable {

    @ApiModelProperty(value = "销售日期")
    private String salesDate;

    @ApiModelProperty(value = "国家代码")
    private String countryCode;

    @ApiModelProperty(value = "税费代码详情列表")
    private List<TaxCodeDetail> taxCodeDetail;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "TaxCodeDetail", description = "税费代码详情")
    public static class TaxCodeDetail implements Serializable {

        @ApiModelProperty(value = "税费代码")
        private String taxCode;

        @ApiModelProperty(value = "税费类型")
        private String taxtype;

        @ApiModelProperty(value = "税费承运人")
        private String taxCarrier;

        @ApiModelProperty(value = "税费名称")
        private String taxName;

        @ApiModelProperty(value = "税费文本")
        private String taxTexts;
    }
}
