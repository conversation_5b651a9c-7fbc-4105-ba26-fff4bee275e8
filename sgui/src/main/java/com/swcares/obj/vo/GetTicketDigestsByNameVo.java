package com.swcares.obj.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 通过姓名查询客票响应VO
 *
 * <AUTHOR>
 * @date 2025/09/11 16:30
 */
@Data
@ApiModel(value = "GetTicketDigestsByNameVo", description = "通过姓名查询客票响应VO")
public class GetTicketDigestsByNameVo {

    @ApiModelProperty(value = "电子票号（中间4-7位以*号代替）")
    private String etNumber;

    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;

    @ApiModelProperty(value = "旅客类型：ADT-成人，CHD-儿童")
    private String passengerType;

    @ApiModelProperty(value = "航段信息列表")
    private List<AirSeg> airSeg;

    /**
     * 航段信息
     */
    @Data
    @ApiModel(value = "AirSeg", description = "航段信息")
    public static class AirSeg {

        @ApiModelProperty(value = "航空公司代码")
        private String airlineCode;

        @ApiModelProperty(value = "出发机场代码")
        private String depAirportCode;

        @ApiModelProperty(value = "到达机场代码")
        private String arrAirportCode;

        @ApiModelProperty(value = "出发日期")
        private String depDate;

        @ApiModelProperty(value = "航班号")
        private String fltNo;

        @ApiModelProperty(value = "票面状态")
        private String status;

        @ApiModelProperty(value = "承运航空公司")
        private String operateAirline;
    }
}
