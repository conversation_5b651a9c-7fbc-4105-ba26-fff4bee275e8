package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量手工退票请求DTO
 *
 * <AUTHOR>
 * @date 2025/1/2 14:30
 */
@Data
@ApiModel("批量手工退票请求DTO")
public class BatchManualRefundTicketDto {

    @ApiModelProperty("退票列表")
    private List<RefundInfo> refundList;

    @Data
    @ApiModel("退票信息")
    public static class RefundInfo {
        @ApiModelProperty("修改类型")
        private String modificationType;

        @ApiModelProperty("打印号")
        private String prntNo;

        @ApiModelProperty("客票管理机构代码")
        private String ticketManagementOrganizationCode;

        @ApiModelProperty("退票结果预览")
        private ResultPre resultpre;
    }

    @Data
    @ApiModel("退票结果预览")
    public static class ResultPre {
        @ApiModelProperty("金额信息")
        private Amount amount;

        @ApiModelProperty("联程")
        private String conjunction;

        @ApiModelProperty("信用卡")
        private String creditCard;

        @ApiModelProperty("是否优惠券")
        private String isCoupon;

        @ApiModelProperty("办公室")
        private String office;

        @ApiModelProperty("操作员")
        private String operator;

        @ApiModelProperty("备注")
        private String remark;

        @ApiModelProperty("航段列表")
        private List<Object> segList;

        @ApiModelProperty("票信息")
        private Ticket ticket;

        @ApiModelProperty("自愿性")
        private String volunteer;
    }

    @Data
    @ApiModel("金额信息")
    public static class Amount {
        @ApiModelProperty("代理费")
        private String commision;

        @ApiModelProperty("代理费率")
        private String commisionRate;

        @ApiModelProperty("净退款")
        private String netRefund;

        @ApiModelProperty("其他扣除")
        private String otherDeduction;

        @ApiModelProperty("税费列表")
        private List<Tax> taxs;

        @ApiModelProperty("总金额")
        private String totalAmount;

        @ApiModelProperty("总税费")
        private String totalTaxs;
    }

    @Data
    @ApiModel("税费信息")
    public static class Tax {
        @ApiModelProperty("税费名称")
        private String name;

        @ApiModelProperty("税费金额")
        private String value;
    }

    @Data
    @ApiModel("票信息")
    public static class Ticket {
        @ApiModelProperty("航司")
        private String airline;

        @ApiModelProperty("CRS PNR号")
        private String crsPnrNo;

        @ApiModelProperty("货币")
        private String currency;

        @ApiModelProperty("ET标签")
        private String etTag;

        @ApiModelProperty("市场航司")
        private String marketAirline;

        @ApiModelProperty("姓名")
        private String name;

        @ApiModelProperty("支付类型")
        private String payType;

        @ApiModelProperty("PNR")
        private String pnr;

        @ApiModelProperty("旅客类型")
        private String psgType;

        @ApiModelProperty("航段列表")
        private List<Segment> segment;

        @ApiModelProperty("航段列表，另一种表现形式")
        private List<String> couponNos;

        @ApiModelProperty("票号")
        private String ticketNo;

        @ApiModelProperty("二次验证因素")
        private SecondFactor secondFactor;

        @ApiModelProperty("票类型")
        private String tktType;
    }

    @Data
    @ApiModel("航段信息")
    public static class Segment {
        @ApiModelProperty("到达代码")
        private String arriveCode;

        @ApiModelProperty("出发代码")
        private String departureCode;

        @ApiModelProperty("E8 RPH")
        private String e8Rph;

        @ApiModelProperty("是否可用")
        private String isAble;

        @ApiModelProperty("RPH")
        private String rph;

        @ApiModelProperty("票标签")
        private String tktTag;

        @ApiModelProperty("是否选中")
        private String isCheck;
    }

    @Data
    @ApiModel("二次验证因素")
    public static class SecondFactor {
        @ApiModelProperty("二次验证代码")
        private String secondFactorCode;

        @ApiModelProperty("二次验证值")
        private String secondFactorValue;
    }
}
