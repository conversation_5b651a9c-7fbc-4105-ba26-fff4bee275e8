package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通过姓名查询客票请求DTO
 *
 * <AUTHOR>
 * @date 2025/09/11 16:30
 */
@Data
@ApiModel(value = "GetTicketDigestsByNameDto", description = "通过姓名查询客票请求DTO")
public class GetTicketDigestsByNameDto {

    @ApiModelProperty(value = "证件类型代码", required = true)
    private String certCode;

    @ApiModelProperty(value = "证件号（Base64编码的姓名）", required = true)
    private String certNo;
}
