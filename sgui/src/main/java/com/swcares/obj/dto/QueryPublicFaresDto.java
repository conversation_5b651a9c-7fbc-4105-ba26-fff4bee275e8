package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/8 15:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryPublicFaresDto", description = "运价查询传输对象")
public class QueryPublicFaresDto {

    @ApiModelProperty(value = "成人旅客数量")
    private String adtPassNum;

    @ApiModelProperty(value = "航司")
    private String airCode;

    @ApiModelProperty(value = "航司类型")
    private String airlineType;

    @ApiModelProperty(value = "到达城市")
    private String arriveCity;

    @ApiModelProperty(value = "儿童旅客数量")
    private String cnnPassNum;

    @ApiModelProperty(value = "出发城市")
    private String departureCity;

    @ApiModelProperty(value = "旅行日期")
    private String departureDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "婴儿旅客数量")
    private String infPassNum;

    @ApiModelProperty(value = "中转城市")
    private String transitCity;

    @ApiModelProperty(value = "舱位")
    private String cabin;

    @ApiModelProperty(value = "出票日期")
    private String issueDate;
}
