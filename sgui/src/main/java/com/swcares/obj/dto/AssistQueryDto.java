package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/10 10:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "AssistQueryDto", description = "辅助查询传输对象")
public class AssistQueryDto {

    @ApiModelProperty(value = "查询列表")
    private List<String> queryList;

    @ApiModelProperty(value = "航空公司代码列表")
    private List<String> airlineCode;

    @ApiModelProperty(value = "航司")
    private String airline;

    @ApiModelProperty(value = "城市")
    private String city;
}
