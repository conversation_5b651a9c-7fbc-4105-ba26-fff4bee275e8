package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryFareDomesticDto", description = "国内运价查询传输对象")
public class QueryFareDomesticDto implements Serializable {

    @ApiModelProperty(value = "选择的旅客")
    private List<SelectedPassenger> selectedPassengers;

    @ApiModelProperty(value = "公司")
    private String company;

    @ApiModelProperty(value = "协议运价代码")
    private String negotiatedFareCode;

    @ApiModelProperty(value = "是否查询独家协议运价")
    private Boolean queryExclusiveNegotiatedFare;

    @ApiModelProperty(value = "运价基础")
    private String farebasic;

    @ApiModelProperty(value = "allb")
    private String allb;

    @ApiModelProperty(value = "运价类型")
    private String fareType;

    @ApiModelProperty(value = "价格来源")
    private String pricingSource;

    @ApiModelProperty(value = "货币代码")
    private String currencyCode;

    @ApiModelProperty(value = "是否创建PNR")
    private Boolean isCreatePnr;

    @ApiModelProperty(value = "重新定价")
    private String reprice;

    @ApiModelProperty(value = "行程")
    private List<List<VoyageSegment>> voyages;

    @ApiModelProperty(value = "团队数量")
    private Integer groupNum;

    @ApiModelProperty(value = "是否信用卡")
    private Boolean creditCard;

    @ApiModelProperty(value = "折扣代码")
    private String discountCode;

    @ApiModelProperty(value = "运价选项，手工运价时只传该参数，值为M，自动运价时不传该参数")
    private String option;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "SelectedPassenger", description = "选择的旅客")
    public static class SelectedPassenger implements Serializable {
        @ApiModelProperty(value = "旅客类型")
        private String passengerType;

        @ApiModelProperty(value = "选择的数量")
        private Integer selectedNumber;

        @ApiModelProperty(value = "旅客信息列表")
        private List<PassengerInfo> passengerInfos;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "SelectedPassenger", description = "选择的旅客")
    public static class PassengerInfo implements Serializable {
        @ApiModelProperty(value = "身份证号")
        private String idCard;

        @ApiModelProperty(value = "中文名")
        private String chineseName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(value = "VoyageSegment", description = "行程航段")
    public static class VoyageSegment implements Serializable {
        @ApiModelProperty(value = "出发日期时间")
        private String departureDateTime;

        @ApiModelProperty(value = "到达日期时间")
        private String arrivalDateTime;

        @ApiModelProperty(value = "航班号")
        private String flightNumber;

        @ApiModelProperty(value = "出发机场")
        private String departureAirport;

        @ApiModelProperty(value = "到达机场")
        private String arrivalAirport;

        @ApiModelProperty(value = "航班类型")
        private String flightType;

        @ApiModelProperty(value = "舱位代码")
        private String cabinCode;

        @ApiModelProperty(value = "航空公司代码")
        private String companyCode;

        @ApiModelProperty(value = "是否开放")
        private Boolean openFlag;
    }
}
