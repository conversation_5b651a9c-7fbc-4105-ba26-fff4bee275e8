package com.swcares.obj.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/10 18:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "QueryInternationalTaxDto", description = "国际税费查询传输对象")
public class QueryInternationalTaxDto implements Serializable {

    @ApiModelProperty(value = "销售日期")
    private String salesDate;

    @ApiModelProperty(value = "机场城市代码")
    private String airportCityCode;

    @ApiModelProperty(value = "国家代码")
    private String countryCode;

    @ApiModelProperty(value = "税费类型")
    private String taxtype;

    @ApiModelProperty(value = "税费代码")
    private String taxCode;
}
