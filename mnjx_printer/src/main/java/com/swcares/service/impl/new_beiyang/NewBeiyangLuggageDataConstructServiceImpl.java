package com.swcares.service.impl.new_beiyang;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.snbc.sdk.LabelPrinter;
import com.swcares.core.print.PrinterConfiguration;
import com.swcares.service.INewBeiyangLuggageDataConstructService;
import com.swcares.vo.LuggageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 根据打印耗材不同，构建不同的行李条数据
 *
 * <AUTHOR>
 * @date 2023/5/22
 */
@Slf4j
@Service
public class NewBeiyangLuggageDataConstructServiceImpl implements INewBeiyangLuggageDataConstructService {

    @Resource
    private PrinterConfiguration printerConfiguration;

    @Override
    public void constructLuggage(LabelPrinter labelPrinter, LuggageVo luggageVo) {
        log.info("读取到行李条配置：{}", printerConfiguration.getNewBeiyangLuggage());
        switch (printerConfiguration.getNewBeiyangLuggage()) {
            case "上海民航职业技术学院":
                this.constructShanghai(labelPrinter, luggageVo);
                break;
            case "中国航信":
                this.constructTravelsky(labelPrinter, luggageVo);
                break;
            case "南方航空":
                this.constructSouthAirline(labelPrinter, luggageVo);
                break;
            case "重庆海联":
                this.constructHailian(labelPrinter, luggageVo);
                break;
            default:
                break;
        }
    }

    /**
     * Title: constructHailian
     * Description: 海联行李条<br>
     *
     * @param labelPrinter
     * @param luggageVo
     * @return void
     * <AUTHOR>
     * @date 2025/4/9 13:37
     */
    private void constructHailian(LabelPrinter labelPrinter, LuggageVo luggageVo) {
        log.info("打印重庆海联行李条");
        labelPrinter.SetLabelSize(54 * 8, 510 * 8);
        String fltNo = luggageVo.getLuggageSegVos().get(0).getFlightNo();
        String fltDate = luggageVo.getLuggageSegVos().get(0).getFlightDate();
        String dst = luggageVo.getLuggageSegVos().get(0).getDst();
        String dstZh = luggageVo.getLuggageSegVos().get(0).getDstZh();
        labelPrinter.PrintText(4 * 8, 55 * 8, "4", fltNo, 0, 1, 1, 0);

        labelPrinter.PrintText(5 * 8, 50 * 8, "2", "NM:", 0, 1, 1, 0);
        labelPrinter.PrintTrueTypeText(12 * 8, 50 * 8, "2", 20, 20, luggageVo.getName(), 0, 0);

        labelPrinter.PrintText(5 * 8, 45 * 8, "2", "SEQ NBR:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 45 * 8, "2", luggageVo.getSeqNo(), 0, 0, 0, 0);

        String bagCntWeight = luggageVo.getBagCnt() + "/" + luggageVo.getBagWeight();
        labelPrinter.PrintText(5 * 8, 40 * 8, "2", "BAGS/WT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 40 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 35 * 8, "2", "ISSUED AT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 35 * 8, "2", luggageVo.getBagNoAirlineCode(), 0, 0, 0, 0);

        String to = dst + " " + fltNo + " " + fltDate;
        labelPrinter.PrintText(5 * 8, 30 * 8, "2", "TO:", 0, 0, 0, 0);
        labelPrinter.PrintText(10 * 8, 30 * 8, "2", to, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 25 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintText(5 * 8, 22 * 8, "2", "VIA:", 0, 0, 0, 0);

        labelPrinter.PrintBarcode1D(6 * 8, 7 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        String to2 = fltNo + " " + fltDate + " " + dst;
        labelPrinter.PrintText(5 * 8, 83 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(6 * 8, 68 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 115 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(6 * 8, 100 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 140 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 143 * 8, 50 * 8, 143 * 8, 1);
        labelPrinter.PrintText(5 * 8, 155 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 158 * 8, 50 * 8, 158 * 8, 1);

        labelPrinter.PrintLine(0 * 8, 184 * 8, 50 * 8, 184 * 8, 1);
        labelPrinter.PrintText(5 * 8, 180 * 8, "2", "TO:", 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 180 * 8, "3", dst, 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 175 * 8, "2", PinyinUtil.getPinyin(dstZh, StrUtil.EMPTY).toUpperCase(), 0, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(15 * 8, 169 * 8, "2", 20, 20, dstZh, 0, 0);
        labelPrinter.PrintText(5 * 8, 160 * 8, "2", fltNo + "  " + fltDate, 0, 0, 0, 0);

        labelPrinter.PrintBarcode1D(8 * 8, 213 * 8, 1, 90, "B" + luggageVo.getBarCode(), 38 * 8, 0, 1, 2);
        labelPrinter.PrintBarcode1D(8 * 8, 218 * 8, 1, 0, "B" + luggageVo.getBarCode(), 33 * 8, 0, 2, 4);

        labelPrinter.PrintLine(0 * 8, 257 * 8, 50 * 8, 257 * 8, 1);
        labelPrinter.PrintText(11 * 8, 293 * 8, "4", fltNo, 0, 1, 1, 0);

        labelPrinter.PrintText(5 * 8, 285 * 8, "2", "NM:", 0, 1, 1, 0);
        labelPrinter.PrintTrueTypeText(12 * 8, 285 * 8, "2", 20, 20, luggageVo.getName(), 0, 0);

        labelPrinter.PrintText(5 * 8, 280 * 8, "2", "SEQ NBR:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 280 * 8, "2", luggageVo.getSeqNo(), 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 275 * 8, "2", "BAGS/WT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 275 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 270 * 8, "2", "ISSUED AT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 270 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 265 * 8, "2", "DATE:", 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 265 * 8, "2", fltDate, 0, 0, 0, 0);

        labelPrinter.PrintLine(0 * 8, 298 * 8, 50 * 8, 298 * 8, 1);
        labelPrinter.PrintBarcode1D(8 * 8, 305 * 8, 1, 0, "B" + luggageVo.getBarCode(), 33 * 8, 0, 2, 4);
        labelPrinter.PrintBarcode1D(8 * 8, 378 * 8, 1, 90, "B" + luggageVo.getBarCode(), 38 * 8, 0, 1, 2);

        labelPrinter.PrintLine(0 * 8, 385 * 8, 50 * 8, 385 * 8, 1);
        labelPrinter.PrintText(46 * 8, 389 * 8, "2", "TO:", 180, 0, 0, 0);
        labelPrinter.PrintText(35 * 8, 393 * 8, "3", dst, 180, 0, 0, 0);
        labelPrinter.PrintText(35 * 8, 398 * 8, "2", PinyinUtil.getPinyin(dstZh, StrUtil.EMPTY).toUpperCase(), 180, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(25 * 8, 402 * 8, "2", 20, 20, dstZh, 180, 0);
        labelPrinter.PrintText(46 * 8, 408 * 8, "2", fltNo + "  " + fltDate, 180, 0, 0, 0);

        labelPrinter.PrintLine(0 * 8, 411 * 8, 50 * 8, 411 * 8, 1);
        labelPrinter.PrintText(46 * 8, 414 * 8, "2", "VIA:", 180, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 433 * 8, 50 * 8, 433 * 8, 1);
        labelPrinter.PrintText(46 * 8, 436 * 8, "2", "VIA:", 180, 0, 0, 0);

        labelPrinter.PrintText(46 * 8, 475 * 8, "2", to2, 180, 1, 1, 0);
        labelPrinter.PrintBarcode1D(8 * 8, 477 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 0, 2, 4);
        labelPrinter.PrintText(40 * 8, 490 * 8, "2", luggageVo.getBarCode(), 180, 1, 1, 0);
    }

    private void constructShanghai(LabelPrinter labelPrinter, LuggageVo luggageVo) {
        log.info("打印上海航空行李条");
        String fltNo = luggageVo.getLuggageSegVos().get(0).getFlightNo();
        String fltDate = luggageVo.getLuggageSegVos().get(0).getFlightDate();
        String dst = luggageVo.getLuggageSegVos().get(0).getDst();
        String dstZh = luggageVo.getLuggageSegVos().get(0).getDstZh();
        labelPrinter.PrintText(4 * 8, 55 * 8, "4", fltNo, 0, 1, 1, 0);

        labelPrinter.PrintText(5 * 8, 50 * 8, "2", "NM:", 0, 1, 1, 0);
        labelPrinter.PrintTrueTypeText(12 * 8, 50 * 8, "2", 20, 20, luggageVo.getName(), 0, 0);

        labelPrinter.PrintText(5 * 8, 45 * 8, "2", "SEQ NBR:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 45 * 8, "2", luggageVo.getSeqNo(), 0, 0, 0, 0);

        String bagCntWeight = luggageVo.getBagCnt() + "/" + luggageVo.getBagWeight();
        labelPrinter.PrintText(5 * 8, 40 * 8, "2", "BAGS/WT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 40 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 35 * 8, "2", "ISSUED AT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 35 * 8, "2", luggageVo.getBagNoAirlineCode(), 0, 0, 0, 0);

        String to = dst + " " + fltNo + " " + fltDate;
        labelPrinter.PrintText(5 * 8, 30 * 8, "2", "TO:", 0, 0, 0, 0);
        labelPrinter.PrintText(10 * 8, 30 * 8, "2", to, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 25 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintText(5 * 8, 25 * 8, "2", "VIA:", 0, 0, 0, 0);

        labelPrinter.PrintBarcode1D(8 * 8, 8 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        String to2 = fltNo + " " + fltDate + " " + dst;
        labelPrinter.PrintText(5 * 8, 78 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(8 * 8, 63 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 98 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(8 * 8, 83 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 121 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 124 * 8, 50 * 8, 124 * 8, 1);
        labelPrinter.PrintText(5 * 8, 145 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 148 * 8, 50 * 8, 148 * 8, 1);

        labelPrinter.PrintLine(0 * 8, 174 * 8, 50 * 8, 174 * 8, 1);
        labelPrinter.PrintText(5 * 8, 170 * 8, "2", "TO:", 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 170 * 8, "3", dst, 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 165 * 8, "2", PinyinUtil.getPinyin(dstZh, StrUtil.EMPTY).toUpperCase(), 0, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(15 * 8, 160 * 8, "2", 20, 20, dstZh, 0, 0);
        labelPrinter.PrintText(5 * 8, 150 * 8, "2", fltNo + "  " + fltDate, 0, 0, 0, 0);

        labelPrinter.PrintBarcode1D(8 * 8, 213 * 8, 1, 90, "B" + luggageVo.getBarCode(), 38 * 8, 0, 1, 2);
        labelPrinter.PrintBarcode1D(8 * 8, 218 * 8, 1, 0, "B" + luggageVo.getBarCode(), 33 * 8, 0, 2, 4);

        labelPrinter.PrintLine(0 * 8, 257 * 8, 50 * 8, 257 * 8, 1);
        labelPrinter.PrintText(11 * 8, 293 * 8, "4", fltNo, 0, 1, 1, 0);

        labelPrinter.PrintText(5 * 8, 285 * 8, "2", "NM:", 0, 1, 1, 0);
        labelPrinter.PrintTrueTypeText(12 * 8, 285 * 8, "2", 20, 20, luggageVo.getName(), 0, 0);

        labelPrinter.PrintText(5 * 8, 280 * 8, "2", "SEQ NBR:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 280 * 8, "2", luggageVo.getSeqNo(), 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 275 * 8, "2", "BAGS/WT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 275 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 270 * 8, "2", "ISSUED AT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 270 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 265 * 8, "2", "DATE:", 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 265 * 8, "2", fltDate, 0, 0, 0, 0);

        labelPrinter.PrintLine(0 * 8, 298 * 8, 50 * 8, 298 * 8, 1);
        labelPrinter.PrintBarcode1D(8 * 8, 305 * 8, 1, 0, "B" + luggageVo.getBarCode(), 33 * 8, 0, 2, 4);
        labelPrinter.PrintBarcode1D(8 * 8, 378 * 8, 1, 90, "B" + luggageVo.getBarCode(), 38 * 8, 0, 1, 2);

        labelPrinter.PrintLine(0 * 8, 385 * 8, 50 * 8, 385 * 8, 1);
        labelPrinter.PrintText(46 * 8, 389 * 8, "2", "TO:", 180, 0, 0, 0);
        labelPrinter.PrintText(35 * 8, 393 * 8, "3", dst, 180, 0, 0, 0);
        labelPrinter.PrintText(35 * 8, 398 * 8, "2", PinyinUtil.getPinyin(dstZh, StrUtil.EMPTY).toUpperCase(), 180, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(25 * 8, 402 * 8, "2", 20, 20, dstZh, 180, 0);
        labelPrinter.PrintText(46 * 8, 408 * 8, "2", fltNo + "  " + fltDate, 180, 0, 0, 0);

        labelPrinter.PrintLine(0 * 8, 411 * 8, 50 * 8, 411 * 8, 1);
        labelPrinter.PrintText(46 * 8, 414 * 8, "2", "VIA:", 180, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 433 * 8, 50 * 8, 433 * 8, 1);
        labelPrinter.PrintText(46 * 8, 436 * 8, "2", "VIA:", 180, 0, 0, 0);

        labelPrinter.PrintText(46 * 8, 470 * 8, "2", to2, 180, 1, 1, 0);
        labelPrinter.PrintBarcode1D(42 * 8, 485 * 8, 1, 180, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

    }

    private void constructTravelsky(LabelPrinter labelPrinter, LuggageVo luggageVo) {
        log.info("打印中国航信行李条");
        String fltNo = luggageVo.getLuggageSegVos().get(0).getFlightNo();
        String fltDate = luggageVo.getLuggageSegVos().get(0).getFlightDate();
        String dst = luggageVo.getLuggageSegVos().get(0).getDst();
        String dstZh = luggageVo.getLuggageSegVos().get(0).getDstZh();
        labelPrinter.PrintText(4 * 8, 55 * 8, "4", fltNo, 0, 1, 1, 0);

        labelPrinter.PrintText(5 * 8, 50 * 8, "2", "NM:", 0, 1, 1, 0);
        labelPrinter.PrintTrueTypeText(12 * 8, 50 * 8, "2", 20, 20, luggageVo.getName(), 0, 0);

        labelPrinter.PrintText(5 * 8, 45 * 8, "2", "SEQ NBR:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 45 * 8, "2", luggageVo.getSeqNo(), 0, 0, 0, 0);

        String bagCntWeight = luggageVo.getBagCnt() + "/" + luggageVo.getBagWeight();
        labelPrinter.PrintText(5 * 8, 40 * 8, "2", "BAGS/WT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 40 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 35 * 8, "2", "ISSUED AT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 35 * 8, "2", luggageVo.getBagNoAirlineCode(), 0, 0, 0, 0);

        String to = dst + " " + fltNo + " " + fltDate;
        labelPrinter.PrintText(5 * 8, 30 * 8, "2", "TO:", 0, 0, 0, 0);
        labelPrinter.PrintText(10 * 8, 30 * 8, "2", to, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 25 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintText(5 * 8, 22 * 8, "2", "VIA:", 0, 0, 0, 0);

        labelPrinter.PrintBarcode1D(6 * 8, 7 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        String to2 = fltNo + " " + fltDate + " " + dst;
        labelPrinter.PrintText(5 * 8, 79 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(6 * 8, 64 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 99 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(6 * 8, 84 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 121 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 124 * 8, 50 * 8, 124 * 8, 1);
        labelPrinter.PrintText(5 * 8, 145 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 148 * 8, 50 * 8, 148 * 8, 1);

        labelPrinter.PrintLine(0 * 8, 174 * 8, 50 * 8, 174 * 8, 1);
        labelPrinter.PrintText(5 * 8, 170 * 8, "2", "TO:", 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 170 * 8, "3", dst, 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 165 * 8, "2", PinyinUtil.getPinyin(dstZh, StrUtil.EMPTY).toUpperCase(), 0, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(15 * 8, 160 * 8, "2", 20, 20, dstZh, 0, 0);
        labelPrinter.PrintText(5 * 8, 150 * 8, "2", fltNo + "  " + fltDate, 0, 0, 0, 0);

        labelPrinter.PrintBarcode1D(8 * 8, 213 * 8, 1, 90, "B" + luggageVo.getBarCode(), 38 * 8, 0, 1, 2);
        labelPrinter.PrintBarcode1D(8 * 8, 218 * 8, 1, 0, "B" + luggageVo.getBarCode(), 33 * 8, 0, 2, 4);

        labelPrinter.PrintLine(0 * 8, 257 * 8, 50 * 8, 257 * 8, 1);
        labelPrinter.PrintText(11 * 8, 293 * 8, "4", fltNo, 0, 1, 1, 0);

        labelPrinter.PrintText(5 * 8, 285 * 8, "2", "NM:", 0, 1, 1, 0);
        labelPrinter.PrintTrueTypeText(12 * 8, 285 * 8, "2", 20, 20, luggageVo.getName(), 0, 0);

        labelPrinter.PrintText(5 * 8, 280 * 8, "2", "SEQ NBR:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 280 * 8, "2", luggageVo.getSeqNo(), 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 275 * 8, "2", "BAGS/WT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 275 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 270 * 8, "2", "ISSUED AT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 270 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 265 * 8, "2", "DATE:", 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 265 * 8, "2", fltDate, 0, 0, 0, 0);

        labelPrinter.PrintLine(0 * 8, 298 * 8, 50 * 8, 298 * 8, 1);
        labelPrinter.PrintBarcode1D(8 * 8, 305 * 8, 1, 0, "B" + luggageVo.getBarCode(), 33 * 8, 0, 2, 4);
        labelPrinter.PrintBarcode1D(8 * 8, 378 * 8, 1, 90, "B" + luggageVo.getBarCode(), 38 * 8, 0, 1, 2);

        labelPrinter.PrintLine(0 * 8, 385 * 8, 50 * 8, 385 * 8, 1);
        labelPrinter.PrintText(46 * 8, 389 * 8, "2", "TO:", 180, 0, 0, 0);
        labelPrinter.PrintText(35 * 8, 393 * 8, "3", dst, 180, 0, 0, 0);
        labelPrinter.PrintText(35 * 8, 398 * 8, "2", PinyinUtil.getPinyin(dstZh, StrUtil.EMPTY).toUpperCase(), 180, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(25 * 8, 402 * 8, "2", 20, 20, dstZh, 180, 0);
        labelPrinter.PrintText(46 * 8, 408 * 8, "2", fltNo + "  " + fltDate, 180, 0, 0, 0);

        labelPrinter.PrintLine(0 * 8, 411 * 8, 50 * 8, 411 * 8, 1);
        labelPrinter.PrintText(46 * 8, 414 * 8, "2", "VIA:", 180, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 433 * 8, 50 * 8, 433 * 8, 1);
        labelPrinter.PrintText(46 * 8, 436 * 8, "2", "VIA:", 180, 0, 0, 0);

        labelPrinter.PrintText(46 * 8, 470 * 8, "2", to2, 180, 1, 1, 0);
        labelPrinter.PrintBarcode1D(8 * 8, 472 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 0, 2, 4);
        labelPrinter.PrintText(40 * 8, 485 * 8, "2", luggageVo.getBarCode(), 180, 1, 1, 0);
    }

    private void constructSouthAirline(LabelPrinter labelPrinter, LuggageVo luggageVo) {
        log.info("打印南方航空行李条");
        String fltNo = luggageVo.getLuggageSegVos().get(0).getFlightNo();
        String fltDate = luggageVo.getLuggageSegVos().get(0).getFlightDate();
        String dst = luggageVo.getLuggageSegVos().get(0).getDst();
        String dstZh = luggageVo.getLuggageSegVos().get(0).getDstZh();
        labelPrinter.PrintText(4 * 8, 110 * 8, "4", fltNo, 0, 1, 1, 0);

        labelPrinter.PrintText(5 * 8, 105 * 8, "2", "NM:", 0, 1, 1, 0);
        labelPrinter.PrintTrueTypeText(12 * 8, 105 * 8, "2", 20, 20, luggageVo.getName(), 0, 0);

        labelPrinter.PrintText(5 * 8, 100 * 8, "2", "SEQ NBR:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 100 * 8, "2", luggageVo.getSeqNo(), 0, 0, 0, 0);

        String bagCntWeight = luggageVo.getBagCnt() + "/" + luggageVo.getBagWeight();
        labelPrinter.PrintText(5 * 8, 95 * 8, "2", "BAGS/WT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 95 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 90 * 8, "2", "ISSUED AT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 90 * 8, "2", luggageVo.getBagNoAirlineCode(), 0, 0, 0, 0);

        String to = dst + " " + fltNo + " " + fltDate;
        labelPrinter.PrintText(5 * 8, 85 * 8, "2", "TO:", 0, 0, 0, 0);
        labelPrinter.PrintText(10 * 8, 85 * 8, "2", to, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 80 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintText(5 * 8, 80 * 8, "2", "VIA:", 0, 0, 0, 0);

        labelPrinter.PrintBarcode1D(6 * 8, 65 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        String to2 = fltNo + " " + fltDate + " " + dst;
        labelPrinter.PrintText(5 * 8, 58 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(6 * 8, 43 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 40 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(6 * 8, 25 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 22 * 8, "2", to2, 0, 1, 1, 0);
        labelPrinter.PrintBarcode1D(6 * 8, 7 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 1, 2, 4);

        labelPrinter.PrintText(5 * 8, 121 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 124 * 8, 50 * 8, 124 * 8, 1);
        labelPrinter.PrintText(5 * 8, 145 * 8, "2", "VIA:", 0, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 148 * 8, 50 * 8, 148 * 8, 1);

        labelPrinter.PrintLine(0 * 8, 174 * 8, 50 * 8, 174 * 8, 1);
        labelPrinter.PrintText(5 * 8, 170 * 8, "2", "TO:", 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 170 * 8, "3", dst, 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 165 * 8, "2", PinyinUtil.getPinyin(dstZh, StrUtil.EMPTY).toUpperCase(), 0, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(15 * 8, 160 * 8, "2", 20, 20, dstZh, 0, 0);
        labelPrinter.PrintText(5 * 8, 150 * 8, "2", fltNo + "  " + fltDate, 0, 0, 0, 0);

        labelPrinter.PrintBarcode1D(8 * 8, 213 * 8, 1, 90, "B" + luggageVo.getBarCode(), 38 * 8, 0, 1, 2);
        labelPrinter.PrintBarcode1D(8 * 8, 218 * 8, 1, 0, "B" + luggageVo.getBarCode(), 33 * 8, 0, 2, 4);

        labelPrinter.PrintLine(0 * 8, 257 * 8, 50 * 8, 257 * 8, 1);
        labelPrinter.PrintText(11 * 8, 293 * 8, "4", fltNo, 0, 1, 1, 0);

        labelPrinter.PrintText(5 * 8, 285 * 8, "2", "NM:", 0, 1, 1, 0);
        labelPrinter.PrintTrueTypeText(12 * 8, 285 * 8, "2", 20, 20, luggageVo.getName(), 0, 0);

        labelPrinter.PrintText(5 * 8, 280 * 8, "2", "SEQ NBR:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 280 * 8, "2", luggageVo.getSeqNo(), 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 275 * 8, "2", "BAGS/WT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 275 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 270 * 8, "2", "ISSUED AT:", 0, 0, 0, 0);
        labelPrinter.PrintText(25 * 8, 270 * 8, "2", bagCntWeight, 0, 0, 0, 0);

        labelPrinter.PrintText(5 * 8, 265 * 8, "2", "DATE:", 0, 0, 0, 0);
        labelPrinter.PrintText(15 * 8, 265 * 8, "2", fltDate, 0, 0, 0, 0);

        labelPrinter.PrintLine(0 * 8, 298 * 8, 50 * 8, 298 * 8, 1);
        labelPrinter.PrintBarcode1D(8 * 8, 305 * 8, 1, 0, "B" + luggageVo.getBarCode(), 33 * 8, 0, 2, 4);
        labelPrinter.PrintBarcode1D(8 * 8, 378 * 8, 1, 90, "B" + luggageVo.getBarCode(), 38 * 8, 0, 1, 2);

        labelPrinter.PrintLine(0 * 8, 385 * 8, 50 * 8, 385 * 8, 1);
        labelPrinter.PrintText(46 * 8, 389 * 8, "2", "TO:", 180, 0, 0, 0);
        labelPrinter.PrintText(35 * 8, 393 * 8, "3", dst, 180, 0, 0, 0);
        labelPrinter.PrintText(35 * 8, 398 * 8, "2", PinyinUtil.getPinyin(dstZh, StrUtil.EMPTY).toUpperCase(), 180, 0, 0, 0);
        labelPrinter.PrintTrueTypeText(25 * 8, 402 * 8, "2", 20, 20, dstZh, 180, 0);
        labelPrinter.PrintText(46 * 8, 408 * 8, "2", fltNo + "  " + fltDate, 180, 0, 0, 0);

        labelPrinter.PrintLine(0 * 8, 411 * 8, 50 * 8, 411 * 8, 1);
        labelPrinter.PrintText(46 * 8, 414 * 8, "2", "VIA:", 180, 0, 0, 0);
        labelPrinter.PrintLine(0 * 8, 433 * 8, 50 * 8, 433 * 8, 1);
        labelPrinter.PrintText(46 * 8, 436 * 8, "2", "VIA:", 180, 0, 0, 0);

        labelPrinter.PrintText(46 * 8, 470 * 8, "2", to2, 180, 1, 1, 0);
        labelPrinter.PrintBarcode1D(8 * 8, 472 * 8, 1, 0, "B" + luggageVo.getBarCode(), 10 * 8, 0, 2, 4);
        labelPrinter.PrintText(40 * 8, 485 * 8, "2", luggageVo.getBarCode(), 180, 1, 1, 0);
    }
}
