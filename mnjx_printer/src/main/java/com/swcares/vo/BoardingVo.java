package com.swcares.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 登机牌
 *
 * <AUTHOR>
 */
@ApiModel(value = "boardingVo", description = "登机牌打印规范（17项，18个序号）")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BoardingVo {
    /**
     * 中文姓名
     */
    @ApiModelProperty("中文姓名")
    private String nameZh;
    /**
     * 航班号
     */
    @ApiModelProperty("航班号")
    private String flightNo;
    /**
     * 始发站
     */
    @ApiModelProperty("始发站")
    private String from;
    /**
     * 始发站中文
     */
    @ApiModelProperty("始发站")
    private String fromZh;
    /**
     * 目的站
     */
    @ApiModelProperty("目的站")
    private String to;
    /**
     * 目的站中文
     */
    @ApiModelProperty("目的站")
    private String toZh;
    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    private String flightDate;
    /**
     * 登机序号
     */
    @ApiModelProperty("登机序号")
    private String boardingNo;
    /**
     * 舱位
     */
    @ApiModelProperty("舱位")
    private String cabin;
    /**
     * 常旅客卡号
     */
    @ApiModelProperty("常旅客卡号")
    private String frequentNo;
    /**
     * 登机口
     */
    @ApiModelProperty("登机口")
    private String gate;
    /**
     * 登机时间
     */
    @ApiModelProperty("登机时间")
    private String boardingTime;
    /**
     * 座位号
     */
    @ApiModelProperty("座位号")
    private String seatNo;
    /**
     * 票号
     */
    @ApiModelProperty("票号")
    private String ticketNo;
    /**
     * 票号前缀
     */
    @ApiModelProperty("票号前缀")
    private String ticketPre;
    /**
     * 条形码
     */
    @ApiModelProperty("条形码信息")
    private String barcode;
}
