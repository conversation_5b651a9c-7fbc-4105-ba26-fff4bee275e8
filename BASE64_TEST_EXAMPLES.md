# Base64编码测试示例

## 中文姓名Base64编码示例

### 常见姓名编码
```
张三 -> 5byg5LiJ
李四 -> 5Lit5Zub
王五 -> 546L5LqU
赵六 -> 6LW15YWt
钱七 -> 6ZKx5LiD
孙八 -> 5a2Z5YWr
周九 -> 5ZGo5Lmd
吴十 -> 5ZC05Y2B
```

### 英文姓名编码
```
ZHANG/SAN -> WkhBTkcvU0FO
LI/SI -> TEkvU0k
WANG/WU -> V0FORy9XVQ==
```

### 复杂姓名编码
```
欧阳修 -> 5qyh6Ziz5L+u
司马光 -> 5Y+45LmQ5YWJ
诸葛亮 -> 6K+46JGb5Lqu
```

## Java测试代码

### 编码测试
```java
import java.util.Base64;
import java.nio.charset.StandardCharsets;

public class Base64Test {
    public static void main(String[] args) {
        String[] names = {"张三", "李四", "王五", "赵六"};
        
        for (String name : names) {
            String encoded = Base64.getEncoder().encodeToString(name.getBytes(StandardCharsets.UTF_8));
            System.out.println(name + " -> " + encoded);
        }
    }
}
```

### 解码测试
```java
public static void testDecode() {
    String[] encodedNames = {"5byg5LiJ", "5Lit5Zub", "546L5LqU", "6LW15YWt"};
    
    for (String encoded : encodedNames) {
        String decoded = new String(Base64.getDecoder().decode(encoded), StandardCharsets.UTF_8);
        System.out.println(encoded + " -> " + decoded);
    }
}
```

## 接口测试用例

### 请求示例1
```json
{
  "certCode": "NM",
  "certNo": "5byg5LiJ"
}
```
解码后姓名：张三

### 请求示例2
```json
{
  "certCode": "NM", 
  "certNo": "5Lit5Zub"
}
```
解码后姓名：李四

### 请求示例3
```json
{
  "certCode": "NM",
  "certNo": "546L5LqU"
}
```
解码后姓名：王五

## 票号脱敏测试

### 脱敏逻辑
```java
public String maskTicketNo(String ticketNo) {
    if (CharSequenceUtil.isNotEmpty(ticketNo) && ticketNo.length() >= 10) {
        return ticketNo.substring(0, 3) + "-" + 
               ticketNo.substring(3, 6) + "****" + 
               ticketNo.substring(10);
    }
    return ticketNo;
}
```

### 脱敏示例
```
7813551234681 -> 781-355****681
7813552345678 -> 781-355****678
7813553456789 -> 781-355****789
```

## 航段序号解析测试

### 解析逻辑测试
```java
// 单个数字
parseSegmentNumbers("1") -> [1]
parseSegmentNumbers("2") -> [2]

// 逗号分隔
parseSegmentNumbers("1,3") -> [1, 3]
parseSegmentNumbers("1,2,4") -> [1, 2, 4]

// 范围格式
parseSegmentNumbers("1-2") -> [1, 2]
parseSegmentNumbers("1-4") -> [1, 2, 3, 4]

// 异常情况
parseSegmentNumbers("") -> []
parseSegmentNumbers("abc") -> []
parseSegmentNumbers("1-") -> []
```

## 时间范围测试

### 时间计算示例
```java
// 当前日期：2025-09-11
String currentDate = "2025-09-11";
String startDate = "2025-08-12"; // 30天前

// 符合条件的日期
"2025-09-11" -> true (当天)
"2025-09-01" -> true (过去30天内)
"2025-08-12" -> true (正好30天前)

// 不符合条件的日期
"2025-08-11" -> false (超过30天)
"2025-09-12" -> false (未来日期)
```
