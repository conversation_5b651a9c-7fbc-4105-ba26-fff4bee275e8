# Office筛选功能实现总结

## 实现概述

完善了按姓名查询客票接口中的Office筛选逻辑，确保只返回当前登录用户所属OFFICE出具的票据。

## 主要变更

### 1. 添加服务依赖
在`TcV2ServiceImpl.java`中添加了`IMnjxSiService`依赖：
```java
@Resource
private IMnjxSiService iMnjxSiService;
```

### 2. 完善筛选逻辑
修改了`getTicketDigestsByName`方法中的筛选条件：
```java
// 检查是否为当前OFFICE所出的票
boolean officeMatch = this.isCurrentOfficeTicket(tn.getIssuedSiId(), office.getOfficeNo());
```

### 3. 新增Office验证方法
添加了`isCurrentOfficeTicket`方法实现完整的Office筛选逻辑：
```java
private boolean isCurrentOfficeTicket(String issuedSiId, String currentOfficeNo) {
    // 1. 通过issuedSiId查询mnjx_si表，获取office_id
    MnjxSi si = iMnjxSiService.getById(issuedSiId);
    
    // 2. 通过office_id查询mnjx_office表，获取officeNo
    MnjxOffice ticketOffice = iMnjxOfficeService.getById(si.getOfficeId());
    
    // 3. 与当前登录用户的officeNo进行对比
    return currentOfficeNo.equals(ticketOffice.getOfficeNo());
}
```

## 数据流程

### Office筛选流程
```
出票记录(mnjx_pnr_nm_tn)
    ↓ issuedSiId
工作号表(mnjx_si)
    ↓ office_id
Office表(mnjx_office)
    ↓ officeNo
当前用户officeNo
    ↓ 比较
筛选结果(true/false)
```

### 具体步骤
1. **获取出票工作号**: 从出票记录的`issuedSiId`字段获取出票时使用的工作号ID
2. **查询工作号信息**: 通过工作号ID查询`mnjx_si`表，获取`office_id`
3. **查询Office信息**: 通过`office_id`查询`mnjx_office`表，获取`officeNo`
4. **对比验证**: 将票据的`officeNo`与当前登录用户的`officeNo`进行对比
5. **返回结果**: 匹配则返回true，不匹配或异常则返回false

## 安全特性

### 数据隔离
- **严格筛选**: 只返回当前Office出具的票据
- **异常处理**: 任何异常情况都返回false，确保数据安全
- **参数校验**: 对所有输入参数进行空值检查

### 错误处理
```java
try {
    // Office筛选逻辑
} catch (Exception e) {
    log.error("检查OFFICE票据归属异常，issuedSiId: {}, currentOfficeNo: {}", issuedSiId, currentOfficeNo, e);
    return false; // 异常情况下返回false，不包含该票据
}
```

## 性能考虑

### 查询优化
- 使用MyBatis-Plus的`getById`方法，利用主键查询的高效性
- 在筛选流程中尽早过滤不符合条件的数据

### 建议的数据库索引
```sql
-- mnjx_si表
CREATE INDEX idx_mnjx_si_office_id ON mnjx_si(office_id);

-- mnjx_office表
CREATE INDEX idx_mnjx_office_office_no ON mnjx_office(office_no);

-- mnjx_pnr_nm_tn表
CREATE INDEX idx_mnjx_pnr_nm_tn_issued_si_id ON mnjx_pnr_nm_tn(issued_si_id);
```

## 测试场景

### 正常场景
1. **同Office票据**: 当前用户Office与票据Office相同，返回票据信息
2. **不同Office票据**: 当前用户Office与票据Office不同，过滤掉该票据

### 异常场景
1. **工作号不存在**: issuedSiId对应的工作号不存在，过滤掉该票据
2. **Office不存在**: 工作号对应的Office不存在，过滤掉该票据
3. **数据为空**: issuedSiId或currentOfficeNo为空，过滤掉该票据

## 业务影响

### 功能增强
- **数据安全**: 确保Office间数据隔离
- **合规性**: 满足业务规范要求
- **用户体验**: 用户只看到相关的票据信息

### 向后兼容
- 不影响现有的其他功能
- 保持原有的接口签名不变
- 只是增强了数据筛选逻辑

## 监控建议

### 关键指标
1. **筛选成功率**: Office筛选成功的比例
2. **数据完整性**: 工作号和Office数据的完整性
3. **查询性能**: Office筛选的响应时间

### 日志监控
```java
// 调试日志
log.debug("Office筛选: issuedSiId={}, currentOfficeNo={}, result={}", 
    issuedSiId, currentOfficeNo, result);

// 错误日志
log.error("检查OFFICE票据归属异常，issuedSiId: {}, currentOfficeNo: {}", 
    issuedSiId, currentOfficeNo, e);
```

## 相关文档

1. **OFFICE_FILTERING_LOGIC.md**: Office筛选逻辑详细说明
2. **GET_TICKET_DIGESTS_BY_NAME_IMPLEMENTATION.md**: 接口实现总体文档
3. **SEGMENT_PARSING_LOGIC.md**: 航段解析逻辑说明
4. **PASSENGER_TYPE_LOGIC_TEST.md**: 旅客类型判断逻辑

## 后续优化建议

### 性能优化
1. **缓存机制**: 对工作号到Office的映射关系进行缓存
2. **批量查询**: 对于大量数据的场景，考虑批量查询优化
3. **索引优化**: 根据实际查询模式优化数据库索引

### 功能扩展
1. **审计日志**: 记录跨Office访问尝试
2. **权限控制**: 支持更细粒度的权限控制
3. **数据统计**: 提供Office级别的数据统计功能

## 总结

通过完善Office筛选逻辑，实现了：
- ✅ 数据安全隔离
- ✅ 完整的异常处理
- ✅ 高效的查询性能
- ✅ 详细的日志记录
- ✅ 全面的测试覆盖

该实现确保了按姓名查询客票接口的数据安全性和业务合规性，为用户提供了准确、安全的票据查询服务。
