# 备注

    dev_tmp:是BIO的多线程版本，内存对象绑定在线程上面的，可以正常使用,因为使用的是线程池，并且是长链接，所以在队列中的线程不能被执行
    dev_BIO:是BIO的多线程版本，内存对象绑定在线程上面的，可以正常使用，他就是dev_tmp的版本,,因为使用的是线程池，并且是长链接，所以在队列中的线程不能被执行
    dev_NIO:是NIO的非阻塞版本，内存对象是不完善的版本，不要使用,因为使用的是线程池，并且是长链接，所以在队列中的线程不能被执行
    dev_selector: 是NIO的selector和非阻塞一起使用的版本，不要使用
    dev_netty:是我们最终的版本，使用netty框架

    NIO的版本是不完善的版本，不要使用
    BIO是需要多个线程来支持多用户的连接，有缺陷
    NIO的阻塞版本和BIO是一样的了。
    NIO非阻塞的版本就只需要一个线程就可以了，但是CPU会空转，并且一般是和selector一起使用了
    netty是最终的版本。我们后期都是这样的了
####开发说明
    xxParam参数对象
    xxDto 数据传输对象
    xxOption 存放指令的常量
    xxError 存放指令的错误列表

####查看系统发的tcp连接数
    netstat -an |grep 'TIME_WAIT' |grep 'tcp'

####查询监听的端口
    netstat -an |grep 'tcp' | grep '370'
    
####查询监听的端口（使用或者关系）

    -a或--all 显示所有连线中的Socket。
    
    -A<网络类型>或--<网络类型> 列出该网络类型连线中的相关地址。
    
    -c或--continuous 持续列出网络状态。
    
    -C或--cache 显示路由器配置的快取信息。
    
    -e或--extend 显示网络其他相关信息。
    -F或--fib 显示FIB。
    -g或--groups 显示多重广播功能群组组员名单。
    -h或--help 在线帮助。
    -i或--interfaces 显示网络界面信息表单。
    -l或--listening 显示监控中的服务器的Socket。
    -M或--masquerade 显示伪装的网络连线。
    -n或--numeric 直接使用IP地址，而不通过域名服务器。
    -N或--netlink或--symbolic 显示网络硬件外围设备的符号连接名称。
    -o或--timers 显示计时器。
    -p或--programs 显示正在使用Socket的程序识别码和程序名称。
    -r或--route 显示Routing Table。
    -s或--statistics 显示网络工作信息统计表。
    -t或--tcp 显示TCP传输协议的连线状况。
    -u或--udp 显示UDP传输协议的连线状况。
    -v或--verbose 显示指令执行过程。
    -V或--version 显示版本信息。
    -w或--raw 显示RAW传输协议的连线状况。ss
    -x或--unix 此参数的效果和指定"-A unix"参数相同。
    --ip或--inet 此参数的效果和指定"-A inet"参数相同。
    
    netstat -an | grep -E '350|37001'  
    
    netstat -anpto | grep -E '350|370|360|380' 
    
####查看系统开启端口
    netstat -tnlp    


####启动jar包
    前台启动 java -Dfile.encoding=utf-8 -jar mnjx_eterm-1.0-SNAPSHOT-shaded.jar
    后台启动 nohup java -Dfile.encoding=utf-8 -jar mnjx_eterm-1.0-SNAPSHOT-shaded.jar 2>&1 &
    
####更新分支元数据
    参考：https://www.cnblogs.com/bigtreei/p/********.html
    强制同步远程分支到本地
    git remote update origin --prune
    强制推送
    git push -f
    自动删除orig文件
    git config --global mergetool.keepBackup false

#### 服务编排
    docker-compose -d up


#### 查看CPU的个数
    cat /proc/cpuinfo
#### 查看半连接个数（容器中没得这个值），默认256
    more /proc/sys/net/ipv4/tcp_max_syn_backlog 
#### 查看全连接的个数，默认128
    more /proc/sys/net/core/somaxconn

#### ChannelOption.SO_LINGER
        /*
            ChannelOption.SO_LINGER参数对应于套接字选项中的SO_LINGER，Linux内核默认的处理方式是当用户调用close（）方法的时候，函数返回，
            在可能的情况下，尽量发送数据，不一定保证会发送剩余的数据，造成了数据的不确定性，使用SO_LINGER可以阻塞close()的调用时间，直到数据完全发送
            关闭Socket的延迟时间。值含义如下：
                -1：表示socket.close()方法立即返回，但OS底层会将发送缓冲区的数据全部发送到对端。
                0： 表示socket.close()方法立即返回，OS放弃发送缓冲区的数据直接向对端发送RST包，对端收到复位错误。
                非0整数值：表示调用socket.close()方法的线程被阻塞直到延迟时间到或发送缓冲区中的数据发送完毕，若超时，则对端会收到复位错误。
            默认值为-1，表示禁用该功能
         */
        serverBootstrap.option(ChannelOption.SO_LINGER,-1);

#### docker中的日志拷贝
    docker cp mnjx_eterm:/usr/local/mnjx_eterm/logs/eterm /home/<USER>

    
