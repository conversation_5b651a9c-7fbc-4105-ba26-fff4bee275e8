<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.eterm.cmn.mapper.CntzMapper">

    <select id="retrieveCityInfo" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        select distinct
            mc.city_ename,
            mc.city_cname,
            mc.city_code,
            m.country_iso
        from
            mnjx_city mc
            left join mnjx_country m on mc.country_id = m.country_id
        where
            mc.city_status = '1'
            and (mc.city_ename like concat('%',#{cityName},'%') or mc.city_cname like concat('%',#{cityName},'%'))
    </select>

    <select id="retrieveAirlineInfoByName" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        select distinct
            ma.airline_full_name,
            ma.airline_ename,
            ma.airline_code,
            ma.airline_three_code,
            m.country_iso
        from
            mnjx_airline ma
            left join mnjx_country m on ma.country_id = m.country_id
        where
            ma.airline_status = '1'
            and (ma.airline_ename like concat('%',#{airlineName},'%') or ma.airline_full_name like concat('%',#{airlineName},'%'))
    </select>

    <select id="retrieveAirportByCityCode" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        SELECT DISTINCT
            mc.city_ename,
            mc.city_cname,
            mc.city_abbr,
            mco.country_iso,
            mco.country_three_code,
            ma.airport_code,
            ma.airport_ename,
            ma.airport_cname,
            ma.latitude,
            ma.longitude
        FROM
            mnjx_city mc
            LEFT JOIN mnjx_airport ma ON mc.city_id = ma.city_id
            LEFT JOIN mnjx_country mco ON mc.country_id = mco.country_id
        WHERE
            mc.city_code = #{cityCode}
    </select>

    <select id="retrieveAirlineInfoByAirlineCode" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        SELECT
            ma.*,
            mc.country_iso
        FROM
            mnjx_airline ma,
            mnjx_country mc
        WHERE
            ma.country_id = mc.country_id
            AND ma.airline_code = #{airlineCode}
    </select>

    <select id="retrieveAirportByAirportCode" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        SELECT
            ma.*,
            mc.city_code,
            mco.country_iso
        FROM
            mnjx_airport ma,
            mnjx_city mc,
            mnjx_country mco
        WHERE
            ma.city_id = mc.city_id
            AND mc.country_id = mco.country_id
            AND ma.airport_code = #{airportCode}
    </select>

    <select id="retrieveAirportByAirportName" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        SELECT
            ma.*,
            mc.city_code,
            mco.country_iso
        FROM
            mnjx_airport ma,
            mnjx_city mc,
            mnjx_country mco
        WHERE
            ma.city_id = mc.city_id
            AND mc.country_id = mco.country_id
            AND (ma.airport_cname like concat('%',#{airportName},'%') or ma.airport_ename like concat('%',#{airportName},'%'))
    </select>

    <select id="retrieveCountryByCountryCode" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        SELECT
            mc.*,
            mco.continent_code
        FROM
            mnjx_country mc
            LEFT JOIN mnjx_continent mco ON mc.continent_id = mco.continent_id
        WHERE
            mc.country_three_code = #{countryCode}
            OR mc.country_iso = #{countryCode}
    </select>

    <select id="retrieveCountryByCountryName" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        SELECT
            mc.*,
            mco.continent_code
        FROM
            mnjx_country mc
            LEFT JOIN mnjx_continent mco ON mc.continent_id = mco.continent_id
        WHERE
            mc.country_ename like concat('%',#{countryName},'%')
            OR mc.country_cname like concat('%',#{countryName},'%')
    </select>

    <select id="retrieveStateByStateName" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        SELECT
            ma.airport_code,
            ms.*,
            mco.country_iso,
            mco.country_three_code
        FROM
            mnjx_state ms
            LEFT JOIN mnjx_country mco ON ms.country_id = mco.country_id
            LEFT JOIN mnjx_city mc ON mc.state_id = ms.state_id
            LEFT JOIN mnjx_airport ma ON mc.city_id = ma.city_id
        WHERE
            1 = 1
            AND (ms.state_cname LIKE concat('%',#{stateName},'%') OR ms.state_ename LIKE concat('%',#{stateName},'%'))
    </select>

    <select id="retrieveStateByStateCode" resultType="com.swcares.eterm.cmn.dto.CntzResultDto">
        SELECT
            ma.airport_code,
            ms.*,
            mco.country_iso,
            mco.country_three_code
        FROM
            mnjx_state ms
            LEFT JOIN mnjx_country mco ON ms.country_id = mco.country_id
            LEFT JOIN mnjx_city mc ON mc.state_id = ms.state_id
            LEFT JOIN mnjx_airport ma ON mc.city_id = ma.city_id
        WHERE
            ms.state_code = #{stateCode}
    </select>

</mapper>