HELP INFORMATION FOR PD: TRANSACTION. APPLICATION GRP IS CKI
THIS FUNCTION IS ALLOWED UNDER THE FOLLOWING <PERSON><PERSON> GROUPS
THE SECURITY GROUP IS : 39
REPRESENTING THE FOLLOWING USER GROUPS: 1-10-88-93
<PERSON><PERSON><PERSON> TRANSACTION      PD
DESCRIPTIONS: PA<PERSON>ENGER DISPLAY
FORMAT:  PD:FLT CLASS (SEGMENT) (,ITEM)
EXPLANATIONS: SEGMENT---1 CITY FOR ORIGIN CITY
                        DEFAULT FOR VDT CITY TO ALL DESTINATIONS
              ITEM---IE ACC,NACC,INF,JMP,WCH,I,O,RLXXXXX,...
                        WHEN MULTIPLES ITEMS INPUT:
                        ','--- PD WITH ALL OF THE ITEMS
                        ';'--- PD WITH ANY OF THE SPECIFIED ITEMS
EXAMPLE: PD:101X,PEK,NACC,UM