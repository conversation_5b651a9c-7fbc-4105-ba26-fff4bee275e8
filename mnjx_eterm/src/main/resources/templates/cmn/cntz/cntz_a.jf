#set(firstRes = results[0])
#(args[0].getQueryParam())  #(firstRes.cityEname)   #(firstRes.cityCname)    #(StrUtils.isEmpty(firstRes.cityAbbr) == true ? "00" : (firstRes.cityAbbr))/#(firstRes.countryIso)/#(firstRes.countryThreeCode)  1#(wrap())
SIGN:#(wrap())
#for(res:results)
#if(isNotEmpty(res.airportEname) && res.airportEname.length() > 15)
#(res.airportCode),#(fillString(res.airportEname, 50, false))#(res.latitude)/#(res.longitude)#(wrap())
    #(res.airportCname)#(wrap())
#else
#(res.airportCode??""),#(fillString(res.airportEname??"", 25, false))#(fillString(res.airportCname??"", 25, false))#(res.latitude??"")/#(res.longitude??"")#(wrap())
#end
#end