#set(skDto=results[1])
#set(skResultList=results[0])
#(ymd2Com(skDto.flightDate)+"("+ymd2WeekEn(skDto.flightDate)+")") #(skDto.cityPair) #((skDto.airlineCode??)?'VIA'+skDto.airlineCode:"") #(skDto.cabin??)#(wrap())
#for(x:skResultList)
#(fillIndex((for.index + 1), 2, false)) #if(x.carrierFlight??)#("*")#else#(" ")#end#(x.flightNo)  #(x.org)#(x.dst) #(x.estimateOff)#(x.estimateOffChange??"  ") #(x.estimateArr)#(x.estimateArrChange??"  ") #((x.planeModelType??)?fillAfter(x.planeModelType,cn.hutool.core.util.CharUtil::SPACE,4):"    ") #(x.stopPoint) #(x.mealCode??" ")  ES# #(x.startDate)#(x.endDate) #(x.sellCabin)#(wrap())
#end
