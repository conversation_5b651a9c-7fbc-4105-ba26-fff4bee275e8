#set(ticketDto = resDto[0].ticketDto,segList= resDto[0].segList)
ETKD:TN/#(ticketDto.ticketNo)#(wrap())
ISSUED BY: #(fillString(ticketDto.issuedAirlineEn,26,false))ORG/DST: #(fillString(format("{}/{}",ticketDto.org,ticketDto.dst),24,false))BSP-D #(wrap())
E/R: #(wrap())
TOUR CODE: #(wrap())
PASSENGER: #((ticketDto.adlName??)?ticketDto.adlName:ticketDto.xnName)#(wrap())
EXCH:                               CONJ TKT:#(ticketDto.couponTicketNo)#(wrap())
#for(r:segList)
#(r)#(wrap())
#end
#if("REFUNDED"!=ticketDto.ticketStatus1)
FC:#(ticketDto.fcInputValue)#(wrap())
#end
FARE:#(fillString(ticketDto.fare,22,true))|FOP:#(ticketDto.payType)#(wrap())
TAX:#(fillString(ticketDto.taxCn,23,true))|OI: #(wrap())
TAX:#(fillString(ticketDto.taxYq,23,true))|#(wrap())
TOTAL:#(fillString(ticketDto.totalPrice,21,true))|TKTN: #(format("{}-{}",subPre(ticketDto.ticketNo,3),subSuf(ticketDto.ticketNo,3)))