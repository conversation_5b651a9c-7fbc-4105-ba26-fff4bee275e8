#set(ticketDto = resDto)
#for(d:ticketDto)
ETKD:TN/#((d.ticketNo??)?format("{}-{}",subPre(d.ticketNo,3),subSuf(d.ticketNo,3)):'')               NAME:#((d.adlName??)?d.adlName:d.xnName)#(wrap())
#for(f:d.flightAndFoidList)
    FOID:PF#((f.ssrFoid??)?fillAfter(f.ssrFoid,cn.hutool.core.util.CharUtil::SPACE,20):"                    ")               #(f.flightNo) /#(f.flightDate)/#(f.org)#(f.dst) #(f.ticketStatus)#(wrap())
#end
#end