package com.swcares.server.netty;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.stream.ChunkedWriteHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/19 15:15
 */
@Slf4j
@Component
public class WebSocketServerChannelInitializer extends ChannelInitializer<NioSocketChannel> {

    @Resource
    private ApplicationContext applicationContext;

    @Override
    protected void initChannel(NioSocketChannel ch) {
        ChannelPipeline pipeline = ch.pipeline();
        // 修正登录请求的协议数据
        pipeline.addLast(new HttpServerCodec());
        // 粘包问题处理
        pipeline.addLast(new ChunkedWriteHandler());
        // channel的协议后日志打印
        pipeline.addLast(new HttpObjectAggregator(1024 * 1024 * 1024));
        // 空闲检测
        pipeline.addLast(new WebSocketServerProtocolHandler("/eterm"));
        // 业务处理
        pipeline.addLast(applicationContext.getBean(WebSocketServerHandler.class));
    }
}
