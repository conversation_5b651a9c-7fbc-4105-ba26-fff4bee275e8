package com.swcares.core.util;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 */
@Slf4j
public class EtermMsgUtil {

    public static final byte LINK_ID = 0x39;

    /**
     * 文本结束标识
     */
    private static final byte[] B3 = {0x03};

    /**
     * 向Eterm发送弹出窗口
     *
     * @param msg 需要提示的消息
     * @return 提示消息的字节数组
     */
    public static byte[] alertMessage(String msg) throws UnsupportedEncodingException {
        byte[] send = ArrayUtil.addAll(new byte[]{0x01, (byte) 0xf8, 0x00, 0x00}, msg.getBytes(CharsetUtil.GBK), B3);
        log.info("原始数据：{}", send);
        byte displacement = (byte) (send.length >> 8);
        log.info("原始数据:{},>>8 右移8位的字节数据:{}", send.length, displacement);
        send[2] = displacement;
        byte versus = (byte) (send.length & 0xff);
        log.info("原始数据:{},与255（0xff）做位运算后的字节数据:{}", send.length, versus);
        send[3] = versus;
        log.info("运算后的数据：{}", send);
        return send;
    }

    /**
     * 向eterm发送显示在黑屏上的字符
     *
     * @param message 要回显的消息
     * @throws UnsupportedEncodingException Exception
     */
    public static byte[] sendMessage(String message, byte position) throws UnsupportedEncodingException {
        byte[] b3 = {0x03};
        String s3 = new String(b3);
        // 初始化头尾
        byte[] head = {0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, EtermMsgUtil.LINK_ID, 0x51, 0x70, 0x02, 0x1b, 0x0b, position, 0x20, 0x0f, 0x1b, 0x4d};
        // byte[] tail = {13,0x1e,27,98,0x03};
        // 理论上应该用这个结尾，但是对于中文显示会有一些问题，所以加入了不规范的27,15，幸运的是在显示上没有出现异常
        byte[] tail = {27, 15, 13, 0x1e, 27, 98, 0x03};
        // 处理null对象的情况
        message = StrUtils.isNotEmpty(message) ? message : StrUtils.EMPTY;
        // 去掉末尾的结束标记
        message = message.endsWith(s3) ? (message.replace(s3, StrUtil.EMPTY)) : message;
        // web过来的请求不需要转换成eterm能识别的中文
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        if (!memoryData.isRequestFromWebSocket()) {
            // 转换中文
            message = EtermCodeUtil.encodeChinese(message);
            // 去掉末尾的乱七八糟的东西
            message = message.substring(0, (message.indexOf(message.trim()) + message.trim().length()));
        }
        // 准备发送
        byte[] messageByte = message.getBytes();
        // 初始化将要发送的字符串byte
        byte[] sendByte = ArrayUtil.addAll(head, messageByte, tail);

        sendByte[2] = (byte) (sendByte.length >> 8);
        sendByte[3] = (byte) (sendByte.length & 0xff);
        return sendByte;
    }
}


