package com.swcares.eterm.cmn.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.cmn.service.IAiService;

import javax.annotation.Resource;

/**
 * AI指令处理：工作号临时退出后登陆
 *
 * <AUTHOR>
 */
@OperateType(action = "AI", template = "/cmn/ai.jf")
public class AiHandler implements Handler {

    private static final String FMT_AI_NUM = "\\d{4,5}";

    private static final String FMT_AI_LET = "\\w{5}";

    @Resource
    private IAiService iAiService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        //系统暂时不支持临时退出及恢复
        return Constant.FUNCTION;
    }
}
