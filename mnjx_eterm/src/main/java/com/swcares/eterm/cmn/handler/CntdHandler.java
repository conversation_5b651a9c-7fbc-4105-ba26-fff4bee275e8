package com.swcares.eterm.cmn.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.cmn.dto.CntdResultDto;
import com.swcares.eterm.cmn.dto.CntdRetrieveDto;
import com.swcares.eterm.cmn.service.ICntdService;

import javax.annotation.Resource;
import java.util.List;

/**
 * CNTD指令验证
 * <p>
 * CNTD:T/城市英文名
 * CNTD:M/航司英文名
 * CNTD:D/航司二字码
 * CNTD:ALL
 *
 * <AUTHOR>
 */
@OperateType(action = "CNTD", template = "cmn/cntd.jf")
public class CntdHandler implements Handler {

    @Resource
    private ICntdService iCntdService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException {
        if (cmd.startsWith("CNTD")) {
            throw new UnifiedResultException("请参考指令CNTZ  / PLEASE REFER FUNCTION CNTZ");
        }
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        CntdRetrieveDto cntdRetrieveDto = iCntdService.parseCmd(cmd);
        List<CntdResultDto> resultList = iCntdService.handle(cntdRetrieveDto, unifiedResult);
        unifiedResult.setResults(resultList.toArray());
        return unifiedResult;
    }
}
