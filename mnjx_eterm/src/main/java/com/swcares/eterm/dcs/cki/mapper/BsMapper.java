package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.eterm.dcs.cki.obj.dto.BsDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-7-30
 */
public interface BsMapper {


    /**
     * retrieveSeat
     *
     * @param bsDto bsDto
     * @return retrieveSeat
     */
    List<BsDto> retrieveSeat(@Param("bsDto") BsDto bsDto);

    /**
     * updateSeatStatusX
     *
     * @param bs bs
     */
    void updateSeatStatusX(@Param("bsDto") BsDto bs);

    /**
     * updateSeatStatusC
     *
     * @param bs bs
     */
    void updateSeatStatusC(@Param("bsDto") BsDto bs);

    /**
     * updateSeatRowsStatusX
     *
     * @param bs    bs
     * @param end   end
     * @param start start
     */
    void updateSeatRowsStatusX(@Param("bsDto") BsDto bs, @Param("end") Integer end, @Param("start") Integer start);

    /**
     * updateSeatRowsStatusC
     *
     * @param bs    bs
     * @param end   end
     * @param start start
     */
    void updateSeatRowsStatusC(@Param("bsDto") BsDto bs, @Param("end") Integer end, @Param("start") Integer start);
}
