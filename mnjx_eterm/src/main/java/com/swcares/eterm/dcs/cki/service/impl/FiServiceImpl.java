package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.*;
import com.swcares.eterm.dcs.cki.mapper.FiMapper;
import com.swcares.eterm.dcs.cki.obj.dto.FiPlanFltDto;
import com.swcares.eterm.dcs.cki.obj.dto.FiPlanSectionDto;
import com.swcares.eterm.dcs.cki.obj.dto.FiRetrieveDto;
import com.swcares.eterm.dcs.cki.obj.dto.FiTcardSection;
import com.swcares.eterm.dcs.cki.service.IFiService;
import com.swcares.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * FI查询航班信息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FiServiceImpl implements IFiService {

    @Resource
    private FiMapper fiMapper;

    @Resource
    private IMnjxAirlineService iMnjxAirlineService;
    @Resource
    private IMnjxFlightService iMnjxFlightService;
    /**
     * 日期格式匹配，用于单独判断日期格式是否正确
     */
    private static final Pattern DATE_PATTERN = Pattern.compile("[+-.]|(\\d{1,2}[A-Za-z]{3}\\d{0,2})");
    /**
     * 航班号格式匹配，用于单独判断航班号格式是否正确
     */
    private static final Pattern FLT_NO_PATTERN = Pattern.compile("\\w{2}\\d{3,4}");

    /**
     * 参数解析
     *
     * @param args 解析的参数数组
     * @return 参数对象
     */
    private FiRetrieveDto parseFi(Object[] args) throws UnifiedResultException {
        String fltNo = ObjectUtil.isNotEmpty(args[1]) ? StrUtils.toString(args[1]) : StrUtils.EMPTY;
        if (StrUtil.isNotEmpty(fltNo) && !ReUtil.isMatch(FLT_NO_PATTERN, fltNo)) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        //航班日期
        String fltDate = ObjectUtil.isNotEmpty(args[5]) ? StrUtils.toString(args[5]) : StrUtils.EMPTY;
        if (StrUtil.isNotEmpty(fltDate) && !ReUtil.isMatch(DATE_PATTERN, fltDate)) {
            throw new UnifiedResultException(Constant.DATE);
        }
        //参数处理(时间格式)
        fltDate = StrUtils.isNotEmpty(fltDate) ? DateUtils.com2ymd(fltDate, 2) : DateUtils.today();
        //机场三字码
        String airportCode = ObjectUtil.isNotEmpty(args[7]) ? StrUtils.toString(args[7]) : StrUtils.EMPTY;

        FiRetrieveDto fiRetrieveDto = new FiRetrieveDto();
        fiRetrieveDto.setFltNo(fltNo);
        fiRetrieveDto.setAirlineCode(ObjectUtil.isNotEmpty(args[2]) ? StrUtils.toString(args[2]) : StrUtils.EMPTY);
        fiRetrieveDto.setFltDate(fltDate);
        fiRetrieveDto.setAirportCode(airportCode);
        return fiRetrieveDto;
    }

    /**
     * FI指令的业务处理
     *
     * @return 查询航班信息
     * @throws UnifiedResultException 统一异常处理
     */
    @Override
    public FiPlanFltDto handle(UnifiedResult unifiedResult) throws UnifiedResultException {
        //参数解析
        FiRetrieveDto fiRetrieveDto = parseFi(unifiedResult.getArgs());
        //根据航空公司查询航空公司id
        MnjxAirline mnjxAirline = retrieveMnjxAirline(fiRetrieveDto.getAirlineCode());
        if (ObjectUtil.isEmpty(mnjxAirline)) {
            throw new UnifiedResultException(Constant.AIRLINE);
        }
        //如果是共享航班，报错
        MnjxFlight mnjxFlight = iMnjxFlightService.lambdaQuery().eq(MnjxFlight::getFlightNo, fiRetrieveDto.getFltNo()).one();
        if (ObjectUtil.isEmpty(mnjxFlight) || StrUtil.isNotEmpty(mnjxFlight.getCarrierFlight())) {
            throw new UnifiedResultException(Constant.FLT_NBR);
        }
        //判断日期 查询仅返回当天日期前三天，至未来的航班信息，再更早的航班信息查询返回：NO-OP
        if (DateUtil.offsetDay(DateUtil.date(), -Constant.THREE).toString().compareTo(fiRetrieveDto.getFltDate()) > 0) {
            throw new UnifiedResultException(Constant.NO_OP);
        }
        //业务数据查询--查询计划航班信息
        FiPlanFltDto fiPlanFltDto = fiMapper.retrievePlanFlt(fiRetrieveDto.getFltNo(), fiRetrieveDto.getFltDate(), mnjxAirline.getAirlineId());
        if (ObjectUtil.isEmpty(fiPlanFltDto)) {
            throw new UnifiedResultException(Constant.NO_OP);
        }
        //根据计划航班id查询tCard数据
        List<FiTcardSection> tcardSectionList = fiMapper.retrieveTcardSection(fiPlanFltDto.getTcardId());
        //如果有输入城市参数的时候判断
        if (StrUtils.isNotEmpty(fiRetrieveDto.getAirportCode())) {
            //筛选 判断输入的城市是否正确
            List<FiTcardSection> collect = tcardSectionList.stream().filter(a -> a.getAirportCode().equals(fiRetrieveDto.getAirportCode())).collect(Collectors.toList());
            if (CollUtil.isEmpty(collect)) {
                throw new UnifiedResultException(Constant.CITY);
            }
        }
        //业务数据查询--查询计划航节数据
        List<FiPlanSectionDto> planSectionList = fiMapper.retrievePlanSection(fiPlanFltDto.getPlanFlightId());
        if (CollUtil.isEmpty(planSectionList)) {
            throw new UnifiedResultException(Constant.NO_OP);
        }
        //机场id与三字码转换
        planSectionList.forEach(section -> {
            //不相等时，表示该数据使用FU指令修改过
            if (section.getPlaneNo().equals(section.getPrePlaneNo())) {
                section.setPlaneNo(StrUtil.EMPTY);
            }
        });
        //返回的数据处理
        return setResultFi(fiRetrieveDto.getAirportCode(), planSectionList, fiPlanFltDto, tcardSectionList);
    }

    /**
     * 处理参数带城市查询时的回显
     *
     * @param airportCode      机场三字码
     * @param fiPlanFltDto     数据库查询的航班计划数据
     * @param fiPlanSectionDto 数据库查询的航节计划数据
     */
    private void handleParamCity(String airportCode, FiPlanFltDto fiPlanFltDto, FiPlanSectionDto fiPlanSectionDto) {
        if (StrUtil.isNotEmpty(airportCode)) {
            if (fiPlanSectionDto.getOffCity().equals(airportCode)) {
                fiPlanFltDto.setCity(StrUtils.format("{} ORIG {}", airportCode, fiPlanSectionDto.getEstimateOff()));
            } else if (fiPlanSectionDto.getArrCity().equals(airportCode)) {
                fiPlanFltDto.setCity(StrUtils.format("{} {} TERM", airportCode, fiPlanSectionDto.getEstimateArr()));
            }
        }
    }

    /**
     * 处理返回的结果
     *
     * @param airportCode     机场三字码
     * @param fiPlanFltDto    计划航班信息
     * @param planSectionList 计划航节信息
     * @return 返回fi指令需要的最终结果
     */
    private FiPlanFltDto setResultFi(String airportCode, List<FiPlanSectionDto> planSectionList, FiPlanFltDto fiPlanFltDto, List<FiTcardSection> tcardSectionList) {
        if (CollUtil.isNotEmpty(planSectionList)) {
            fiPlanFltDto.setOfficeNo(StrUtil.format("{}001", planSectionList.get(0).getOffCity()));
        }
        fiPlanFltDto.setStartDate(DateUtils.ymd2Com(fiPlanFltDto.getStartDate()));
        fiPlanFltDto.setEndDate(DateUtils.ymd2Com(fiPlanFltDto.getEndDate()));
        fiPlanFltDto.setFlightDate(DateUtils.ymd2Com(fiPlanFltDto.getFlightDate()));
        fiPlanFltDto.setParamCity(airportCode);
        //筛选FU指令修改过的数据,并添加标识
        boolean isFuUpdate = filterPlanSection(planSectionList);
        if (isFuUpdate) {
            fiPlanFltDto.setIsFuUpdate(true);
        }
        //筛选，重新设置航节数据--把航节数据按照航站格式进行拆分设置
        setPlanSection(airportCode, planSectionList, fiPlanFltDto, tcardSectionList);
        return fiPlanFltDto;
    }

    /**
     * 筛选，重新设置航节数据--把航节数据按照航站格式进行拆分设置
     *
     * @param airportCode      输入参数，机场三字码
     * @param planSectionList  数据库查询的计划航节数据
     * @param fiPlanFltDto     数据库查询的计划航班数据 （最终返回的对象-数据都需要设置在这个对象中）
     * @param tcardSectionList Tcard航站数据（新建航班时存入的航站数据）
     */
    private void setPlanSection(String airportCode, List<FiPlanSectionDto> planSectionList, FiPlanFltDto fiPlanFltDto, List<FiTcardSection> tcardSectionList) {
        List<FiPlanSectionDto> resList = new ArrayList<>();
        // 填充航站列表信息 单航站、多航站
        if (planSectionList.size() == Constant.ONE) {
            FiPlanSectionDto fiPlanSectionDto = planSectionList.get(0);
            //参数中有机场三字码，回显处理
            handleParamCity(airportCode, fiPlanFltDto, fiPlanSectionDto);
            //起始航站数据
            FiPlanSectionDto orgRes = new FiPlanSectionDto();
            BeanUtil.copyProperties(fiPlanSectionDto, orgRes);
            orgRes.setAirportCode(orgRes.getOffCity());
            orgRes.setEstimateArr(StrUtil.EMPTY);
            orgRes.setActualArr(StrUtil.EMPTY);
            orgRes.setEstimateArrChange(StrUtil.EMPTY);
            orgRes.setCndNo(fiPlanFltDto.getCndNo());
            orgRes.setEqt(fiPlanFltDto.getEqt());
            orgRes.setVers(fiPlanFltDto.getVers());
            orgRes.setActualOff(StrUtil.isNotEmpty(fiPlanSectionDto.getActualOff()) ? StrUtil.format("ETD {}", fiPlanSectionDto.getActualOff()) : fiPlanSectionDto.getActualOff());
            orgRes.setPlaneNo(StrUtil.isNotEmpty(fiPlanSectionDto.getPlaneNo()) ? StrUtil.format("R:{}", fiPlanSectionDto.getPlaneNo()) : fiPlanSectionDto.getPlaneNo());
            //到达航站数据
            FiPlanSectionDto dstRes = new FiPlanSectionDto();
            BeanUtil.copyProperties(fiPlanSectionDto, dstRes);
            dstRes.setAirportCode(dstRes.getArrCity());
            dstRes.setEstimateOff(StrUtil.EMPTY);
            dstRes.setActualOff(StrUtil.EMPTY);
            dstRes.setEstimateOffChange(StrUtil.EMPTY);
            dstRes.setGate(StrUtil.EMPTY);
            dstRes.setActualArr(StrUtil.isNotEmpty(fiPlanSectionDto.getActualArr()) ? StrUtil.format("ADJ {}", fiPlanSectionDto.getActualArr()) : fiPlanSectionDto.getActualArr());
            dstRes.setPlaneNo(StrUtil.EMPTY);
            resList.add(orgRes);
            resList.add(dstRes);
        } else {
            for (int i = 0; i < tcardSectionList.size(); i++) {
                FiTcardSection fiTcardSection = tcardSectionList.get(i);
                for (int j = 0; j < planSectionList.size(); j++) {
                    FiPlanSectionDto planSectionDto = planSectionList.get(j);
                    // 第一个航站
                    if (j == 0 && fiTcardSection.getAirportCode().equals(planSectionDto.getOffCity())) {
                        if (StrUtil.isNotEmpty(airportCode)) {
                            fiPlanFltDto.setCity(StrUtils.format("{} ORIG {}", fiTcardSection.getAirportCode(), planSectionDto.getEstimateOff()));
                        }
                        //设置起始站
                        FiPlanSectionDto res = new FiPlanSectionDto();
                        BeanUtil.copyProperties(planSectionDto, res);
                        res.setEstimateArr(StrUtil.EMPTY);
                        res.setActualArr(StrUtil.EMPTY);
                        res.setEstimateArrChange(StrUtil.EMPTY);
                        res.setAirportCode(fiTcardSection.getAirportCode());
                        res.setCndNo(fiPlanFltDto.getCndNo());
                        res.setEqt(fiPlanFltDto.getEqt());
                        res.setVers(fiPlanFltDto.getVers());
                        res.setActualOff(StrUtil.isNotEmpty(planSectionDto.getActualOff()) ? StrUtil.format("ETD {}", planSectionDto.getActualOff()) : planSectionDto.getActualOff());
                        res.setPlaneNo(StrUtil.isNotEmpty(planSectionDto.getPlaneNo()) ? StrUtil.format("R:{}", planSectionDto.getPlaneNo()) : planSectionDto.getPlaneNo());
                        resList.add(res);
                        break;
                    }
                    // 中间的航站
                    else if (fiTcardSection.getAirportCode().equals(planSectionDto.getOffCity()) && tcardSectionList.get(j + 1).getAirportCode().equals(planSectionDto.getArrCity())) {
                        if (StrUtil.isNotEmpty(airportCode)) {
                            fiPlanFltDto.setCity(StrUtils.format("{} {} {}", airportCode, planSectionList.get(i - 1).getEstimateArr(), planSectionList.get(i).getEstimateOff()));
                        }
                        FiPlanSectionDto res = new FiPlanSectionDto();
                        BeanUtil.copyProperties(planSectionDto, res);
                        res.setAirportCode(fiTcardSection.getAirportCode());
                        res.setEstimateArr(planSectionList.get(i - 1).getEstimateArr());
                        res.setActualOff(StrUtil.isNotEmpty(planSectionDto.getActualOff()) ? StrUtil.format("ETD {}", planSectionDto.getActualOff()) : planSectionDto.getActualOff());
                        res.setActualArr(StrUtil.isNotEmpty(planSectionList.get(i - 1).getActualArr()) ? StrUtil.format("ADJ {}", planSectionList.get(i - 1).getActualArr()) : planSectionList.get(i - 1).getActualArr());
                        res.setPlaneNo(StrUtil.EMPTY);
                        resList.add(res);
                        break;
                    }
                    // 最后一个航站
                    else if (j == planSectionList.size() - 1 && fiTcardSection.getAirportCode().equals(planSectionDto.getArrCity())) {
                        if (StrUtil.isNotEmpty(airportCode)) {
                            fiPlanFltDto.setCity(StrUtils.format("{} {} TERM", airportCode, planSectionDto.getEstimateArr()));
                        }
                        FiPlanSectionDto res = new FiPlanSectionDto();
                        BeanUtil.copyProperties(planSectionDto, res);
                        res.setAirportCode(fiTcardSection.getAirportCode());
                        res.setEstimateOff(StrUtil.EMPTY);
                        res.setActualOff(StrUtil.EMPTY);
                        res.setEstimateOffChange(StrUtil.EMPTY);
                        res.setGate(StrUtil.EMPTY);
                        res.setActualArr(StrUtil.isNotEmpty(planSectionDto.getActualArr()) ? StrUtil.format("ADJ {}", planSectionDto.getActualArr()) : planSectionDto.getActualArr());
                        res.setPlaneNo(StrUtil.EMPTY);
                        resList.add(res);
                        break;
                    }
                }
            }
        }
        fiPlanFltDto.setPlanSectionList(resList);
    }


    /**
     * 筛选FU指令修改过的数据，添加标识。fi指令回显的模板会不同，需要特殊处理
     *
     * @param planSectionList 数据库查询的计划航节数据
     */
    private boolean filterPlanSection(List<FiPlanSectionDto> planSectionList) {
        //设置筛选-fu指令修改过的值
        return planSectionList.stream().anyMatch(p -> StrUtil.isNotEmpty(p.getGate()) || StrUtil.isNotEmpty(p.getActualOff()) || StrUtil.isNotEmpty(p.getActualArr()));
    }

    /**
     * 查询航空公司数据
     *
     * @param airlineCode 航空二字码
     * @return 查询航空公司数据
     */
    private MnjxAirline retrieveMnjxAirline(String airlineCode) {
        return iMnjxAirlineService.lambdaQuery()
                .eq(MnjxAirline::getAirlineCode, airlineCode)
                .one();
    }

}
