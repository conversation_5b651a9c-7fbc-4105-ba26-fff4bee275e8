package com.swcares.eterm.dcs.cki.service;


import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.RkParamDto;

/**
 * 指令RK处理
 *
 * <AUTHOR>
 */

public interface IRkService {


    /**
     * 业务处理
     *
     * @param rkParamDto 解析的对象
     * @return 餐食
     * @throws UnifiedResultException 异常
     */
    String handle(RkParamDto rkParamDto) throws UnifiedResultException;

    /**
     * retrievePlanFlight
     *
     * @param fltNo   fltNo
     * @param fltDate fltDate
     * @return retrievePlanFlight
     */
    String retrievePlanFlight(String fltNo, String fltDate);
}
