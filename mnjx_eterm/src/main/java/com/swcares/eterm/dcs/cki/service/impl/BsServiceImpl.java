package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataHandlerResult;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.*;
import com.swcares.entity.MnjxSeat;
import com.swcares.eterm.dcs.cki.mapper.BsMapper;
import com.swcares.eterm.dcs.cki.obj.dto.BsDto;
import com.swcares.eterm.dcs.cki.service.IBsService;
import com.swcares.eterm.dcs.cki.service.ISeService;
import com.swcares.service.IMnjxOpenCabinService;
import com.swcares.service.IMnjxSeatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * BS指令数据处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BsServiceImpl implements IBsService {

    /**
     * BS:2L,BS:-4A
     */
    private static final Pattern BS_PATTERN_ONE = Pattern.compile("(BS|bs):(-)?([\\d]{1,3}[A-Za-z])((,(-)?([\\d]{1,3}[A-Za-z]))*)");
    /**
     * BS:2,BS:-4
     */
    private static final Pattern BS_PATTERN_TWO = Pattern.compile("(BS|bs):(-)?([\\d]{1,4})");
    /**
     * BS:2-7,BS:-2-7
     */
    private static final Pattern BS_PATTERN_THR = Pattern.compile("(BS|bs):(-)?([\\d]{1,4})-([\\d]{1,4})");

    @Resource
    private IMnjxSeatService iMnjxSeatService;
    @Resource
    private ISeService iSeService;
    @Resource
    private BsMapper bsMapper;
    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;

    @Override
    public String handle(String cmd) throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MemoryDataHandlerResult handlerResult = memoryData.getMemoryDataHandlerResult();

        String lastAction = handlerResult.getLastAction();
        this.parseSe(lastAction);
        List<String> records = handlerResult.getRecords();
        //得到上一条指令记录的第一条数据
        String s = records.get(0);
        //以空格分割，取第一组数据
        String[] split2 = s.split(" ");
        String a = split2[0];
        String[] split = s.split(":");
        String[] split1 = split[1].split("/");
        //通过指令记录分割，得到航班号和航班日期
        String fltNo = split1[0];
        String fltDate = DateUtils.com2ymd(split1[1]);
        //通过指令记录分割，得到起止航站
        String[] s1 = split1[2].split(" ");
        String dep = s1[0].substring(1, 4);
        String arr = s1[0].substring(4);
        BsDto bsDto = BsDto.builder().flightNo(fltNo).flightDate(fltDate).dep(dep).arr(arr).build();
        List<BsDto> bsDtos = bsMapper.retrieveSeat(bsDto);
        String substring = a.substring(1, a.length() - 6);
        String result;
        if (ReUtils.isMatch(BS_PATTERN_ONE, cmd)) {
            result = parseParamOne(cmd, bsDtos, bsDto, substring);
            return result;
        } else if (ReUtils.isMatch(BS_PATTERN_TWO, cmd)) {
            result = parseParamTwo(cmd, bsDtos, bsDto, substring);
            return result;
        } else if (ReUtils.isMatch(BS_PATTERN_THR, cmd)) {
            result = parseParamThr(cmd, bsDtos, bsDto, substring);
            return result;
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
    }

    /**
     * BS:1-6,BS:-1-6
     *
     * @param cmd    指令
     * @param bsDtos dto对象组
     * @param bs     解析指令
     * @param cmdSub 参数
     * @return 返回结果
     * @throws UnifiedResultException 异常
     */
    private String parseParamThr(String cmd, List<BsDto> bsDtos, BsDto bs, String cmdSub) throws UnifiedResultException {
        List<String> allGroups = ReUtils.getAllGroups(BS_PATTERN_THR, cmd);
        String sub = cmd.substring(0, 4);
        //前面的数
        String start = allGroups.get(3);
        //后面的数
        String end = allGroups.get(4);
        if (Integer.valueOf(start).compareTo(Integer.valueOf(end)) >= 0) {
            throw new UnifiedResultException(Constant.ROW);
        } else {
            //BS:1-6
            List<String> seatRows = bsDtos.stream().map(BsDto::getSeatRow).distinct().collect(Collectors.toList());
            if (seatRows.contains(start) && seatRows.contains(end)) {
                //检查由-号连接的两个正整数，如果所代表的座位排号跨越了舱等区域，则报错：SEATS
                List<String> s = bsDtos.stream().filter(bsDto -> start.equals(bsDto.getSeatRow())).map(BsDto::getCabinClass).distinct().collect(Collectors.toList());
                String cabinClassS = s.get(0);

                List<String> e = bsDtos.stream().filter(bsDto -> end.equals(bsDto.getSeatRow())).map(BsDto::getCabinClass).distinct().collect(Collectors.toList());
                String cabinClassE = e.get(0);

                if (ObjectUtils.equals(cabinClassS, cabinClassE)) {
                    //如果是格式5，检查要锁定的这几排座位，只要其中有一个座位的状态是X，则报错：ALREADY DONE
                    long countX = bsDtos.stream().filter(bsDto -> Integer.valueOf(start).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(bsDto.getSeatRow()) ? bsDto.getSeatRow() : "0")) <= 0 && Integer.valueOf(end).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(bsDto.getSeatRow()) ? bsDto.getSeatRow() : "0")) >= 0 && Constant.TCARD_CYCLE_X.equals(bsDto.getSeatStatus()) && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))).count();
                    if (!sub.contains(StrUtils.DASHED) && countX > 0) {
                        throw new UnifiedResultException(Constant.ALREADY_DONE);
                    }

                    //如果是格式5，检查要锁定的这几排座位，只要其中有一个座位的状态是.（英文句号），则报错：UNABLE
                    long countPoint = bsDtos.stream()
                            .filter(bsDto -> Integer.valueOf(start).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(bsDto.getSeatRow()) ? bsDto.getSeatRow() : "0")) <= 0
                                    && Integer.valueOf(end).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(bsDto.getSeatRow()) ? bsDto.getSeatRow() : "0")) >= 0
                                    && Constant.OTHER.equals(bsDto.getSeatStatus())
                                    && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr())))
                            .count();
                    if (!sub.contains(StrUtils.DASHED) && countPoint > 0) {
                        throw new UnifiedResultException(Constant.UNABLE);
                    }
                    // 如果是格式5，检查要锁定的这几排座位，只要其中有一个座位的状态是V或D，则报错：CONFLICT
                    long count = bsDtos.stream()
                            .filter(bsDto -> Integer.valueOf(start).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(bsDto.getSeatRow()) ? bsDto.getSeatRow() : "0")) <= 0
                                    && Integer.valueOf(end).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(bsDto.getSeatRow()) ? bsDto.getSeatRow() : "0")) >= 0
                                    && (Constant.SEAT_STATUS_V.equals(bsDto.getSeatStatus()) || Constant.SEAT_STATUS_D.equals(bsDto.getSeatStatus()))
                                    && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr())))
                            .count();
                    if (!sub.contains(StrUtils.DASHED) && count > 0) {
                        throw new UnifiedResultException(Constant.CONFLICT);
                    }
                    //如果是格式6，检查要锁定的这几排床位，只要其中有一个座位的状态不是X，则报错：ALREADY DONE
                    long countNx = bsDtos.stream().filter(bsDto -> Integer.valueOf(start).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(bsDto.getSeatRow()) ? bsDto.getSeatRow() : "0")) <= 0 && Integer.valueOf(end).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(bsDto.getSeatRow()) ? bsDto.getSeatRow() : "0")) >= 0 && !Constant.TCARD_CYCLE_X.equals(bsDto.getSeatStatus()) && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))).count();
                    if (sub.contains(StrUtils.DASHED) && countNx > 0) {
                        throw new UnifiedResultException(Constant.ALREADY_DONE);
                    }
                    //条件没问题情况下修改状态
                    //锁定座位
                    List<BsDto> results = this.getBsDtosBySeatRows(bsDtos, bs, start, end);
                    if (!sub.contains(StrUtils.DASHED)) {
                        results.forEach(r -> iMnjxSeatService.lambdaUpdate()
                                .eq(MnjxSeat::getSeatRow, r.getSeatRow())
                                .eq(MnjxSeat::getSeatId, r.getSeatId())
                                .set(MnjxSeat::getSeatStatusOld, r.getSeatStatus())
                                .set(MnjxSeat::getSeatStatus, "X").update());
                    } else if (sub.contains(StrUtils.DASHED)) {
                        //取消锁定座位
                        results.forEach(r -> iMnjxSeatService.lambdaUpdate()
                                .eq(MnjxSeat::getSeatRow, r.getSeatRow())
                                .eq(MnjxSeat::getSeatId, r.getSeatId())
                                .set(MnjxSeat::getSeatStatus, StrUtil.isEmpty(r.getSeatStatusOld()) ? "C" : r.getSeatStatusOld())
                                .set(MnjxSeat::getSeatStatusOld, r.getSeatStatus())
                                .update());
                    }
                    return iSeService.handler(cmdSub);
                } else {
                    throw new UnifiedResultException(Constant.SEATS);
                }
            } else {
                throw new UnifiedResultException(Constant.COMPARTMENT);
            }
        }
    }

    private List<BsDto> getBsDtosBySeatRows(List<BsDto> bsDtos, BsDto bs, String start, String end) {
        return bsDtos.stream().filter(b -> Integer.valueOf(start).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(b.getSeatRow()) ? b.getSeatRow() : "0")) <= 0 && Integer.valueOf(end).compareTo(Integer.valueOf(ObjectUtils.isNotEmpty(b.getSeatRow()) ? b.getSeatRow() : "0")) >= 0 && (ObjectUtil.equals(bs.getDep(), b.getDep()) || ObjectUtil.equals(bs.getArr(), b.getArr()))).collect(Collectors.toList());
    }

    private String parseParamTwo(String cmd, List<BsDto> bsDtos, BsDto bs, String cmdSub) throws UnifiedResultException {
        List<String> allGroups = ReUtils.getAllGroups(BS_PATTERN_TWO, cmd);
        String seatRow = allGroups.get(3);
        List<String> seatRows = bsDtos.stream().map(BsDto::getSeatRow).distinct().collect(Collectors.toList());
        if (seatRows.contains(seatRow)) {
            //如果是格式3，检查要锁定的这一排座位，只要其中有一个座位的状态是X，则报错：ALREADY DONE
            long countX = bsDtos.stream().filter(bsDto -> seatRow.equals(bsDto.getSeatRow()) && Constant.TCARD_CYCLE_X.equals(bsDto.getSeatStatus()) && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))).count();
            if (!cmd.contains(StrUtils.DASHED) && countX > 0) {
                throw new UnifiedResultException(Constant.ALREADY_DONE);
            }
            //如果是格式3，检查要锁定的这一排座位，只要其中有一个座位的状态是.（英文句号），则报错：UNABLE
            long countPoint = bsDtos.stream().filter(bsDto -> seatRow.equals(bsDto.getSeatRow()) && Constant.OTHER.equals(bsDto.getSeatStatus()) && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))).count();
            if (!cmd.contains(StrUtils.DASHED) && countPoint > 0) {
                throw new UnifiedResultException(Constant.UNABLE);
            }
            //如果是格式3，检查要锁定的这一排座位，只要其中有一个座位的状态是V或D，则报错：CONFLICT
            long count = bsDtos.stream()
                    .filter(bsDto -> seatRow.equals(bsDto.getSeatRow())
                            && (Constant.SEAT_STATUS_V.equals(bsDto.getSeatStatus()) || Constant.SEAT_STATUS_D.equals(bsDto.getSeatStatus()))
                            && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr())))
                    .count();
            if (!cmd.contains(StrUtils.DASHED) && count > 0) {
                throw new UnifiedResultException(Constant.CONFLICT);
            }
            //如果是格式4，检查要解除锁定的这一排座位，只要其中有一个座位的状态不是X，则报错：ALREADY DONE
            long countNx = bsDtos.stream().filter(bsDto -> seatRow.equals(bsDto.getSeatRow()) && !Constant.TCARD_CYCLE_X.equals(bsDto.getSeatStatus()) && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))).count();
            if (cmd.contains(StrUtils.DASHED) && countNx > 0) {
                throw new UnifiedResultException(Constant.ALREADY_DONE);
            }

            //条件没问题情况下修改状态
            //锁定座位
            bs.setSeatRow(seatRow);
            List<BsDto> results = this.getBsDtosBySeatRow(bsDtos, bs, seatRow);
            if (!cmd.contains(StrUtils.DASHED)) {
                results.forEach(r -> iMnjxSeatService.lambdaUpdate()
                        .eq(MnjxSeat::getSeatRow, r.getSeatRow())
                        .eq(MnjxSeat::getSeatId, r.getSeatId())
                        .set(MnjxSeat::getSeatStatusOld, r.getSeatStatus())
                        .set(MnjxSeat::getSeatStatus, "X").update());
            }
            //取消锁定座位
            else if (cmd.contains(StrUtils.DASHED)) {
                List<String> seatIdList = results.stream()
                        .map(BsDto::getSeatId)
                        .collect(Collectors.toList());
                List<String> openCabinIdList = iMnjxSeatService.listByIds(seatIdList).stream()
                        .map(MnjxSeat::getOpenCabinId)
                        .collect(Collectors.toList());
                long count1 = iMnjxOpenCabinService.listByIds(openCabinIdList).stream()
                        .filter(o -> StrUtil.equalsAny(o.getOpenCabinStatus(), Constant.C, Constant.X))
                        .count();
                if (count1 > 0) {
                    throw new UnifiedResultException(Constant.UNABLE);
                }
                results.forEach(r -> iMnjxSeatService.lambdaUpdate()
                        .eq(MnjxSeat::getSeatRow, r.getSeatRow())
                        .eq(MnjxSeat::getSeatId, r.getSeatId())
                        .set(MnjxSeat::getSeatStatus, StrUtils.isNotEmpty(r.getSeatStatusOld()) ? r.getSeatStatusOld() : "C")
                        .set(MnjxSeat::getSeatStatusOld, r.getSeatStatus())
                        .update());
            }
            return iSeService.handler(cmdSub);
        } else {
            throw new UnifiedResultException(Constant.COMPARTMENT);
        }
    }

    private String parseParamOne(String cmd, List<BsDto> bsDtos, BsDto bs, String cmdSub) throws UnifiedResultException {
        List<String> allGroups = ReUtils.getAllGroups(BS_PATTERN_ONE, cmd);
        String firstSeatNo = allGroups.get(3);
        List<String> seatNos = bsDtos.stream()
                .map(BsDto::getSeatNo)
                .collect(Collectors.toList());
        if (seatNos.contains(firstSeatNo)) {
            List<String> seatNoList = new ArrayList<>();
            seatNoList.add(firstSeatNo);
            String multiSeatNo = allGroups.get(4);
            int dashCount = StrUtil.isNotEmpty(allGroups.get(2)) ? 1 : 0;
            if (StrUtil.isNotEmpty(multiSeatNo)) {
                String[] split = multiSeatNo.substring(1).split(",");
                List<String> multiSeatNoList = Arrays.asList(split);
                dashCount += multiSeatNoList.stream()
                        .filter(s -> s.startsWith(StrUtils.DASHED))
                        .count();
                for (String s : multiSeatNoList) {
                    seatNoList.add(s.startsWith(StrUtils.DASHED) ? s.substring(1) : s);
                }
                // 必须全部输入锁定或者解锁
                if (dashCount > 0 && dashCount != seatNoList.size()) {
                    throw new UnifiedResultException(Constant.FORMAT);
                }
            }
            for (String seatNo : seatNoList) {
                // 如果是格式1 且座位状态是X，则报错：ALREADY DONE
                long countX = bsDtos.stream()
                        .filter(bsDto -> seatNo.equals(bsDto.getSeatNo())
                                && Constant.TCARD_CYCLE_X.equals(bsDto.getSeatStatus())
                                && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))
                        )
                        .count();
                if (!cmd.contains(StrUtils.DASHED) && countX > 0) {
                    throw new UnifiedResultException(Constant.ALREADY_DONE);
                }
                // 如果是格式1 且座位状态是 .（英文句号），则报错：UNABLE
                long countPoint = bsDtos.stream()
                        .filter(bsDto -> seatNo.equals(bsDto.getSeatNo())
                                && Constant.OTHER.equals(bsDto.getSeatStatus())
                                && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))
                        )
                        .count();
                if (!cmd.contains(StrUtils.DASHED) && countPoint > 0) {
                    throw new UnifiedResultException(Constant.UNABLE);
                }
                //如果是格式1 且座位状态是 V或D，则报错：CONFLICT
                long count = bsDtos.stream()
                        .filter(bsDto -> seatNo.equals(bsDto.getSeatNo())
                                && (Constant.SEAT_STATUS_D.equals(bsDto.getSeatStatus()) || Constant.SEAT_STATUS_V.equals(bsDto.getSeatStatus()))
                                && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))
                        )
                        .count();
                if (!cmd.contains(StrUtils.DASHED) && count > 0) {
                    throw new UnifiedResultException(Constant.CONFLICT);
                }
                //如果是格式2 且座位状态不是X，则报错：ALREADY DONE
                long countNx = bsDtos.stream()
                        .filter(bsDto -> seatNo.equals(bsDto.getSeatNo())
                                && !Constant.TCARD_CYCLE_X.equals(bsDto.getSeatStatus())
                                && (ObjectUtil.equals(bs.getDep(), bsDto.getDep()) || ObjectUtil.equals(bs.getArr(), bsDto.getArr()))
                        )
                        .count();
                if (cmd.contains(StrUtils.DASHED) && countNx > 0) {
                    throw new UnifiedResultException(Constant.ALREADY_DONE);
                }
            }

            //条件没问题情况下修改状态
            //锁定座位
            List<BsDto> results = this.getBsDtosBySeatNo(bsDtos, bs, seatNoList);
            if (dashCount == 0) {
                results.forEach(r -> iMnjxSeatService.lambdaUpdate()
                        .eq(MnjxSeat::getSeatNo, r.getSeatNo())
                        .eq(MnjxSeat::getSeatId, r.getSeatId())
                        .set(MnjxSeat::getSeatStatusOld, r.getSeatStatus())
                        .set(MnjxSeat::getSeatStatus, "X").update());
            }
            //取消锁定座位
            else {
                List<String> seatIdList = results.stream()
                        .map(BsDto::getSeatId)
                        .collect(Collectors.toList());
                List<String> openCabinIdList = iMnjxSeatService.listByIds(seatIdList).stream()
                        .map(MnjxSeat::getOpenCabinId)
                        .collect(Collectors.toList());
                long count1 = iMnjxOpenCabinService.listByIds(openCabinIdList).stream()
                        .filter(o -> StrUtil.equalsAny(o.getOpenCabinStatus(), Constant.C, Constant.X))
                        .count();
                if (count1 > 0) {
                    throw new UnifiedResultException(Constant.UNABLE);
                }
                results.forEach(r -> iMnjxSeatService.lambdaUpdate()
                        .eq(MnjxSeat::getSeatNo, r.getSeatNo())
                        .eq(MnjxSeat::getSeatId, r.getSeatId())
                        .set(MnjxSeat::getSeatStatus, StrUtils.isNotEmpty(r.getSeatStatusOld()) ? r.getSeatStatusOld() : "C")
                        .set(MnjxSeat::getSeatStatusOld, r.getSeatStatus())
                        .update());
            }
            return iSeService.handler(cmdSub);
        } else {
            throw new UnifiedResultException(Constant.SEATS);
        }
    }

    private List<BsDto> getBsDtosBySeatNo(List<BsDto> bsDtos, BsDto bs, List<String> seatNoList) {
        return bsDtos.stream()
                .filter(b -> seatNoList.contains(b.getSeatNo()) && (ObjectUtil.equals(bs.getDep(), b.getDep()) || ObjectUtil.equals(bs.getArr(), b.getArr())))
                .collect(Collectors.toList());
    }

    private List<BsDto> getBsDtosBySeatRow(List<BsDto> bsDtos, BsDto bs, String seatRow) {
        return bsDtos.stream().filter(b -> seatRow.equals(b.getSeatRow()) && (ObjectUtil.equals(bs.getDep(), b.getDep()) || ObjectUtil.equals(bs.getArr(), b.getArr()))).collect(Collectors.toList());
    }

    private void parseSe(String lastAction) throws UnifiedResultException {
        if (!ObjectUtils.equals(lastAction, Constant.SE)) {
            throw new UnifiedResultException(Constant.NOSE);
        }
    }

}
