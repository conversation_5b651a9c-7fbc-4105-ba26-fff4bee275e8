package com.swcares.eterm.dcs.cki.service;


import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.PdInfoDto;

import java.util.List;

/**
 * RN指令的服务提供方法
 *
 * <AUTHOR>
 */

public interface IRnService {


    /**
     * 指令解析
     *
     * @param cmd RN指令
     * @return pd字符串
     * @throws UnifiedResultException 异常信息
     */
    String parseRn(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     *
     * @param pdCmdStr pd指令
     * @return 旅客数据
     * @throws UnifiedResultException 异常信息
     */
    List<PdInfoDto> handle(String pdCmdStr) throws UnifiedResultException;
}
