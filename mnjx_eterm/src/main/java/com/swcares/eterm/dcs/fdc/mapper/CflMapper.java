package com.swcares.eterm.dcs.fdc.mapper;

import com.swcares.eterm.dcs.fdc.dto.CflPlanFlightDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * description：CflMapper
 *
 * <AUTHOR>
 */
public interface CflMapper {

    /**
     * 查询计划航班
     *
     * @param airlineId
     * @param fltDate
     * @return
     */
    List<CflPlanFlightDto> retrievePlanFlight(@Param("airlineId") String airlineId, @Param("fltDate") String fltDate);
}
