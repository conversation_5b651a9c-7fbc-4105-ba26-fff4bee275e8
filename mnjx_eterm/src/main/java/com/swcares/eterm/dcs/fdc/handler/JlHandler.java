package com.swcares.eterm.dcs.fdc.handler;

import cn.hutool.core.util.StrUtil;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.dcs.fdc.service.IjlService;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 旅客登机名单统计
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@OperateType(action = "JL")
public class JlHandler implements Handler {

    @Resource
    private IjlService ijlService;

    /**
     * @param cmd       cmd
     * @return handleBusiness
     * @throws  UnifiedResultException 异常
     */
    @Override
    public String handle(String cmd) throws UnifiedResultException {
        String validateRes = ijlService.validateCmd(cmd);
        if (StrUtil.isNotEmpty(validateRes)) {
            return validateRes;
        }
        Map<String, Object> resMap = ijlService.dealCmd(cmd);
        if (resMap.containsKey("error")) {
            return resMap.get("error").toString();
        }
        return resMap.get("accepted").toString();
    }
}
