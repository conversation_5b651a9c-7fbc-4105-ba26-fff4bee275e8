package com.swcares.eterm.dcs.cki.mapper;

import com.swcares.eterm.dcs.cki.obj.dto.FuPlanSection;
import com.swcares.eterm.dcs.cki.obj.dto.FuTcardSection;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * description：FuMapper
 *
 * <AUTHOR>
 * @date 2021/7/21
 */
public interface FuMapper {
    /**
     * 查询航班是否在有效期内
     *
     * @param fltNo   航班号
     * @param fltDate 航班日期
     * @return 如果在有效期内，返回1条数据，如果不在返回0
     */
    Integer retrieveFltDate(@Param("fltNo") String fltNo, @Param("fltDate") String fltDate);


    /**
     * 根据航班号与航班日期查询该航班的航节飞行计划信息
     *
     * @param fltNo   航班号
     * @param fltDate 航班日期
     * @return 航节飞行计划信息
     */
    List<FuPlanSection> retrievePlanSection(@Param("fltNo") String fltNo, @Param("fltDate") String fltDate);

    /**
     * 查询航站信息
     *
     * @param fltNo 航班号
     * @return 查询tcard_section 航站信息
     */
    List<FuTcardSection> retrieveTcardSection(@Param("fltNo") String fltNo);

    /**
     * 修改计划航节中的飞机注册号
     *
     * @param planSectionList 航节飞行计划信息列表
     * @param planeId         飞机注册号id
     * @param isUpdate        选项参数标识（ERS 单项取消修改，RTNS 取消所有修改，还原到T-card状态）
     */
    void updatePlanSectionAndPlaneNo(@Param("planSectionList") List<FuPlanSection> planSectionList, @Param("planeId") String planeId, @Param("isUpdate") String isUpdate);
}
