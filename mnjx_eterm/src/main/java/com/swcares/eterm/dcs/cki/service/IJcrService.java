package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.JcrDto;
import com.swcares.eterm.dcs.cki.obj.dto.PaResultDto;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
public interface IJcrService {

    /**
     * 指令解析
     *
     * @param cmd 指令
     * @return 指令解析
     * @throws UnifiedResultException 统一异常
     */
    JcrDto parseCmd(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     *
     * @param jcrDto 指令对象
     * @return 业务处理
     * @throws UnifiedResultException 统一异常
     */
    PaResultDto handle(JcrDto jcrDto) throws UnifiedResultException;
}
