package com.swcares.eterm.dcs.cki.service;

import com.swcares.core.cache.MemoryData;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.HbpwCmdDto;
import com.swcares.eterm.dcs.cki.obj.dto.PwResultDto;

import java.util.List;

/**
 * description：IHbpwService <br>
 *
 * <AUTHOR> <br>
 * date 2022/08/17 <br>
 * @version v1.0 <br>
 */
public interface IHbpwService {

    /**
     * 指令解析
     *
     * @param cmd 指令
     * @return 指令解析
     * @throws UnifiedResultException 统一异常
     */
    List<HbpwCmdDto> parsePw(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     *
     * @param memoryData 内存对象
     * @param pwDtoList  指令数据
     * @return 业务处理
     * @throws UnifiedResultException 统一异常
     */
    List<PwResultDto> handle(MemoryData memoryData, List<HbpwCmdDto> pwDtoList) throws UnifiedResultException;

    void savePwForPrint(List<PwResultDto> pwResultDtoList);
}
