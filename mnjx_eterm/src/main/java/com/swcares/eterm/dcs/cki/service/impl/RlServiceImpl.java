package com.swcares.eterm.dcs.cki.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataFt;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.ReUtils;
import com.swcares.eterm.dcs.cki.obj.dto.PdInfoDto;
import com.swcares.eterm.dcs.cki.obj.dto.PdParamDto;
import com.swcares.eterm.dcs.cki.service.ICkiCacheService;
import com.swcares.eterm.dcs.cki.service.IPdService;
import com.swcares.eterm.dcs.cki.service.IRlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * RL处理 Service 旅客名单显示
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RlServiceImpl implements IRlService {

    private static final Pattern RL_PATTERN = Pattern.compile("RL:(\\w{6})((,[A-Z/]+)+)?");

    @Resource
    private IPdService iPdService;

    @Resource
    private ICkiCacheService iCkiCacheService;

    @Override
    public String parseRl(String cmd) throws UnifiedResultException {
        if (!ReUtils.isMatch(RL_PATTERN, cmd)) {
            throw new UnifiedResultException(Constant.FORMAT);
        }
        MemoryDataFt mdFt = MemoryDataUtils.getMemoryData().getMemoryDataFt();
        //判断是否已经设置默认航班
        if (StrUtil.isEmpty(mdFt.getFlightNo()) || StrUtil.isEmpty(mdFt.getFlightDate())) {
            throw new UnifiedResultException(Constant.NO_DEFAULT_FLIGHT);
        }
        String flightNo = mdFt.getFlightNo();
        String flightDate = mdFt.getFlightDate();
        String officeNo = MemoryDataUtils.getMemoryData().getMnjxOffice().getOfficeNo();
        String city = StrUtil.subPre(officeNo, 3);
        List<String> allGroups = ReUtils.getAllGroups(RL_PATTERN, cmd, false);
        String pnrCrs = allGroups.get(0);
        String options = allGroups.get(1);
        String pdCmdStr;
        if (StrUtil.isNotEmpty(options)) {
            pdCmdStr = StrUtil.format("PD:{}/{}*{},PNR{}{}", flightNo, DateUtils.ymd2Com(flightDate), city, pnrCrs, options);
        } else {
            pdCmdStr = StrUtil.format("PD:{}/{}*{},PNR{}", flightNo, DateUtils.ymd2Com(flightDate), city, pnrCrs);
        }
        return pdCmdStr;
    }

    @Override
    public List<PdInfoDto> handle(String pdCmdStr) throws UnifiedResultException {
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        List<PdInfoDto> res = new ArrayList<>();
        PdParamDto pdParamDto = iPdService.dealCmd(pdCmdStr);
        if (CollUtil.isNotEmpty(pdParamDto.getParams()) && pdParamDto.getParams().contains(Constant.ABC)) {
            res.add(iPdService.returnAbcInfo(memoryData, pdParamDto));
        } else if (CollUtil.isNotEmpty(pdParamDto.getParams()) && pdParamDto.getParams().contains(Constant.PARAM_SBY)) {
            res.add(iPdService.returnSbyInfo(memoryData, pdParamDto));
        } else {
            PdInfoDto pdDefaultInfo = iCkiCacheService.getPdDefaultInfo(memoryData);
            if (StrUtil.isBlank(pdParamDto.getQueryName())
                    || (StrUtil.isNotBlank(pdParamDto.getQueryName()) && !pdParamDto.getParams().contains("HBNB"))
                    || (pdParamDto.getParams().contains("HBNB") && StrUtil.isNotEmpty(pdParamDto.getQueryName()) && ObjectUtil.isNull(pdDefaultInfo))) {
                res.add(iPdService.returnDefaultInfo(memoryData, pdParamDto));
            } else {
                res.add(iPdService.returnPdInfo(pdParamDto));
            }
            iCkiCacheService.clearPrCache(memoryData);
            iCkiCacheService.clearPaCache(memoryData);
            iCkiCacheService.clearHbpaCache(memoryData);
            iCkiCacheService.clearHbpuCache(memoryData);
            iCkiCacheService.clearHbprCache(memoryData);
            iCkiCacheService.clearPdSbyInfo(memoryData);
            iCkiCacheService.clearPwCache(memoryData);
            iCkiCacheService.clearHbpwCache(memoryData);
        }
        return res;
    }
}
