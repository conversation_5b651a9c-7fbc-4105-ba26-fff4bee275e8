package com.swcares.eterm.dcs.cki.service;


import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.dcs.cki.obj.dto.RkcParamDto;

/**
 * 指令RKC处理 Service 建立及显示航班备注信息
 *
 * <AUTHOR>
 */

public interface IRkcService {

    /**
     * 指令解析
     *
     * @param cmd RKC指令
     * @return 参数对象
     * @throws UnifiedResultException 异常
     */
    RkcParamDto parseRkc(String cmd) throws UnifiedResultException;

    /**
     * 业务处理
     *
     * @param rkcParamDto 参数对象
     * @return 最终结果
     * @throws UnifiedResultException 异常
     */
    String handle(RkcParamDto rkcParamDto) throws UnifiedResultException;
}
