package com.swcares.eterm.crs.obj.vo;

import lombok.Data;

/**
 * Ddi查询返回对象
 *
 * <AUTHOR>
 */
@Data
public class DdiVo {
    /**
     * office主键
     */
    private String officeId;

    /**
     * office号
     */
    private String officeNo;

    /**
     * 航司二字码
     */
    private String airCodes;

    /**
     * 代理人ID
     */
    private String agentId;

    /**
     * 代理人Iata号
     */
    private String agentIata;

    /**
     * 代理人英文名
     */
    private String agentEname;

    /**
     * 联系人
     */
    private String agentContactCname;

    /**
     * 电话
     */
    private String agentContactPhone;

    /**
     * 地址
     */
    private String agentContactAddress;

    /**
     * 打票机序号
     */
    private String printerNo;

    /**
     * 打票机PID
     */
    private String printerPid;

    /**
     * SI工作号的PID编号
     */
    private String siPid;

    /**
     * SI工作号
     */
    private String siNo;

    /**
     * 货币类型
     */
    private String currencyType;

    /**
     * 统一显示BSP
     */
    private String tkt;

    /**
     * 票号范围
     */
    private String ticketRange;


}
