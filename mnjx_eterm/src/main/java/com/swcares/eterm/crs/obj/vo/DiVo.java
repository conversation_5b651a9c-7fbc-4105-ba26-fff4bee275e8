package com.swcares.eterm.crs.obj.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * TOL 模板返回参数
 *
 * <AUTHOR> by yaodan
 */
@Data
public class DiVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 打票机ID
     */
    private String printerId;

    /**
     * 打票机序号
     */
    private String printerNo;

    /**
     * 打票机所属OFFICE号
     */
    private String office;

    private String controlPid;

    private String controlAgent;

    /**
     * Si id
     */
    private String siId;

    /**
     * 打票机本身的PID
     */
    private String printerPid;

    /**
     *
     */
    private String printerStatus;

    private String inputStatus;

    private String outputStatus;

    private String isNormal;
    /**
     * 打票机属性
     */
    private String printAttribute;
    /**
     * 打票机模式
     */
    private String printerMode;
    /**
     * 货币类型
     */
    private String currencyType;

    /**
     * 打票机类型
     */
    private String printerType;

    /**
     * 起始票号
     */
    private String startTicket;

    /**
     * 票证归属
     */
    private String airline;

    /**
     * 结束票号
     */
    private String endTicket;

    /**
     * 本打票机出的最后一张票
     */
    private String lastTicket;

    private String ticketStart;

    private String ticketEnd;
}
