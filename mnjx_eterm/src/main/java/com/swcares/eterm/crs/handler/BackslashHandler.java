package com.swcares.eterm.crs.handler;

import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IAtService;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@OperateType(action = "\\", shorthand = true, template = "crs/at.jf")
public class BackslashHandler implements Handler {

    @Resource
    private IAtService iAtService;

    @Override
    public Object handle(String cmd) throws UnifiedResultException, IOException {
        // 就是检查指令输入是否符合格式
        iAtService.parseAt(cmd);
        // 指令的业务逻辑处理
        iAtService.handleAt(cmd);
        // 重新提取PNR数据
        return iAtService.recall();
    }
}
