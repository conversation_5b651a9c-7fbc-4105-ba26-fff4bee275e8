package com.swcares.eterm.crs.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.core.cache.MemoryData;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.DateUtils;
import com.swcares.core.util.IdUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.entity.*;
import com.swcares.eterm.crs.mapper.RfisMapper;
import com.swcares.eterm.crs.obj.dto.TrfdZdto;
import com.swcares.eterm.crs.service.IRfisService;
import com.swcares.eterm.crs.service.ITrfdService;
import com.swcares.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * RFIS指令业务处理
 *
 * <AUTHOR>
 */
@Service
public class RfisServiceImpl implements IRfisService {

    @Resource
    private IMnjxPnrNmTicketService iMnjxPnrNmTicketService;

    @Resource
    private IMnjxPnrNmTnService iMnjxPnrNmTnService;
    @Resource
    private IMnjxSiService iMnjxSiService;
    @Resource
    private IMnjxOfficeService iMnjxOfficeService;

    @Resource
    private RfisMapper rfisMapper;
    @Resource
    private IMnjxPnrGnService iMnjxPnrGnService;
    @Resource
    private IMnjxNmXnService iMnjxNmXnService;
    @Resource
    private IMnjxNmOiService iMnjxNmOiService;
    @Resource
    private IMnjxOpenCabinService iMnjxOpenCabinService;
    @Resource
    private IMnjxPnrSegService iMnjxPnrSegService;
    @Resource
    private IMnjxAirportService iMnjxAirportService;
    @Resource
    private IMnjxPsgCkiService iMnjxPsgCkiService;
    @Resource
    private IMnjxPsgSeatService iMnjxPsgSeatService;
    @Resource
    private IMnjxPsgCkiOptionService iMnjxPsgCkiOptionService;
    @Resource
    private ITrfdService iTrfdService;
    @Resource
    private IMnjxRefundTicketService iMnjxRefundTicketService;
    @Resource
    private IMnjxTicketOperateRecordService iMnjxTicketOperateRecordService;
    @Resource
    private IMnjxPlanSectionService iMnjxPlanSectionService;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void handle(String ticketNo) throws UnifiedResultException {
        MnjxPnrNmTicket pnrNmTicket = iMnjxPnrNmTicketService.lambdaQuery()
                .eq(MnjxPnrNmTicket::getTicketNo, ticketNo)
                .one();
        if (ObjectUtil.isEmpty(pnrNmTicket)) {
            throw new UnifiedResultException("票号不存在");
        }
        //查询票号属于哪个部门的
        MnjxPnrNmTn pnrNmTn = iMnjxPnrNmTnService.getById(pnrNmTicket.getPnrNmTnId());
        if (ObjectUtil.isEmpty(pnrNmTn)) {
            throw new UnifiedResultException(Constant.NOT_EXIST);
        }
        // 参数校验
        this.checkParam(pnrNmTicket, pnrNmTn);
        //更新客票状态
        this.updateTicketStatus(pnrNmTicket);
        //释放开舱座位
        this.updateAvailableSeat(pnrNmTicket);
        //删除mnjx_psg_cki mnjx_psg_cki_option mnjx_psg_seat
        this.deletePsgCki(pnrNmTicket);
        MemoryData memoryData = MemoryDataUtils.getMemoryData();
        MnjxSi mnjxSi = memoryData.getMemoryDataContainer().getActiveMemoryDataCabinet().getMnjxSi();
        //生成退票记录
        TrfdZdto trfdZdto = iTrfdService.handle(memoryData);
        MnjxRefundTicket refundTicket = trfdZdto.getRefundTicket();
        iMnjxRefundTicketService.save(refundTicket);
        //新增客票操作记录
        MnjxTicketOperateRecord operateRecord = structureOperateRecord(pnrNmTicket, mnjxSi);
        operateRecord.setRefundNo(refundTicket.getRefundNo());
        iMnjxTicketOperateRecordService.save(operateRecord);
    }

    /**
     * 构建客票操作记录
     */
    private MnjxTicketOperateRecord structureOperateRecord(MnjxPnrNmTicket pnrNmTicket, MnjxSi mnjxSi) {
        MnjxTicketOperateRecord operateRecord = new MnjxTicketOperateRecord();
        operateRecord.setTicketOperateRecordId(IdUtils.getSnowflake(1, 1).nextIdStr());
        operateRecord.setOperateTime(DateUtils.now());
        operateRecord.setSettlementCode(StrUtils.subPre(pnrNmTicket.getTicketNo(), 3));
        operateRecord.setSiNo(mnjxSi.getSiNo());
        operateRecord.setTicketStatus1(Constant.REFUNDED);
        if (StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2())) {
            operateRecord.setTicketStatus2(Constant.REFUNDED);
        }
        operateRecord.setTicketNo(pnrNmTicket.getTicketNo());
        return operateRecord;
    }

    /**
     * 删除PsgCki数据
     */
    private void deletePsgCki(MnjxPnrNmTicket pnrNmTicket) {
        String s1Id = pnrNmTicket.getS1Id();
        String s2Id = pnrNmTicket.getS2Id();
        List<String> segNoList = new ArrayList<>();
        MnjxPnrSeg segId2;
        MnjxPnrSeg segId1;
        //处理多个航段时，进行退票时，只退其中某一段
        if (StrUtil.isNotEmpty(s1Id) && StrUtil.isEmpty(s2Id)) {
            //缺口程在第二段或者第四段，或者只有一段
            segId1 = iMnjxPnrSegService.getById(s1Id);
            Integer pnrSegNo = segId1.getPnrSegNo();
            segNoList.add(String.valueOf(pnrSegNo));
            segNoList.add(String.valueOf(pnrSegNo + 1));
        } else if (StrUtil.isEmpty(s1Id) && StrUtil.isNotEmpty(s2Id)) {
            //缺口程在第一段或者第三段
            segId2 = iMnjxPnrSegService.getById(s2Id);
            Integer pnrSegNo = segId2.getPnrSegNo();
            segNoList.add(String.valueOf(pnrSegNo));
            segNoList.add(String.valueOf(pnrSegNo - 1));
        } else {
            //两段都有
            segId1 = iMnjxPnrSegService.getById(s1Id);
            segId2 = iMnjxPnrSegService.getById(s2Id);
            Integer pnrSegNo1 = segId1.getPnrSegNo();
            Integer pnrSegNo2 = segId2.getPnrSegNo();
            segNoList.add(String.valueOf(pnrSegNo1));
            segNoList.add(String.valueOf(pnrSegNo2));
        }
        String tnId = pnrNmTicket.getPnrNmTnId();
        MnjxPnrNmTn nmTn = iMnjxPnrNmTnService.getById(tnId);
        String pnrNmId = nmTn.getPnrNmId();
        List<MnjxPsgCki> psgCkis = iMnjxPsgCkiService.lambdaQuery()
                .eq(MnjxPsgCki::getPnrNmId, pnrNmId)
                .list();
        List<String> psgCkiIds = psgCkis.stream().filter(p->segNoList.contains(p.getPnrSegNo()))
                .map(MnjxPsgCki::getPsgCkiId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(psgCkiIds)) {
            //根据SegNo筛选删除cki相关记录
            iMnjxPsgSeatService.lambdaUpdate().in(MnjxPsgSeat::getPsgCkiId, psgCkiIds).remove();
            iMnjxPsgCkiService.removeByIds(psgCkiIds);
        }
    }

    /**
     * 参数校验
     */
    private void checkParam(MnjxPnrNmTicket pnrNmTicket, MnjxPnrNmTn pnrNmTn) throws UnifiedResultException {
        //不支持对非ET客票进行全自动退票
        if (!Constant.STR_ONE.equals(pnrNmTicket.getIsEt())) {
            throw new UnifiedResultException(Constant.TKT_NOT_ET);
        }
        MnjxOffice office = getMnjxOffice(pnrNmTn.getIssuedSiId());
        //内存中正在登的部门号
        MnjxOffice mnjxOffice = MemoryDataUtils.getMemoryData().getMnjxOffice();
        //无权退其他 office 票
        if (!mnjxOffice.getOfficeNo().equals(office.getOfficeNo())) {
            throw new UnifiedResultException(Constant.HAVE_NO_AUTHORITY_TO_REFUND_OTHEROFFICE_TICKET);
        }
        //获取pnrId
        String pnrId = rfisMapper.retrievePnrId(pnrNmTn.getPnrNmId(), pnrNmTn.getNmXnId());
        MnjxPnrGn pnrGn = iMnjxPnrGnService.lambdaQuery().eq(MnjxPnrGn::getPnrId, pnrId).one();
        //不支持团队客票自动退票
        if (ObjectUtil.isNotEmpty(pnrGn)) {
            throw new UnifiedResultException(Constant.GROUP_PNR_MANUAL_REFUND_REQUIRED);
        }
        String pnrNmId;
        if (StrUtil.isNotEmpty(pnrNmTn.getNmXnId())) {
            MnjxNmXn nmXn = iMnjxNmXnService.getById(pnrNmTn.getNmXnId());
            pnrNmId = nmXn.getPnrNmId();
        } else {
            pnrNmId = pnrNmTn.getPnrNmId();
        }
        MnjxNmOi mnjxNmOi = iMnjxNmOiService.lambdaQuery().eq(MnjxNmOi::getPnrNmId, pnrNmId).one();
        //不支持部分已使用客票换开后退票请手工退票
        if (ObjectUtil.isNotEmpty(mnjxNmOi)) {
            throw new UnifiedResultException(Constant.REISSUED_TICKET_HAS_BEEN_PARTIALLY_USED_MANUAL_REFUND_REQUIRED);
        }
    }

    /**
     * 查询出票相关的部门
     */
    private MnjxOffice getMnjxOffice(String issueSiId) {
        MnjxSi mnjxSi = iMnjxSiService.getById(issueSiId);
        return iMnjxOfficeService.lambdaQuery()
                .eq(MnjxOffice::getOfficeId, mnjxSi.getOfficeId())
                .one();
    }

    /**
     * 更新mnjxNmTicket表
     */
    private void updateTicketStatus(MnjxPnrNmTicket pnrNmTicket) {
        iMnjxPnrNmTicketService.lambdaUpdate()
                .eq(MnjxPnrNmTicket::getNmTicketId, pnrNmTicket.getNmTicketId())
                .set(MnjxPnrNmTicket::getTicketStatus1, Constant.REFUNDED)
                .set(StrUtil.isNotEmpty(pnrNmTicket.getTicketStatus2()), MnjxPnrNmTicket::getTicketStatus2, Constant.REFUNDED)
                .update();
    }

    /**
     * Title: updateAvailableSeat
     * Description: 更新对应销售舱位剩余座位数
     */
    private void updateAvailableSeat(MnjxPnrNmTicket pnrNmTicket) {
        MnjxPnrSeg pnrSeg1 = iMnjxPnrSegService.getById(pnrNmTicket.getS1Id());
        //有第一段的情况
        if (ObjectUtil.isNotEmpty(pnrSeg1)) {
            List<MnjxOpenCabin> openCabins = retrieveOpenCabin(pnrSeg1);
            iMnjxOpenCabinService.updateBatchById(openCabins);
        }
        //如果有第二段的情况
        if (StrUtil.isNotEmpty(pnrNmTicket.getS2Id())) {
            MnjxPnrSeg pnrSeg2 = iMnjxPnrSegService.getById(pnrNmTicket.getS1Id());
            if (ObjectUtil.isNotEmpty(pnrSeg2)) {
                List<MnjxOpenCabin> openCabins = retrieveOpenCabin(pnrSeg2);
                iMnjxOpenCabinService.updateBatchById(openCabins);
            }
        }
    }

    /**
     * 查询开舱数据，并更新已退票的的旅客开舱座位数
     */
    private List<MnjxOpenCabin> retrieveOpenCabin(MnjxPnrSeg pnrSeg) {
        String flightNo = pnrSeg.getFlightNo();
        String flightDate = pnrSeg.getFlightDate();
        String sellCabin = pnrSeg.getSellCabin();
        int seatNumber = pnrSeg.getSeatNumber();
        List<MnjxOpenCabin> openCabinList = rfisMapper.retrieveOpenCabinList(flightNo, flightDate, sellCabin);
        openCabinList = this.setMultiSegOpenCabin(pnrSeg.getOrg(), pnrSeg.getDst(), openCabinList);
        if (ObjectUtil.isNotEmpty(openCabinList)) {
            for (MnjxOpenCabin mnjxOpenCabin : openCabinList) {
                int availableNumber = mnjxOpenCabin.getSeatAvailable() + seatNumber;
                mnjxOpenCabin.setSeatAvailable(availableNumber);
            }
        }
        //更新释放退票的开舱座位
        return openCabinList;
    }

    private List<MnjxOpenCabin> setMultiSegOpenCabin(String org, String dst, List<MnjxOpenCabin> mnjxOpenCabins) {
        List<String> planSectionIdList = mnjxOpenCabins.stream()
                .map(MnjxOpenCabin::getPlanSectionId)
                .distinct()
                .collect(Collectors.toList());
        // 多航节航班需要判断消耗哪一节的座位数
        if (planSectionIdList.size() > 1) {
            MnjxAirport orgAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, org)
                    .one();
            MnjxAirport dstAirport = iMnjxAirportService.lambdaQuery()
                    .eq(MnjxAirport::getAirportCode, dst)
                    .one();
            List<MnjxPlanSection> planSectionList = iMnjxPlanSectionService.lambdaQuery()
                    .in(MnjxPlanSection::getPlanSectionId, planSectionIdList)
                    .orderByAsc(MnjxPlanSection::getEstimateOff)
                    .list();
            List<MnjxPlanSection> singlePlanSectionList = planSectionList.stream()
                    .filter(s -> orgAirport.getAirportId().equals(s.getDepAptId()) && dstAirport.getAirportId().equals(s.getArrAptId()))
                    .collect(Collectors.toList());
            // 如果为空说明出发到达包含了多个航节
            if (CollUtil.isEmpty(singlePlanSectionList)) {
                List<MnjxPlanSection> list = new ArrayList<>();
                for (MnjxPlanSection planSection : planSectionList) {
                    if (CollUtil.isNotEmpty(list)) {
                        list.add(planSection);
                        if (dstAirport.getAirportId().equals(planSection.getArrAptId())) {
                            break;
                        }
                    } else if (orgAirport.getAirportId().equals(planSection.getDepAptId())) {
                        list.add(planSection);
                    }
                }
                List<String> finalPlanSectionIdList = list.stream()
                        .map(MnjxPlanSection::getPlanSectionId)
                        .collect(Collectors.toList());
                return mnjxOpenCabins.stream()
                        .filter(o -> finalPlanSectionIdList.contains(o.getPlanSectionId()))
                        .collect(Collectors.toList());
            } else {
                List<String> finalPlanSectionIdList = singlePlanSectionList.stream()
                        .map(MnjxPlanSection::getPlanSectionId)
                        .collect(Collectors.toList());
                return mnjxOpenCabins.stream()
                        .filter(o -> finalPlanSectionIdList.contains(o.getPlanSectionId()))
                        .collect(Collectors.toList());
            }
        } else {
            return mnjxOpenCabins;
        }
    }

}