package com.swcares.eterm.crs.handler;

import cn.hutool.core.util.ReUtil;
import com.swcares.core.cache.MemoryDataUtils;
import com.swcares.core.type.OperateType;
import com.swcares.core.unified.UnifiedResult;
import com.swcares.core.unified.UnifiedResultException;
import com.swcares.core.util.Constant;
import com.swcares.core.util.ListUtils;
import com.swcares.core.util.StrUtils;
import com.swcares.eterm.base.handler.Handler;
import com.swcares.eterm.crs.service.IXiService;

import javax.annotation.Resource;

/**
 * XI指令验证
 * eg: XI:1
 *
 * <AUTHOR>
 */
@OperateType(action = "XI", shorthand = true, template = "/crs/xi.jf")
public class XiHandler implements Handler {

    @Resource
    private IXiService iXiService;

    @Override
    public UnifiedResult handle(String cmd) throws UnifiedResultException {
        // 统一返回对象
        UnifiedResult unifiedResult = new UnifiedResult();
        // XI:1
        cmd = cmd.replace(StrUtils.SPACE, StrUtils.EMPTY).toUpperCase();
        String reg = "XI[:|\\s](\\d{1,3})";
        // 指令格式验证
        if (ReUtil.isMatch(reg, cmd)) {
            //打印机序号
            String printNo = ReUtil.get(reg, cmd, 1);
            String res = iXiService.handle(MemoryDataUtils.getMemoryData(), printNo);
            unifiedResult.setResults(ListUtils.toList(res).toArray());
            return unifiedResult;
        } else {
            throw new UnifiedResultException(Constant.FORMAT);
        }
    }
}
