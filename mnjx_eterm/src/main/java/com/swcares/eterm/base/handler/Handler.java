package com.swcares.eterm.base.handler;

import com.swcares.core.unified.UnifiedResultException;

import java.io.IOException;

/**
 * 策略模式
 * 一个通用接口，用于处理各项不同的指令
 *
 * <AUTHOR>
 * @version 2010-01-07
 */
public interface Handler {
    /**
     * 分别对不同的指令进行处理的方法
     *
     * @param cmd 指令
     * @return 返回结果
     * @throws UnifiedResultException 自定义的异常
     * @throws IOException            io异常
     */
    Object handle(String cmd) throws UnifiedResultException, IOException, ClassNotFoundException;
}
